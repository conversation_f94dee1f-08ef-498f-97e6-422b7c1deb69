using System;
using System.Threading.Tasks;
using BBC.Test.Tests.Viagem.Fixture;
using Moq;
using SistemaInfo.BBC.Application.Objects.Mobile.Viagem.Request;
using SistemaInfo.BBC.Application.Services.Viagem;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Domain.Models.PagamentoEvento.Repository;
using SistemaInfo.BBC.Domain.Models.Transacao.Repository;
using SistemaInfo.BBC.Domain.Models.Viagem.Repository;
using Xunit;

namespace BBC.Test.Tests.Viagem
{
    [Collection(nameof(ViagemCollection))]
    public class ViagemMobileAppServiceAlterarStatusAntecipacaoSimpleTest
    {
        private readonly ViagemFixture _fixture;
        private readonly ViagemMobileAppService _appService;
        private readonly Mock<IPagamentoEventoReadRepository> _pagamentoEventoReadRepository;
        private readonly Mock<IPagamentoEventoWriteRepository> _pagamentoEventoWriteRepository;
        private readonly Mock<ITransacaoReadRepository> _transacaoReadRepository;

        public ViagemMobileAppServiceAlterarStatusAntecipacaoSimpleTest(ViagemFixture fixture)
        {
            _fixture = fixture;
            _pagamentoEventoReadRepository = fixture.Mocker.GetMock<IPagamentoEventoReadRepository>();
            _pagamentoEventoWriteRepository = fixture.Mocker.GetMock<IPagamentoEventoWriteRepository>();
            _transacaoReadRepository = fixture.Mocker.GetMock<ITransacaoReadRepository>();
            _appService = fixture.Mocker.CreateInstance<ViagemMobileAppService>();
        }

        [Theory(DisplayName = "AlterarStatusAntecipacao - Deve falhar com filtros inválidos")]
        [Trait(nameof(ViagemMobileAppService), nameof(ViagemMobileAppService.AlterarStatusAntecipacao))]
        [InlineData(0, 1, "Informe um ViagemId válido.")]
        [InlineData(-1, 1, "Informe um ViagemId válido.")]
        [InlineData(1, 0, "Informe um PagamentoId válido.")]
        [InlineData(1, -1, "Informe um PagamentoId válido.")]
        public async Task AlterarStatusAntecipacao_FiltrosInvalidos_DeveLancarExcecao(
            int viagemId, int pagamentoId, string mensagemEsperada)
        {
            // Arrange
            var request = new AtualizarStatusAntecipacaoRequest
            {
                ViagemId = viagemId,
                PagamentoId = pagamentoId,
                StatusAntecipacao = StatusAntecipacaoRequest.AguardandoProcessamento
            };

            // Act & Assert
            var exception = await Assert.ThrowsAsync<Exception>(() => _appService.AlterarStatusAntecipacao(request));
            Assert.Equal(mensagemEsperada, exception.Message);
        }

        [Fact(DisplayName = "AlterarStatusAntecipacao - Validação de request válido")]
        [Trait(nameof(ViagemMobileAppService), nameof(ViagemMobileAppService.AlterarStatusAntecipacao))]
        public void AlterarStatusAntecipacao_RequestValido_NaoDeveLancarExcecaoValidacao()
        {
            // Arrange
            var request = _fixture.GerarAtualizarStatusAntecipacaoRequest(
                viagemId: 1, 
                pagamentoId: 1, 
                status: StatusAntecipacaoRequest.AguardandoProcessamento);

            // Act & Assert - Validação não deve lançar exceção
            var validacao = request.validarFiltros();
            Assert.Null(validacao);
        }

        [Theory(DisplayName = "AlterarStatusAntecipacao - Conversão de enum")]
        [Trait(nameof(ViagemMobileAppService), nameof(ViagemMobileAppService.AlterarStatusAntecipacao))]
        [InlineData(StatusAntecipacaoRequest.AguardandoProcessamento, StatusAntecipacaoParcelaProprietario.AguardandoProcessamento)]
        [InlineData(StatusAntecipacaoRequest.Aprovado, StatusAntecipacaoParcelaProprietario.Aprovado)]
        [InlineData(StatusAntecipacaoRequest.Erro, StatusAntecipacaoParcelaProprietario.Erro)]
        public void AlterarStatusAntecipacao_ConversaoEnum_DeveConverterCorretamente(
            StatusAntecipacaoRequest statusRequest, StatusAntecipacaoParcelaProprietario statusEsperado)
        {
            // Act
            var statusConvertido = statusRequest.ParaStatusParcelaProprietario();

            // Assert
            Assert.Equal(statusEsperado, statusConvertido);
        }

        [Fact(DisplayName = "AlterarStatusAntecipacao - Geração de dados fake")]
        [Trait(nameof(ViagemMobileAppService), nameof(ViagemMobileAppService.AlterarStatusAntecipacao))]
        public void AlterarStatusAntecipacao_GeracaoDadosFake_DeveGerarDadosValidos()
        {
            // Act
            var request = _fixture.GerarAtualizarStatusAntecipacaoRequest();
            var pagamentoEvento = _fixture.GerarPagamentoEventoComStatusAntecipacao();
            var transacao = _fixture.GerarTransacaoRetencao(1);

            // Assert
            Assert.True(request.ViagemId > 0);
            Assert.True(request.PagamentoId > 0);
            Assert.NotNull(pagamentoEvento);
            Assert.NotNull(pagamentoEvento.Viagem);
            Assert.NotNull(transacao);
            Assert.Equal(FormaPagamentoEvento.RetencaoAntecipacao, transacao.FormaPagamento);
            Assert.Equal(StatusPagamento.Fechado, transacao.Status);
        }

        [Fact(DisplayName = "AlterarStatusAntecipacao - Estrutura de resposta")]
        [Trait(nameof(ViagemMobileAppService), nameof(ViagemMobileAppService.AlterarStatusAntecipacao))]
        public void AlterarStatusAntecipacao_EstruturaResposta_DeveConterPropriedadesNecessarias()
        {
            // Arrange
            var pagamentoEvento = _fixture.GerarPagamentoEventoComStatusAntecipacao(
                StatusAntecipacaoParcelaProprietario.Disponivel);

            // Act - Mapear para response usando o mapper do fixture
            var response = _fixture.Mocker.GetMock<SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine.IAppEngine>()
                .Object.Mapper.Map<SistemaInfo.BBC.Application.Objects.Mobile.Viagem.Response.PagamentoMobileResponse>(pagamentoEvento);

            // Assert
            Assert.NotNull(response);
            // Verificar se as propriedades essenciais estão presentes
            Assert.Equal(pagamentoEvento.Id, response.Id);
            Assert.Equal(pagamentoEvento.StatusAntecipacaoParcelaProprietario, response.StatusAntecipacaoParcelaProprietario);
        }
    }
}

using System.Collections.Generic;
using SistemaInfo.BBC.Domain.External.CIOT.DTO;

namespace SistemaInfo.BBC.Domain.Contracts.Operacoes
{
    
    public class ConsultarBancosReqMessage
    {
    }
    
    public class ConsultarBancosRespMessage
    {
        public ConsultarBancosRespMessage(bool sucesso, string excecao)
        {
            Sucesso = sucesso;
            Erro = new Excecao()
            {
                Mensagem = excecao
            };
        }
        public ConsultarBancosRespMessage() { }
        public bool Sucesso { get; set; }
        public Excecao Erro { get; set; }
        public List<Banco> Bancos { get; set; }
    }
}
<div class="container main-container">
    <form novalidate (ngSubmit)="avancar()" [formGroup]="motoristaForm">
        <div class="form-horizontal">
            <div class="row">
                <div class="col-sm-12 col-md-6 col-lg-3">
                    <div class="form-group required" [ngClass]="{'has-error': displayMessage.cpf}">
                        <label class="control-label" for="formaPagamento">CPF</label>
                        <cpf [control]="motoristaForm.get('cpf')"></cpf>
                        <span class="text-danger" *ngIf="displayMessage.cpf">
                            <p [innerHTML]="displayMessage.cpf"></p>
                        </span>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12 col-md-6 col-lg-3">
                    <div class="form-group required" [ngClass]="{'has-error': displayMessage.nome}">
                        <label class="control-label" for="nome">Nome</label>
                        <input class="form-control" id="nome" type="text" formControlName="nome" maxlength="100" />
                        <span class="text-danger" *ngIf="displayMessage.nome">
                            <p [innerHTML]="displayMessage.nome"></p>
                        </span>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12 col-md-6 col-lg-3">
                    <div class="form-group required" [ngClass]="{'has-error': displayMessage.cnh}">
                        <label class="control-label" for="nome">Número CNH</label>
                        <input class="form-control" maxlength="11" id="cnh" type="text" formControlName="cnh"
                            OnlyNumber />
                        <span class="text-danger" *ngIf="displayMessage.cnh">
                            <p [innerHTML]="displayMessage.cnh"></p>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using SistemaInfo.BBC.Application.External.Conductor.Interface;
using SistemaInfo.BBC.Application.Interface.Portador;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.Cartao;
using SistemaInfo.BBC.Application.Objects.Web.Portador;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Domain.Models.Portador;
using SistemaInfo.BBC.Web.Attributes;
using SistemaInfo.BBC.Web.Controllers.Base;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Web.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [Route("Portador")]
    public class PortadorController : WebControllerBase<IPortadorAppService>
    {
        private readonly ICartaoAppService _cartaoAppService;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="engine"></param>
        /// <param name="appService"></param>
        /// <param name="cartaoAppService"></param>
        public PortadorController(IAppEngine engine, IPortadorAppService appService, ICartaoAppService cartaoAppService)
            : base(engine, appService)
        {
            _cartaoAppService = cartaoAppService;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("ConsultarGridPortador")]
        [Menu(new[] { EMenus.Veiculo, EMenus.Portador  })]
        public JsonResult ConsultarGridPortador([FromBody] BaseGridRequest request)
        {
            try
            {
                var consultarGridPortador = AppService.ConsultarGridPortador(request.Take, request.Page,
                    request.Order, request.Filters);

                return ResponseBase.ResponderSucesso(consultarGridPortador);
            }
            catch (Exception e)
            {
                return ResponseBase.ResponderErro(e);
            }
        }
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("ConsultarGridUsuarioFrota")]
        [Menu(new[] { EMenus.Veiculo, EMenus.Portador, EMenus.UsuarioFrota  })]
        public JsonResult ConsultarGridUsuarioFrota([FromBody] BaseGridRequest request)
        {
            try
            {
                var consultarGridPortador = AppService.ConsultarGridPortador(request.Take, request.Page,
                    request.Order, request.Filters);

                return ResponseBase.ResponderSucesso(consultarGridPortador);
            }
            catch (Exception e)
            {
                return ResponseBase.ResponderErro(e);
            }
        }
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("ConsultarGridPortadorCombo")]
        [Menu(new[] { EMenus.Emprestimo, EMenus.PainelCiot, EMenus.PainelPagamento })]
        public JsonResult ConsultarGridPortadorCombo([FromBody] BaseGridRequest request)
        {
            try
            {
                var consultarGridPortador = AppService.ConsultarGridPortadorCombo(request.Take, request.Page,
                    request.Order, request.Filters);

                return ResponseBase.ResponderSucesso(consultarGridPortador);
            }
            catch (Exception e)
            {
                return ResponseBase.ResponderErro(e);
            }
        }
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("ConsultarGridPortadorEmpresaCombo")]
        [Menu(new[] { EMenus.Emprestimo, EMenus.PainelCiot, EMenus.PainelPagamento })]
        public async Task<JsonResult> ConsultarGridPortadorEmpresaCombo([FromBody] BaseGridRequest request)
        {
            try
            {
                var consultarGridPortador = await AppService.ConsultarGridPortadorEmpresaCombo(request.Take, request.Page,
                    request.Order, request.Filters);

                return ResponseBase.ResponderSucesso(consultarGridPortador);
            }
            catch (Exception e)
            {
                return ResponseBase.ResponderErro(e);
            }
        }
        
        

        /// <summary>
        /// 
        /// </summary>
        /// <param name="lModel"></param>
        /// <returns></returns>
        [HttpPost("Salvar")]
        [Menu(new[] { EMenus.Portador })]
        public JsonResult Salvar([FromBody] PortadorRequest lModel)
        {
            try
            {
                var lSavePortador = AppService.Save(lModel).Result;
                
                return ResponseBase.BigJson(lSavePortador);
            }
            catch (Exception e)
            {
                return ResponseBase.ResponderErro("Não foi possível realizar a operação. Mensagem: " + e.Message);
            }
        }
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("CadastrarPortadorAutomatico")]
        [Menu(new[] { EMenus.PainelCiot })]
        public async Task<JsonResult> CadastrarPortadorAutomatico([FromBody] CadastroPortadorPainelCiotRequest request) =>
            ResponseBase.Responder(await AppService.CadastrarPortadorAutomatico(request));
        
        

        /// <summary>
        /// 
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("ConsultarGridPortadorPessoaFisica")]
        [Menu(new[] { EMenus.Empresa, EMenus.PainelCiot, EMenus.Portador })]
        public async Task<JsonResult> ConsultarGridPortadorPessoaFisica([FromBody] BaseGridRequest request)
        {
            try
            {
                var consultarGridPortador = await AppService.ConsultarGridPortadorPessoaFisica(request.Take, request.Page,
                    request.Order, request.Filters);

                return ResponseBase.ResponderSucesso(consultarGridPortador);
            }
            catch (Exception e)
            {
                return ResponseBase.ResponderErro(e);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="lPortadorStatus"></param>
        /// <returns></returns>
        [HttpPost("AlterarStatus")]
        [Menu(new[] { EMenus.Portador })]
        public async Task<JsonResult> AlterarStatus([FromBody] PortadorStatusRequest lPortadorStatus)
        {
            var lAlterarStatus = await AppService.AlterarStatus(lPortadorStatus);
            return lAlterarStatus.sucesso
                ? ResponseBase.ResponderSucesso(lAlterarStatus)
                : ResponseBase.ResponderErro(lAlterarStatus.mensagem);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="idPortador"></param>
        /// <returns></returns>
        [HttpGet("BuscarPorId")]
        [Menu(new[] { EMenus.Portador })]
        public JsonResult BuscarPorId(int idPortador)
        {
            try
            {
                return ResponseBase.ResponderSucesso(AppService.BuscarPorId(idPortador));
            }
            catch (Exception e)
            {
                return ResponseBase.ResponderErro("Erro ao consultar portador! Mensagem: " + e.Message);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="lPortadorRequest"></param>
        /// <returns></returns>
        [HttpPost("AdicionarCartao")]
        [Menu(new[] { EMenus.Portador })]
        public JsonResult AdicionarCartao([FromBody] PortadorRequest lPortadorRequest)
        {
            try
            {
                var response = AppService.AdicionarCartao(lPortadorRequest)?.Result;
                return !response.sucesso
                    ? ResponseBase.ResponderErro(response.mensagem)
                    : ResponseBase.ResponderSucesso(response);
            }
            catch (Exception e)
            {
                return ResponseBase.ResponderErro("Não foi possível realizar a operação. Mensagem: " + e.Message);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="lDtoConta"></param>
        /// <returns></returns>
        [HttpPost("ReativarConta")]
        [Menu(new[] { EMenus.Portador })]
        public JsonResult ReativarConta([FromBody] DtoConta lDtoConta)
        {
            try
            {
                var response = _cartaoAppService.ReativarConta(lDtoConta.idConta).Result;
                return response == null
                    ? ResponseBase.ResponderSucesso(null)
                    : ResponseBase.ResponderErro(response.message);
            }
            catch (Exception e)
            {
                return ResponseBase.ResponderErro(e.Message);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="lDtoConta"></param>
        /// <returns></returns>
        [HttpPost("BloquearConta")]
        [Menu(new[] { EMenus.Portador })]
        public JsonResult BloquearConta([FromBody] DtoConta lDtoConta)
        {
            try
            {
                var response = _cartaoAppService.BloquearConta(lDtoConta.idConta, EStatusConta.Bloqueada.ToInt())
                    .Result;
                return !response.Sucesso
                    ? ResponseBase.ResponderErro(response.message)
                    : ResponseBase.ResponderSucesso(response);
            }
            catch (Exception e)
            {
                return ResponseBase.ResponderErro(e.Message);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="idConta"></param>
        /// <returns></returns>
        [HttpGet("ConsultaCartao")]
        [Menu(new[] { EMenus.Portador })]
        public JsonResult ConsultaCartao(int idConta)
        {
            try
            {
                var response = _cartaoAppService.ConsultaCartaoPorConta(idConta).Result;

                var content = response.content != null
                    ? response.content.FirstOrDefault(c => c.idStatus == 1 || c.idStatus == 2)
                    : null;

                return response.content == null || !response.Sucesso
                    ? ResponseBase.ResponderErro(response.message)
                    : ResponseBase.ResponderSucesso(content);
            }
            catch (Exception e)
            {
                return ResponseBase.ResponderErro(e.Message);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="lDtoCartao"></param>
        /// <returns></returns>
        [HttpPost("DesbloquearCartao")]
        [Menu(new[] { EMenus.Portador })]
        public JsonResult DesbloquearCartao([FromBody] DtoCartao lDtoCartao)
        {
            try
            {
                var response = _cartaoAppService.DesbloquearCartao(lDtoCartao.idCartao).Result;
                return !response.Sucesso
                    ? ResponseBase.ResponderErro("Não foi possível realizar essa operação! " + response.message)
                    : ResponseBase.ResponderSucesso(response);
            }
            catch (Exception e)
            {
                return ResponseBase.ResponderErro(e.Message);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="lDtoCartao"></param>
        /// <returns></returns>
        [HttpPost("BloquearCartao")]
        [Menu(new[] { EMenus.Portador })]
        public JsonResult BloquearCartao([FromBody] DtoCartao lDtoCartao)
        {
            try
            {
                int idStatus = 2;
                
                var response = _cartaoAppService.BloquearCartao(lDtoCartao.idCartao, idStatus, lDtoCartao.motivo).Result;
                return !response.Sucesso
                    ? ResponseBase.ResponderErro("Não foi possível realizar essa operação! " + response.message)
                    : ResponseBase.ResponderSucesso(response);
            }
            catch (Exception e)
            {
                return ResponseBase.ResponderErro(e.Message);
            }
        }

        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="lPortadorEmpReq"></param>
        /// <returns></returns>
        [HttpPost("SalvarPortadorEmpresa")]
        public JsonResult SavePortadorEmpresa([FromBody]PortadorEmpresaRequest lPortadorEmpReq)
        {
            try
            {
                var lSavePortadorEmp = AppService.SalvarPortadorEmpresa(lPortadorEmpReq).Result;
                
                return ResponseBase.BigJson(lSavePortadorEmp);
            }
            catch (Exception e)
            {
                return ResponseBase.ResponderErro("Não foi possível realizar a operação. Mensagem: " + e.Message);
            }
        }
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="cpfCnpj"></param>
        /// <returns></returns>
        [HttpGet("VerificaPortadorCadastrado")]
        public async Task<JsonResult> VerificaPortadorCadastrado(string cpfCnpj)
        {
            try
            {
                var lPortadorEmp = await AppService.VerificaPortadorCadastradoEmOutraEmp(cpfCnpj);
                
                return ResponseBase.BigJson(lPortadorEmp);
            }
            catch (Exception e)
            {
                return ResponseBase.ResponderErro("Não foi possível realizar a operação. Mensagem: " + e.Message);
            }
        }
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("CriarNovaSenha")]
        public JsonResult CriarNovaSenha([FromBody] CriarNovaSenhaRequest request)
        {
            try
            {
                var response = AppService.CriarNovaSenha(request).Result;
                
                return !response.sucesso
                    ? ResponseBase.ResponderErro("Não foi possível realizar essa operação! " + response.mensagem)
                    : ResponseBase.ResponderSucesso(response.data);
            }
            catch (Exception e)
            {
                return ResponseBase.ResponderErro("Não foi possível realizar a operação. Mensagem: " + e.Message);
            }
        }
        
    }
}
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using NLog;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Domain.Models.Portador.Repository;
using SistemaInfo.BBC.Domain.Models.Portador.Exeptions;
using SistemaInfo.Framework.CQRS;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using SistemaInfo.Framework.DomainDrivenDesign.Infra.CQRS;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Domain.Models.Portador.Commands.CommandHandlers
{
    public class PortadorCommandHandler :
        CommandHandler<Portador, IPortadorReadRepository, IPortadorWriteRepository>,
        I<PERSON><PERSON>ler<PortadorSalvarCommand>,
        IHandler<PortadorAlterarStatusCommand>, 
        I<PERSON>andler<PortadorSalvarComRetornoCommand, Portador>,
        IHandler<TransportadorSalvarCommand>,
        IHandler<TransportadorSalvarComRetornoCommand, Portador>,
        IHandler<PortadorAlterarSenhaCommand>,
        I<PERSON>andler<PortadorSenhaErradaCommand>,
        <PERSON><PERSON>andler<PortadorSenhaCorretaCommand>,
        IHandler<PortadorUltimoAcessoCommand>
    {
        public PortadorCommandHandler(IAppEngine engine, IPortadorReadRepository readRepository, IPortadorWriteRepository writeRepository) : base(engine, readRepository, writeRepository)
        {
        }

        public async Task HandlerAsync(PortadorSalvarCommand request)
        {
            var lLog = LogManager.GetCurrentClassLogger();
            try
            {
                var lPortador = Mapper.Map<Portador>(request);

                if (lPortador.TipoPessoa <= 0)
                {
                    if (lPortador.CpfCnpj.OnlyNumbers().Length <= 11)
                    {
                        lPortador.TipoPessoa = ETipoPessoa.Fisica;
                    }
                    else
                    {
                        lPortador.TipoPessoa = ETipoPessoa.Juridica;
                    }
                }
                
                lPortador.PortadorRepresentanteLegal = new List<PortadorRepresentanteLegal.PortadorRepresentanteLegal>();
                lPortador.PortadorCentroCusto = new List<PortadorCentroCusto.PortadorCentroCusto>();
                
                lPortador.UsuarioCadastroId = User.Id > 0 ? User.Id : 1;
    
                if (lPortador.Id == 0)
                {
                    //recurso para codificar senha para uso em api mobile
                    lPortador.SenhaApi = request.SenhaApi.GetHashSha1();
                    
                    lPortador.DataCadastro = DateTime.Now;
                    lPortador.Ativo = 1;
                    lPortador.SenhaProvisoria = request.SenhaProvisoria == 1 ? 1 : 0;
                    foreach (var lRepLegais in request.RepLegaisList)
                    {
                        lPortador.PortadorRepresentanteLegal.Add(new PortadorRepresentanteLegal.PortadorRepresentanteLegal()
                        {
                            PortadorRepresentanteId = lRepLegais.Id
                        });
                    }
                    
                    foreach (var lPortadorCentroCusto in request.PortadorCentroCusto)
                    {
                        lPortador.PortadorCentroCusto.Add(new PortadorCentroCusto.PortadorCentroCusto()
                        {
                            CentroCustoId = lPortadorCentroCusto.CentroCustoId
                        });
                    }
                    
                    Repository.Command.Add(lPortador);
                }
                else
                {
                    Repository.Command.RemoveRepresentanteLegal(request.Id);
                    Repository.Command.RemoveCentroCusto(request.Id);
                    lPortador = Repository.Query.GetById(request.Id);
                    
                    request.Visibilidade = lPortador.Visibilidade;
                    request.CiotTacAgregado = lPortador.CiotTacAgregado;
                    
                    Mapper.Map(request, lPortador);
                    lPortador.Ativo = 1;
                    lPortador.SenhaProvisoria = request.SenhaProvisoria == 1 ? 1 : 0;
                    if (Engine.User.EmpresaId != request.EmpresaIdFrota &&
                                !Engine.User.IsNivelSuperUsuario &&
                                !Engine.User.IsNivelAdministradora)
                    {
                        lPortador.EmpresaIdFrota = Engine.User.EmpresaId;
                    }
                    if (!lPortador.DataUltimoAcesso.HasValue)
                    {
                        lPortador.DataUltimoAcesso = DateTime.Now;
                    }
                    
                    //recurso para remover bloqueio de acesso ao login da api mobile
                    if (request.QuantidadeErroSenha != 3)
                    {
                        lPortador.DataDesbloqueioMobile = DateTime.Now;
                        lPortador.UsuarioDesbloqueioMobileId = 1;
                    }

                    lPortador.PortadorRepresentanteLegal = new List<PortadorRepresentanteLegal.PortadorRepresentanteLegal>();

                    if (request.RepLegaisList != null)
                    {
                        foreach (var lRepLegais in request.RepLegaisList)
                        {
                            lPortador.PortadorRepresentanteLegal.Add(new PortadorRepresentanteLegal.PortadorRepresentanteLegal()
                            {
                                PortadorId = lPortador.Id,
                                PortadorRepresentanteId = lRepLegais.Id
                            });
                        }
                    }
                    
                    lPortador.PortadorCentroCusto = new List<PortadorCentroCusto.PortadorCentroCusto>();

                    if (request.PortadorCentroCusto != null)
                    {
                        foreach (var lPortadorCentroCusto in request.PortadorCentroCusto)
                        {
                            lPortador.PortadorCentroCusto.Add(new PortadorCentroCusto.PortadorCentroCusto()
                            {
                                PortadorId = lPortador.Id,
                                CentroCustoId = lPortadorCentroCusto.CentroCustoId
                            });
                        }
                    }
                    Repository.Command.Update(lPortador);
                }
    
                await SaveChangesAsync();
            
            }
            catch (Exception e)
            {
                lLog.Error(e,"Erro ao salvar Portador, erro: ");
                throw new PortadorInvalidException("Erro ao salvar!");
            }
        }
        
        public async Task HandlerAsync(PortadorAlterarStatusCommand request)
        {
            var lPortador =  await Repository.Query.GetByIdAsync(request.Id);
            if( !Engine.User.IsNivelSuperUsuario &&
                !Engine.User.IsNivelAdministradora)
                lPortador.Ativo = 
                    lPortador.EmpresaIdFrota == Engine.User.EmpresaId ? lPortador.Ativo : 0;
            
            if (lPortador.Ativo == 1)
            {
                lPortador.Ativo = 0;
                lPortador.DataBloqueio = DateTime.Now;
                lPortador.UsuarioBloqueioId = User.Id > 0 ? User.Id : 1;
            }
            else
            {
                lPortador.Ativo = 1;
                lPortador.DataDesbloqueio = DateTime.Now;
                lPortador.UsuarioDesbloqueioId = User.Id > 0 ? User.Id : 1;
                lPortador.QuantidadeErroSenha = 0;
                if (Engine.User.EmpresaId != request.EmpresaIdFrota &&
                    !Engine.User.IsNivelSuperUsuario &&
                    !Engine.User.IsNivelAdministradora)
                {
                    lPortador.EmpresaIdFrota = Engine.User.EmpresaId;
                }
            }
            
            await SaveChangesAsync();
        }
        
        public async Task<Portador> HandlerAsync(PortadorSalvarComRetornoCommand request)
        {
            var lLog = LogManager.GetCurrentClassLogger();
            try
            {
                var lPortador = Mapper.Map<Portador>(request);
                if (lPortador.TipoPessoa <= 0)
                {
                    if (lPortador.CpfCnpj.OnlyNumbers().Length <= 11)
                    {
                        lPortador.TipoPessoa = ETipoPessoa.Fisica;
                    }
                    else
                    {
                        lPortador.TipoPessoa = ETipoPessoa.Juridica;
                    }
                }
                
                lPortador.PortadorRepresentanteLegal = new List<PortadorRepresentanteLegal.PortadorRepresentanteLegal>();
                lPortador.UsuarioCadastroId = User.Id > 0 ? User.Id : 1;
    
                if (lPortador.Id == 0)
                {
                    //recurso para codificar senha para uso em api mobile
                    if (request.SenhaApi.IsNullOrWhiteSpace())
                    {
                        Random rdn = new Random();
                        
                        var senhaNova = rdn.Next(100000, 999999);
                        
                        request.SenhaApi = senhaNova.ToString();
                    }
                    lPortador.SenhaApi = request.SenhaApi.GetHashSha1();
                    
                    lPortador.DataCadastro = DateTime.Now;
                    lPortador.Ativo = 1;
                    lPortador.SenhaProvisoria = request.SenhaProvisoria == 1 ? 1 : 0;
                    if (request.RepLegaisList != null)
                    {
                        foreach (var lRepLegais in request.RepLegaisList)
                        {
                            lPortador.PortadorRepresentanteLegal.Add(new PortadorRepresentanteLegal.PortadorRepresentanteLegal()
                            {
                                PortadorRepresentanteId = lRepLegais.Id
                            });
                        }
                    }
                    Repository.Command.Add(lPortador);
                }
                else
                {
                    Repository.Command.RemoveRepresentanteLegal(request.Id);
                    Repository.Command.RemoveCentroCusto(request.Id);
                    lPortador = Repository.Query.GetById(request.Id);
                    
                    request.Visibilidade = lPortador.Visibilidade;
                    request.CiotTacAgregado = lPortador.CiotTacAgregado;

                    
                    if (Engine.User.EmpresaId != request.EmpresaIdFrota &&
                        !Engine.User.IsNivelSuperUsuario &&
                        !Engine.User.IsNivelAdministradora)
                    {
                        lPortador.EmpresaIdFrota = Engine.User.EmpresaId;
                    }
                    Mapper.Map(request, lPortador);
                    lPortador.Ativo = 1;
                    lPortador.SenhaProvisoria = request.SenhaProvisoria == 1 ? 1 : 0;
                    
                    //recurso para remover bloqueio de acesso ao login da api mobile
                    if (request.QuantidadeErroSenha != 3)
                    {
                        lPortador.DataDesbloqueioMobile = DateTime.Now;
                        lPortador.UsuarioDesbloqueioMobileId = 1;
                    }
                    
                    lPortador.PortadorRepresentanteLegal = new List<PortadorRepresentanteLegal.PortadorRepresentanteLegal>();

                    if (request.RepLegaisList != null)
                    {
                        foreach (var lRepLegais in request.RepLegaisList)
                        {
                            lPortador.PortadorRepresentanteLegal.Add(new PortadorRepresentanteLegal.PortadorRepresentanteLegal()
                            {
                                PortadorId = lPortador.Id,
                                PortadorRepresentanteId = lRepLegais.Id
                            });
                        }
                    }
                    
                    
                    lPortador.PortadorCentroCusto = new List<PortadorCentroCusto.PortadorCentroCusto>();
                    if (request.PortadorCentroCusto != null)
                    {
                        foreach (var lPortadorCentroCusto in request.PortadorCentroCusto)
                        {
                            lPortador.PortadorCentroCusto.Add(new PortadorCentroCusto.PortadorCentroCusto()
                            {
                                PortadorId = lPortador.Id,
                                CentroCustoId = lPortadorCentroCusto.CentroCustoId
                            });
                        }
                    }
                    Repository.Command.Update(lPortador);
                }
                
                await SaveChangesAsync();
                
                
                return lPortador;

            }
            catch (Exception e)
            {
                lLog.Error(e,"Erro ao salvar Portador, erro: ");
                throw new PortadorInvalidException("Erro ao salvar!");
            }
        }
        
        public async Task HandlerAsync(PortadorAlterarSenhaCommand command)
        {
            var lPortador = await Repository.Query.GetByCpfCnpjAsync(command.CpfCnpj);
            
            lPortador.SenhaApi = command.SenhaApi.GetHashSha1();
            lPortador.SenhaProvisoria = 0;
            
            await SaveChangesAsync();
        }
        
        public async Task HandlerAsync(PortadorSenhaErradaCommand command)
        {
            var lPortador = await Repository.Query.GetByIdAsync(command.Id);
            
            lPortador.QuantidadeErroSenha++;

            if (lPortador.QuantidadeErroSenha >= command.TotalTentativas)
            {
                lPortador.Ativo = 0;
                lPortador.DataBloqueio = DateTime.Now;
                lPortador.UsuarioBloqueioId = User.Id > 0 ? User.Id : 1;
            }
                
            await SaveChangesAsync();
        }
        
        public async Task HandlerAsync(PortadorSenhaCorretaCommand command)
        {
            var lPortador = await Repository.Query.GetByIdAsync(command.Id);
            
            lPortador.QuantidadeErroSenha = 0;

            lPortador.ValidarCriacao();
            
            await SaveChangesAsync();
        }
        public async Task HandlerAsync(PortadorUltimoAcessoCommand command)
        {
            var lPortador = await Repository.Query.GetByIdAsync(command.Id);
            
            lPortador.DataUltimoAcesso = DateTime.Now;

            await SaveChangesAsync();
        }

        public async Task HandlerAsync(TransportadorSalvarCommand request)
        {
            var lLog = LogManager.GetCurrentClassLogger();
            try
            {
                var lPortador = Mapper.Map<Portador>(request);

                if (lPortador.TipoPessoa <= 0)
                {
                    if (lPortador.CpfCnpj.OnlyNumbers().Length <= 11)
                    {
                        lPortador.TipoPessoa = ETipoPessoa.Fisica;
                    }
                    else
                    {
                        lPortador.TipoPessoa = ETipoPessoa.Juridica;
                    }
                }

                lPortador.UsuarioCadastroId = User.Id > 0 ? User.Id : 1;
    
                if (lPortador.Id == 0)
                {
                    lPortador.DataCadastro = DateTime.Now;
                    lPortador.Ativo = 1;
                    lPortador.SenhaProvisoria = 0;
                    lPortador.EmpresaIdFrota = User.EmpresaId;
                    lPortador.Visibilidade = EVisibilidadePortador.Transportador;
                    
                    Repository.Command.Add(lPortador);
                }
                else
                {
                    lPortador = Repository.Query.GetById(request.Id);
                    lPortador.Ativo = 1;
                    lPortador.CiotTacAgregado = request.CiotTacAgregado;
                    lPortador.Visibilidade = EVisibilidadePortador.Transportador;
                    lPortador.SenhaProvisoria = 0;
                    lPortador.EmpresaIdFrota = User.EmpresaId;
                    
                    Repository.Command.Update(lPortador);
                }
                
                await SaveChangesAsync();
            }
            catch (Exception e)
            {
                lLog.Error(e,"Erro ao salvar Transportador, erro: ");
                throw new PortadorInvalidException("Erro ao salvar!");
            }
        }

        public async Task<Portador> HandlerAsync(TransportadorSalvarComRetornoCommand request)
        {
            var lLog = LogManager.GetCurrentClassLogger();
            try
            {
                var lPortador = Mapper.Map<Portador>(request);

                if (lPortador.TipoPessoa <= 0)
                {
                    if (lPortador.CpfCnpj.OnlyNumbers().Length <= 11)
                    {
                        lPortador.TipoPessoa = ETipoPessoa.Fisica;
                    }
                    else
                    {
                        lPortador.TipoPessoa = ETipoPessoa.Juridica;
                    }
                }

                lPortador.UsuarioCadastroId = User.Id > 0 ? User.Id : 1;
    
                if (lPortador.Id == 0)
                {
                    lPortador.DataCadastro = DateTime.Now;
                    lPortador.Ativo = 1;
                    lPortador.SenhaProvisoria = 0;
                    lPortador.EmpresaIdFrota = User.EmpresaId;
                    lPortador.Visibilidade = EVisibilidadePortador.Transportador;
                    
                    Repository.Command.Add(lPortador);
                }
                else
                {
                    lPortador = Repository.Query.GetById(request.Id);
                    lPortador.Ativo = 1;
                    lPortador.CiotTacAgregado = request.CiotTacAgregado;
                    lPortador.Visibilidade = EVisibilidadePortador.Transportador;
                    lPortador.SenhaProvisoria = 0;
                    lPortador.EmpresaIdFrota = User.EmpresaId;
                    
                    Repository.Command.Update(lPortador);
                }
                
                await SaveChangesAsync();
                
                
                return lPortador;

            }
            catch (Exception e)
            {
                lLog.Error(e,"Erro ao salvar Transportador, erro: ");
                throw new PortadorInvalidException("Erro ao salvar!");
            }
        }
    }
}
Exemplos de Json utilizados
{
  "ciot": "251000004567/6538",
  "rntrc": "123456879",
  "contratado": "12345 - Teste",
  "dtInicioFrete": "1993-07-13T00:00:00.000Z",
  "dtTerminoFrete": "1993-07-15T00:00:00.000Z",
  "valorFrete": 150.5,
  "totalImposto": 19.9,
  "totalPedagio": 5.78,
  "tipoViagem": "P",
  "senha": "8087",
  "encerrada": 0,
  "aviso": "ajoaiejaiojeioa"
}

{
	"ciot": {
		"ciot": "251000004567/6538",
		"rntrc": "123456879",
		"contratado": "12345 - Teste",
		"dtInicioFrete": "1993-07-13T00:00:00.000Z",
		"dtTerminoFrete": "1993-07-15T00:00:00.000Z",
		"valorFrete": 150.5,
		"totalImposto": 19.9,
		"totalPedagio": 5.78,
		"tipoViagem": "P",
		"senha": "8087",
		"encerrada": 0,
		"aviso": "ajoaiejaiojeioa",
		"pesoCarga": 15.98,
		"naturezaCarga": 1,
		"cidadeOrigem": "Berlin",
		"cidadeDestino": 324,
		"quantidadeTarifas": 3,
		"valorTotalTarifas": 534.98
	},
	"veiculos": [
		{
			"placa": "MKJ-3607",
			"rntrcVeiculo": "12345"
		},
		{
			"placa": "MKJ-3608",
			"rntrcVeiculo": "123456"
		}
	]
}

{
	"ciot": {
		"ciot": "251000004567/6538",
		"rntrc": "123456879",
		"contratado": "12345 - Teste",
		"dtInicioFrete": "1993-07-13T00:00:00.000Z",
		"dtTerminoFrete": "1993-07-15T00:00:00.000Z",
		"valorFrete": 150.5,
		"totalImposto": 19.9,
		"totalPedagio": 5.78,
		"tipoViagem": "P",
		"senha": "8087",
		"encerrada": 0,
		"aviso": "ajoaiejaiojeioa",
		"pesoCarga": 15.98,
		"naturezaCarga": 1,
		"cidadeOrigem": "Berlin",
		"cidadeDestino": 324,
		"quantidadeTarifas": 3,
		"valorTotalTarifas": 534.98
	},
	"viagens": [
		{
			"cidadeOrigem": "Gold Coast",
			"cidadeDestino": "Brisbane",
			"naturezaCarga": 1,
			"pesoCarga": 3.9,
			"qtdViagens": 1
		},
		{
			"cidadeOrigem": "Sydney",
			"cidadeDestino": "Byron Bay",
			"naturezaCarga": 1,
			"pesoCarga": 2.7,
			"qtdViagens": 1
		},
		{
			"cidadeOrigem": "Auckland",
			"cidadeDestino": "Paihia",
			"naturezaCarga": 1,
			"pesoCarga": 58,
			"qtdViagens": 1
		}
	]
}
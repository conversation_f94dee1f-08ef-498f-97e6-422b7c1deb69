<div class="container main-container">
    <div>
        <div class="container main-container">
            <form novalidate (ngSubmit)="retificar()" [formGroup]="retificacaoForm">
                <div class="form-horizontal">
                    <div class="container">
                        <div class="panel-body">
                            <fieldset class="scheduler-border">
                                <legend class="scheduler-border">Retificar declaração de transporte</legend>
                                <div class="row">
                                    <div class="col-sm-12 col-md-6 col-lg-3">
                                        <div class="form-group " [ngClass]="{'has-error': displayMessage.ciot }">
                                            <label class="control-label" for="ciot">CIOT:</label>
                                            <input type="text" class="form-control" id="ciot" formControlName="ciot"
                                                disabled style="font-weight: bold;" />
                                            <span class="text-danger" *ngIf="displayMessage.ciot">
                                                <p [innerHTML]="displayMessage.ciot"></p>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-sm-12 col-md-6 col-lg-3">
                                        <div class="form-group " [ngClass]="{'has-error': displayMessage.contratado }">
                                            <label class="control-label" for="contratado">Contratado</label>
                                            <input type="text" class="form-control" id="contratado"
                                                formControlName="contratado" disabled />
                                            <span class="text-danger" *ngIf="displayMessage.contratado">
                                                <p [innerHTML]="displayMessage.contratado"></p>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="col-sm-12 col-md-6 col-lg-3">
                                        <div class="form-group " [ngClass]="{'has-error': displayMessage.rntrc }">
                                            <label class="control-label" for="rntrc">RNTRC</label>
                                            <input type="text" class="form-control" id="rntrc" formControlName="rntrc"
                                                disabled maxlength="9" RntrcMask />
                                            <span class="text-danger" *ngIf="displayMessage.rntrc">
                                                <p [innerHTML]="displayMessage.rntrc"></p>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-sm-12 col-md-6 col-lg-3">
                                        <div class="form-group required"
                                            [ngClass]="{'has-error': displayMessage.dataInicio }">
                                            <label class="control-label" for="dataInicio">Início do frete</label>
                                            <input type="text" class="form-control" #dp="bsDatepicker" bsDatepicker
                                                [bsValue]="dataInicio" [bsConfig]="bsConfig" [(ngModel)]="dataInicio"
                                                [disabled]="'3' == tipoViagem || naoPodeEditar"
                                                [ngModelOptions]="{standalone: true}">
                                            <span class="text-danger" *ngIf="displayMessage.dataInicio">
                                                <p [innerHTML]="displayMessage.dataInicio"></p>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="col-sm-12 col-md-6 col-lg-3">
                                        <div class="form-group required"
                                            [ngClass]="{'has-error': displayMessage.dataFim }">
                                            <label class="control-label" for="dataFim">Final do frete</label>
                                            <input type="text" class="form-control" #dp="bsDatepicker" bsDatepicker
                                                [bsValue]="dataFim" [bsConfig]="bsConfig" [(ngModel)]="dataFim"
                                                [disabled]="'3' == tipoViagem || naoPodeEditar"
                                                [ngModelOptions]="{standalone: true}">
                                            <span class="text-danger" *ngIf="displayMessage.dataFim">
                                                <p [innerHTML]="displayMessage.dataFim"></p>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-sm-12 col-md-6 col-lg-3">
                                        <div class="form-group " [ngClass]="{'has-error': displayMessage.valorFrete }">
                                            <label class="control-label" for="valorFrete">Valor do frete</label>
                                            <div class="input-group mb-2">
                                                <div class="input-group-prepend">
                                                    <div class="input-group-text">R$</div>
                                                </div>
                                                <input currencyMask
                                                    [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
                                                    maxlength="20" type="text" class="form-control" id="valorFrete"
                                                    formControlName="valorFrete" disabled placeholder="R$0,00" />
                                                <span class="text-danger" *ngIf="displayMessage.valorFrete">
                                                    <p [innerHTML]="displayMessage.valorFrete"></p>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-12 col-md-6 col-lg-3">
                                        <div class="form-group "
                                            [ngClass]="{'has-error': displayMessage.totalImposto }">
                                            <label class="control-label" for="totalImposto">Total impostos</label>
                                            <div class="input-group mb-2">
                                                <div class="input-group-prepend">
                                                    <div class="input-group-text">R$</div>
                                                </div>
                                                <input currencyMask
                                                    [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
                                                    maxlength="20" type="text" class="form-control" id="totalImposto"
                                                    formControlName="totalImposto" disabled placeholder="0,00" />
                                                <span class="text-danger" *ngIf="displayMessage.totalImposto">
                                                    <p [innerHTML]="displayMessage.totalImposto"></p>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-12 col-md-6 col-lg-3">
                                        <div class="form-group "
                                            [ngClass]="{'has-error': displayMessage.totalPedagio }">
                                            <label class="control-label" for="totalPedagio">Total do pedágio</label>
                                            <div class="input-group mb-2">
                                                <div class="input-group-prepend">
                                                    <div class="input-group-text">R$</div>
                                                </div>
                                                <input currencyMask
                                                    [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
                                                    maxlength="20" type="text" class="form-control" id="totalPedagio"
                                                    formControlName="totalPedagio" disabled placeholder="R$0,00" />
                                                <span class="text-danger" *ngIf="displayMessage.totalPedagio">
                                                    <p [innerHTML]="displayMessage.totalPedagio"></p>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-sm-12 col-md-6 col-lg-3">
                                        <div class="form-group " [ngClass]="{'has-error': displayMessage.tipoViagem }">
                                            <label class="control-label" for="tipoViagem">Tipo de viagem</label>
                                            <input type="text" class="form-control" id="tipoViagem"
                                                [(ngModel)]="tipoViagemDescricao" [ngModelOptions]="{standalone: true}"
                                                disabled />
                                            <span class="text-danger" *ngIf="displayMessage.tipoViagem">
                                                <p [innerHTML]="displayMessage.tipoViagem"></p>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="col-sm-12 col-md-6 col-lg-3">
                                        <div class="form-group required"
                                            [ngClass]="{'has-error': displayMessage.pesoCarga}">
                                            <label class="control-label" for="pesoCarga">Peso da carga (KG):</label>
                                            <div class="input-group">
                                                <input OnlyNumber class="form-control" id="pesoCarga" type="text"
                                                    maxlength="5" formControlName="pesoCarga" disabled />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- <div class="form-group required"
                                    [ngClass]="{'has-error': displayMessage.naturezaCarga}">
                                    <label class="control-label" for="naturezaCarga">Natureza da carga:</label>
                                    <div class="alert alert-danger" *ngIf="noResultNatureza">Natureza não encontrada
                                    </div>
                                    <input [(ngModel)]="naturezaCompleterText" [typeahead]="naturezaCargaList.Retorno"
                                        (typeaheadNoResults)="typeaheadNoResultsNatureza($event)"
                                        (typeaheadOnSelect)="onSelectNatureza($event)" typeaheadOptionField="DESCRICAO"
                                        class="form-control" [disabled]="'3' == tipoViagem || naoPodeEditar"
                                        autocomplete="off" [ngModelOptions]="{standalone: true}">

                                </div>
                                <div class="row">
                                    <div class="col-sm-12 col-md-6 col-lg-3">
                                        <div class="form-group required"
                                            [ngClass]="{'has-error': displayMessage.cidadeOrigem}">
                                            <label class="control-label" for="cidadeOrigem">Cidade origem</label>
                                            <div class="alert alert-danger" *ngIf="noResultOrigem">Cidade não encontrada
                                            </div>
                                            <input [(ngModel)]="cidadeOrigemCompleterText"
                                                [typeahead]="cidadeList.Retorno"
                                                (typeaheadNoResults)="typeaheadNoResultsOrigem($event)"
                                                (typeaheadOnSelect)="onSelectCidadeOrigem($event)"
                                                typeaheadOptionField="NOME" class="form-control"
                                                [disabled]="'3' == tipoViagem || naoPodeEditar" autocomplete="off"
                                                [ngModelOptions]="{standalone: true}">
                                        </div>
                                    </div>
                                    <div class="col-sm-12 col-md-6 col-lg-3">
                                        <div class="form-group required"
                                            [ngClass]="{'has-error': displayMessage.cidadeDestino }">
                                            <label class="control-label" for="cidadeDestino">Cidade destino</label>
                                            <div class="alert alert-danger" *ngIf="noResultDestino">Cidade não
                                                encontrada</div>
                                            <input [(ngModel)]="cidadeDestinoCompleterText"
                                                [typeahead]="cidadeList.Retorno"
                                                (typeaheadNoResults)="typeaheadNoResultsDestino($event)"
                                                typeaheadOptionField="NOME"
                                                (typeaheadOnSelect)="onSelectCidadeDestino($event)" class="form-control"
                                                [disabled]="'3' == tipoViagem || naoPodeEditar" autocomplete="off"
                                                [ngModelOptions]="{standalone: true}">
                                        </div>
                                    </div>
                                </div> -->
                                <div class="row">
                                    <div class="col-sm-12 col-md-6 col-lg-3">
                                        <div class="form-group required"
                                            [ngClass]="{'has-error': displayMessage.quantidadeTarifas }">
                                            <label class="control-label" for="quantidadeTarifas">Quantidade de
                                                tarifas</label>
                                            <input RntrcMask type="text" class="form-control" id="quantidadeTarifas"
                                                maxlength="5" formControlName="quantidadeTarifas" />
                                            <span class="text-danger" *ngIf="displayMessage.quantidadeTarifas">
                                                <p [innerHTML]="displayMessage.quantidadeTarifas"></p>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="col-sm-12 col-md-6 col-lg-3">
                                        <div class="form-group required"
                                            [ngClass]="{'has-error': displayMessage.valorTotalTarifas }">
                                            <label class="control-label" for="valorTotalTarifas">Valor total das
                                                tarifas</label>
                                            <div class="input-group mb-2">
                                                <div class="input-group-prepend">
                                                    <div class="input-group-text">R$</div>
                                                </div>
                                                <input currencyMask
                                                    [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
                                                    maxlength="20" size="10" class="form-control" step="0.01" min="0"
                                                    id="valorTotalTarifas" formControlName="valorTotalTarifas"
                                                    placeholder="0.00" pattern="^\d+(?:\.\d{1,2})?$" />
                                                <span class="text-danger" *ngIf="displayMessage.valorTotalTarifas">
                                                    <p [innerHTML]="displayMessage.valorTotalTarifas"></p>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </fieldset>
                        </div>
                    </div>
                    <br />
                    <div class="container">
                        <div class="panel-body">
                            <fieldset class="scheduler-border">
                                <legend class="scheduler-border">Veículos</legend>
                                <form novalidate (ngSubmit)="adicionarVeiculo()" [formGroup]="veiculoForm">
                                    <div class="row">
                                        <div class="col-sm-12 col-md-6 col-lg-3">
                                            <div class="form-group required"
                                                [ngClass]="{'has-error': displayMessage.placa}">
                                                <label class="control-label" for="formaPagamento">Placa:</label>
                                                <input PlacaAutomovel class="form-control" id="placa" type="text"
                                                    formControlName="placa" maxlength="7" />
                                                <span class="text-danger" *ngIf="displayMessage.placa">
                                                    <p [innerHTML]="displayMessage.placa"></p>
                                                </span>
                                            </div>
                                        </div>
                                        <div class="col-sm-12 col-md-6 col-lg-3">
                                            <div class="form-group required"
                                                [ngClass]="{'has-error': displayMessage.RNTRC }">
                                                <label class="control-label" for="RNTRC">RNTRC</label>
                                                <input RntrcMask maxlength="9" type="text" class="form-control"
                                                    id="rntrc" formControlName="rntrc" />
                                                <span class="text-danger" *ngIf="displayMessage.rntrc">
                                                    <p [innerHTML]="displayMessage.rntrc"></p>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-sm-12 col-md-6 col-lg-3">
                                            <button class="btn btn-danger" [disabled]="!veiculoForm.valid"
                                                id="adicionarVeiculo" type="submit">Adicionar</button>
                                        </div>
                                    </div>
                                </form>
                                <br />
                                <div class="control-group">
                                    <table class="table table-striped" style="width:91%">
                                        <thead>
                                            <tr>
                                                <th scope="col">Ação</th>
                                                <th scope="col">Placa</th>
                                                <th scope="col">RNTRC</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr *ngFor='let veiculo of veiculoList; let i = index'>
                                                <td>
                                                    <button type="button" class="btn btn-danger btn-sm"
                                                        (click)='removerVeiculo(veiculo)'>
                                                        <span aria-hidden="true">Excluir</span>
                                                    </button>
                                                </td>
                                                <td>{{veiculo?.placa | placaAutomovel}}</td>
                                                <td>{{veiculo?.rntrc}}</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </fieldset>
                        </div>
                    </div>
                    <br>
                    <button class="btn btn-primary" id="voltar" (click)="voltar()">Voltar</button>
                    <button class="btn btn-danger" id="retificar" type="submit">Retificar</button>
                </div>
            </form>
        </div>
    </div>
</div>

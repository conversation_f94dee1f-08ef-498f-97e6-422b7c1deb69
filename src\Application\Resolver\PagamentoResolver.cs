﻿using System;
using System.Collections.Generic;
using System.Linq;
using AutoMapper;
using SistemaInfo.BBC.Application.Objects.Api.Viagem;
using SistemaInfo.BBC.Domain.External.CIOT.DTO;
using SistemaInfo.BBC.Domain.Models.DeclaracaoCiot.Exeptions;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.Resolver;

public class PagamentoResolver : IValueResolver<ViagemIntegrarV2Request, DeclararOperacaoTransporteReq, Pagamento>
{
    public Pagamento Resolve(ViagemIntegrarV2Request src, DeclararOperacaoTransporteReq dest, Pagamento destMember, ResolutionContext context)
    {
        if (src.Pagamentos == null || !src.Pagamentos.Any())
            throw new Exception("É necessário informar ao menos um pagamento para viagens do tipo 1.");
    
        var pagamento = MapearPagamentoCIOT(src);
        return pagamento;
    }

    private Pagamento MapearPagamentoCIOT(ViagemIntegrarV2Request request)
    {
        var primeiroPagamento = request.Pagamentos.First(); // Regra definida via chat
        var parcelas = MapearParaParcelasPagamentoCIOT(request.Pagamentos);
        return new Pagamento
        {
            AgenciaPagamento = primeiroPagamento.Agencia,
            ContaPagamento = primeiroPagamento.Conta,
            FormaPagamento = RetornarPagamentoCaruana(primeiroPagamento.FormaPagamento.ToIntSafe(1)),
            ParcelaUnica = request.Pagamentos.Count <= 1,
            Parcelas = parcelas.ToArray()
        };
    }

    private List<ParcelaPagamento> MapearParaParcelasPagamentoCIOT(List<PagamentoV2Request> pagamentos)
    {
        return pagamentos.Select((p, index) => new ParcelaPagamento
        {
            CodigoParcela = $"PARC{index + 1:000}",
            ValorParcela = p.Valor,
            Vencimento = p.DataPrevisaoPagamento ?? DateTime.Now
        }).ToList();
    }

    private int RetornarPagamentoCaruana(int formaPagamento)
    {
        switch (formaPagamento)
        {
            case 0:
                return 2;
            case 1:
                return 1;
            case 2:
                return 3;
            case 3:
                return 3;
            case 4:
                return 1;
        }
        throw new DeclararCiotException("Erro ao efetuar transação!");
    }
}

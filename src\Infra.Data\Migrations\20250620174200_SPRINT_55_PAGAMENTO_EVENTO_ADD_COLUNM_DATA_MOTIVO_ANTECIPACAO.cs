﻿using Microsoft.EntityFrameworkCore.Migrations;
using System;
using System.Collections.Generic;

namespace SistemaInfo.BBC.Infra.Data.Migrations
{
    public partial class SPRINT_55_PAGAMENTO_EVENTO_ADD_COLUNM_DATA_MOTIVO_ANTECIPACAO : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "AntecipacaoMotivo",
                schema: "BBC",
                table: "PagamentoEvento",
                type: "varchar(400)",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DataAlteracaoAntecipacao",
                schema: "BBC",
                table: "PagamentoEvento",
                type: "timestamp",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DataCadastroAntecipacao",
                schema: "BBC",
                table: "PagamentoEvento",
                type: "timestamp",
                nullable: true);
            
            migrationBuilder.AddColumn<DateTime>(
                name: "DataPrevisaoPagamento",
                schema: "BBC",
                table: "PagamentoEvento",
                type: "timestamp",
                nullable: true);

        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "AntecipacaoMotivo",
                schema: "BBC",
                table: "PagamentoEvento");

            migrationBuilder.DropColumn(
                name: "DataAlteracaoAntecipacao",
                schema: "BBC",
                table: "PagamentoEvento");

            migrationBuilder.DropColumn(
                name: "DataCadastroAntecipacao",
                schema: "BBC",
                table: "PagamentoEvento");
            
            migrationBuilder.DropColumn(
                name: "DataPrevisaoPagamento",
                schema: "BBC",
                table: "PagamentoEvento");
        }
    }
}

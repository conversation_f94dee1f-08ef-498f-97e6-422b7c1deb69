﻿using System.Threading.Tasks;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Domain.External.CIOT.DTO;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;

namespace SistemaInfo.BBC.Application.Interface.Operacoes;

public interface IOperacoesAppService : IAppService
{
    Task<DeclararOperacaoTransporteResp> DeclararOperacaoTransporte(DeclararOperacaoTransporteReq declararOperacaoTransporteReq);
    Task<EncerrarOperacaoTransporteResp> EncerrarOperacaoTransporte(EncerrarOperacaoTransporteReq encerrarOperacaoTransporteReq);
    Task<EncerrarOperacaoTransporteResp> EncerrarOperacaoTransporteBbcService(EncerrarOperacaoTransporteReq encerrarOperacaoTransporteReq);
    Task<ConsultarSituacaoCiotResp> ConsultarSituacaoCiot(ConsultarSituacaoCiotReq consultarSituacaoCiotReq);
    Task<CancelarOperacaoTransporteResp> CancelarOperacaoTransporte(CancelarOperacaoTransporteReq cancelarOperacaoTransporteReq);
    Task<RetificarOperacaoTransporteResp> RetificarOperacaoTransporte(RetificarOperacaoTransporteReq retificarOperacaoTransporteReq);
    Task<ConsultarSituacaoTransportadorResp> ConsultarSituacaoTransportador(ConsultarSituacaoTransportadorReq req);
    Task<ConsultarFrotaTransportadorResp> ConsultarFrotaTransportador(ConsultarFrotaTransportadorReq req);
    Task<RespBase> ConsultarOperacaoTacAgregado(ConsultarOperacaoTacAgregadoReq req);
    Task<ConsultarTacAgregadoResponse> ConsultarTacAgregado(ConsultarTacAgregadoRequest req);
}
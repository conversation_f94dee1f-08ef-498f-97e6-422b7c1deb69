﻿using System.Threading.Tasks;
using DinkToPdf.Contracts;
using SistemaInfo.BBC.Domain.External.CIOT.DTO;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;

namespace SistemaInfo.BBC.Application.Interface.Operacoes;

public interface IOperacoesAppService : IAppService
{
    Task<DeclararOperacaoTransporteResp> DeclararOperacaoTransporte(DeclararOperacaoTransporteReq declararOperacaoTransporteReq, bool isCiotPublico = false);
    Task<EncerrarOperacaoTransporteResp> EncerrarOperacaoTransporte(EncerrarOperacaoTransporteReq encerrarOperacaoTransporteReq, bool isCiotPublico = false);
    Task<EncerrarOperacaoTransporteResp> EncerrarOperacaoTransporteBbcService(EncerrarOperacaoTransporteReq encerrarOperacaoTransporteReq);
    Task<ConsultarSituacaoCiotResp> ConsultarSituacaoCiot(ConsultarSituacaoCiotReq consultarSituacaoCiotReq, bool isCiotPublico = false);
    Task<CancelarOperacaoTransporteResp> CancelarOperacaoTransporte(CancelarOperacaoTransporteReq cancelarOperacaoTransporteReq, bool isCiotPublico = false);
    Task<RetificarOperacaoTransporteResp> RetificarOperacaoTransporte(RetificarOperacaoTransporteReq retificarOperacaoTransporteReq, bool isCiotPublico = false);
    Task<ConsultarSituacaoTransportadorResp> ConsultarSituacaoTransportador(ConsultarSituacaoTransportadorReq req, bool isCiotPublico = false);
    Task<ConsultarFrotaTransportadorResp> ConsultarFrotaTransportador(ConsultarFrotaTransportadorReq req);
    Task<RespBase> ConsultarOperacaoTacAgregado(ConsultarOperacaoTacAgregadoReq req);
    Task<ConsultarTacAgregadoResponse> ConsultarTacAgregado(ConsultarTacAgregadoRequest req);
    
    #region Exclusivo ciot público

    Task<ConsultarBancosResp> ConsultarBancos();
    Task<ConsultarEstadosResp> ConsultarEstados();
    Task<ConsultarCidadesResp> ConsultarCidades(string nome);
    Task<ConsultarNaturezasCargaResp> ConsultarNaturezasCarga(string descricao);
    Task<ConsultarTiposCargaResp> ConsultarTiposCarga(string txt);
    Task<ConsultarNaturezaCargaPorIdResp> ConsultarNaturezasCargaPorId(string codigo);
    Task<ConsultarDadosEncerramentoResp> ConsultarDadosEncerramento(ConsultarDadosEncerramentoReq req);
    Task<ConsultarDadosRetificacaoResp> ConsultarDadosRetificacao(ConsultarDadosRetificacaoReq req);
    Task<IDocument> GerarRelatorioGestaoCiot(string ciot, string senhaAlteracao);
    
    #endregion
    
}
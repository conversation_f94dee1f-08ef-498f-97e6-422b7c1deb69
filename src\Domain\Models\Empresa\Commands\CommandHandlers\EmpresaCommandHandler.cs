using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using NLog;
using SistemaInfo.BBC.Domain.Contracts.Empresa;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.BBC.Domain.Models.Empresa.Exeptions;
using SistemaInfo.BBC.Domain.Models.Empresa.Repository;
using SistemaInfo.Framework.CQRS;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using SistemaInfo.Framework.DomainDrivenDesign.Infra.CQRS;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Domain.Models.Empresa.Commands.CommandHandlers
{
    public class EmpresaCommandHandler : CommandHandler<Empresa, IEmpresaReadRepository, IEmpresaWriteRepository>, 
        IHandler<EmpresaSaveCommand,Empresa>,
        I<PERSON>and<PERSON><EmpresaSaveStatusCommand>,
        <PERSON><PERSON><PERSON><PERSON><EmpresaSaveComRetornoCommand, Empresa>
    {
        public EmpresaCommandHandler(IAppEngine engine, IEmpresaReadRepository readRepository, IEmpresaWriteRepository writeRepository) 
            : base(engine, readRepository, writeRepository)
        {
            
        }

        public async Task<Empresa> HandlerAsync(EmpresaSaveCommand command)
        {
            var cnpj = command.Cnpj.OnlyNumbers();
            var empresaCadastrada = await Repository.Query.GetByCnpjAsync(cnpj);

            var empresa = Mapper.Map<Empresa>(command);

            empresa.ValidarCriacao();

            if (empresa.Id > 0)
            {
                // if (!empresa.SenhaApi.IsNullOrWhiteSpace())
                // {
                //     empresaCadastrada.SenhaApi = empresa.SenhaApi;
                // }

                empresaCadastrada.Bairro = empresa.Bairro;
                empresaCadastrada.Complemento = empresa.Complemento;
                empresaCadastrada.Email = empresa.Email;
                empresaCadastrada.Endereco = empresa.Endereco;
                empresaCadastrada.CidadeId = empresa.CidadeId;
                empresaCadastrada.RazaoSocial = empresa.RazaoSocial;
                empresaCadastrada.StatusCadastro = empresa.StatusCadastro;
                empresaCadastrada.Ativo = empresa.Ativo;
                empresaCadastrada.DataBloqueio =
                    empresa.DataBloqueio.HasValue && empresa.DataBloqueio.Value > DateTime.MinValue
                        ? empresa.DataBloqueio
                        : null;
                empresaCadastrada.DataCadastro = empresa.DataCadastro;
                empresaCadastrada.DataDesbloqueio =
                    empresa.DataDesbloqueio.HasValue && empresa.DataDesbloqueio.Value > DateTime.MinValue
                        ? empresa.DataDesbloqueio
                        : null;
                empresaCadastrada.DataValidacao =
                    empresa.DataValidacao.HasValue && empresa.DataValidacao.Value > DateTime.MinValue
                        ? empresa.DataValidacao
                        : null;
                ;
                empresaCadastrada.EnderecoNumero = empresa.EnderecoNumero;
                empresaCadastrada.NomeFantasia = empresa.NomeFantasia;
                empresaCadastrada.ParecerExterno = empresa.ParecerExterno;
                empresaCadastrada.ParecerInterno = empresa.ParecerInterno;
                empresaCadastrada.UsuarioBloqueioId = empresa.UsuarioBloqueioId;
                empresaCadastrada.UsuarioDesbloqueioId = empresa.UsuarioDesbloqueioId;
                empresaCadastrada.UsuarioValidacaoId = empresa.UsuarioValidacaoId;
                empresaCadastrada.Ativo = empresa.Ativo;
                empresaCadastrada.InscricaoEstadual = empresa.InscricaoEstadual;
                empresaCadastrada.FormaConstituicao = empresa.FormaConstituicao;
                empresaCadastrada.LiberaBloqueioSPD = empresa.LiberaBloqueioSPD;
                empresaCadastrada.CobrancaTarifa = empresa.CobrancaTarifa;
                empresaCadastrada.TempoAbastecimento = empresa.TempoAbastecimento;
                empresaCadastrada.ValorTolerancia = empresa.ValorTolerancia;
                empresaCadastrada.ControlaOdometro = empresa.ControlaOdometro;
                empresaCadastrada.ControlaAutonomia = empresa.ControlaAutonomia;
                empresaCadastrada.TaxaAbastecimento = empresa.TaxaAbastecimento;
                empresaCadastrada.Cashback = empresa.Cashback;
                empresaCadastrada.RegistraCiot = empresa.RegistraCiot;
                empresaCadastrada.Link = empresa.Link;
                empresaCadastrada.LinkSAP = empresa.LinkSAP;
                empresaCadastrada.UsuarioSAP = empresa.UsuarioSAP;
                empresaCadastrada.SenhaLink = empresa.SenhaLink;
                empresaCadastrada.SenhaSAP = empresa.SenhaSAP;
                empresaCadastrada.TipoEmpresaId = empresa.TipoEmpresaId;
                empresaCadastrada.ImpostoCSLL = empresa.ImpostoCSLL;
                empresaCadastrada.ImpostoPIS = empresa.ImpostoPIS;
                empresaCadastrada.ImpostoIRRF = empresa.ImpostoIRRF;
                empresaCadastrada.ImpostoCOFINS = empresa.ImpostoCOFINS;
                empresaCadastrada.PercentualAutonomiaInferior = empresa.PercentualAutonomiaInferior;
                empresaCadastrada.PercentualAutonomiaSuperior = empresa.PercentualAutonomiaSuperior;
                empresaCadastrada.ControlaContingencia = empresa.ControlaContingencia;
                empresaCadastrada.DebitoPrazo = empresa.DebitoPrazo;
                empresaCadastrada.DebitoProtocolo = empresa.DebitoProtocolo;
                empresaCadastrada.Prazo = empresa.Prazo;
                empresaCadastrada.UtilizaTarifaEmpresa = empresa.UtilizaTarifaEmpresa;
                empresaCadastrada.GrupoEmpresaId = empresa.GrupoEmpresaId == 0 ? null : empresa.GrupoEmpresaId;
                empresaCadastrada.ValorTarifaPix = empresa.ValorTarifaPix;
                empresaCadastrada.ValorTarifaBbc = empresa.ValorTarifaBbc;
                empresaCadastrada.QtdMensalSemTaxaPix = empresa.QtdMensalSemTaxaPix;
                empresaCadastrada.RecebedorAutorizado = empresa.RecebedorAutorizado;
                empresaCadastrada.PorcentagemTarifaServiceValePedagio = empresa.PorcentagemTarifaServiceValePedagio;
                empresaCadastrada.PermitirPagamentoValePedagio = empresa.PermitirPagamentoValePedagio;
                empresaCadastrada.CobrarTarifaBbcValePedagio = empresa.CobrarTarifaBbcValePedagio;
                empresaCadastrada.UtilizaTarifaEmpresaPagamentoPedagio = empresa.UtilizaTarifaEmpresaPagamentoPedagio;
                empresaCadastrada.HabilitaPainelSaldo = empresa.HabilitaPainelSaldo;
                empresaCadastrada.ImagemCartao = empresa.ImagemCartao;
                empresaCadastrada.ValorAdiantamentoBbc = empresa.ValorAdiantamentoBbc;
                if (empresaCadastrada.NotificacaoContingenciaCiot != empresa.NotificacaoContingenciaCiot)
                {
                    await Engine.CommandBus.SendCommandAsync(new EmailClienteSincronizarMessage()
                    {
                        CnpjCliente = empresa.Cnpj,
                        EmailNotificacaoExterna = empresa.NotificacaoContingenciaCiot
                    });
                }
                empresaCadastrada.NotificacaoContingenciaCiot = empresa.NotificacaoContingenciaCiot;
                empresaCadastrada.StatusReprocessamentoPagamentoFrete = empresa.StatusReprocessamentoPagamentoFrete;
                empresaCadastrada.HabilitaReprocessamentoValePedagio = empresa.HabilitaReprocessamentoValePedagio;
                empresaCadastrada.PermitirEncerramentoPainelCiot = empresa.PermitirEncerramentoPainelCiot;
                empresaCadastrada.UtilizaCiot = empresa.UtilizaCiot;
                if (empresaCadastrada.DebitoPrazo == 1)
                {
                    empresaCadastrada.DataAlteracaoModelo = DateTime.Now;
                }

                empresaCadastrada.RNTRC = empresa.RNTRC;
                empresaCadastrada.UsuarioCadastroId = empresaCadastrada.UsuarioCadastroId;
                empresaCadastrada.Telefone = empresa.Telefone.OnlyNumbers();
                empresaCadastrada.Celular = empresa.Celular.OnlyNumbers();
                empresaCadastrada.Cep = empresa.Cep.OnlyNumbers();
                empresaCadastrada.Cnpj = empresa.Cnpj.OnlyNumbers();

                if (empresaCadastrada.ContaAbastecimento != empresa.ContaAbastecimento)
                {
                    empresaCadastrada.DataAlteracaoContaAbastecimento = DateTime.Now;
                    empresaCadastrada.UsuarioAlteracaoContaAbastecimentoId = User.Id;
                }
                
                if (empresaCadastrada.ContaFrete != empresa.ContaFrete)
                {
                    empresaCadastrada.DataAlteracaoContaFrete = DateTime.Now;
                    empresaCadastrada.UsuarioAlteracaoContaFreteId = User.Id;
                }

                empresaCadastrada.ContaAbastecimento = empresa.ContaAbastecimento;
                empresaCadastrada.ContaFrete = empresa.ContaFrete;

                if (empresaCadastrada.ContaValePedagio != empresa.ContaValePedagio)
                {
                    empresaCadastrada.DataAlteracaoContaValePedagio = DateTime.Now;
                    empresaCadastrada.UsuarioAlteracaoContaValePedagioId = User.Id;
                }
                
                empresaCadastrada.ContaValePedagio = empresa.ContaValePedagio;

                if (empresa.EmpresaCfop.Count > 0)
                {
                    Repository.Command.RemoveCfopEmpresa(command.Id);
                    empresaCadastrada.EmpresaCfop = new List<EmpresaCfop.EmpresaCfop>();

                    if (command.EmpresaCfop != null)
                    {
                        foreach (var lEmpresaCfop in command.EmpresaCfop)
                        {
                            empresaCadastrada.EmpresaCfop.Add(new EmpresaCfop.EmpresaCfop()
                            {
                                EmpresaId = empresa.Id,
                                CfopId = lEmpresaCfop.CfopId
                            });
                        }
                    }
                }

                await SaveChangesAsync();
                return empresaCadastrada;
            }

            if (empresaCadastrada != null)
            {
                throw new EmpresaInvalidException($"Empresa de CNPJ {command.Cnpj.ToCNPJFormato()} já cadastrada.");
            }

            empresa.DataBloqueio = empresa.DataBloqueio.HasValue && empresa.DataBloqueio.Value > DateTime.MinValue
                ? empresa.DataBloqueio
                : null;
            empresa.DataDesbloqueio =
                empresa.DataDesbloqueio.HasValue && empresa.DataDesbloqueio.Value > DateTime.MinValue
                    ? empresa.DataDesbloqueio
                    : null;
            empresa.UsuarioCadastroId = User.Id;
            empresa.Telefone = empresa.Telefone.OnlyNumbers();
            empresa.Celular = empresa.Celular.OnlyNumbers();
            empresa.Cep = empresa.Cep.OnlyNumbers();
            empresa.Cnpj = empresa.Cnpj.OnlyNumbers();
            empresa.DataCadastro = DateTime.Now;
            empresa.DataAberturaEmpresa = DateTime.Now;
            empresa.Ativo = 1;
            empresa.LiberaBloqueioSPD = 1;

            if (empresa.ContaAbastecimento.HasValue)
            {
                empresa.DataAlteracaoContaAbastecimento = DateTime.Now;
                empresa.UsuarioAlteracaoContaAbastecimentoId = User.Id;
            }

            if (empresa.ContaValePedagio.HasValue)
            {
                empresa.DataAlteracaoContaValePedagio = DateTime.Now;
                empresa.UsuarioAlteracaoContaValePedagioId = User.Id;
            }

            if (empresa.ContaFrete.HasValue)
            {
                empresa.DataAlteracaoContaFrete = DateTime.Now;
                empresa.UsuarioAlteracaoContaFreteId = User.Id;
            }

            empresa.DebitoPrazo = empresa.DebitoPrazo;
            empresa.DebitoProtocolo = empresa.DebitoProtocolo;
            empresa.Prazo = empresa.Prazo;
            if (empresa.DebitoPrazo == 1)
            {
                empresa.DataAlteracaoModelo = DateTime.Now;
            }

            empresa.GrupoEmpresaId = empresa.GrupoEmpresaId == 0 ? null : empresa.GrupoEmpresaId;

            await Repository.Command.AddAsync(empresa);

            await SaveChangesAsync();

            return empresa;
        }

        public async Task HandlerAsync(EmpresaSaveStatusCommand command)
        {
            var cnpj = command.Cnpj.OnlyNumbers();
            var empresaCadastrada = Repository.Query
                .Include(x => x.GrupoEmpresa)
                .FirstOrDefault(o => o.Cnpj == cnpj && o.Id == command.Id);

            if (empresaCadastrada == null)
                throw new Exception("Empresa não encontrada!");

            empresaCadastrada.Ativo = empresaCadastrada.Ativo == 1 ? 0 : 1;
            await SaveChangesAsync();
        }

        public async Task<Empresa> HandlerAsync(EmpresaSaveComRetornoCommand command)
        {
            var cnpj = command.Cnpj.OnlyNumbers();
            var empresaCadastrada = await Repository.Query.GetByCnpjAsync(cnpj);
            var empresa = Mapper.Map<Empresa>(command);
            empresa.ValidarCriacao();

            if (empresaCadastrada != null)
                throw new EmpresaInvalidException($"Empresa de CNPJ {command.Cnpj.ToCNPJFormato()} já cadastrada.");

            if (empresa.NotificacaoContingenciaCiot != null)
            {
                if (empresa.NotificacaoContingenciaCiot != empresaCadastrada.NotificacaoContingenciaCiot)
                {
                    await Engine.CommandBus.SendCommandAsync(new EmailClienteSincronizarMessage()
                    {
                        CnpjCliente = empresa.Cnpj,
                        EmailNotificacaoExterna = empresa.NotificacaoContingenciaCiot
                    });  
                } 
            }
            
            empresa.GrupoEmpresaId = empresa.GrupoEmpresaId == 0 ? null : empresa.GrupoEmpresaId;
            empresa.UsuarioCadastroId = User.Id;
            empresa.Telefone = empresa.Telefone.OnlyNumbers();
            empresa.Celular = empresa.Celular.OnlyNumbers();
            empresa.Cep = empresa.Cep.OnlyNumbers();
            empresa.Cnpj = empresa.Cnpj.OnlyNumbers();
            empresa.StatusCadastro = StatusCadastro.Ativo;
        
            empresa.DataCadastro = DateTime.Now;
            empresa.Ativo = 1;

            await Repository.Command.AddAsync(empresa);
            await Repository.Command.SaveChangesAsync();
            return empresa;
        }
    }
}
using System;
using System.Linq;
using System.Threading.Tasks;
using BBC.Test.Tests.Viagem.Fixture;
using Microsoft.EntityFrameworkCore;
using Moq;
using SistemaInfo.BBC.Application.Objects.Mobile.Viagem.Request;
using SistemaInfo.BBC.Application.Services.Viagem;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Domain.Models.PagamentoEvento.Repository;
using SistemaInfo.BBC.Domain.Models.Transacao.Repository;
using SistemaInfo.BBC.Domain.Models.Viagem.Repository;
using Xunit;

namespace BBC.Test.Tests.Viagem
{
    [Collection(nameof(ViagemCollection))]
    public class ViagemMobileAppServiceAlterarStatusAntecipacaoTest
    {
        private readonly ViagemFixture _fixture;
        private readonly ViagemMobileAppService _appService;
        private readonly Mock<IPagamentoEventoReadRepository> _pagamentoEventoReadRepository;
        private readonly Mock<IPagamentoEventoWriteRepository> _pagamentoEventoWriteRepository;
        private readonly Mock<ITransacaoReadRepository> _transacaoReadRepository;

        public ViagemMobileAppServiceAlterarStatusAntecipacaoTest(ViagemFixture fixture)
        {
            _fixture = fixture;
            _pagamentoEventoReadRepository = fixture.Mocker.GetMock<IPagamentoEventoReadRepository>();
            _pagamentoEventoWriteRepository = fixture.Mocker.GetMock<IPagamentoEventoWriteRepository>();
            _transacaoReadRepository = fixture.Mocker.GetMock<ITransacaoReadRepository>();
            _appService = fixture.Mocker.CreateInstance<ViagemMobileAppService>();
        }

        [Fact(DisplayName = "AlterarStatusAntecipacao - Sucesso para status Disponivel")]
        [Trait(nameof(ViagemMobileAppService), nameof(ViagemMobileAppService.AlterarStatusAntecipacao))]
        public async Task AlterarStatusAntecipacao_StatusDisponivel_DeveRetornarSucesso()
        {
            // Arrange
            var request = _fixture.GerarAtualizarStatusAntecipacaoRequest(
                status: StatusAntecipacaoRequest.Aprovado);
            
            var pagamentoEvento = _fixture.GerarPagamentoEventoComStatusAntecipacao(
                StatusAntecipacaoParcelaProprietario.AguardandoProcessamento);
            
            pagamentoEvento.Viagem.Id = request.ViagemId;
            pagamentoEvento.Id = request.PagamentoId;

            // Setup mocks
            _pagamentoEventoReadRepository
                .Setup(x => x.AsNoTracking())
                .Returns(CreateMockDbSet(new[] { pagamentoEvento }).Object);

            _pagamentoEventoReadRepository
                .Setup(x => x.GetByIdAsync(request.PagamentoId))
                .ReturnsAsync(pagamentoEvento);

            _pagamentoEventoWriteRepository
                .Setup(x => x.UpdateAsync(It.IsAny<SistemaInfo.BBC.Domain.Models.PagamentoEvento.PagamentoEvento>()))
                .Returns(Task.FromResult(0));

            _pagamentoEventoWriteRepository
                .Setup(x => x.SaveChangesAsync())
                .Returns(Task.FromResult(0));

            // Act
            var result = await _appService.AlterarStatusAntecipacao(request);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(request.PagamentoId, result.Id);
            Assert.Equal(StatusAntecipacaoParcelaProprietario.Disponivel, result.StatusAntecipacaoParcelaProprietario);
            
            // Verify that UpdateAsync was called
            _pagamentoEventoWriteRepository.Verify(x => x.UpdateAsync(It.IsAny<SistemaInfo.BBC.Domain.Models.PagamentoEvento.PagamentoEvento>()), Times.Once);
            _pagamentoEventoWriteRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        }

        [Fact(DisplayName = "AlterarStatusAntecipacao - Sucesso para status AguardandoProcessamento")]
        [Trait(nameof(ViagemMobileAppService), nameof(ViagemMobileAppService.AlterarStatusAntecipacao))]
        public async Task AlterarStatusAntecipacao_StatusAguardandoProcessamento_DeveRetornarSucesso()
        {
            // Arrange
            var request = _fixture.GerarAtualizarStatusAntecipacaoRequest(
                status: StatusAntecipacaoRequest.AguardandoProcessamento);
            
            var pagamentoEvento = _fixture.GerarPagamentoEventoComStatusAntecipacao(
                StatusAntecipacaoParcelaProprietario.Disponivel);
            
            pagamentoEvento.Viagem.Id = request.ViagemId;
            pagamentoEvento.Id = request.PagamentoId;

            // Setup mocks
            _pagamentoEventoReadRepository
                .Setup(x => x.AsNoTracking())
                .Returns(CreateMockDbSet([pagamentoEvento]).Object);

            _pagamentoEventoReadRepository
                .Setup(x => x.GetByIdAsync(request.PagamentoId))
                .ReturnsAsync(pagamentoEvento);

            _pagamentoEventoWriteRepository
                .Setup(x => x.UpdateAsync(It.IsAny<SistemaInfo.BBC.Domain.Models.PagamentoEvento.PagamentoEvento>()))
                .Returns(Task.FromResult(0));

            _pagamentoEventoWriteRepository
                .Setup(x => x.SaveChangesAsync())
                .Returns(Task.FromResult(0));

            // Act
            var result = await _appService.AlterarStatusAntecipacao(request);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(request.PagamentoId, result.Id);
            Assert.Equal(StatusAntecipacaoParcelaProprietario.AguardandoProcessamento, result.StatusAntecipacaoParcelaProprietario);
            
            // Verify that UpdateAsync was called
            _pagamentoEventoWriteRepository.Verify(x => x.UpdateAsync(It.IsAny<SistemaInfo.BBC.Domain.Models.PagamentoEvento.PagamentoEvento>()), Times.Once);
            _pagamentoEventoWriteRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        }

        [Theory(DisplayName = "AlterarStatusAntecipacao - Deve falhar para status já Aprovado ou Erro")]
        [Trait(nameof(ViagemMobileAppService), nameof(ViagemMobileAppService.AlterarStatusAntecipacao))]
        [InlineData(StatusAntecipacaoParcelaProprietario.Aprovado)]
        [InlineData(StatusAntecipacaoParcelaProprietario.Erro)]
        public async Task AlterarStatusAntecipacao_StatusJaAprovadoOuErro_DeveLancarExcecao(
            StatusAntecipacaoParcelaProprietario statusAtual)
        {
            // Arrange
            var request = _fixture.GerarAtualizarStatusAntecipacaoRequest(
                status: StatusAntecipacaoRequest.AguardandoProcessamento);
            
            var pagamentoEvento = _fixture.GerarPagamentoEventoComStatusAntecipacao(statusAtual);
            pagamentoEvento.Viagem.Id = request.ViagemId;
            pagamentoEvento.Id = request.PagamentoId;

            // Setup mocks
            _pagamentoEventoReadRepository
                .Setup(x => x.AsNoTracking())
                .Returns(CreateMockDbSet(new[] { pagamentoEvento }).Object);

            // Act & Assert
            var exception = await Assert.ThrowsAsync<Exception>(() => _appService.AlterarStatusAntecipacao(request));
            Assert.Equal("Não foi possível alterar o status do pagamento envento, tente novamente mais tarde", exception.Message);
            
            // Verify that UpdateAsync was NOT called
            _pagamentoEventoWriteRepository.Verify(x => x.UpdateAsync(It.IsAny<SistemaInfo.BBC.Domain.Models.PagamentoEvento.PagamentoEvento>()), Times.Never);
        }

        [Fact(DisplayName = "AlterarStatusAntecipacao - Deve falhar quando pagamento não encontrado")]
        [Trait(nameof(ViagemMobileAppService), nameof(ViagemMobileAppService.AlterarStatusAntecipacao))]
        public async Task AlterarStatusAntecipacao_PagamentoNaoEncontrado_DeveLancarExcecao()
        {
            // Arrange
            var request = _fixture.GerarAtualizarStatusAntecipacaoRequest();

            // Setup mocks - retorna null para simular pagamento não encontrado
            _pagamentoEventoReadRepository
                .Setup(x => x.AsNoTracking())
                .Returns(CreateMockDbSet(new SistemaInfo.BBC.Domain.Models.PagamentoEvento.PagamentoEvento[0]).Object);

            // Act & Assert
            var exception = await Assert.ThrowsAsync<Exception>(() => _appService.AlterarStatusAntecipacao(request));
            Assert.Equal("Pagamento Evento não encontrado para realizar a atualização", exception.Message);
        }

        [Theory(DisplayName = "AlterarStatusAntecipacao - Deve falhar com filtros inválidos")]
        [Trait(nameof(ViagemMobileAppService), nameof(ViagemMobileAppService.AlterarStatusAntecipacao))]
        [InlineData(0, 1, "Informe um ViagemId válido.")]
        [InlineData(-1, 1, "Informe um ViagemId válido.")]
        [InlineData(1, 0, "Informe um PagamentoId válido.")]
        [InlineData(1, -1, "Informe um PagamentoId válido.")]
        public async Task AlterarStatusAntecipacao_FiltrosInvalidos_DeveLancarExcecao(
            int viagemId, int pagamentoId, string mensagemEsperada)
        {
            // Arrange
            var request = new AtualizarStatusAntecipacaoRequest
            {
                ViagemId = viagemId,
                PagamentoId = pagamentoId,
                StatusAntecipacao = StatusAntecipacaoRequest.AguardandoProcessamento
            };

            // Act & Assert
            var exception = await Assert.ThrowsAsync<Exception>(() => _appService.AlterarStatusAntecipacao(request));
            Assert.Equal(mensagemEsperada, exception.Message);
        }

        [Fact(DisplayName = "AlterarStatusAntecipacao - Status Erro sem transação de retenção existente")]
        [Trait(nameof(ViagemMobileAppService), nameof(ViagemMobileAppService.AlterarStatusAntecipacao))]
        public async Task AlterarStatusAntecipacao_StatusErroSemTransacaoRetencao_DeveRetornarSucesso()
        {
            // Arrange
            var request = _fixture.GerarAtualizarStatusAntecipacaoRequest(
                status: StatusAntecipacaoRequest.Erro);

            var pagamentoEvento = _fixture.GerarPagamentoEventoComStatusAntecipacao(
                StatusAntecipacaoParcelaProprietario.AguardandoProcessamento);

            pagamentoEvento.Viagem.Id = request.ViagemId;
            pagamentoEvento.Id = request.PagamentoId;

            // Setup mocks - não existe transação de retenção
            _pagamentoEventoReadRepository
                .Setup(x => x.AsNoTracking())
                .Returns(CreateMockDbSet(new[] { pagamentoEvento }).Object);

            _transacaoReadRepository
                .Setup(x => x.AsNoTracking())
                .Returns(CreateMockDbSet(new SistemaInfo.BBC.Domain.Models.Transacao.Transacao[0]).Object);

            _pagamentoEventoReadRepository
                .Setup(x => x.GetByIdAsync(request.PagamentoId))
                .ReturnsAsync(pagamentoEvento);

            _pagamentoEventoWriteRepository
                .Setup(x => x.UpdateAsync(It.IsAny<SistemaInfo.BBC.Domain.Models.PagamentoEvento.PagamentoEvento>()))
                .Returns(Task.FromResult(0));

            _pagamentoEventoWriteRepository
                .Setup(x => x.SaveChangesAsync())
                .Returns(Task.FromResult(0));

            // Act
            var result = await _appService.AlterarStatusAntecipacao(request);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(request.PagamentoId, result.Id);
            Assert.Equal(StatusAntecipacaoParcelaProprietario.Erro, result.StatusAntecipacaoParcelaProprietario);

            // Verify that UpdateAsync was called
            _pagamentoEventoWriteRepository.Verify(x => x.UpdateAsync(It.IsAny<SistemaInfo.BBC.Domain.Models.PagamentoEvento.PagamentoEvento>()), Times.Once);
            _pagamentoEventoWriteRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        }

        [Fact(DisplayName = "AlterarStatusAntecipacao - Status Erro com transação de retenção existente")]
        [Trait(nameof(ViagemMobileAppService), nameof(ViagemMobileAppService.AlterarStatusAntecipacao))]
        public async Task AlterarStatusAntecipacao_StatusErroComTransacaoRetencao_DeveRetornarSucesso()
        {
            // Arrange
            var request = _fixture.GerarAtualizarStatusAntecipacaoRequest(
                status: StatusAntecipacaoRequest.Erro);

            var pagamentoEvento = _fixture.GerarPagamentoEventoComStatusAntecipacao(
                StatusAntecipacaoParcelaProprietario.AguardandoProcessamento);

            pagamentoEvento.Viagem.Id = request.ViagemId;
            pagamentoEvento.Id = request.PagamentoId;

            var transacaoRetencao = _fixture.GerarTransacaoRetencao(pagamentoEvento.Id);

            // Setup mocks - existe transação de retenção
            _pagamentoEventoReadRepository
                .Setup(x => x.AsNoTracking())
                .Returns(CreateMockDbSet(new[] { pagamentoEvento }).Object);

            _transacaoReadRepository
                .Setup(x => x.AsNoTracking())
                .Returns(CreateMockDbSet(new[] { transacaoRetencao }).Object);

            _pagamentoEventoReadRepository
                .Setup(x => x.GetByIdAsync(request.PagamentoId))
                .ReturnsAsync(pagamentoEvento);

            _pagamentoEventoWriteRepository
                .Setup(x => x.UpdateAsync(It.IsAny<SistemaInfo.BBC.Domain.Models.PagamentoEvento.PagamentoEvento>()))
                .Returns(Task.FromResult(0));

            _pagamentoEventoWriteRepository
                .Setup(x => x.SaveChangesAsync())
                .Returns(Task.FromResult(0));

            // Act
            var result = await _appService.AlterarStatusAntecipacao(request);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(request.PagamentoId, result.Id);
            Assert.Equal(StatusAntecipacaoParcelaProprietario.Erro, result.StatusAntecipacaoParcelaProprietario);

            // Verify that UpdateAsync was called
            _pagamentoEventoWriteRepository.Verify(x => x.UpdateAsync(It.IsAny<SistemaInfo.BBC.Domain.Models.PagamentoEvento.PagamentoEvento>()), Times.Once);
            _pagamentoEventoWriteRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        }

        private Mock<IQueryable<T>> CreateMockDbSet<T>(T[] data) where T : class
        {
            var queryable = data.AsQueryable();
            var mockSet = new Mock<IQueryable<T>>();
            mockSet.As<IQueryable<T>>().Setup(m => m.Provider).Returns(queryable.Provider);
            mockSet.As<IQueryable<T>>().Setup(m => m.Expression).Returns(queryable.Expression);
            mockSet.As<IQueryable<T>>().Setup(m => m.ElementType).Returns(queryable.ElementType);
            mockSet.As<IQueryable<T>>().Setup(m => m.GetEnumerator()).Returns(queryable.GetEnumerator());
            return mockSet;
        }
    }
}

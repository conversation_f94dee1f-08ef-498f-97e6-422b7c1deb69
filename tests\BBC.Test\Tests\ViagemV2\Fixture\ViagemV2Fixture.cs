using System.Collections.Generic;
using System.Linq;
using Bogus;
using Bogus.Extensions.Brazil;
using SistemaInfo.BBC.Application.Objects.Api.Viagem;
using SistemaInfo.BBC.Domain.Enum;
using Xunit;

namespace BBC.Test.Tests.ViagemV2.Fixture
{
    [CollectionDefinition(nameof(ViagemV2Collection))]
    public class ViagemV2Collection : ICollectionFixture<ViagemV2Fixture>
    {
    }

    public class ViagemV2Fixture : MockEngine
    {
        public CancelamentoEventoViagemV2Request GerarCancelamentoEventoViagemV2Request()
        {
            var faker = new Faker<CancelamentoEventoViagemV2Request>("pt_BR")
                .CustomInstantiator(f => new CancelamentoEventoViagemV2Request
                {
                    ViagemExternoId = f.Random.Number(1, 100000),
                    Motivo = f.Lorem.Sentence(10)
                });

            return faker;
        }

        public SistemaInfo.BBC.Domain.Models.Viagem.Viagem GerarViagem()
        {
            var faker = new Faker<SistemaInfo.BBC.Domain.Models.Viagem.Viagem>("pt_BR")
                .CustomInstantiator(f => new SistemaInfo.BBC.Domain.Models.Viagem.Viagem
                {
                    Id = f.Random.Number(1, 100000),
                    EmpresaId = f.Random.Number(1, 100),
                    Status = f.PickRandom<StatusViagem>(),
                    ViagemExternoId = f.Random.Number(1, 100000),
                    PortadorProprietarioId = f.Random.Number(1, 1000),
                    NomeProprietario = f.Person.FullName,
                    PortadorMotoristaId = f.Random.Number(1, 1000),
                    NomeMotorista = f.Person.FullName,
                    CidadeOrigemId = f.Random.Number(1, 10000),
                    CidadeDestinoId = f.Random.Number(1, 10000),
                    TipoBanco = f.PickRandom<TipoBanco>(),
                    PagamentoExternoId = f.Random.Number(1, 100000),
                    Agencia = f.Random.Number(1000, 9999).ToString(),
                    Conta = f.Finance.Account(10),
                    Ciot = f.Random.Number(*********, *********).ToString(),
                    CiotId = f.Random.Number(1, 100000),
                    DataDeclaracaoCiot = f.Date.Recent(30),
                    TipoCiot = f.PickRandom<TipoCiot?>(),
                    StatusCiot = f.PickRandom<StatusCiot?>(),
                    DescricaoCiot = f.Lorem.Sentence(5),
                    DataCancelamento = f.Date.Recent(10),
                    ValorFrete = f.Random.Decimal(1000, 50000),
                    ValorSaldo = f.Random.Decimal(0, 10000),
                    ValorAdiantamento = f.Random.Decimal(0, 20000),
                    QuantidadeTarifas = f.Random.Number(0, 10),
                    ValorTarifas = f.Random.Decimal(0, 1000),
                    ValorCombustivel = f.Random.Decimal(0, 5000),
                    ValorDespesa = f.Random.Decimal(0, 2000),
                    TotalImposto = f.Random.Decimal(0, 3000),
                    TotalPedagio = f.Random.Decimal(0, 1500),
                    DataCadastro = f.Date.Past(1),
                    UsuarioCadastroId = f.Random.Number(1, 100),
                    DataAlteracao = f.Date.Recent(30),
                    UsuarioAlteracaoId = f.Random.Number(1, 100),
                    // Propriedades de navegação serão configuradas conforme necessário nos testes
                    Empresa = GerarEmpresa(),
                    PortadorProprietario = GerarPortador(),
                    PortadorMotorista = GerarPortador(),
                    PagamentoEvento = new List<SistemaInfo.BBC.Domain.Models.PagamentoEvento.PagamentoEvento>()
                });

            return faker;
        }

        public SistemaInfo.BBC.Domain.Models.PagamentoEvento.PagamentoEvento GerarPagamentoEvento()
        {
            var faker = new Faker<SistemaInfo.BBC.Domain.Models.PagamentoEvento.PagamentoEvento>("pt_BR")
                .CustomInstantiator(f => new SistemaInfo.BBC.Domain.Models.PagamentoEvento.PagamentoEvento
                {
                    Id = f.Random.Number(1, 100000),
                    EmpresaId = f.Random.Number(1, 100),
                    ViagemId = f.Random.Number(1, 100000),
                    PagamentoExternoId = f.Random.Number(1, 100000),
                    Tipo = f.PickRandom<Tipo>(),
                    FormaPagamento = f.PickRandom<FormaPagamentoEvento>(),
                    Status = f.PickRandom<StatusPagamento>(),
                    Valor = f.Random.Decimal(100, 10000),
                    ValorTransferenciaMotorista = f.Random.Decimal(0, 5000),
                    CodigoTransacao = f.Random.AlphaNumeric(20),
                    DataCadastro = f.Date.Past(1),
                    UsuarioCadastroId = f.Random.Number(1, 100),
                    DataAlteracao = f.Date.Recent(30),
                    UsuarioAlteracaoId = f.Random.Number(1, 100),
                    DataCancelamento = f.Date.Recent(10),
                    JsonRetornoCancelamento = f.Lorem.Sentence(20),
                    DataRetornoCancelamento = f.Date.Recent(10)
                });

            return faker;
        }

        public SistemaInfo.BBC.Domain.Models.Empresa.Empresa GerarEmpresa()
        {
            var faker = new Faker<SistemaInfo.BBC.Domain.Models.Empresa.Empresa>("pt_BR")
                .CustomInstantiator(f => new SistemaInfo.BBC.Domain.Models.Empresa.Empresa
                {
                    Id = f.Random.Number(1, 100),
                    NomeFantasia = f.Company.CompanyName(),
                    RazaoSocial = f.Company.CompanyName(),
                    Cnpj = f.Company.Cnpj(),
                    Email = f.Internet.Email(),
                    Telefone = f.Phone.PhoneNumber(),
                    Celular = f.Phone.PhoneNumber(),
                    Endereco = f.Address.FullAddress(),
                    Bairro = f.Address.County(),
                    CidadeId = f.Random.Number(1, 10000),
                    RecebedorAutorizado = f.Random.Bool()
                });

            return faker;
        }

        public SistemaInfo.BBC.Domain.Models.Portador.Portador GerarPortador()
        {
            var faker = new Faker<SistemaInfo.BBC.Domain.Models.Portador.Portador>("pt_BR")
                .CustomInstantiator(f => new SistemaInfo.BBC.Domain.Models.Portador.Portador
                {
                    Id = f.Random.Number(1, 100000),
                    Nome = f.Person.FullName,
                    CpfCnpj = f.Person.Cpf(),
                    Email = f.Person.Email,
                    Endereco = f.Address.StreetAddress(),
                    EstadoId = f.Random.Number(1, 27),
                    CidadeId = f.Random.Number(1, 10000),
                    Cep = f.Address.ZipCode(),
                    EnderecoNumero = f.Random.Number(1, 9999),
                    Telefone = f.Phone.PhoneNumber(),
                    Celular = f.Phone.PhoneNumber(),
                    Bairro = f.Address.County(),
                    Complemento = f.Address.SecondaryAddress(),
                    Ativo = f.Random.Number(0, 1),
                    RNTRC = f.Random.Number(*********, *********).ToString(),
                    NumeroCNH = f.Random.Number(1000000, 9999999).ToString(),
                    RazaoSocial = f.Company.CompanyName(),
                    TipoPessoa = f.PickRandom<SistemaInfo.BBC.Domain.Enum.ETipoPessoa>(),
                    DataNascimento = f.Person.DateOfBirth,
                    Atividade = f.PickRandom<SistemaInfo.BBC.Domain.Enum.EAtividade>(),
                    DataCadastro = f.Date.Past(2),
                    UsuarioCadastroId = f.Random.Number(1, 100)
                });

            return faker;
        }

        public CancelamentoPagamentoEventoViagemResponse GerarCancelamentoPagamentoEventoViagemResponse(bool sucesso = true)
        {
            var faker = new Faker<CancelamentoPagamentoEventoViagemResponse>("pt_BR")
                .CustomInstantiator(f => new CancelamentoPagamentoEventoViagemResponse
                {
                    Sucesso = sucesso,
                    Mensagem = sucesso ? "Cancelamento realizado com sucesso" : "Erro no cancelamento",
                    Data = sucesso ? new List<SistemaInfo.BBC.Domain.Models.PagamentoEvento.PagamentoEvento> { GerarPagamentoEvento() } : []
                });

            return faker;
        }

        public IQueryable<SistemaInfo.BBC.Domain.Models.Viagem.Viagem> GerarQueryableViagemVazio()
        {
            return new List<SistemaInfo.BBC.Domain.Models.Viagem.Viagem>().AsQueryable();
        }

        public IQueryable<SistemaInfo.BBC.Domain.Models.Viagem.Viagem> GerarQueryableViagem(params SistemaInfo.BBC.Domain.Models.Viagem.Viagem[] viagens)
        {
            return viagens.ToList().AsQueryable();
        }

        public CancelamentoEventoViagemV2Response GerarCancelamentoEventoViagemV2Response(bool sucesso = true, string mensagem = null)
        {
            return new CancelamentoEventoViagemV2Response
            {
                Sucesso = sucesso,
                Mensagem = mensagem ?? (sucesso ? "Operação realizada com sucesso" : "Erro na operação"),
                Data = sucesso ? new CancelamentoViagemV2Response
                {
                    Viagem = new CancelamentoInfoViagemV2Response
                    {
                        Id = new Faker().Random.Number(1, 100000),
                        ViagemExternoId = new Faker().Random.Number(1, 100000),
                        Status = (int)StatusViagem.Cancelado,
                        Ciot = new Faker().Random.Number(*********, *********).ToString(),
                        StatusCiot = (int)StatusCiot.Cancelado,
                        ValorFrete = new Faker().Random.Decimal(1000, 50000),
                        PagamentoEventos = []
                    }
                } : null
            };
        }
    }
}

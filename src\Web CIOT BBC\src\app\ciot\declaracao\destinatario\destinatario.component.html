<div class="container main-container">
    <form novalidate [formGroup]="destinatarioForm">
        <div class="form-horizontal">
            <fieldset>
                <legend><PERSON><PERSON></legend>
                <div class="row">
                    <div class="col-sm-12 col-md-6 col-lg-6">
                        <div class="form-group required" [ngClass]="{'has-error': displayMessage.cpfCnpj }">
                            <label class="control-label" for="formaPagamento">CPF/CNPJ</label>
                            <input [mask]="maskCpfCnpj" class="form-control" maxlength="18" id="cpfCnpj" type="text"
                                formControlName="cpfCnpj" OnlyNumber />
                            <span class="text-danger" *ngIf="displayMessage.cpfCnpj">
                                <p [innerHTML]="displayMessage.cpfCnpj"></p>
                            </span>
                        </div>
                    </div>
                    <!-- <div class="col-sm-12 col-md-6 col-lg-3">
                        <div class="form-group" [ngClass]="{'has-error': displayMessage.rntrc }">
                            <label class="control-label" for="formaPagamento">RNTRC</label>
                            <input class="form-control" id="rntrc" type="text" formControlName="rntrc" maxlength="9" RntrcMask disabled />
                            <span class="text-danger" *ngIf="displayMessage.rntrc">
                <p [innerHTML]="displayMessage.rntrc"></p>
              </span>
                        </div>
                    </div> -->
                    <div class="col-sm-12 col-md-6 col-lg-3">
                        <div class="btn-group" dropdown container="body">
                            <button dropdownToggle type="button" class="btn btn-danger dropdownToggle">
                                Copiar de:
                                <span class="caret"></span>
                            </button>
                            <ul *dropdownMenu class="dropdown-menu" role="menu">
                                <li role="menuitem">
                                    <a class="dropdown-item" (click)='copiarContratante()'
                                        href="javascript:;">Contratante</a>
                                </li>
                                <li role="menuitem">
                                    <a class="dropdown-item" (click)='copiarRemetente()'
                                        href="javascript:;">Remetente</a>
                                </li>
                                <li role="menuitem">
                                    <a class="dropdown-item" (click)='copiarConsignatario()'
                                        href="javascript:;">Consignatário</a>
                                </li>
                                <li role="menuitem">
                                    <a class="dropdown-item" (click)='copiarTransportador()'
                                        href="javascript:;">Transportador</a>
                                </li>
                                <li class="divider dropdown-divider"></li>
                                <li role="menuitem">
                                    <a class="dropdown-item" (click)='limpar()' href="javascript:;">Limpar</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-12 col-md-6 col-lg-3">
                        <div class="form-group required" [ngClass]="{'has-error': displayMessage.nome}">
                            <label class="control-label" for="nome">Nome/Razão social</label>
                            <input class="form-control" id="nome" type="text" formControlName="nome" />
                            <span class="text-danger" *ngIf="displayMessage.nome">
                                <p [innerHTML]="displayMessage.nome"></p>
                            </span>
                        </div>
                    </div>
                    <div class="col-sm-12 col-md-6 col-lg-3">
                        <label class="control-label" for="nomeFantasia">Nome fantasia:</label>
                        <input class="form-control" id="nomeFantasia" type="text" formControlName="nomeFantasia" />
                    </div>
                </div>
            </fieldset>
            <br />
            <fieldset>
                <legend>Endereço</legend>
                <div class="row">
                    <div class="col-sm-12 col-md-6 col-lg-3">
                        <div class="form-group required" [ngClass]="{'has-error': displayMessage.cep}">
                            <label class="control-label" for="cep">CEP</label>
                            <div class="input-group">
                                <input mask="00000-000" class="form-control" id="cep" type="text"
                                    formControlName="cep" />
                            </div>
                            <span class="text-danger" *ngIf="displayMessage.cep">
                                <p [innerHTML]="displayMessage.cep"></p>
                            </span>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-12 col-md-6 col-lg-3">
                        <div class="form-group required" [ngClass]="{'has-error': displayMessage.logradouro}">
                            <label class="control-label" for="logradouro">Logradouro</label>
                            <input class="form-control" maxlength="40" id="logradouro" type="text"
                                formControlName="logradouro" />
                            <span class="text-danger" *ngIf="displayMessage.logradouro">
                                <p [innerHTML]="displayMessage.logradouro"></p>
                            </span>
                        </div>
                    </div>
                    <div class="col-sm-12 col-md-6 col-lg-3">
                        <label class="control-label" for="numero">Número</label>
                        <input class="form-control" id="numero" type="text" formControlName="numero" OnlyNumber />
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-12 col-md-6 col-lg-3">
                        <label class="control-label" for="bairro">Bairro</label>
                        <input class="form-control" id="bairro" type="text" formControlName="bairro" />
                    </div>
                    <div class="col-sm-12 col-md-6 col-lg-3">
                        <label class="control-label" for="complemento">Complemento</label>
                        <input class="form-control" id="complemento" type="text" formControlName="complemento" />
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-12 col-md-6 col-lg-3">
                        <div class="form-group required" [ngClass]="{'has-error': displayMessage.cidade}">
                            <label class="control-label" for="cidade">Cidade</label>
                            <div class="alert alert-danger" *ngIf="noResult">Cidade não encontrada</div>
                            <input [(ngModel)]="cidadeCompleterText" [typeahead]="cidadeList"
                                (typeaheadNoResults)="typeaheadNoResults($event)" typeaheadOptionField="nome"
                                (typeaheadOnSelect)="onSelectCidade($event)" class="form-control" autocomplete="off"
                                [ngModelOptions]="{standalone: true}">
                        </div>
                    </div>
                </div>
            </fieldset>
            <br />
            <fieldset>
                <legend>Contato</legend>
                <div class="row">
                    <div class="col-sm-12 col-md-6 col-lg-3">
                        <div class="form-group required" [ngClass]="{'has-error': displayMessage.telefone}">
                            <label class="control-label" for="telefone">Telefone</label>
                             <telefone [control]="destinatarioForm.get('telefone')"></telefone>
                            <span class="text-danger" *ngIf="displayMessage.telefone">
                                <p [innerHTML]="displayMessage.telefone"></p>
                            </span>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-12 col-md-6 col-lg-3">
                        <div class="form-group" [ngClass]="{'has-error': displayMessage.email}">
                            <label class="control-label" for="email">E-mail</label>
                            <div class="input-group">
                                <input class="form-control" SpecialCharacterNotAllowed id="compemaillemento"
                                    type="email" formControlName="email" />
                            </div>
                        </div>
                    </div>
                </div>
            </fieldset>
        </div>
    </form>
</div>

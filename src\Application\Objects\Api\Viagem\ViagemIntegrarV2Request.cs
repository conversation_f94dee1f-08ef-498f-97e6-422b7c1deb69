using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Domain.External.CIOT.DTO;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.Objects.Api.Viagem
{
    /// <summary>
    /// Request para integração completa de viagem V2 com múltiplos pagamentos e CIOT
    /// </summary>
    public class ViagemIntegrarV2Request
    {
        [Required(ErrorMessage = "O campo ViagemExternoId é obrigatório.")]
        public int ViagemExternoId { get; set; }

        public DateTime? DataInicioFrete { get; set; }

        [Required(ErrorMessage = "O campo DataTerminoFrete é obrigatório.")]
        public DateTime DataTerminoFrete { get; set; }

        public int? CodigoMunicipioOrigem { get; set; }
        public int? CodigoMunicipioDestino { get; set; }

        public int? TipoViagem { get; set; }
        public decimal? PesoCarga { get; set; }
        public string CodigoNaturezaCarga { get; set; }

        [Required(ErrorMessage = "O campo Motorista é obrigatório.")]
        public PortadorV2Request Motorista { get; set; }

        [Required(ErrorMessage = "O campo Proprietario é obrigatório.")]
        public PortadorV2Request Proprietario { get; set; }

        [Required(ErrorMessage = "O campo Contratante é obrigatório.")]
        public PortadorV2Request Contratante { get; set; }

        public PortadorV2Request Remetente { get; set; }
        public PortadorV2Request Destinatario { get; set; }

        [Required(ErrorMessage = "O campo Valores é obrigatório.")]
        public ValoresV2Request Valores { get; set; }

        public List<VeiculoV2Request> Veiculos { get; set; } = new List<VeiculoV2Request>();

        [Required(ErrorMessage = "O campo Pagamentos é obrigatório.")]
        public List<PagamentoV2Request> Pagamentos { get; set; } = new List<PagamentoV2Request>();

        public string Filial { get; set; }


        public RespPadrao ValidarCamposObrigatoriosCiot()
        {
            if (Veiculos == null || !Veiculos.Any())
                return new RespPadrao(false, "Veículos são obrigatórios para declaração de CIOT.");

            if (!Valores.QuantidadeTarifas.HasValue)
                return new RespPadrao(false, "QuantidadeTarifas é obrigatória para declaração de CIOT.");

            if (!Valores.ValorTarifas.HasValue)
                return new RespPadrao(false, "ValorTarifas é obrigatória para declaração de CIOT.");

            if (!TipoViagem.HasValue)
                return new RespPadrao(false, "TipoViagem é obrigatória para declaração de CIOT.");

            if (!PesoCarga.HasValue)
                return new RespPadrao(false, "PesoCarga é obrigatória para declaração de CIOT.");

            if (Valores.ValorFrete <= 0)
                return new RespPadrao(false, "ValorFrete deve ser maior que zero para declaração de CIOT.");
            
            if (!DataInicioFrete.HasValue)
                return new RespPadrao(false, "Data de Início do Frete é obrigatória para o tipo de viagem 1.");

            if (Contratante == null)
                return new RespPadrao(false, "Contratante é obrigatório para declaração de CIOT.");

            return new RespPadrao(true, "Validação de CIOT realizada com sucesso.");
        }
        
        public bool DeclaraTacAgregado()
        {
            return TipoViagem == 3;
        }
        
        public bool DeclaraCiotNormal()
        {
            return TipoViagem == 1;
        }
    }

    /// <summary>
    /// Dados do portador para integração V2
    /// </summary>
    public class PortadorV2Request
    {
        [Required(ErrorMessage = "O campo CpfCnpj é obrigatório.")]
        public string CpfCnpj { get; set; }
        
        [Required(ErrorMessage = "O campo NomeRazaoSocial é obrigatório.")]
        public string NomeRazaoSocial { get; set; }
        
        public string RNTRC { get; set; }
        
        [Required(ErrorMessage = "O campo TipoPessoa é obrigatório.")]
        public string TipoPessoa { get; set; } // "F" ou "J"
        
        public EnderecoV2Request Endereco { get; set; }
    }
    
    /// <summary>
    /// Dados do endereço para integração V2
    /// </summary>
    public class EnderecoV2Request
    {
        public string Cep { get; set; }
        public int CodigoMunicipio { get; set; }
        public string Logradouro { get; set; }
        public string Numero { get; set; }
        public string Complemento { get; set; }
        public string Bairro { get; set; }
        public string Telefone { get; set; }
        public string Email { get; set; }
    }
    
    /// <summary>
    /// Valores da viagem para integração V2
    /// </summary>
    public class ValoresV2Request
    {
        public int? QuantidadeTarifas { get; set; }
        public decimal? ValorTarifas { get; set; }
        public decimal? ValorCombustivel { get; set; }
        public decimal? ValorDespesas { get; set; }
        public decimal? TotalImposto { get; set; }
        public decimal? TotalPegadio { get; set; }
        
        [Required(ErrorMessage = "O campo Adiantamentos é obrigatório.")]
        public decimal Adiantamentos { get; set; }
        
        [Required(ErrorMessage = "O campo Saldo é obrigatório.")]
        public decimal Saldo { get; set; }

        public decimal? Complementos { get; set; }

        [Required(ErrorMessage = "O campo ValorFrete é obrigatório.")]
        public decimal ValorFrete { get; set; }
    }
    
    /// <summary>
    /// Dados do veículo para integração V2
    /// </summary>
    public class VeiculoV2Request
    {
        [Required(ErrorMessage = "O campo Placa é obrigatório.")]
        public string Placa { get; set; }
        
        public string RNTRC { get; set; }
    }
    
    /// <summary>
    /// Dados do pagamento para integração V2
    /// </summary>
    public class PagamentoV2Request
    {
        [Required(ErrorMessage = "O campo PagamentoExternoId é obrigatório.")]
        public int? PagamentoExternoId { get; set; }
        
        [Required(ErrorMessage = "O campo DataPrevisaoPagamento é obrigatório.")]
        public DateTime? DataPrevisaoPagamento { get; set; }
        
        [Required(ErrorMessage = "O campo Tipo é obrigatório.")]
        public Tipo Tipo { get; set; } // "Adiantamento" ou "Saldo"
        
        [Required(ErrorMessage = "O campo FormaPagamento é obrigatório.")]
        public FormaPagamentoEvento FormaPagamento { get; set; }
        
        [Required(ErrorMessage = "O campo Valor é obrigatório.")]
        public decimal Valor { get; set; }
        
        // public string TipoBanco { get; set; }
        public string Agencia { get; set; }
        public string Conta { get; set; }
        public ETipoContaDock TipoConta { get; set; } 
        public string RecebedorAutorizado { get; set; }
        public string ChavePix { get; set; }
        public string HashValidacao { get; set; }
        public string WebhookUrl { get; set; }

        [Required(ErrorMessage = "O campo Status é obrigatório.")]
        public int Status { get; set; }
    }
}

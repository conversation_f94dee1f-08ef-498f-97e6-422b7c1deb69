import { Component, Input, ViewContainerRef } from '@angular/core';
import { Router } from '@angular/router';
import { ToastsManager } from 'ng2-toastr';
import {
  Consignatario,
  DeclararOperacaoTransporteReq,
  Endereco,
  Frete,
  Motorista,
  InformacoesPagamento,
  FreteRetorno,
  Pagamento,
  Valores,
  DadosBancarios,
  Parcela
} from '../../models/declarar-operacao-transporte';
import { CiotService } from '../../services/ciot.service';
import { ContratanteComponent } from '../contratante/contratante.component';
import { DestinatarioComponent } from '../destinatario/destinatario.component';
import { ConsignatarioComponent } from '../consignatario/consignatario.component';
import { TransportadorVeiculosComponent } from '../transportador-veiculos/transportador-veiculos.component';
import { RemetenteComponent } from '../remetente/remetente.component';
import { ViagemComponent } from '../viagem/viagem.component';
import { PagamentoComponent } from '../pagamento/pagamento.component';
import { MotoristaComponent } from '../motorista/motorista.component';
import { ViagemDTO } from '../../models/declaracao-dto/viagem-dto';
import { MotoristaDTO } from '../../models/declaracao-dto/motorista-dto';
import { PagamentoDTO } from "../../models/declaracao-dto/pagamento-dto";
import { DadosPessoaisDTO } from '../../models/declaracao-dto/dados-pessoais';
import { Mensagens, TipoPessoa, TipoViagem } from '../../util/enums';

@Component({
  selector: 'app-salvar-declaracao',
  templateUrl: './salvar-declaracao.component.html',
  styles: []
})
export class SalvarDeclaracaoComponent {

  public errors: any[] = [];
  public declaracao: DeclararOperacaoTransporteReq = new DeclararOperacaoTransporteReq;
  private viagemDTO: ViagemDTO;
  private motoristaDTO: MotoristaDTO;
  private pagamentoDTO: PagamentoDTO;
  private dadosPessoaisDTO: DadosPessoaisDTO
  private valid: boolean = true;
  private validFreteRetorno: boolean = false;

  // Declaração form inputs
  @Input() viagemComponent: ViagemComponent;
  @Input() pagamentoComponent: PagamentoComponent;
  @Input() transportadorVeiculosComponent: TransportadorVeiculosComponent;
  @Input() motoristaComponent: MotoristaComponent;

  @Input() contratanteComponent: ContratanteComponent;
  @Input() remetenteComponent: RemetenteComponent;
  @Input() destinatarioComponent: DestinatarioComponent;
  @Input() consignatarioComponent: ConsignatarioComponent;

  constructor(private ciotService: CiotService,
    private vRef: ViewContainerRef,
    private toastr: ToastsManager,
    private router: Router) {
  }

  salvar() {
    this.valid = true;
    this.populaFrete();
    this.populaPagamento();
    this.populaTransportadorVeiculos();
    this.populaMotorista();
    this.populaContratante();
    if (TipoViagem.Padrao == this.viagemComponent.tipoViagem) {
      this.populaRemetente();
      this.populaDestinatario();
      this.populaConsignatario();
    }
    if (this.valid) {
      this.declaracao.ciotAjuste = "";
      this.ciotService.declararOperacaoTransporte(this.declaracao)
        .subscribe(
          result => {
            this.onSaveComplete(result)
          },
          error => {
            this.onError(error);
          });
    }
  }

  private populaTransportadorVeiculos() {
    if (this.transportadorVeiculosComponent != undefined && this.transportadorVeiculosComponent.transportadorForm.dirty) {
      this.dadosPessoaisDTO = this.transportadorVeiculosComponent.transportadorForm.value;
      this.declaracao.frete.proprietario = new Consignatario;
      this.declaracao.frete.proprietario.cpfCnpj = this.transportadorVeiculosComponent.CpfCnpjTransportador;
      this.declaracao.frete.proprietario.tipoPessoa = (this.declaracao.frete.proprietario.cpfCnpj != undefined
        && this.declaracao.frete.proprietario.cpfCnpj.length == 11) ? TipoPessoa.Fisica : TipoPessoa.Juridica;
      this.declaracao.frete.proprietario.nomeFantasia = this.transportadorVeiculosComponent.NomeRazaoSocialTransportador;
      this.declaracao.frete.proprietario.rntrc = this.transportadorVeiculosComponent.RNTRCTransportador.length == 9 && this.transportadorVeiculosComponent.RNTRCTransportador.substring(1, 0) == "0" ? this.transportadorVeiculosComponent.RNTRCTransportador.substring(9, 1) : this.transportadorVeiculosComponent.RNTRCTransportador;
      this.declaracao.frete.proprietario.endereco = new Endereco;
      this.declaracao.frete.proprietario.nomeRazaoSocial = this.transportadorVeiculosComponent.NomeRazaoSocialTransportador;
      this.declaracao.frete.proprietario.endereco.bairro = this.dadosPessoaisDTO.bairro;
      this.declaracao.frete.proprietario.endereco.cep = this.dadosPessoaisDTO.cep.replace("-", "");
      if (this.transportadorVeiculosComponent.cidade != undefined) {
        this.declaracao.frete.proprietario.endereco.codigoMunicipio = this.transportadorVeiculosComponent.cidade.idCidade;
      }
      this.declaracao.frete.proprietario.endereco.complemento = this.dadosPessoaisDTO.complemento;
      this.declaracao.frete.proprietario.endereco.email = this.dadosPessoaisDTO.email;
      this.declaracao.frete.proprietario.endereco.logradouro = this.dadosPessoaisDTO.logradouro;
      this.declaracao.frete.proprietario.endereco.numero = this.dadosPessoaisDTO.numero;
      this.declaracao.frete.proprietario.endereco.telefone = this.dadosPessoaisDTO.telefone;
      // this.declaracao.contratante.tipoPessoa = 
      this.declaracao.veiculos = this.transportadorVeiculosComponent.veiculoList;
      this.executarValidacoesTransportador();
    }

    else {
      this.toastr.error('Por favor, preencha a aba Transportador/Veículos.', Mensagens.OOPS);
      this.valid = false;
      throw new DOMException();
    }
  }

  private populaConsignatario() {
    if (this.consignatarioComponent != undefined) {
      this.dadosPessoaisDTO = this.consignatarioComponent.consignatarioForm.value;
      if (this.dadosPessoaisDTO.cpfCnpj.toString() == "" || this.dadosPessoaisDTO.nome.toString() == "") {
        return;
      }
      this.declaracao.consignatario = new Consignatario;
      this.declaracao.consignatario.cpfCnpj = this.dadosPessoaisDTO.cpfCnpj;
      this.declaracao.consignatario.tipoPessoa = (this.declaracao.consignatario.cpfCnpj != undefined
        && this.declaracao.consignatario.cpfCnpj.length == 11) ? TipoPessoa.Fisica : TipoPessoa.Juridica;
      this.declaracao.consignatario.nomeFantasia = this.dadosPessoaisDTO.nomeFantasia;
      //this.declaracao.consignatario.rntrc = this.dadosPessoaisDTO.rntrc;
      this.declaracao.consignatario.endereco = new Endereco;
      this.declaracao.consignatario.nomeRazaoSocial = this.dadosPessoaisDTO.nome;
      this.declaracao.consignatario.endereco.bairro = this.dadosPessoaisDTO.bairro;
      this.declaracao.consignatario.endereco.cep = this.dadosPessoaisDTO.cep.replace("-", "");
      if (this.consignatarioComponent.cidade != undefined) {
        this.declaracao.consignatario.endereco.codigoMunicipio = this.consignatarioComponent.cidade.idCidade;
      }
      this.declaracao.consignatario.endereco.complemento = this.dadosPessoaisDTO.complemento;
      this.declaracao.consignatario.endereco.email = this.dadosPessoaisDTO.email;
      this.declaracao.consignatario.endereco.logradouro = this.dadosPessoaisDTO.logradouro;
      this.declaracao.consignatario.endereco.numero = this.dadosPessoaisDTO.numero;
      this.declaracao.consignatario.endereco.telefone = this.dadosPessoaisDTO.telefone;
      this.executarValidacoesConsignatario();
    }
  }

  private populaDestinatario() {
    if (this.destinatarioComponent != undefined && this.destinatarioComponent.destinatarioForm.dirty) {
      this.dadosPessoaisDTO = this.destinatarioComponent.destinatarioForm.value;
      this.declaracao.destinatario = new Consignatario;
      this.declaracao.destinatario.cpfCnpj = this.dadosPessoaisDTO.cpfCnpj;
      this.declaracao.destinatario.tipoPessoa = (this.declaracao.destinatario.cpfCnpj != undefined
        && this.declaracao.destinatario.cpfCnpj.length == 11) ? TipoPessoa.Fisica : TipoPessoa.Juridica;
      this.declaracao.destinatario.nomeFantasia = this.dadosPessoaisDTO.nomeFantasia;
      //this.declaracao.destinatario.rntrc = this.dadosPessoaisDTO.rntrc;
      this.declaracao.destinatario.endereco = new Endereco;
      this.declaracao.destinatario.nomeRazaoSocial = this.dadosPessoaisDTO.nome;
      this.declaracao.destinatario.endereco.bairro = this.dadosPessoaisDTO.bairro;
      this.declaracao.destinatario.endereco.cep = this.dadosPessoaisDTO.cep.replace("-", "");
      if (this.destinatarioComponent.cidade != undefined) {
        this.declaracao.destinatario.endereco.codigoMunicipio = this.destinatarioComponent.cidade.idCidade;
      }
      this.declaracao.destinatario.endereco.complemento = this.dadosPessoaisDTO.complemento;
      this.declaracao.destinatario.endereco.email = this.dadosPessoaisDTO.email;
      this.declaracao.destinatario.endereco.logradouro = this.dadosPessoaisDTO.logradouro;
      this.declaracao.destinatario.endereco.numero = this.dadosPessoaisDTO.numero;
      this.declaracao.destinatario.endereco.telefone = this.dadosPessoaisDTO.telefone;
      this.executarValidacoesDestinatario();
    }
    else {
      if (Number(TipoViagem.Padrao) == this.declaracao.frete.tipoViagem) {
        this.toastr.error(Mensagens.PREENCHA_ABA_DESTINATARIO, Mensagens.OOPS);
        this.valid = false;
      }
    }
  }

  private populaRemetente() {
    if (this.remetenteComponent != undefined) {
      this.dadosPessoaisDTO = this.remetenteComponent.remetenteForm.value;
      this.declaracao.remetente = new Consignatario;
      this.declaracao.remetente.cpfCnpj = this.dadosPessoaisDTO.cpfCnpj;
      this.declaracao.remetente.tipoPessoa = (this.declaracao.remetente.cpfCnpj != undefined
        && this.declaracao.remetente.cpfCnpj.length == 11) ? TipoPessoa.Fisica : TipoPessoa.Juridica;
      this.declaracao.remetente.nomeFantasia = this.dadosPessoaisDTO.nomeFantasia;
      //this.declaracao.remetente.rntrc = this.dadosPessoaisDTO.rntrc;
      this.declaracao.remetente.endereco = new Endereco;
      this.declaracao.remetente.nomeRazaoSocial = this.dadosPessoaisDTO.nome;
      this.declaracao.remetente.endereco.bairro = this.dadosPessoaisDTO.bairro;
      this.declaracao.remetente.endereco.cep = this.dadosPessoaisDTO.cep.replace("-", "");
      if (this.remetenteComponent.cidade != undefined) {
        this.declaracao.remetente.endereco.codigoMunicipio = this.remetenteComponent.cidade.idCidade;
      }
      this.declaracao.remetente.endereco.complemento = this.dadosPessoaisDTO.complemento;
      this.declaracao.remetente.endereco.email = this.dadosPessoaisDTO.email;
      this.declaracao.remetente.endereco.logradouro = this.dadosPessoaisDTO.logradouro;
      this.declaracao.remetente.endereco.numero = this.dadosPessoaisDTO.numero;
      this.declaracao.remetente.endereco.telefone = this.dadosPessoaisDTO.telefone;
      this.executarValidacoesRemetente();
    }
    else {
      if (Number(TipoViagem.Padrao) == this.declaracao.frete.tipoViagem) {
        this.toastr.error('Por favor, preencha a aba Remetente.', Mensagens.OOPS);
        this.valid = false;
      }
    }
  }

  private populaContratante() {
    if (this.contratanteComponent != undefined) {
      this.dadosPessoaisDTO = this.contratanteComponent.contratanteForm.value;
      this.declaracao.contratante = new Consignatario;
      this.declaracao.contratante.cpfCnpj = this.contratanteComponent.CpfCnpjInteressado;
      this.declaracao.contratante.tipoPessoa = (this.declaracao.contratante.cpfCnpj != undefined
        && this.declaracao.contratante.cpfCnpj.length == 11) ? TipoPessoa.Fisica : TipoPessoa.Juridica;
      this.declaracao.contratante.nomeFantasia = this.dadosPessoaisDTO.nomeFantasia;
      this.declaracao.contratante.nomeRazaoSocial = this.dadosPessoaisDTO.nome;

      if (this.dadosPessoaisDTO.rntrc && this.dadosPessoaisDTO.rntrc != "") {
        this.declaracao.contratante.rntrc = this.declaracao.frete.tipoViagem != 1 ? this.dadosPessoaisDTO.rntrc : null;
      }

      this.declaracao.contratante.endereco = new Endereco;
      this.declaracao.contratante.endereco.bairro = this.dadosPessoaisDTO.bairro;
      this.declaracao.contratante.endereco.cep = this.dadosPessoaisDTO.cep.replace("-", "");
      if (this.contratanteComponent.cidade != undefined) {
        this.declaracao.contratante.endereco.codigoMunicipio = this.contratanteComponent.cidade.idCidade;
      }
      this.declaracao.contratante.endereco.complemento = this.dadosPessoaisDTO.complemento;
      this.declaracao.contratante.endereco.email = this.dadosPessoaisDTO.email;
      this.declaracao.contratante.endereco.logradouro = this.dadosPessoaisDTO.logradouro;
      this.declaracao.contratante.endereco.numero = this.dadosPessoaisDTO.numero;
      this.declaracao.contratante.endereco.telefone = this.dadosPessoaisDTO.telefone;
      this.executarValidacoesContratante()
    }
    else {
      this.toastr.error('Por favor, preencha a aba Contratante.', Mensagens.OOPS);
      this.valid = false;
    }
  }

  private populaPagamento() {
    if (this.pagamentoComponent != undefined && this.pagamentoComponent.pagamentoForm.dirty) {
      this.pagamentoDTO = this.pagamentoComponent.pagamentoForm.value;

      this.declaracao.pagamento = new Pagamento();
      this.declaracao.pagamento.parcelas = [];

      const parcela = new Parcela();
      parcela.codigoParcela = "1";
      parcela.valorParcela = this.viagemDTO.valorFrete;
      //parcela.vencimento = this.viagemComponent.dataFim; // Opcional: definir data de vencimento

      // Adicionar a parcela ao array
      this.declaracao.pagamento.parcelas.push(parcela);

      this.declaracao.pagamento.formaPagamento = this.pagamentoDTO.formaPagamento; 

      if (this.pagamentoComponent.banco) {
        this.declaracao.pagamento.agenciaPagamento = this.pagamentoDTO.agencia;
        this.declaracao.pagamento.bancoPagamento = this.pagamentoComponent.banco.codigo;
        this.declaracao.pagamento.contaPagamento = this.pagamentoDTO.conta;
        this.declaracao.pagamento.infoPagamento = this.pagamentoDTO.cpfCnpjConta;
      }

      this.executarValidacoesPagamento();
    } else {
      this.toastr.error('Por favor, preencha a aba Pagamento.', Mensagens.OOPS);
      this.valid = false;
    }
  }

  private populaMotorista() {
    if (this.motoristaComponent != undefined && this.motoristaComponent.motoristaForm.dirty) {
      this.motoristaDTO = this.motoristaComponent.motoristaForm.value;
      this.declaracao.frete.motorista = new Motorista;
      this.declaracao.frete.motorista.nome = this.motoristaDTO.nome;
      this.declaracao.frete.motorista.cpfCnpj = this.motoristaDTO.cpf;
      this.declaracao.frete.motorista.numeroCNH = this.motoristaDTO.cnh;
      this.executarValidacoesMotorista();
    }
  }

  private populaFrete() {
    if (this.viagemComponent != undefined && this.viagemComponent.viagemForm.dirty) {
      this.viagemDTO = this.viagemComponent.viagemForm.value;
      this.declaracao.frete = new Frete;
      if (TipoViagem.Padrao == this.viagemComponent.tipoViagem) {
        this.declaracao.frete.dataInicioFrete = this.viagemComponent.dataInicio;

        
        if (this.viagemComponent.cidadeOrigem && this.viagemComponent.cidadeOrigem.idCidade) {
          this.declaracao.frete.codigoMunicipioOrigem = this.viagemComponent.cidadeOrigem.idCidade;
        } else if (this.viagemComponent.codigoIBGECidadeOrigemSelect && this.viagemComponent.codigoIBGECidadeOrigemSelect.idCidade) {
          this.declaracao.frete.codigoMunicipioOrigem = this.viagemComponent.codigoIBGECidadeOrigemSelect.idCidade;
        }

        if (this.viagemComponent.cidadeDestino && this.viagemComponent.cidadeDestino.idCidade) {
          this.declaracao.frete.codigoMunicipioDestino = this.viagemComponent.cidadeDestino.idCidade;
        } else if (this.viagemComponent.codigoIBGECidadeSelect && this.viagemComponent.codigoIBGECidadeSelect.idCidade) {
          this.declaracao.frete.codigoMunicipioDestino = this.viagemComponent.codigoIBGECidadeSelect.idCidade;
        }

        if (this.viagemComponent.naturezaCargaSelect != undefined) {
          this.declaracao.frete.codigoNaturezaCarga = this.viagemComponent.naturezaCargaSelect.codigo;
        }

        this.declaracao.frete.pesoCarga = this.viagemDTO.pesoCarga;
      }

      this.declaracao.frete.tipoViagem = Number(this.viagemComponent.tipoViagem);
      this.declaracao.frete.dataTerminoFrete = this.viagemComponent.dataFim;


      var checkDestinacaoComercial: CheckDestinacaoComercial;
      checkDestinacaoComercial = this.viagemDTO.destinacaoComercial;
      this.declaracao.frete.destinacaoComercial = checkDestinacaoComercial.check;

      this.declaracao.valores = new Valores();
      this.declaracao.valores.valorCombustivel = this.viagemDTO.valorCombustivel;
      this.declaracao.valores.valorFrete = this.viagemDTO.valorFrete;
      this.declaracao.valores.valorTarifas = this.viagemDTO.valorTotalTarifas;
      this.declaracao.valores.valorDespesas = this.viagemDTO.valorDespesas;
      this.declaracao.valores.totalImposto = this.viagemDTO.totalImposto;
      this.declaracao.valores.totalPegadio = this.viagemDTO.totalPedagio;
      this.declaracao.valores.quantidadeTarifas = this.viagemDTO.quantidadeTarifas;
      this.executarValidacoesFrete();
    }
    else {
      this.valid = false;
      this.toastr.error('Por favor, preencha a aba Viagem.', Mensagens.OOPS);
    }
  }

  executarValidacoesMotorista() {

    if (this.declaracao.frete.motorista.nome == null || this.declaracao.frete.motorista.nome.toString() == "") {
      this.valid = false;
      this.toastr.error('Por favor, informe o nome do motorista.', Mensagens.OOPS);
    }
    if (this.declaracao.frete.motorista.cpfCnpj == null || this.declaracao.frete.motorista.cpfCnpj.toString() == "") {
      this.valid = false;
      this.toastr.error('Por favor, informe o CPF ou CNPJ do motorista.', Mensagens.OOPS);
    }
    if (this.declaracao.frete.motorista.numeroCNH == null || this.declaracao.frete.motorista.numeroCNH.toString() == "") {
      this.valid = false;
      this.toastr.error('Por favor, informe a CNH do motorista.', Mensagens.OOPS);
    }

  }
  executarValidacoesFrete() {
    if (TipoViagem.Padrao == this.viagemComponent.tipoViagem && (this.declaracao.frete.codigoNaturezaCarga == null || this.declaracao.frete.codigoNaturezaCarga == undefined)) {
      this.valid = false;
      this.toastr.error('Por favor, informe a natureza da carga.', Mensagens.OOPS);
    }
    if (TipoViagem.Padrao == this.viagemComponent.tipoViagem && (this.declaracao.frete.pesoCarga == null || this.declaracao.frete.pesoCarga.toString() == "")) {
      this.valid = false;
      this.toastr.error('Por favor, informe o peso da carga.', Mensagens.OOPS);
    }
    if (this.declaracao.valores.valorFrete == null || this.declaracao.valores.valorFrete.toString() == "") {
      this.valid = false;
      this.toastr.error('Por favor, informe o valor do frete.', Mensagens.OOPS);
    }
    if (this.declaracao.valores.quantidadeTarifas == null || this.declaracao.valores.quantidadeTarifas.toString() == "") {
      this.valid = false;
      this.toastr.error('Por favor, informe a quantidade de tarifas.', Mensagens.OOPS);
    }
    if (this.declaracao.valores.valorTarifas == null || this.declaracao.valores.valorTarifas.toString() == "") {
      this.declaracao.valores.valorTarifas = 0;
    }
    if (this.declaracao.valores.totalImposto == null || this.declaracao.valores.totalImposto.toString() == "") {
      this.declaracao.valores.totalImposto = 0;
    }

  }

  executarValidacoesPagamento() {
    if (this.declaracao.pagamento.formaPagamento.toString() == "") {
      this.valid = false;
      this.toastr.error('Por favor, informe a forma de pagamento.', Mensagens.OOPS);
    }
    if (this.declaracao.pagamento.infoPagamento.toString() == "") {
      this.valid = false;
      this.toastr.error('Por favor, informe o CPF/CNPJ da conta.', Mensagens.OOPS);
    }
    if (this.declaracao.pagamento.agenciaPagamento.toString() == "") {
      this.valid = false;
      this.toastr.error('Por favor, informe a agência para pagamento.', Mensagens.OOPS);
    }
    if (this.declaracao.pagamento.contaPagamento.toString() == "") {
      this.valid = false;
      this.toastr.error('Por favor, informe a conta para pagamento.', Mensagens.OOPS);
    }
    if (!this.declaracao.pagamento.bancoPagamento || this.declaracao.pagamento.bancoPagamento.toString() == "") {
      this.valid = false;
      this.toastr.error('Por favor, informe o banco.', Mensagens.OOPS);
    }
  }

  executarValidacoesTransportador() {
    if (this.declaracao.frete.proprietario.cpfCnpj.toString() == "") {
      this.toastr.error('Por favor, informe o CPF/CNPJ do transportador.', Mensagens.OOPS);
      this.valid = false;
    }
    if (this.declaracao.frete.proprietario.nomeRazaoSocial.toString() == "") {
      this.toastr.error('Por favor, informe a razão social do transportador.', Mensagens.OOPS);
      this.valid = false;
    }
    if (this.declaracao.frete.proprietario.endereco.cep.toString() == "") {
      this.toastr.error('Por favor, informe o CEP do transportador.', Mensagens.OOPS);
      this.valid = false;
    }
    if (this.declaracao.frete.proprietario.endereco.logradouro.toString() == "") {
      this.toastr.error('Por favor, informe o logradouro do transportador.', Mensagens.OOPS);
      this.valid = false;
    }
    if (this.declaracao.frete.proprietario.endereco.codigoMunicipio == undefined || this.declaracao.frete.proprietario.endereco.codigoMunicipio.toString() == "") {
      this.toastr.error('Por favor, informe o município do transportador.', Mensagens.OOPS);
      this.valid = false;
    }
  }

  executarValidacoesContratante() {
    if (this.declaracao.contratante.cpfCnpj.toString() == "") {
      this.toastr.error('Por favor, informe o CPF/CNPJ do contratante.', Mensagens.OOPS);
      this.valid = false;
    }

    if (TipoViagem.Padrao != this.viagemComponent.tipoViagem && (!this.declaracao.contratante.rntrc || this.declaracao.contratante.rntrc == "")) {
      this.toastr.error('Por favor, informe o RNTRC do contratante.', Mensagens.OOPS);
      this.valid = false;
    }

    if (this.declaracao.contratante.nomeRazaoSocial.toString() == "") {
      this.toastr.error('Por favor, informe a razão social do contratante.', Mensagens.OOPS);
      this.valid = false;
    }
    if (this.declaracao.contratante.endereco.cep.toString() == "") {
      this.toastr.error('Por favor, informe o CEP do contratante.', Mensagens.OOPS);
      this.valid = false;
    }
    if (this.declaracao.contratante.endereco.logradouro.toString() == "") {
      this.toastr.error('Por favor, informe o logradouro do contratante.', Mensagens.OOPS);
      this.valid = false;
    }
    if (this.declaracao.contratante.endereco.codigoMunicipio == undefined || this.declaracao.contratante.endereco.codigoMunicipio.toString() == "") {
      this.toastr.error('Por favor, informe o município do contratante.', Mensagens.OOPS);
      this.valid = false;
    }
  }

  executarValidacoesRemetente() {
    if (TipoViagem.TAC_Agregado == this.viagemComponent.tipoViagem) {
      return;
    }
    if (this.declaracao.remetente.cpfCnpj.toString() == "") {
      this.toastr.error('Por favor, informe o CPF/CNPJ do remetente.', Mensagens.OOPS);
      this.valid = false;
    }
    if (this.declaracao.remetente.nomeRazaoSocial.toString() == "") {
      this.toastr.error('Por favor, informe a razão social do remetente.', Mensagens.OOPS);
      this.valid = false;
    }
    if (this.declaracao.remetente.endereco.cep.toString() == "") {
      this.toastr.error('Por favor, informe o CEP do remetente.', Mensagens.OOPS);
      this.valid = false;
    }
    if (this.declaracao.remetente.endereco.logradouro.toString() == "") {
      this.toastr.error('Por favor, informe o logradouro do remetente.', Mensagens.OOPS);
      this.valid = false;
    }
    if (this.declaracao.remetente.endereco.codigoMunicipio == undefined || this.declaracao.remetente.endereco.codigoMunicipio.toString() == "") {
      this.toastr.error('Por favor, informe o município do remetente.', Mensagens.OOPS);
      this.valid = false;
    }
  }

  executarValidacoesDestinatario() {
    if (TipoViagem.TAC_Agregado == this.viagemComponent.tipoViagem) {
      return;
    }
    if (this.declaracao.destinatario.cpfCnpj.toString() == "") {
      this.toastr.error('Por favor, informe o CPF/CNPJ do destinatario.', Mensagens.OOPS);
      this.valid = false;
    }
    if (this.declaracao.destinatario.nomeRazaoSocial.toString() == "") {
      this.toastr.error('Por favor, informe a razão social do destinatario.', Mensagens.OOPS);
      this.valid = false;
    }
    if (this.declaracao.destinatario.endereco.cep.toString() == "") {
      this.toastr.error('Por favor, informe o CEP do destinatario.', Mensagens.OOPS);
      this.valid = false;
    }
    if (this.declaracao.destinatario.endereco.logradouro.toString() == "") {
      this.toastr.error('Por favor, informe o logradouro do destinatario.', Mensagens.OOPS);
      this.valid = false;
    }
    if (this.declaracao.destinatario.endereco.codigoMunicipio == undefined || this.declaracao.destinatario.endereco.codigoMunicipio.toString() == "") {
      this.toastr.error('Por favor, informe o município do destinatario.', Mensagens.OOPS);
      this.valid = false;
    }
  }

  executarValidacoesConsignatario() {
    if (TipoViagem.TAC_Agregado == this.viagemComponent.tipoViagem) {
      return;
    }
    if (this.declaracao.consignatario.cpfCnpj != "" || this.declaracao.consignatario.nomeRazaoSocial != "") {
      if (this.declaracao.consignatario.cpfCnpj.toString() == "") {
        this.toastr.error('Por favor, informe o CPF/CNPJ do consignatário.', Mensagens.OOPS);
        this.valid = false;
      }
      if (this.declaracao.consignatario.nomeRazaoSocial.toString() == "") {
        this.toastr.error('Por favor, informe a razão social do consignatário.', Mensagens.OOPS);
        this.valid = false;
      }
      if (this.declaracao.consignatario.endereco.cep.toString() == "") {
        this.toastr.error('Por favor, informe o CEP do consignatário.', Mensagens.OOPS);
        this.valid = false;
      }
      if (this.declaracao.consignatario.endereco.logradouro.toString() == "") {
        this.toastr.error('Por favor, informe o logradouro do consignatário.', Mensagens.OOPS);
        this.valid = false;
      }
      if (this.declaracao.consignatario.endereco.codigoMunicipio == undefined || this.declaracao.consignatario.endereco.codigoMunicipio.toString() == "") {
        this.toastr.error('Por favor, informe o município do consignatário.', Mensagens.OOPS);
        this.valid = false;
      }
    }
  }

  onError(serviceReturn) {
    this.errors = Object.assign([], serviceReturn.error.errors);
    this.toastr.error("Não foi possível realizar a declaração", Mensagens.OOPS)
  }

  onSaveComplete(result): void {
    if (result.sucesso) {
      this.router.navigate(['consulta-ciot',
        result.ciot, result.senhaAlteracao,
        this.motoristaComponent.motoristaForm.value.cpf,
        this.contratanteComponent.CpfCnpjInteressado,
        this.viagemComponent.viagemForm.value.codigoIBGECidadeOrigemSelect != "" ? this.viagemComponent.viagemForm.value.codigoIBGECidadeOrigemSelect.idCidade : "",
        this.viagemComponent.viagemForm.value.codigoIBGECidadeSelect != "" ? this.viagemComponent.viagemForm.value.codigoIBGECidadeSelect.idCidade : "", true], { skipLocationChange: true });
    } else {
      if (result.excecao != null) {
        let msgSplited = result.excecao.mensagem.split("\r\n");
        if (msgSplited != null && msgSplited != undefined && msgSplited.length > 1) {
          msgSplited.pop();
          msgSplited.forEach(msgSplited => {
            this.toastr.error(msgSplited, Mensagens.OOPS);
          });
        } else {
          this.toastr.error(result.excecao.mensagem, Mensagens.OOPS);
        }
      } else {
        this.toastr.error(result.Mensagem, Mensagens.OOPS);
      }
    }
  }

  voltar() {
    this.router.navigate(['/'], { skipLocationChange: true });
  }
}

export class CheckDestinacaoComercial {
  name: string;
  id: number;
  check: boolean;
}

using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using AutoMapper.QueryableExtensions;
using Newtonsoft.Json;
using SistemaInfo.BBC.Application.Helpers;
using SistemaInfo.BBC.Application.Interface.CentroCusto;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.CentroCusto;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.BBC.Domain.Models.CentroCusto.Commands;
using SistemaInfo.BBC.Domain.Models.CentroCusto.Repository;
using SistemaInfo.BBC.Domain.Models.Filial.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.Services.CentroCusto
{
    public class CentroCustoAppService : AppService<Domain.Models.CentroCusto.CentroCusto,
        ICentroCustoReadRepository, ICentroCustoWriteRepository>, ICentroCustoAppService
    {
        private readonly IFilialReadRepository _filialReadRepository;
        public CentroCustoAppService(
            IAppEngine engine,
            ICentroCustoReadRepository readRepository,
            ICentroCustoWriteRepository writeRepository,
            IFilialReadRepository filialReadRepository) : base(
            engine, readRepository, writeRepository)
        {
            _filialReadRepository = filialReadRepository;
        }

        public ConsultarGridCentroCustoResponse ConsultarGridCentroCusto(int take, int page, OrderFilters orderFilters,
            List<QueryFilters> filters)
        {
            var lCentroCusto = Repository.Query.GetAll();
            
            var filtroUsuarioEmpresaFilter = filters.Find(q => q.Campo == "EmpresaId")?.Valor;
            
            //Usuario x empresa
            if (filtroUsuarioEmpresaFilter != null)
            {
                lCentroCusto = Repository.Query.GetAll().Where(f => f.EmpresaId == filtroUsuarioEmpresaFilter.ToInt());
                filters.Remove(filters.Find(q => q.Campo == "EmpresaId"));
            }

            var lFiltraFilial = Convert.ToBoolean(filters.FirstOrDefault(x => x.Campo == "FiltraFilial") != null? filters.FirstOrDefault(x => x.Campo == "FiltraFilial")?.Valor : "false" );
            if (lFiltraFilial)
            {

                var lListaFiliaids = filters.FirstOrDefault(x => x.Campo == "ListaFiliaid");
                var filiaisId = new List<int>();

                if (lListaFiliaids != null)
                {
                    filiaisId = JsonConvert.DeserializeObject<List<int>>(lListaFiliaids.Valor);
                }

                if (filiaisId?.Count > 0)
                {
                    lCentroCusto = lCentroCusto.Where(x => filiaisId.Contains(x.FilialId.Value));

                    filters.Remove(filters.Find(q => q.Campo == "ListaFiliaid"));
                }
                else
                {
                    filters.Remove(filters.Find(q => q.Campo == "ListaFiliaid"));
                }

                if (filiaisId.Count == 0)
                {
                    var filtraCentroCustoSemFilial = filters.Find(q => q.Campo == "filtraCentroCustoSemFilial");

                    if (filtraCentroCustoSemFilial?.Valor == null)
                    {
                        lCentroCusto = lCentroCusto.Where(x => x.FilialId == null);
                    }

                    filters.Remove(filters.Find(q => q.Campo == "filtraCentroCustoSemFilial"));
                }

            }

            lCentroCusto = lCentroCusto.AplicarFiltrosDinamicos(filters);
            lCentroCusto = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                ? lCentroCusto.OrderByDescending(o => o.Id)
                : lCentroCusto.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            var lCount = lCentroCusto.Count();
            var retorno = lCentroCusto.Skip((page - 1) * take)
                .Take(take).ProjectTo<ConsultarGridCentroCusto>(Engine.Mapper.ConfigurationProvider).ToList();

            return new ConsultarGridCentroCustoResponse()
            {
                items = retorno,
                totalItems = lCount
            };
        }

        public CentroCustoResponse ConsultarPorId(int idCentroCusto)
        {
            try
            {
                new LogHelper().LogOperationStart("ConsultarPorId");
                return Mapper.Map<CentroCustoResponse>(Repository.Query.ConsultarPorId(idCentroCusto));
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar ConsultarPorId");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("ConsultarPorId");
            }
        }

        
        public async Task<RespPadrao> Save(CentroCustoRequest lCentroCustoReq, bool integracao)
        {
            try
            {
                var command = Mapper.Map<CentroCustoSalvarComRetornoCommand>(lCentroCustoReq);
                var retorno = await Engine.CommandBus.SendCommandAsync<Domain.Models.CentroCusto.CentroCusto>(command);
                
                char[] caracteresIndesejados = { '<', '>', '$', '\'' };
                bool contemCaracterIndesejado = false;

                foreach (char caractere in lCentroCustoReq.Descricao)
                {
                    if (Array.IndexOf(caracteresIndesejados, caractere) != -1)
                    {
                        contemCaracterIndesejado = true;
                        break; 
                    }
                }

                if (contemCaracterIndesejado)
                    return new RespPadrao(false, "A descrição do centro de custo contém caracteres indesejados");
                    
                
                return new RespPadrao
                {
                    id = retorno.Id,
                    sucesso = true,
                    mensagem = "Registro salvo com sucesso!"
                };
            }
            catch (Exception e)
            {
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = e.Message
                };
            }
        }

        public async Task AlterarStatus(CentroCustoStatusRequest lCentroCustoStatus)
        {
            try
            {
                new LogHelper().LogOperationStart("AlterarStatus");
                await Engine.CommandBus.SendCommandAsync(
                    Mapper.Map<CentroCustoAlterarStatusCommand>(lCentroCustoStatus));
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar AlterarStatus");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("AlterarStatus");
            }
        }

        public async Task<RespPadrao> AlterarVinculoFilial(
            CentroCustoAlterarVinculoFilialRequest lCentroCustoAlterarVinculo)
        {
            try
            {
                new LogHelper().LogOperationStart("AlterarVinculoFilial");
                if (lCentroCustoAlterarVinculo.FilialId == null)
                {
                    var retorno =
                        Engine.CommandBus.SendCommandAsync(
                            Mapper.Map<CentroCustoAlterarVinculoFilialCommand>(lCentroCustoAlterarVinculo));

                    return new RespPadrao
                    {
                        id = retorno.Id,
                        sucesso = true,
                        mensagem = "Operação realizada com sucesso!"
                    };
                }
                else
                {
                    var lCentroCusto =
                        await Repository.Query.FirstOrDefaultAsync(x => x.Id == lCentroCustoAlterarVinculo.id);
                    var lFilial =
                        await _filialReadRepository.FirstOrDefaultAsync(x =>
                            x.Id == lCentroCustoAlterarVinculo.FilialId);

                    if (lCentroCusto.EmpresaId != lFilial.EmpresaId)
                    {
                        return new RespPadrao()
                        {
                            sucesso = false,
                            mensagem =
                                "Empresa informada no cadastro do Centro de Custo é diferente da empresa informada no cadastro da Filial"
                        };
                    }

                    var retorno =
                        Engine.CommandBus.SendCommandAsync(
                            Mapper.Map<CentroCustoAlterarVinculoFilialCommand>(lCentroCustoAlterarVinculo));

                    return new RespPadrao
                    {
                        id = retorno.Id,
                        sucesso = true,
                        mensagem = "Operação realizada com sucesso!"
                    };
                }
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar AlterarVinculoFilial");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("AlterarVinculoFilial");
            }
        }
    }
}


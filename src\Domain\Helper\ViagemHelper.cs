﻿using System;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Domain.Models.Transacao;

namespace SistemaInfo.BBC.Domain.Helper;

public static class ViagemHelper
{
    
    public static string GetTransactionCode(this string jsonRespostaDock)
    {
        if (string.IsNullOrEmpty(jsonRespostaDock))
        {
            return null;
        }

        try
        {
            var json = JObject.Parse(jsonRespostaDock);
            var transactionCodeToken = json["transactionCode"];
            if (transactionCodeToken != null)
            {
                return transactionCodeToken.Value<string>();
            }

            var dataToken = json["data"];
            return dataToken is { HasValues: true } ? dataToken["TransactionCode"]?.Value<string>() : null;
        }
        catch (JsonException ex)
        {
            Console.WriteLine($"Erro ao deserializar JsonRespostaDock para TransactionCode: {ex.Message}");
            return null;
        }
    }
    
    public static string GetDocumentoOrigem(this Transacao transacao)
    {
        //transacao do proprietário
        if (transacao.Valor == transacao.PagamentoEvento.Valor)
        {
            return transacao.PagamentoEvento?.Empresa?.Cnpj;
        }

        //transacao do motorista
        return transacao.Valor == transacao.PagamentoEvento?.ValorTransferenciaMotorista 
            ? transacao.PagamentoEvento?.Viagem?.PortadorProprietario?.CpfCnpj 
            : null;
    }
    
    public static string GetDocumentoDestino(this Transacao transacao)
        {
            if (transacao.FormaPagamento == FormaPagamentoEvento.Pix)
            {
                return !string.IsNullOrEmpty(transacao.PagamentoEvento?.RecebedorAutorizado)
                    ? transacao.PagamentoEvento?.RecebedorAutorizado
                    : transacao.PagamentoEvento?.Viagem?.PortadorProprietario?.CpfCnpj;
            }

            // Transação de retenção - extrair CPF/CNPJ da conta de retenção do campo Description
            if ((int)transacao.FormaPagamento == 6) // RetencaoAntecipacao
            {
                // Para transações de retenção, o CPF/CNPJ da conta de retenção é armazenado
                // no campo Description da transação durante a criação
                if (!string.IsNullOrEmpty(transacao.Description))
                {
                    try
                    {
                        // O Description contém um JSON com nationalRegistration da conta de retenção
                        var descriptionObj = Newtonsoft.Json.JsonConvert.DeserializeObject<dynamic>(transacao.Description);
                        var nationalRegistration = descriptionObj?.nationalRegistration?.ToString();

                        if (!string.IsNullOrEmpty(nationalRegistration))
                        {
                            return nationalRegistration;
                        }
                    }
                    catch
                    {
                        // Em caso de erro no parse do JSON, usar fallback
                    }
                }

                // Fallback: usar CPF/CNPJ do proprietário
                return transacao.PagamentoEvento?.Viagem?.PortadorProprietario?.CpfCnpj;
            }

            //transacao do proprietário
            if (transacao.Valor == transacao.PagamentoEvento.Valor)
                return transacao.PagamentoEvento.Viagem?.PortadorProprietario?.CpfCnpj;


            //transacao do motorista
            return transacao.Valor == transacao.PagamentoEvento.ValorTransferenciaMotorista
                ? transacao.PagamentoEvento.Viagem?.PortadorMotorista?.CpfCnpj
                : null;
        }
}
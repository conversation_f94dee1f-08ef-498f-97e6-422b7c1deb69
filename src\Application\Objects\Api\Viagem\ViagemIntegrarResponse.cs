using System.Collections.Generic;
using System.Linq;
using AutoMapper;
using Newtonsoft.Json;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.Objects.Api.Viagem
{
    public class ViagemIntegrarResponse
    {
        public ViagemIntegrarResponse(bool sucesso, string mensagem)
        {
            Sucesso = sucesso;
            Mensagem = mensagem;
        }

        public ViagemIntegrarResponse()
        {
        }
        
        public ViagemIntegrarResponse(string mensagem) : this()
        {
            Mensagem = mensagem;
        }

        public ViagemIntegrarResponse(bool sucesso, int viagemId, int? statusViagem, string mensagem, PagamentoViagemResponse pagamento) : this()
        {
            Sucesso = sucesso;
            ViagemId = viagemId;
            StatusViagem = statusViagem;
            Mensagem = mensagem;
            Pagamento = pagamento;
        }

        public ViagemIntegrarResponse(int viagemId, string mensagem) : this()
        {
            ViagemId = viagemId;
            Mensagem = mensagem;
        }

        public ViagemIntegrarResponse(bool sucesso, string mensagem, Domain.Models.PagamentoEvento.PagamentoEvento pagamentoEvento) : this()
        {
            Sucesso = sucesso;
            Mensagem = mensagem;

            if (pagamentoEvento != null)
            {
                ViagemId = pagamentoEvento.ViagemId;
                ViagemExternoId = pagamentoEvento.Viagem?.ViagemExternoId;
                StatusViagem = pagamentoEvento.Viagem?.Status?.ToInt();

                Pagamento = new PagamentoViagemResponse
                {
                    PagamentoEventoId = pagamentoEvento.Id,
                    PagamentoExternoId = pagamentoEvento.PagamentoExternoId,
                    ValorParcela = pagamentoEvento.Valor,
                    ValorMotorista = pagamentoEvento.ValorTransferenciaMotorista,
                    StatusPagamento = pagamentoEvento.Status?.ToInt(),
                    FormaPagamento = pagamentoEvento.FormaPagamento?.ToInt(),
                    CódTransacao = pagamentoEvento.CodigoTransacao,
                    Mensagem = mensagem,
                    Transacoes = pagamentoEvento.Transacao?
                        .Where(t => t.Tipo != Domain.Enum.Tipo.Tarifas)
                        .Select(t => Mapper.Map<PagamentoViagemTransacaoResponse>(t))
                        .ToList()
                };
            }
        }

        public bool Sucesso { get; set; }
        public int ViagemId { get; set; }
        public int? ViagemExternoId { get; set; }
        public int? StatusViagem { get; set; }
        public string Mensagem { get; set; }
        public PagamentoViagemResponse Pagamento { get; set; }
        public CiotResponse Ciot { get; set; }
    }
    
    public class PagamentoViagemResponse
    {
        public int? PagamentoEventoId { get; set; }
        public int? PagamentoExternoId { get; set; }
        public decimal? ValorParcela { get; set; }
        public decimal? ValorMotorista { get; set; }
        public int? StatusPagamento { get; set; }
        public string CódTransacao { get; set; }
        public int? FormaPagamento { get; set; }
        public string Mensagem { get; set; }
        [JsonIgnore] public List<PagamentoViagemTransacaoResponse> Transacoes { get; set; }
    }
    
    public class CiotResponse
    {

    }

    public class PagamentoViagemTransacaoResponse
    {
        public int Id { get; set; }
        public int Status { get; set; }
        public string CodigoTransacaoCancelamento { get; set; }
        public string CodigoTransacao { get; set; }
        public string DocumentoDestino { get; set; }
        public string DocumentoOrigem { get; set; }
        public int? IdContaOrigem { get; set; }
        public int? IdContaDestino { get; set; }
        public decimal? Valor { get; set; }
        public string Agencia { get; set; }
        public string Conta { get; set; }
        public string CodigoBanco { get; set; }
        public StatusPagamento StatusEnum { get; set; }
    }
}
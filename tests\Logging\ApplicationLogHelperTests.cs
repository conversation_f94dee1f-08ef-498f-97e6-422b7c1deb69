using System;
using Moq;
using NLog;
using SistemaInfo.BBC.Application.Helpers;
using Xunit;

namespace SistemaInfo.BBC.Tests.Logging
{
    public class ApplicationLogHelperTests
    {
        [Fact]
        public void LogOperationStart_ShouldLogCorrectMessage()
        {
            // Arrange
            string operationName = "TestOperation";
            string details = "Test details";

            // Act & Assert - Verificação indireta através do comportamento
            // Como LogManager é estático e GetLoggerFromCaller é privado, não podemos mocá-los diretamente
            // Este teste verifica se o método não lança exceções
            var exception = Record.Exception(() => LogHelper.LogOperationStart(operationName, details));
            
            // Assert
            Assert.Null(exception);
        }

        [Fact]
        public void LogOperationStart_WithoutDetails_ShouldLogCorrectMessage()
        {
            // Arrange
            string operationName = "TestOperation";

            // Act & Assert
            var exception = Record.Exception(() => LogHelper.LogOperationStart(operationName));
            
            // Assert
            Assert.Null(exception);
        }

        [Fact]
        public void LogOperationEnd_ShouldLogCorrectMessage()
        {
            // Arrange
            string operationName = "TestOperation";
            string details = "Test details";

            // Act & Assert
            var exception = Record.Exception(() => LogHelper.LogOperationEnd(operationName, details));
            
            // Assert
            Assert.Null(exception);
        }

        [Fact]
        public void LogOperationEnd_WithoutDetails_ShouldLogCorrectMessage()
        {
            // Arrange
            string operationName = "TestOperation";

            // Act & Assert
            var exception = Record.Exception(() => LogHelper.LogOperationEnd(operationName));
            
            // Assert
            Assert.Null(exception);
        }

        [Fact]
        public void Info_ShouldLogCorrectMessage()
        {
            // Arrange
            string message = "Test info message";

            // Act & Assert
            var exception = Record.Exception(() => LogHelper.Info(message));
            
            // Assert
            Assert.Null(exception);
        }

        [Fact]
        public void Info_WithObject_ShouldLogCorrectMessage()
        {
            // Arrange
            string message = "Test info message";
            var testObject = new { Id = 1, Name = "Test" };

            // Act & Assert
            var exception = Record.Exception(() => LogHelper.Info(message, testObject));
            
            // Assert
            Assert.Null(exception);
        }

        [Fact]
        public void Error_ShouldLogCorrectMessage()
        {
            // Arrange
            string message = "Test error message";

            // Act & Assert
            var exception = Record.Exception(() => LogHelper.Error(message));
            
            // Assert
            Assert.Null(exception);
        }

        [Fact]
        public void Error_WithException_ShouldLogCorrectMessage()
        {
            // Arrange
            var testException = new Exception("Test exception");
            string message = "Test error message";

            // Act & Assert
            var exception = Record.Exception(() => LogHelper.Error(testException, message));
            
            // Assert
            Assert.Null(exception);
        }

        [Fact]
        public void Error_WithExceptionAndObject_ShouldLogCorrectMessage()
        {
            // Arrange
            var testException = new Exception("Test exception");
            string message = "Test error message";
            var testObject = new { Id = 1, Name = "Test" };

            // Act & Assert
            var exception = Record.Exception(() => LogHelper.Error(testException, message, testObject));
            
            // Assert
            Assert.Null(exception);
        }

        [Fact]
        public void Error_WithObject_ShouldLogCorrectMessage()
        {
            // Arrange
            string message = "Test error message";
            var testObject = new { Id = 1, Name = "Test" };

            // Act & Assert
            var exception = Record.Exception(() => LogHelper.Error(message, testObject));
            
            // Assert
            Assert.Null(exception);
        }
    }
}

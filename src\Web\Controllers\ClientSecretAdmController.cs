using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using SistemaInfo.BBC.Application.Interface.ClientSecretAdm;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.ClientSecretAdm;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Web.Attributes;
using SistemaInfo.BBC.Web.Controllers.Base;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;

namespace SistemaInfo.BBC.Web.Controllers
{
    /// <summary>
    /// Controller Web dos Client Secret Adm
    /// </summary>
    [Route("ClientSecretAdm")]
    public class ClientSecretAdmController : WebControllerBase<IClientSecretAdmAppService>
    {
        /// <summary>
        /// Injeções de dependência do Controller Web dos Client Secret Adm
        /// </summary>
        /// <param name="engine"></param>
        /// <param name="AppService"></param>
        public ClientSecretAdmController(IAppEngine engine, IClientSecretAdmAppService AppService) : base(engine, AppService)
        {
        }

        /// <summary>
        /// Consultar Client Secret Adm por ID
        /// </summary>
        /// <param name="idClientSecretAdm"></param>
        /// <returns></returns>
         [HttpGet("ConsultarPorId")]
         [Menu(new[] { EMenus.ClientSecretAdm })]
         public async Task<JsonResult> ConsultarPorId(int idAuthClientSecret) =>
             ResponseBase.Responder(await AppService.ConsultarPorId(idAuthClientSecret));
         
        /// <summary>
        /// Consultar Grid de Client Secret Adm
        /// </summary>
        /// <param name="aGridRequest"></param>
        /// <returns></returns>
        [HttpPost("ConsultarGridClientSecretAdm")]
        [Menu(new[] { EMenus.ClientSecretAdm })]
        public async Task<JsonResult> ConsultarGridClientSecretAdm([FromBody] BaseGridRequest aGridRequest) =>
            ResponseBase.Responder(await AppService.ConsultarGridClientSecretAdm(aGridRequest.Take, aGridRequest.Page,
                aGridRequest.Order, aGridRequest.Filters));
        
        /// <summary>
        /// Alterar Status do Client Secret Adm
        /// </summary>
        /// <param name="aClientSecretAdmStatus"></param>
        /// <returns></returns>
        [HttpPost("AlterarStatus")]
        [Menu(new[] { EMenus.ClientSecretAdm })]
        public async Task<JsonResult> AlterarStatus([FromBody] ClientSecretAdmAlterarStatusRequest aClientSecretAdmStatus) =>
            ResponseBase.Responder(await AppService.AlterarStatus(aClientSecretAdmStatus));

        /// <summary>
        /// Salvar Client Secret Adm
        /// </summary>
        /// <param name="aModel"></param>
        /// <returns></returns>
        [HttpPost("SaveClientSecretAdm")]
        [Menu(new[] { EMenus.ClientSecretAdm })]
        public async Task<JsonResult> SaveClientSecretAdm([FromBody] ClientSecretAdmRequest aModel) =>
            ResponseBase.Responder(await AppService.SaveClientSecretAdm(aModel));
    }
}

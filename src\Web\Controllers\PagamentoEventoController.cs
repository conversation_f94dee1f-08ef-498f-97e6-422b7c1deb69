﻿using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using SistemaInfo.BBC.Application.Interface.PagamentoEvento;
using SistemaInfo.BBC.Application.Interface.Pix;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.PagamentoEvento;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Web.Attributes;
using SistemaInfo.BBC.Web.Controllers.Base;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;

namespace SistemaInfo.BBC.Web.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [Route("PagamentoEvento")]
    public class PagamentoEventoController : WebControllerBase<IPagamentoEventoAppSerivce>
    {
        
        private readonly IPixAppService _pixAppService;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="engine"></param>
        /// <param name="appService"></param>
        /// <param name="pixAppService"></param>
        public PagamentoEventoController(IAppEngine engine, 
            IPagamentoEventoAppSerivce appService,
            IPixAppService pixAppService) : base(engine, appService)
        {
            _pixAppService = pixAppService;
        }
        /// <summary>
        /// Comsulta de grid de painel de pagamentoevento
        /// </summary>
        /// <param name="eventoRequest"></param>
        /// <returns></returns>
        [HttpPost("ConsultarGridPagamentoEvento")]
        [Menu(new[] { EMenus.PainelPagamento })]
        public async Task<JsonResult> ConsultarGridPagamentoEvento([FromBody]ConsultaGridPagamentoEventoRequest eventoRequest)
        {
            try
            {
                var consultarGridPagamentoEvento = await AppService.ConsultarGridPagamentoEvento(eventoRequest);
                return ResponseBase.ResponderSucesso(consultarGridPagamentoEvento);
            }
            catch (Exception e)
            {
                return ResponseBase.ResponderErro("Não foi possível realizar a operação. Mensagem: " + e.Message);
            }
          
        }

        /// <summary>
        /// Edita a ocorrência de um pagamento evento
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("SalvarOcorrencia")]
        [Menu(new[] { EMenus.PainelPagamento })]
        public async Task<JsonResult> SalvarOcorrencia([FromBody] SalvarOcorrenciaRequest request)
        {
            var lResponse = await AppService.SalvarOcorrencia(request);
            return lResponse.sucesso
                ? ResponseBase.ResponderSucesso(lResponse.data)
                : ResponseBase.ResponderErro(lResponse.mensagem);
        }
        
        /// <summary>
        /// Edita a ocorrência de um pagamento evento
        /// </summary>
        /// <param name="idPagamentoEvento"></param>
        /// <returns></returns>
        [HttpGet("ConsultarPorId")]
        [Menu(new[] { EMenus.PainelPagamento })]
        public async Task<JsonResult> ConsultarPorId([FromQuery] int idPagamentoEvento)
        {
            var lResponse = await AppService.ConsultarPorId(idPagamentoEvento);
            return lResponse.sucesso
                ? ResponseBase.ResponderSucesso(lResponse.data)
                : ResponseBase.ResponderErro(lResponse.mensagem);
        }
        
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="idPagamentoEvento"></param>
        /// <returns></returns>
        [HttpPost("ConsultarPixManual")]
        [Menu(new[] { EMenus.PainelPagamento })]
        public async Task<JsonResult> ConsultarPixManual([FromBody] int idPagamentoEvento)
        {
            var lRetorno = await _pixAppService.ConsultarPixManual(idPagamentoEvento);
            return lRetorno.sucesso 
                ? ResponseBase.Responder(true, lRetorno.mensagem, lRetorno.data) 
                : ResponseBase.ResponderErro(lRetorno.mensagem);
        }

    }
}
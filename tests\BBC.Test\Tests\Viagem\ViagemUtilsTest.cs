using System;
using BBC.Test.Tests.Viagem.Fixture;
using SistemaInfo.BBC.Application.Utils;
using Xunit;

namespace BBC.Test.Tests.Viagem
{
    [Collection(nameof(ViagemCollection))]
    public class ViagemUtilsTest
    {
        private readonly ViagemFixture _fixture;

        public ViagemUtilsTest(ViagemFixture fixture)
        {
            _fixture = fixture;
        }

        #region Testes para valores nulos, vazios e whitespace

        [Fact(DisplayName = "RemoveCaracteresEpeciaisCpfCnpjChavePix - String nula - Deve retornar nulo")]
        [Trait("ViagemUtils", "RemoveCaracteresEpeciaisCpfCnpjChavePix")]
        public void RemoveCaracteresEpeciaisCpfCnpjChavePix_StringNula_DeveRetornarNulo()
        {
            // Arrange && Act
            var resultado = ((string)null).RemoveCaracteresEpeciaisCpfCnpjChavePix();

            // Assert
            Assert.Null(resultado);
        }

        [Fact(DisplayName = "RemoveCaracteresEpeciaisCpfCnpjChavePix - String vazia - Deve retornar string vazia")]
        [Trait("ViagemUtils", "RemoveCaracteresEpeciaisCpfCnpjChavePix")]
        public void RemoveCaracteresEpeciaisCpfCnpjChavePix_StringVazia_DeveRetornarStringVazia()
        {
            // Arrange
            const string valor = "";

            // Act
            var resultado = valor.RemoveCaracteresEpeciaisCpfCnpjChavePix();

            // Assert
            Assert.Equal("", resultado);
        }

        [Fact(DisplayName = "RemoveCaracteresEpeciaisCpfCnpjChavePix - String com espaços - Deve retornar string com espaços")]
        [Trait("ViagemUtils", "RemoveCaracteresEpeciaisCpfCnpjChavePix")]
        public void RemoveCaracteresEpeciaisCpfCnpjChavePix_StringComEspacos_DeveRetornarStringComEspacos()
        {
            // Arrange
            const string valor = "   ";

            // Act
            var resultado = valor.RemoveCaracteresEpeciaisCpfCnpjChavePix();

            // Assert
            Assert.Equal("   ", resultado);
        }

        #endregion

        #region Testes para CPF (11 dígitos com pontos)

        [Fact(DisplayName = "RemoveCaracteresEpeciaisCpfCnpjChavePix - CPF formatado válido - Deve remover caracteres especiais")]
        [Trait("ViagemUtils", "RemoveCaracteresEpeciaisCpfCnpjChavePix")]
        public void RemoveCaracteresEpeciaisCpfCnpjChavePix_CpfFormatadoValido_DeveRemoverCaracteresEspeciais()
        {
            // Arrange
            const string cpfFormatado = "123.456.789-01";

            // Act
            var resultado = cpfFormatado.RemoveCaracteresEpeciaisCpfCnpjChavePix();

            // Assert
            Assert.Equal("12345678901", resultado);
        }

        [Fact(DisplayName = "RemoveCaracteresEpeciaisCpfCnpjChavePix - CPF com pontos sem hífen - Deve remover pontos")]
        [Trait("ViagemUtils", "RemoveCaracteresEpeciaisCpfCnpjChavePix")]
        public void RemoveCaracteresEpeciaisCpfCnpjChavePix_CpfComPontosSemHifen_DeveRemoverPontos()
        {
            // Arrange
            const string cpfComPontos = "123.456.78901";

            // Act
            var resultado = cpfComPontos.RemoveCaracteresEpeciaisCpfCnpjChavePix();

            // Assert
            Assert.Equal("12345678901", resultado);
        }

        [Theory(DisplayName = "RemoveCaracteresEpeciaisCpfCnpjChavePix - Diferentes formatos de CPF - Deve remover caracteres especiais")]
        [Trait("ViagemUtils", "RemoveCaracteresEpeciaisCpfCnpjChavePix")]
        [InlineData("111.222.333-44", "11122233344")]
        [InlineData("000.000.000-00", "00000000000")]
        [InlineData("999.888.777-66", "99988877766")]
        [InlineData("123.456.789.01", "12345678901")] // Com ponto no final
        public void RemoveCaracteresEpeciaisCpfCnpjChavePix_DiferentesFormatosCpf_DeveRemoverCaracteresEspeciais(
            string cpfFormatado, string cpfEsperado)
        {
            // Act
            var resultado = cpfFormatado.RemoveCaracteresEpeciaisCpfCnpjChavePix();

            // Assert
            Assert.Equal(cpfEsperado, resultado);
        }

        #endregion

        #region Testes para CNPJ (14 dígitos com pontos e barra)

        [Fact(DisplayName = "RemoveCaracteresEpeciaisCpfCnpjChavePix - CNPJ formatado válido - Deve remover caracteres especiais")]
        [Trait("ViagemUtils", "RemoveCaracteresEpeciaisCpfCnpjChavePix")]
        public void RemoveCaracteresEpeciaisCpfCnpjChavePix_CnpjFormatadoValido_DeveRemoverCaracteresEspeciais()
        {
            // Arrange
            const string cnpjFormatado = "12.345.678/0001-90";

            // Act
            var resultado = cnpjFormatado.RemoveCaracteresEpeciaisCpfCnpjChavePix();

            // Assert
            Assert.Equal("12345678000190", resultado);
        }

        [Fact(DisplayName = "RemoveCaracteresEpeciaisCpfCnpjChavePix - CNPJ com pontos sem barra - Deve remover pontos")]
        [Trait("ViagemUtils", "RemoveCaracteresEpeciaisCpfCnpjChavePix")]
        public void RemoveCaracteresEpeciaisCpfCnpjChavePix_CnpjComPontosSemBarra_DeveRemoverPontos()
        {
            // Arrange
            const string cnpjComPontos = "12.345.678000190";

            // Act
            var resultado = cnpjComPontos.RemoveCaracteresEpeciaisCpfCnpjChavePix();

            // Assert
            Assert.Equal("12345678000190", resultado);
        }

        [Fact(DisplayName = "RemoveCaracteresEpeciaisCpfCnpjChavePix - CNPJ com barra sem pontos - Deve remover barra")]
        [Trait("ViagemUtils", "RemoveCaracteresEpeciaisCpfCnpjChavePix")]
        public void RemoveCaracteresEpeciaisCpfCnpjChavePix_CnpjComBarraSemPontos_DeveRemoverBarra()
        {
            // Arrange
            const string cnpjComBarra = "12345678/000190";

            // Act
            var resultado = cnpjComBarra.RemoveCaracteresEpeciaisCpfCnpjChavePix();

            // Assert
            Assert.Equal("12345678000190", resultado);
        }

        [Theory(DisplayName = "RemoveCaracteresEpeciaisCpfCnpjChavePix - Diferentes formatos de CNPJ - Deve remover caracteres especiais")]
        [Trait("ViagemUtils", "RemoveCaracteresEpeciaisCpfCnpjChavePix")]
        [InlineData("11.222.333/0001-44", "11222333000144")]
        [InlineData("00.000.000/0000-00", "00000000000000")]
        [InlineData("99.888.777/0001-66", "99888777000166")]
        [InlineData("12.345.678/0001-90", "12345678000190")]
        public void RemoveCaracteresEpeciaisCpfCnpjChavePix_DiferentesFormatosCnpj_DeveRemoverCaracteresEspeciais(
            string cnpjFormatado, string cnpjEsperado)
        {
            // Act
            var resultado = cnpjFormatado.RemoveCaracteresEpeciaisCpfCnpjChavePix();

            // Assert
            Assert.Equal(cnpjEsperado, resultado);
        }

        #endregion

        #region Testes para chaves PIX que não devem ser alteradas

        [Fact(DisplayName = "RemoveCaracteresEpeciaisCpfCnpjChavePix - Chave PIX email - Deve retornar inalterada")]
        [Trait("ViagemUtils", "RemoveCaracteresEpeciaisCpfCnpjChavePix")]
        public void RemoveCaracteresEpeciaisCpfCnpjChavePix_ChavePixEmail_DeveRetornarInalterada()
        {
            // Arrange
            const string chavePixEmail = "<EMAIL>";

            // Act
            var resultado = chavePixEmail.RemoveCaracteresEpeciaisCpfCnpjChavePix();

            // Assert
            Assert.Equal("<EMAIL>", resultado);
        }

        [Fact(DisplayName = "RemoveCaracteresEpeciaisCpfCnpjChavePix - Chave PIX telefone - Deve retornar inalterada")]
        [Trait("ViagemUtils", "RemoveCaracteresEpeciaisCpfCnpjChavePix")]
        public void RemoveCaracteresEpeciaisCpfCnpjChavePix_ChavePixTelefone_DeveRetornarInalterada()
        {
            // Arrange
            const string chavePixTelefone = "+5511999887766";

            // Act
            var resultado = chavePixTelefone.RemoveCaracteresEpeciaisCpfCnpjChavePix();

            // Assert
            Assert.Equal("+5511999887766", resultado);
        }

        [Fact(DisplayName = "RemoveCaracteresEpeciaisCpfCnpjChavePix - Chave PIX aleatória - Deve retornar inalterada")]
        [Trait("ViagemUtils", "RemoveCaracteresEpeciaisCpfCnpjChavePix")]
        public void RemoveCaracteresEpeciaisCpfCnpjChavePix_ChavePixAleatoria_DeveRetornarInalterada()
        {
            // Arrange
            const string chavePixAleatoria = "a1b2c3d4-e5f6-7890-abcd-ef1234567890";

            // Act
            var resultado = chavePixAleatoria.RemoveCaracteresEpeciaisCpfCnpjChavePix();

            // Assert
            Assert.Equal("a1b2c3d4-e5f6-7890-abcd-ef1234567890", resultado);
        }

        #endregion

        #region Testes para casos especiais e edge cases

        [Fact(DisplayName = "RemoveCaracteresEpeciaisCpfCnpjChavePix - String com pontos mas sem 11 ou 14 dígitos - Deve retornar inalterada")]
        [Trait("ViagemUtils", "RemoveCaracteresEpeciaisCpfCnpjChavePix")]
        public void RemoveCaracteresEpeciaisCpfCnpjChavePix_StringComPontosSemTamanhoCorreto_DeveRetornarInalterada()
        {
            // Arrange
            const string valorComPontos = "123.456.78"; // Apenas 8 dígitos

            // Act
            var resultado = valorComPontos.RemoveCaracteresEpeciaisCpfCnpjChavePix();

            // Assert
            Assert.Equal("123.456.78", resultado);
        }

        [Fact(DisplayName = "RemoveCaracteresEpeciaisCpfCnpjChavePix - String com barra mas sem 11 ou 14 dígitos - Deve retornar inalterada")]
        [Trait("ViagemUtils", "RemoveCaracteresEpeciaisCpfCnpjChavePix")]
        public void RemoveCaracteresEpeciaisCpfCnpjChavePix_StringComBarraSemTamanhoCorreto_DeveRetornarInalterada()
        {
            // Arrange
            const string valorComBarra = "123/456"; // Apenas 6 dígitos

            // Act
            var resultado = valorComBarra.RemoveCaracteresEpeciaisCpfCnpjChavePix();

            // Assert
            Assert.Equal("123/456", resultado);
        }

        [Fact(DisplayName = "RemoveCaracteresEpeciaisCpfCnpjChavePix - CPF com letras - Deve remover apenas números")]
        [Trait("ViagemUtils", "RemoveCaracteresEpeciaisCpfCnpjChavePix")]
        public void RemoveCaracteresEpeciaisCpfCnpjChavePix_CpfComLetras_DeveRemoverApenasNumeros()
        {
            // Arrange
            const string cpfComLetras = "123.456.789-0A"; // 11 dígitos + letra

            // Act
            var resultado = cpfComLetras.RemoveCaracteresEpeciaisCpfCnpjChavePix();

            // Assert
            Assert.Equal("1234567890", resultado); // Remove a letra 'A'
        }

        [Fact(DisplayName = "RemoveCaracteresEpeciaisCpfCnpjChavePix - CNPJ com caracteres especiais extras - Deve remover apenas números")]
        [Trait("ViagemUtils", "RemoveCaracteresEpeciaisCpfCnpjChavePix")]
        public void RemoveCaracteresEpeciaisCpfCnpjChavePix_CnpjComCaracteresEspeciaisExtras_DeveRemoverApenasNumeros()
        {
            // Arrange
            const string cnpjComExtras = "12.345.678/0001-90#@"; // 14 dígitos + caracteres extras

            // Act
            var resultado = cnpjComExtras.RemoveCaracteresEpeciaisCpfCnpjChavePix();

            // Assert
            Assert.Equal("12345678000190", resultado);
        }

        [Theory(DisplayName = "RemoveCaracteresEpeciaisCpfCnpjChavePix - Strings sem pontos ou barras - Deve retornar inalteradas")]
        [Trait("ViagemUtils", "RemoveCaracteresEpeciaisCpfCnpjChavePix")]
        [InlineData("12345678901")] // CPF sem formatação
        [InlineData("12345678000190")] // CNPJ sem formatação
        [InlineData("abc123def456")] // String alfanumérica
        [InlineData("!@#$%^&*()")] // Apenas caracteres especiais
        public void RemoveCaracteresEpeciaisCpfCnpjChavePix_StringsSemPontosOuBarras_DeveRetornarInalteradas(string valor)
        {
            // Act
            var resultado = valor.RemoveCaracteresEpeciaisCpfCnpjChavePix();

            // Assert
            Assert.Equal(valor, resultado);
        }

        #endregion

        #region Testes de performance e casos extremos

        [Fact(DisplayName = "RemoveCaracteresEpeciaisCpfCnpjChavePix - String muito longa com pontos - Deve processar corretamente")]
        [Trait("ViagemUtils", "RemoveCaracteresEpeciaisCpfCnpjChavePix")]
        public void RemoveCaracteresEpeciaisCpfCnpjChavePix_StringMuitoLongaComPontos_DeveProcessarCorretamente()
        {
            // Arrange
            const string stringLonga = "123.456.789.012.345.678.901.234.567.890.123.456.789.012.345.678.901.234.567.890";

            // Act
            var resultado = stringLonga.RemoveCaracteresEpeciaisCpfCnpjChavePix();

            // Assert
            // Como tem mais de 14 dígitos, deve retornar inalterada
            Assert.Equal(stringLonga, resultado);
        }

        [Fact(DisplayName = "RemoveCaracteresEpeciaisCpfCnpjChavePix - String com caracteres Unicode - Deve processar corretamente")]
        [Trait("ViagemUtils", "RemoveCaracteresEpeciaisCpfCnpjChavePix")]
        public void RemoveCaracteresEpeciaisCpfCnpjChavePix_StringComCaracteresUnicode_DeveProcessarCorretamente()
        {
            // Arrange
            const string stringUnicode = "123.456.789-01ção"; // CPF com caracteres especiais

            // Act
            var resultado = stringUnicode.RemoveCaracteresEpeciaisCpfCnpjChavePix();

            // Assert
            Assert.Equal("12345678901", resultado); // Remove caracteres não numéricos
        }

        #endregion
    }
}

using System;
using BBC.Test.Tests.Viagem.Fixture;
using SistemaInfo.BBC.Application.Utils;
using Xunit;

namespace BBC.Test.Tests.Viagem
{
    [Collection(nameof(ViagemCollection))]
    public class ViagemUtilsTest
    {
        private readonly ViagemFixture _fixture;

        public ViagemUtilsTest(ViagemFixture fixture)
        {
            _fixture = fixture;
        }

        #region Testes para valores nulos, vazios e whitespace

        [Theory(DisplayName = "RemoveCaracteresEpeciaisCpfCnpjChavePix - Valores nulos/vazios/espaços - Deve retornar inalterados")]
        [Trait("ViagemUtils", "RemoveCaracteresEpeciaisCpfCnpjChavePix")]
        [InlineData(null, null)]
        [InlineData("", "")]
        [InlineData("   ", "   ")]
        [InlineData(" \t\n ", " \t\n ")]
        public void RemoveCaracteresEpeciaisCpfCnpjChavePix_ValoresNulosVaziosEspacos_DeveRetornarInalterados(
            string valor, string esperado)
        {
            // Act
            var resultado = valor?.RemoveCaracteresEpeciaisCpfCnpjChavePix() ?? null;

            // Assert
            Assert.Equal(esperado, resultado);
        }

        #endregion

        #region Testes para CPF (11 dígitos com pontos)

        [Theory(DisplayName = "RemoveCaracteresEpeciaisCpfCnpjChavePix - Diferentes formatos de CPF - Deve remover caracteres especiais")]
        [Trait("ViagemUtils", "RemoveCaracteresEpeciaisCpfCnpjChavePix")]
        [InlineData("123.456.789-01", "12345678901")] // CPF formatado padrão
        [InlineData("123.456.78901", "12345678901")] // CPF com pontos sem hífen
        [InlineData("111.222.333-44", "11122233344")] // Outro CPF formatado
        [InlineData("000.000.000-00", "00000000000")] // CPF com zeros
        [InlineData("999.888.777-66", "99988877766")] // CPF com números altos
        [InlineData("123.456.789.01", "12345678901")] // Com ponto no final
        [InlineData("12.345.678-90A", "1234567890")] // CPF com letra (remove letra)
        [InlineData("123.456.789-01#", "12345678901")] // CPF com caractere especial
        public void RemoveCaracteresEpeciaisCpfCnpjChavePix_DiferentesFormatosCpf_DeveRemoverCaracteresEspeciais(
            string cpfFormatado, string cpfEsperado)
        {
            // Act
            var resultado = cpfFormatado.RemoveCaracteresEpeciaisCpfCnpjChavePix();

            // Assert
            Assert.Equal(cpfEsperado, resultado);
        }

        #endregion

        #region Testes para CNPJ (14 dígitos com pontos e barra)

        [Theory(DisplayName = "RemoveCaracteresEpeciaisCpfCnpjChavePix - Diferentes formatos de CNPJ - Deve remover caracteres especiais")]
        [Trait("ViagemUtils", "RemoveCaracteresEpeciaisCpfCnpjChavePix")]
        [InlineData("12.345.678/0001-90", "12345678000190")] // CNPJ formatado padrão
        [InlineData("12.345.678000190", "12345678000190")] // CNPJ com pontos sem barra
        [InlineData("12345678/000190", "12345678000190")] // CNPJ com barra sem pontos
        [InlineData("11.222.333/0001-44", "11222333000144")] // Outro CNPJ formatado
        [InlineData("00.000.000/0000-00", "00000000000000")] // CNPJ com zeros
        [InlineData("99.888.777/0001-66", "99888777000166")] // CNPJ com números altos
        [InlineData("12.345.678/0001-90#@", "12345678000190")] // CNPJ com caracteres extras
        [InlineData("AB.345.678/0001-90", "34567800190")] // CNPJ com letras (remove letras)
        public void RemoveCaracteresEpeciaisCpfCnpjChavePix_DiferentesFormatosCnpj_DeveRemoverCaracteresEspeciais(
            string cnpjFormatado, string cnpjEsperado)
        {
            // Act
            var resultado = cnpjFormatado.RemoveCaracteresEpeciaisCpfCnpjChavePix();

            // Assert
            Assert.Equal(cnpjEsperado, resultado);
        }

        #endregion

        #region Testes para chaves PIX que não devem ser alteradas

        [Theory(DisplayName = "RemoveCaracteresEpeciaisCpfCnpjChavePix - Chaves PIX - Devem retornar inalteradas")]
        [Trait("ViagemUtils", "RemoveCaracteresEpeciaisCpfCnpjChavePix")]
        [InlineData("<EMAIL>")] // Email
        [InlineData("<EMAIL>")] // Outro email
        [InlineData("+5511999887766")] // Telefone
        [InlineData("+55119876543210")] // Telefone longo
        [InlineData("a1b2c3d4-e5f6-7890-abcd-ef1234567890")] // UUID
        [InlineData("12345678-1234-1234-1234-123456789012")] // Outro UUID
        [InlineData("<EMAIL>")] // Email com subdomínio
        [InlineData("+55 11 99988-7766")] // Telefone com espaços
        public void RemoveCaracteresEpeciaisCpfCnpjChavePix_ChavesPix_DevemRetornarInalteradas(string chavePix)
        {
            // Act
            var resultado = chavePix.RemoveCaracteresEpeciaisCpfCnpjChavePix();

            // Assert
            Assert.Equal(chavePix, resultado);
        }

        #endregion

        #region Testes para casos especiais e edge cases

        [Theory(DisplayName = "RemoveCaracteresEpeciaisCpfCnpjChavePix - Casos especiais - Comportamentos específicos")]
        [Trait("ViagemUtils", "RemoveCaracteresEpeciaisCpfCnpjChavePix")]
        [InlineData("123.456.78", "123.456.78")] // Pontos mas apenas 8 dígitos - inalterada
        [InlineData("123/456", "123/456")] // Barra mas apenas 6 dígitos - inalterada
        [InlineData("12345678901", "12345678901")] // CPF sem formatação - inalterada
        [InlineData("12345678000190", "12345678000190")] // CNPJ sem formatação - inalterada
        [InlineData("abc123def456", "abc123def456")] // String alfanumérica - inalterada
        [InlineData("!@#$%^&*()", "!@#$%^&*()")] // Apenas caracteres especiais - inalterada
        [InlineData("123.456.789.012.345", "123.456.789.012.345")] // Muitos pontos, mais de 14 dígitos - inalterada
        [InlineData("12/34/56/78/90", "12/34/56/78/90")] // Muitas barras, menos de 11 dígitos - inalterada
        public void RemoveCaracteresEpeciaisCpfCnpjChavePix_CasosEspeciais_ComportamentosEspecificos(
            string valor, string esperado)
        {
            // Act
            var resultado = valor.RemoveCaracteresEpeciaisCpfCnpjChavePix();

            // Assert
            Assert.Equal(esperado, resultado);
        }

        #endregion

        #region Testes de performance e casos extremos

        [Theory(DisplayName = "RemoveCaracteresEpeciaisCpfCnpjChavePix - Casos extremos - Deve processar corretamente")]
        [Trait("ViagemUtils", "RemoveCaracteresEpeciaisCpfCnpjChavePix")]
        [InlineData("123.456.789.012.345.678.901.234.567.890.123.456.789.012.345.678.901.234.567.890",
                   "123.456.789.012.345.678.901.234.567.890.123.456.789.012.345.678.901.234.567.890")] // String muito longa - inalterada
        [InlineData("123.456.789-01ção", "12345678901")] // CPF com caracteres Unicode - remove não numéricos
        [InlineData("12.345.678/0001-90àáâã", "12345678000190")] // CNPJ com acentos - remove não numéricos
        [InlineData("123.456.789-01🚀", "12345678901")] // CPF com emoji - remove não numéricos
        [InlineData("12.345.678/0001-90€£¥", "12345678000190")] // CNPJ com símbolos monetários - remove não numéricos
        public void RemoveCaracteresEpeciaisCpfCnpjChavePix_CasosExtremos_DeveProcessarCorretamente(
            string valor, string esperado)
        {
            // Act
            var resultado = valor.RemoveCaracteresEpeciaisCpfCnpjChavePix();

            // Assert
            Assert.Equal(esperado, resultado);
        }

        #endregion
    }
}

using SistemaInfo.BBC.Domain.Enum;
using Xunit;

namespace BBC.Test.Tests.StatusCadastroEnum.Fixture
{
    [CollectionDefinition(nameof(StatusCadastroEnumCollection))]
    public class StatusCadastroEnumCollection : ICollectionFixture<StatusCadastroEnumFixture>
    {
        
    }

    public class StatusCadastroEnumFixture : MockEngine
    {
        public StatusCadastro ObterStatusCadastroAtivo()
        {
            return StatusCadastro.Ativo;
        }

        public StatusCadastro ObterStatusCadastroBloqueado()
        {
            return StatusCadastro.Bloqueado;
        }

        public StatusCadastro ObterStatusCadastroPendente()
        {
            return StatusCadastro.PendentedeValidação;
        }

        public StatusCadastro[] ObterTodosStatusCadastro()
        {
            return new[]
            {
                StatusCadastro.Bloqueado,
                StatusCadastro.Ativo,
                StatusCadastro.PendentedeValidação
            };
        }

        public string[] ObterDescricoesStatusCadastro()
        {
            return new[]
            {
                "Bloqueado",
                "Ativo",
                "Pendente de validação"
            };
        }

        public int[] ObterValoresStatusCadastro()
        {
            return new[] { 0, 1, 2 };
        }
    }
}

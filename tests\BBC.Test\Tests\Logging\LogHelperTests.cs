using System;
using SistemaInfo.BBC.Application.Helpers;
using Xunit;

namespace BBC.Test.Tests.Logging
{
    public class LogHelperTests
    {
        [Fact]
        public void LogOperationStart_ShouldLogCorrectMessage()
        {
            var operationName = "TestOperation";
            var details = "Test details";
            var expectedMessage = $"--> Início da operação: {operationName} <-- Detalhes: {details}";

            // Act & Assert - Verificação indireta através do comportamento
            // Como LogManager é estático, não podemos mocá-lo diretamente
            // Este teste verifica se o método não lança exceções
            var exception = Record.Exception(() => new LogHelper().LogOperationStart(operationName, details));
            
            Assert.Null(exception);
        }

        [Fact]
        public void LogOperationStart_WithoutDetails_ShouldLogCorrectMessage()
        {
            var operationName = "TestOperation";
            var expectedMessage = $"--> Início da operação: {operationName} <--";
            
            var exception = Record.Exception(() => new LogHelper().LogOperationStart(operationName));
            
            Assert.Null(exception);
        }

        [Fact]
        public void LogOperationEnd_ShouldLogCorrectMessage()
        {
            var operationName = "TestOperation";
            var details = "Test details";
            var expectedMessage = $"--> Fim da operação: {operationName} <-- Detalhes: {details}";
            
            var exception = Record.Exception(() => new LogHelper().LogOperationEnd(operationName, details));
            
            Assert.Null(exception);
        }

        [Fact]
        public void LogOperationEnd_WithoutDetails_ShouldLogCorrectMessage()
        {
            var operationName = "TestOperation";
            var expectedMessage = $"--> Fim da operação: {operationName} <--";
            
            var exception = Record.Exception(() => new LogHelper().LogOperationEnd(operationName));
            
            Assert.Null(exception);
        }

        [Fact]
        public void Info_ShouldLogCorrectMessage()
        {
            var message = "Test info message";
            
            var exception = Record.Exception(() => new LogHelper().Info(message));
            
            Assert.Null(exception);
        }

        [Fact]
        public void Info_WithObject_ShouldLogCorrectMessage()
        {
            var message = "Test info message";
            var testObject = new { Id = 1, Name = "Test" };
            
            var exception = Record.Exception(() => new LogHelper().Info(message, testObject));
            
            Assert.Null(exception);
        }

        [Fact]
        public void Error_ShouldLogCorrectMessage()
        {
            var message = "Test error message";
            
            var exception = Record.Exception(() => new LogHelper().Error(message));

            Assert.Null(exception);
        }

        [Fact]
        public void Error_WithException_ShouldLogCorrectMessage()
        {
            var testException = new Exception("Test exception");
            var message = "Test error message";
            
            var exception = Record.Exception(() => new LogHelper().Error(testException, message));
            
            Assert.Null(exception);
        }

        [Fact]
        public void Error_WithObject_ShouldLogCorrectMessage()
        {
            var message = "Test error message";
            var testObject = new { Id = 1, Name = "Test" };
            
            var exception = Record.Exception(() => new LogHelper().Error(message, testObject));
            
            Assert.Null(exception);
        }
    }
}

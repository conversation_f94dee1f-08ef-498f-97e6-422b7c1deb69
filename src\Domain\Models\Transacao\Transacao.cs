using System;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Models;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Models.Validator;

namespace SistemaInfo.BBC.Domain.Models.Transacao
{
    public class Transacao : Entity<Transacao, int, NotImplementedEntityValidator<Transacao>>
    {
        public int IdPagamentoEvento { get; set; }
        public int? Destino { get; set; }
        public int Origem { get; set; }
        public string Agencia { get; set; }
        public string Conta { get; set; }
        public string CodigoBanco { get; set; }
        /// <summary>
        /// ETipoContaDock Corrente = 1, Poupanca = 2, Salario = 3
        /// </summary>
        public int? TipoConta { get; set; }
        public decimal Valor { get; set; }
        public string Descricao { get; set; }
        public string IdEndToEnd { get; set; }
        public DateTime DataCadastro { get; set; }
        public DateTime? DataCancelamento { get; set; }
        public DateTime? DataBaixa { get; set; }
        public DateTime? DataAlteracao { get; set; }
        public FormaPagamentoEvento FormaPagamento { get; set; }
        public Tipo? Tipo { get; set;  }
        public StatusPagamento Status { get; set; }
        public string Description { get; set; }
        public int? Qualificado { get; set; }

        #region Logs

        public string JsonEnvioDock { get; set; }
        public string JsonEnvioDockCancelamento { get; set; }
        public string JsonRespostaDock { get; set; }
        public string JsonRespostaDockCancelamento { get; set; }
        public int? ResponseCodeDockCancelamento { get; set; }
        public DateTime? DataRetornoDock { get; set; }
        public int ResponseCodeDock { get; set; }

        #endregion
        
        #region Propriedades de Navegacao
        
        public virtual PagamentoEvento.PagamentoEvento PagamentoEvento { get; set; }
        
        #endregion
    }
}
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using AutoMapper.QueryableExtensions;
using SistemaInfo.BBC.Application.Helpers;
using SistemaInfo.BBC.Application.Interface.NaturezaCarga;
using SistemaInfo.BBC.Application.Objects.Web.NaturezaCarga;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.BBC.Domain.Models.NaturezaCarga.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;

namespace SistemaInfo.BBC.Application.Services.NaturezaCarga
{
    public class NaturezaCargaAppService :
        AppService<Domain.Models.NaturezaCarga.NaturezaCarga, INaturezaCargaReadRepository,
            INaturezaCargaWriteRepository>, INaturezaCargaAppService
    {
        public NaturezaCargaAppService(IAppEngine engine, INaturezaCargaReadRepository readRepository,
            INaturezaCargaWriteRepository writeRepository) : base(engine, readRepository, writeRepository)
        {
        }

        public ConsultarGridNaturezaCargaResponse ConsultarGridNaturezaCarga(int take, int page,
            OrderFilters orderFilters, List<QueryFilters> filters)
        {
            try
            {
                new LogHelper().LogOperationStart("ConsultarGridNaturezaCarga");
                var lNaturezaCarga = Repository.Query.GetAll();

                var lCount = lNaturezaCarga.Count();

                lNaturezaCarga = lNaturezaCarga.AplicarFiltrosDinamicos(filters);
                lNaturezaCarga = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                    ? lNaturezaCarga.OrderByDescending(o => o.Id)
                    : lNaturezaCarga.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

                var retorno = lNaturezaCarga.Skip((page - 1) * take)
                    .Take(take)
                    .ProjectTo<ConsultarGridNaturezaCarga>(Engine.Mapper.ConfigurationProvider).ToList();

                return new ConsultarGridNaturezaCargaResponse
                {
                    items = retorno,
                    totalItems = lCount
                };
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar ConsultarGridNaturezaCarga");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("ConsultarGridNaturezaCarga");
            }
        }
    }
}
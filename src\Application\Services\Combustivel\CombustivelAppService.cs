using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using AutoMapper.QueryableExtensions;
using Microsoft.EntityFrameworkCore;
using NLog;
using SistemaInfo.BBC.Application.Helpers;
using SistemaInfo.BBC.Application.Interface.Combustivel;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.Combustivel;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.BBC.Domain.Models.Combustivel.Commands;
using SistemaInfo.BBC.Domain.Models.Combustivel.Repository;
using SistemaInfo.BBC.Domain.Models.PostoCombustivel.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.Services.Combustivel
{
    public class CombustivelAppService : AppService<Domain.Models.Combustivel.Combustivel, ICombustivelReadRepository, ICombustivelWriteRepository>, ICombustivelAppService
    {
        private readonly IPostoCombustivelReadRepository _postoCombustivelReadRepository;

        
        public CombustivelAppService(IAppEngine engine, ICombustivelReadRepository readRepository, ICombustivelWriteRepository writeRepository, 
            IPostoCombustivelReadRepository postoCombustivelReadRepository) 
            : base(engine, readRepository, writeRepository)
        {
            _postoCombustivelReadRepository = postoCombustivelReadRepository;
        }

        public ConsultarGridCombustivelResponse ConsultarGridCombustivel(int take, int page, OrderFilters orderFilters, List<QueryFilters> filters)
        {
            try
            {
                new LogHelper().LogOperationStart("ConsultarGridCombustivel");
            var lCombustivel = Repository.Query.GetAll();

            var combustiveisPosto = _postoCombustivelReadRepository.Where(x => x.PostoId == Engine.User.AdministradoraId).ToList();

            var lCount = lCombustivel.Count();
            
            lCombustivel = lCombustivel.AplicarFiltrosDinamicos(filters);
            lCombustivel = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                ? lCombustivel.OrderByDescending(o => o.Id)
                : lCombustivel.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            var retorno = lCombustivel.Skip((page - 1) * take)
                .Take(take)
                .ProjectTo<ConsultarGridCombustivel>(Engine.Mapper.ConfigurationProvider).ToList();
            
            foreach (var combustivelPosto in combustiveisPosto)
            {
                foreach (var combustivel in retorno.Where(combustivel => combustivel.Id == combustivelPosto.CombustivelId))
                {
                    combustivel.ValorBomba = combustivelPosto.ValorCombustivelBomba.ToDecimal();
                }
            }
            
            return new ConsultarGridCombustivelResponse
            {
                items = retorno,
                totalItems = lCount
            };
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar ConsultarGridCombustivel");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("ConsultarGridCombustivel");
            }        }
        
        public ConsultarGridCombustivelResponse ConsultarGridPostoCombustivel(int take, int page, OrderFilters orderFilters, List<QueryFilters> filters)
        {
            try
            {
                new LogHelper().LogOperationStart("ConsultarGridPostoCombustivel");
            var lCombustivel = Repository.Query.GetAll();

            var combustiveisPosto = _postoCombustivelReadRepository.Where(x => x.PostoId == Engine.User.AdministradoraId).ToList();

            var lCount = lCombustivel.Count();
            
            lCombustivel = lCombustivel.AplicarFiltrosDinamicos(filters);
            lCombustivel = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                ? lCombustivel.OrderByDescending(o => o.Id)
                : lCombustivel.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            var retorno = lCombustivel.Skip((page - 1) * take)
                .Take(take)
                .ProjectTo<ConsultarGridCombustivel>(Engine.Mapper.ConfigurationProvider).ToList();
            
            foreach (var combustivelPosto in combustiveisPosto)
            {
                foreach (var combustivel in retorno.Where(combustivel => combustivel.Id == combustivelPosto.CombustivelId))
                {
                    combustivel.ValorBomba = combustivelPosto.ValorCombustivelBomba.ToDecimal();
                    combustivel.ValorBbc = combustivelPosto.ValorCombustivelBBC.ToDecimal();
                }
            }
            
            return new ConsultarGridCombustivelResponse
            {
                items = retorno,
                totalItems = lCount
            };
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar ConsultarGridPostoCombustivel");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("ConsultarGridPostoCombustivel");
            }        }

        public CombustivelResponse ConsultarPorId(int idCombustivel)
        {
            try
            {
                new LogHelper().LogOperationStart("ConsultarPorId");
            return Mapper.Map<CombustivelResponse>(Repository.Query
                .FirstOrDefault(a => a.Id == idCombustivel));
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar ConsultarPorId");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("ConsultarPorId");
            }        }

        public List<CombustivelResponse> ConsultarCombustiveis()
        {
            try
            {
                new LogHelper().LogOperationStart("ConsultarCombustiveis");
                return Repository.Query.GetAll().ProjectTo<CombustivelResponse>(Engine.Mapper.ConfigurationProvider)
                    .ToList();
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar ConsultarCombustiveis");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("ConsultarCombustiveis");
            }
        }

        public async Task<RespPadrao> Save(CombustivelRequest request)
        {
            try
            {
                var command = Mapper.Map<CombustivelSalvarComRetornoCommand>(request);
                var retorno = await Engine.CommandBus.SendCommandAsync<Domain.Models.Combustivel.Combustivel>(command);
                return new RespPadrao(true, "Combustível salvo com sucesso.");
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return new RespPadrao(false, "Ocorreu um erro ao salvar o Combustível: " + e.Message);
            }
        }

        public async Task AlterarStatus(CombustivelStatusRequest lCombustivelStatus)
        {
            try
            {
                new LogHelper().LogOperationStart("AlterarStatus");
                await Engine.CommandBus.SendCommandAsync(
                    Mapper.Map<CombustivelAlterarStatusCommand>(lCombustivelStatus));
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar AlterarStatus");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("AlterarStatus");
            }
        }
    }
}

﻿using System;

namespace SistemaInfo.BBC.Domain.Models.UsuarioFrota.Base;

public class BaseUsuarioFrotaCommand
{
        public int Id { get; set; }
        public string CpfCnpj { get; set; }
        public string Nome { get; set; }
        public string Telefone { get; set; }
        public string Email { get; set; }
        public string Placa { get; set; }
        public string Cnh { get; set; }
        public string Celular { get; set; }
        public DateTime? DataEmissaoCNH { get; set; }
        public DateTime? DataVencimentoCNH { get; set; }
        
        public int? CarretaId { get; set; }
        public int? Carreta2Id { get; set; }
        public int? Carreta3Id { get; set; }
        public int? TipoEmpresaId { get; set; }
        public int? ControlaAbastecimentoCentroCusto { get; set; }
        public string Link { get; set; }
        public int? EmpresaIdFrota { get; set; }
        
        //campos adicionais api mobile
        public string SenhaApi { get; set; }
        public int QuantidadeErroSenha { get; set; } = 0;
        
        //Colunas adicionais para controle de acesso mobile
        public DateTime? DataCriacaoSenha { get; set; }
        public DateTime? DataUltimoAcesso { get; set; }
        public int? SenhaProvisoria { get; set; } = 0;
        
        public int? UsuarioDesbloqueioMobileId { get; set; }
}
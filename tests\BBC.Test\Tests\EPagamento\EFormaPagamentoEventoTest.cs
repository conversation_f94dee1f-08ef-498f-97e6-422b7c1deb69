﻿using BBC.Test.Tests.EPagamento.Fixture;
using SistemaInfo.BBC.Domain.Enum;
using Xunit;

namespace BBC.Test.Tests.EPagamento;

[Collection(nameof(EnumPagamentoCollection))]
public class EFormaPagamentoEventoTest
{
    private readonly EnumPagamentoFixture _fixture;

    public EFormaPagamentoEventoTest(EnumPagamentoFixture fixture)
    {
        _fixture = fixture;
    }
    
    [Theory]
    [InlineData(FormaPagamentoEvento.Deposito, 1)]
    [InlineData(FormaPagamentoEvento.Pix, 4)]
    public void StatusFormaPagamentoEvento_Enum_DeveRetornarValoresCorretos(FormaPagamentoEvento formaPagamentoEvento, int expectedValue)
    {
        // Act
        var actualValue = (int)formaPagamentoEvento;

        // Assert
        Assert.Equal(expectedValue, actualValue);
    }
    
}
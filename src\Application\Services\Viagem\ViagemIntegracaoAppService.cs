using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using NLog;
using SistemaInfo.BBC.Application.Interface.Operacoes;
using SistemaInfo.BBC.Application.Interface.Parametros;
using SistemaInfo.BBC.Application.Interface.Veiculo;
using SistemaInfo.BBC.Application.Interface.Viagem;
using SistemaInfo.BBC.Application.Objects.Api.Viagem;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.Portador;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Domain.External.CIOT.DTO;
using SistemaInfo.BBC.Domain.External.Conductor.DTO.Cartao;
using SistemaInfo.BBC.Domain.External.Conductor.DTO.Transferencia;
using SistemaInfo.BBC.Domain.External.Conductor.Interface;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.BBC.Domain.Models.Cidade.Repository;
using SistemaInfo.BBC.Domain.Models.DeclaracaoCiot.Exeptions;
using SistemaInfo.BBC.Domain.Models.Empresa.Repository;
using SistemaInfo.BBC.Domain.Models.Notificacao;
using SistemaInfo.BBC.Domain.Models.Notificacao.Commands;
using SistemaInfo.BBC.Domain.Models.PagamentoEvento.Commands;
using SistemaInfo.BBC.Domain.Models.PagamentoEvento.Repository;
using SistemaInfo.BBC.Domain.Models.Portador.Commands;
using SistemaInfo.BBC.Domain.Models.Portador.Repository;
using SistemaInfo.BBC.Domain.Models.Transacao.Repository;
using SistemaInfo.BBC.Domain.Models.Viagem.Commands;
using SistemaInfo.BBC.Domain.Models.Viagem.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.Services.Viagem
{
    /// <summary>
    /// Serviço de integração para viagem V2
    /// </summary>
    public class ViagemIntegracaoAppService :
        AppService<Domain.Models.Viagem.Viagem, IViagemReadRepository, IViagemWriteRepository>,
        IViagemIntegracaoAppService
    {
        private readonly IViagemAppService _viagemAppService;
        private readonly ICartaoRepository _cartaoRepository;
        private readonly IEmpresaReadRepository _empresaReadRepository;
        private readonly IPortadorReadRepository _portadorReadRepository;
        private readonly IPagamentoEventoReadRepository _pagamentoEventoReadRepository;
        private readonly IPagamentoEventoWriteRepository _pagamentoEventoWriteRepository;
        private readonly ITransferenciaRepository _transferenciaRepository;
        private readonly ITransacaoReadRepository _transacaoReadRepository;
        private readonly IParametrosAppService _parametrosAppService;
        private readonly IVeiculoAppService _veiculoAppService;
        private readonly ICidadeReadRepository _cidadeReadRepository;
        private readonly ILogger _logger;
        private readonly IOperacoesAppService _operacoesAppService;

        /// <summary>
        /// Construtor do serviço de integração de viagem
        /// </summary>
        /// <param name="engine">Engine da aplicação</param>
        /// <param name="viagemAppService">Serviço de viagem</param>
        public ViagemIntegracaoAppService(IAppEngine engine,
            IViagemReadRepository readRepository,
            IViagemWriteRepository writeRepository,
            IViagemAppService viagemAppService,
            IPagamentoEventoReadRepository pagamentoEventoReadRepository,
            IPortadorReadRepository portadorReadRepository,
            IParametrosAppService parametrosAppService,
            IVeiculoAppService veiculoAppService,
            ICartaoRepository cartaoRepository,
            ICidadeReadRepository cidadeReadRepository,
            IEmpresaReadRepository empresaReadRepository,
            IPagamentoEventoWriteRepository pagamentoEventoWriteRepository,
            ITransferenciaRepository transferenciaRepository,
            ITransacaoReadRepository transacaoReadRepository,
            IOperacoesAppService operacoesAppService) : base(engine, readRepository, writeRepository)
        {
            _viagemAppService = viagemAppService;
            _pagamentoEventoReadRepository = pagamentoEventoReadRepository;
            _portadorReadRepository = portadorReadRepository;
            _parametrosAppService = parametrosAppService;
            _veiculoAppService = veiculoAppService;
            _cidadeReadRepository = cidadeReadRepository;
            _pagamentoEventoWriteRepository = pagamentoEventoWriteRepository;
            _operacoesAppService = operacoesAppService;
            _cartaoRepository = cartaoRepository;
            _empresaReadRepository = empresaReadRepository;
            _transferenciaRepository = transferenciaRepository;
            _transacaoReadRepository = transacaoReadRepository;
            _logger = LogManager.GetCurrentClassLogger();
        }

        #region Cancelamento

        /*
         * Regras de Cancelamento de Viagem com CIOT
         * =========================================
         *
         * Viagem de CIOT Padrão
         * ---------------------
         * - CIOT declarado há mais de 24 horas:
         *     -> Cancela pagamentos e viagem. [mensagem 1]
         * - CIOT declarado há menos de 24 horas:
         *     -> Cancela pagamentos, viagem e CIOT. [mensagem 3]
         *
         * Viagem de CIOT Agregado
         * -----------------------
         * - CIOT declarado há mais de 24 horas:
         *     -> Apenas uma viagem vinculada: Não cancela viagem nem CIOT. [mensagem 5]
         *     -> Mais de uma viagem vinculada: Cancela pagamentos e viagem. [mensagem 4]
         * - CIOT declarado há menos de 24 horas:
         *     -> Apenas uma viagem vinculada: Cancela pagamentos, viagem e CIOT. [mensagem 3]
         *     -> Mais de uma viagem vinculada: Cancela pagamentos e viagem. [mensagem 4]
         *
         * Outras regras
         * -------------
         * - Viagem já está cancelada: [mensagem 2]
         * - Pagamento via Pix: Não pode cancelar nada. [mensagem 6]
         * - Erro nas transações: [mensagem 8]
         */

        public async Task<CancelamentoEventoViagemV2Response> Cancelar(
            CancelamentoEventoViagemV2Request cancelarEventoViagemRequest, int empresaId)
        {
            try
            {
                #region Mensagens de retorno

                var mensagens = new Dictionary<int, string>
                {
                    { 1, "Viagem cancelada com sucesso." },
                    { 2, "Viagem já cancelada." },
                    { 3, "Viagem e Ciot cancelado com sucesso." },
                    { 4, "Viagem cancelada com sucesso. O CIOT permanece ativo com outras viagens vinculadas." },
                    { 5, "Não é possível cancelar esta viagem, pois ela é a única vinculada a um CIOT declarado há mais de 24 horas. Para prosseguir com o cancelamento, é necessário manter pelo menos uma viagem vinculada. Você pode cadastrar uma nova viagem para o CIOT vigente e, em seguida, cancelar a atual." },
                    { 6, "Não é possível cancelar uma viagem que tenha pagamento Pix." },
                    { 7, "Cancelamento não realizado. Conta {contaOrigem} com saldo insuficiente." },
                    { 8, "Não foi possível cancelar a viagem." }
                };

                #endregion

                #region Validações e coleta de Viagem

                if (!cancelarEventoViagemRequest.ViagemExternoId.HasValue)
                    return ResponseErro("ViagemExternoId é um campo obrigatório!");

                if (empresaId == 0)
                    return ResponseErro("IdEmpresa é um campo obrigatório!");

                var viagem = await Repository.Query
                    .Include(x => x.PagamentoEvento)
                    .Include(x => x.PortadorProprietario)
                    .Include(x => x.Empresa)
                    .FirstOrDefaultAsync(x => x.ViagemExternoId == cancelarEventoViagemRequest.ViagemExternoId
                                         && x.EmpresaId == empresaId);

                if (viagem == null)
                    return ResponseErro("Viagem não localizada!");

                if (viagem.Status == StatusViagem.Cancelado)
                    return ResponseSucesso(mensagem: mensagens[2]);

                #endregion

                #region Determinação de horas e tipo de CIOT

                var horasDataDeclaracaoCiot = CalcularHorasDesdeDeclaracao(viagem.DataDeclaracaoCiot);
                var ciotDeclaradoMais24Horas = horasDataDeclaracaoCiot > 24;
                int? tipoCiot = null;

                if (viagem.TipoCiot != null)
                    tipoCiot = viagem.TipoCiot.GetHashCode();

                #endregion

                CancelamentoPagamentoEventoViagemResponse cancelamentoPagamentosResponse;
                
                #region Cancelamento de Viagem sem CIOT

                if (viagem.TipoCiot == null)
                {
                    cancelamentoPagamentosResponse =
                        await CancelamentoPagamentoEventoViagem(cancelarEventoViagemRequest, viagem);

                    if (!cancelamentoPagamentosResponse.Sucesso)
                        return ResponseErro(mensagens[6]);

                    if (!TodosPagamentosCancelados(cancelamentoPagamentosResponse, out var erro))
                        return erro;

                    viagem.Status = StatusViagem.Cancelado;
                    viagem.DataCancelamento = DateTime.Now;

                    await Repository.Command.SaveChangesAsync();
                    return ResponseSucesso(mensagem: mensagens[1], viagem);
                }
                
                #endregion

                #region Cancelamento de Viagem com CIOT Padrão

                if (tipoCiot == TipoCiot.Padrão.GetHashCode())
                {
                    cancelamentoPagamentosResponse =
                        await CancelamentoPagamentoEventoViagem(cancelarEventoViagemRequest, viagem);

                    if (!cancelamentoPagamentosResponse.Sucesso)
                        return ResponseErro(mensagens[6]);

                    if (!TodosPagamentosCancelados(cancelamentoPagamentosResponse, out var erro))
                        return erro;

                    viagem.Status = StatusViagem.Cancelado;
                    viagem.DataCancelamento = DateTime.Now;

                    if (!ciotDeclaradoMais24Horas)
                    {
                        var retornoCiot = await CancelaCiotViagem(cancelarEventoViagemRequest, viagem);
                        if (retornoCiot.Sucesso)
                        {
                            viagem.DescricaoCiot = "Ciot cancelado com sucesso!";
                            viagem.StatusCiot = StatusCiot.Cancelado;
                        }
                        else
                        {
                            if (!(retornoCiot.Excecao?.Mensagem?.IndexOf("já cancelada", StringComparison.OrdinalIgnoreCase) >= 0))
                                return ResponseErro($"{mensagens[8]} {retornoCiot.Excecao?.Mensagem}");
                            
                            viagem.StatusCiot = StatusCiot.Cancelado;
                            viagem.DescricaoCiot = retornoCiot.Excecao.Mensagem;
                            await Repository.Command.SaveChangesAsync();
                            return ResponseErro($"{mensagens[8]} {retornoCiot.Excecao?.Mensagem}");
                        }
                    }

                    await Repository.Command.SaveChangesAsync();
                    return ResponseSucesso(ciotDeclaradoMais24Horas ? mensagens[1] : mensagens[3], viagem);
                }

                #endregion

                #region Cancelamento de Viagem com CIOT TAC Agregado

                if (tipoCiot == TipoCiot.Agregado.GetHashCode())
                {
                    var viagensVinculadasAbertas = 
                        await Repository.Query.Where(v => v.CiotId == viagem.CiotId && v.Status != StatusViagem.Cancelado).ToListAsync();
                    var viagensVinculadas = 
                        await Repository.Query.Where(v => v.CiotId == viagem.CiotId).ToListAsync();
                    
                    var unicaViagem = viagensVinculadasAbertas.Count == 1;
                    
                    if (ciotDeclaradoMais24Horas && unicaViagem)
                        return ResponseErro(mensagens[5]);

                    cancelamentoPagamentosResponse = await CancelamentoPagamentoEventoViagem(cancelarEventoViagemRequest, viagem);
                    
                    if (!cancelamentoPagamentosResponse.Sucesso)
                        return ResponseErro(mensagens[6]);

                    if (!TodosPagamentosCancelados(cancelamentoPagamentosResponse, out var erro))
                        return erro;

                    viagem.Status = StatusViagem.Cancelado;
                    viagem.DataCancelamento = DateTime.Now;

                    if (!ciotDeclaradoMais24Horas && unicaViagem)
                    {
                        var retornoCiot = await CancelaCiotViagem(cancelarEventoViagemRequest, viagem);
                        if (retornoCiot.Sucesso)
                        {
                            foreach (var viagemVinculada in viagensVinculadas)
                            {
                                viagemVinculada.DescricaoCiot = "Ciot cancelado com sucesso!";
                                viagemVinculada.StatusCiot = StatusCiot.Cancelado;
                            }
                        }
                        else
                        {
                            if (!(retornoCiot.Excecao?.Mensagem?.IndexOf("já cancelada", StringComparison.OrdinalIgnoreCase) >= 0))
                                return ResponseErro($"{mensagens[8]} {retornoCiot.Excecao?.Mensagem}");
                            
                            foreach (var viagemVinculada in viagensVinculadas)
                            {
                                viagemVinculada.StatusCiot = StatusCiot.Cancelado;
                                viagemVinculada.DescricaoCiot = retornoCiot.Excecao.Mensagem;
                            }
                            await Repository.Command.SaveChangesAsync();
                            return ResponseErro($"{mensagens[8]} {retornoCiot.Excecao.Mensagem}");
                        }
                    }
                    await Repository.Command.SaveChangesAsync();
                    return ResponseSucesso(mensagem: !ciotDeclaradoMais24Horas && unicaViagem ? mensagens[3] : mensagens[4], viagem);
                }

                #endregion
                
                #region CIOT com valor inválido (fora dos esperados)

                return ResponseErro("Tipo de CIOT inválido ou não reconhecido.");

                #endregion

            }
            catch (Exception e)
            {
                return new CancelamentoEventoViagemV2Response(false, e.Message);
            }
        }

        private static CancelamentoEventoViagemV2Response ResponseErro(string mensagem) => new(false, mensagem);

        private CancelamentoEventoViagemV2Response ResponseSucesso(string mensagem,
            Domain.Models.Viagem.Viagem viagem = null)
        {
            try
            {
                var response = new CancelamentoEventoViagemV2Response(true, mensagem);

                if (viagem == null)
                    return response;

                response.Data = new CancelamentoViagemV2Response
                {
                    Viagem = Mapper.Map<CancelamentoInfoViagemV2Response>(viagem)
                };
                return response;
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
            
        }

        private static bool TodosPagamentosCancelados(CancelamentoPagamentoEventoViagemResponse cancelamento,
            out CancelamentoEventoViagemV2Response erro)
        {
            var naoCancelados = cancelamento.Data.Where(pe => pe.Status != StatusPagamento.Cancelado).ToList();
            if (naoCancelados.Any())
            {
                var ids = string.Join(", ", naoCancelados.Select(pe => pe.Id));
                var mensagensErro = string.Join("; ", naoCancelados.Select(pe =>
                    JsonConvert.DeserializeObject<dynamic>(pe.JsonRetornoCancelamento)?.Mensagem ??
                    "Erro desconhecido"));
                erro = ResponseErro($"Não foi possível cancelar a viagem. Pagamento(s): {ids} não foram cancelados. " +
                                    $"Erro nos respectivos pagamentos: {mensagensErro}");
                return false;
            }

            erro = null;
            return true;
        }

        private static double? CalcularHorasDesdeDeclaracao(DateTime? dataDeclaracaoCiot)
        {
            return dataDeclaracaoCiot.HasValue
                ? (DateTime.Now - dataDeclaracaoCiot.Value).TotalHours
                : null;
        }

        private async Task<CancelarOperacaoTransporteResp> CancelaCiotViagem(
            CancelamentoEventoViagemV2Request cancelarEventoViagemRequest,
            Domain.Models.Viagem.Viagem viagem)
        {
            var cancelaOpTrans = new CancelarOperacaoTransporteReq
            {
                MotivoCancelamento = cancelarEventoViagemRequest.Motivo,
                CIOT = viagem.Ciot,
                SenhaAlteracao = viagem.CiotId.ToString()
            };

            var retornoCiot = await _operacoesAppService.CancelarOperacaoTransporte(cancelaOpTrans);
            return retornoCiot;
        }

        private async Task<CancelamentoPagamentoEventoViagemResponse> CancelamentoPagamentoEventoViagem(
            CancelamentoEventoViagemV2Request cancelarEventoViagemRequest,
            Domain.Models.Viagem.Viagem viagem)
        {
            #region Coleta de Pagamentos Evento

            var listPagamentosEventoViagem = await _pagamentoEventoReadRepository
                .Include(pe => pe.Transacao)
                .Where(pe => pe.Viagem.Id == viagem.Id && pe.Status != StatusPagamento.Cancelado && 
                             pe.Status != StatusPagamento.NaoExecutado && pe.Status != StatusPagamento.Erro)
                .ToListAsync();

            if (listPagamentosEventoViagem.Any(lpe => lpe.FormaPagamento == FormaPagamentoEvento.Pix))
            {
                var possuiMaisDeUmPagamentoElegivel = listPagamentosEventoViagem
                    .Count(pe => pe.Status is StatusPagamento.Fechado or StatusPagamento.Pendente) > 1;

                if (possuiMaisDeUmPagamentoElegivel)
                    return new CancelamentoPagamentoEventoViagemResponse(
                        false, "Não é possível cancelar uma viagem que tenha pagamento Pix.");
                
            }
                

            #endregion

            #region Cancela pagamentos

            foreach (var pagamentoEvento in listPagamentosEventoViagem)
            {
                pagamentoEvento.JsonEnvioCancelamento = JsonConvert.SerializeObject(cancelarEventoViagemRequest);
                pagamentoEvento.DataCadastroCancelamento = DateTime.Now;

                var lParametroMaxDiasCancelamento = await _parametrosAppService.GetParametrosAsync(-1,
                    Domain.Models.Parametros.Parametros.TipoDoParametro.PrazoMaximaParaCancelamentoPagamentoFrete,
                    Domain.Models.Parametros.Parametros.TipoDoValor.Number);

                var periodoCancelamento =
                    pagamentoEvento.DataBaixa?.AddDays(lParametroMaxDiasCancelamento.Valor.ToInt());

                if (periodoCancelamento.HasValue)
                    if (DateTime.Now > periodoCancelamento)
                    {
                        await RegistrarNotificacaoPagamento("Período máximo para cancelamento excedido.",
                            pagamentoEvento.Id);
                        break;
                    }

                CancelamentoEventoViagemResponse retornoCancelamento = null;

                try
                {
                    retornoCancelamento = await CancelarPagamentosP2P(pagamentoEvento);
                }
                catch (Exception)
                {
                    pagamentoEvento.JsonRetornoCancelamento = JsonConvert.SerializeObject(retornoCancelamento);
                    await _pagamentoEventoWriteRepository.SaveChangesAsync();
                }

                pagamentoEvento.JsonRetornoCancelamento = JsonConvert.SerializeObject(retornoCancelamento);
                pagamentoEvento.DataRetornoCancelamento = DateTime.Now;
                await _pagamentoEventoWriteRepository.SaveChangesAsync();
            }

            #endregion

            return new CancelamentoPagamentoEventoViagemResponse
            {
                Sucesso = true,
                Mensagem = "Sucesso ao realizar método",
                Data = listPagamentosEventoViagem
            };
        }

        private async Task<CancelamentoEventoViagemResponse> CancelarPagamentosP2P(
            Domain.Models.PagamentoEvento.PagamentoEvento pagamentoEvento)
        {
            try
            {
                #region Cancelamento do pagamento

                var lLog = LogManager.GetCurrentClassLogger();

                var tipoPagamentoOrigem = pagamentoEvento.Tipo;
                if (pagamentoEvento.Tipo != Tipo.Cancelamento)
                {
                    await RegistrarPendenciaPagamento(pagamentoEvento.Id, "Solicitado cancelamento do evento",
                        StatusPagamento.Pendente, Tipo.Cancelamento, null, DateTime.Now, true, true);
                }

                var lPagamentosExtorno = pagamentoEvento.Transacao
                    .OrderByDescending(x => x.DataCadastro)
                    .Where(x => x.Tipo != Tipo.Tarifas && x.Status != StatusPagamento.Cancelado).ToList();

                var mensagemNotificacao = $"Estorno do pagamento realizado com sucesso.";
                foreach (var transacao in lPagamentosExtorno)
                {
                    try
                    {
                        var lPagamentosRequest = new ConsultarPagamentosContaRequest()
                        {
                            ContaDestino = transacao.Destino.ToString(),
                            ContaOrigem = transacao.Origem.ToString(),
                            Valor = Math.Round(transacao.Valor, 2, MidpointRounding.AwayFromZero).ToStringSafe()
                                .Replace(',', '.'),
                            DataPagamento = pagamentoEvento.DataCadastro.Date.ToString("u").Replace("Z", "")
                        };
                        //Verificar se já foi feito 
                        var retornoConsulta = await _cartaoRepository.ConsultarPagamentosConta(lPagamentosRequest);

                        var lRetornoExtratoPagamento = JsonConvert.DeserializeObject<List<Transferencia>>(
                            retornoConsulta,
                            new JsonSerializerSettings { DateTimeZoneHandling = DateTimeZoneHandling.Local });

                        #region Validações da transacao

                        //Se n existir n faz o extorno
                        if (!lRetornoExtratoPagamento.Any(x =>
                                x.description.Contains(
                                    $"{pagamentoEvento.Viagem.ViagemExternoId}/{tipoPagamentoOrigem.GetSigla()}")))
                            return new CancelamentoEventoViagemResponse(false,
                                "Ocorreu um erro ao cancelar pagamento. Não foi possível encontrar o extrato do pagamento de origem.");

                        var retornoConsultaSaldo = await VerificarSaldoContaOrigem(transacao.Destino.Value,
                            Math.Round(transacao.Valor, 2, MidpointRounding.AwayFromZero));

                        if (!retornoConsultaSaldo.sucesso)
                            return new CancelamentoEventoViagemResponse(false,
                                $"Estorno da transação cód {transacao.Id} com conta destino {transacao.Origem} " +
                                $"e conta origem {transacao.Destino} no valor de {transacao.Valor.FormatMonetario()} " +
                                $"com pagamento evento cód {transacao.IdPagamentoEvento} não iniciada: {retornoConsultaSaldo.mensagem}");

                        #endregion

                        var transferencia = new Transferencia()
                        {
                            amount = Math.Round(transacao.Valor, 2, MidpointRounding.AwayFromZero),
                            destinationAccount = transacao.Origem,
                            originalAccount = transacao.Destino.Value,
                            description = JsonConvert.SerializeObject(new
                            {
                                description = "Número de Referência: " +
                                              $"{pagamentoEvento.Viagem.ViagemExternoId}/{pagamentoEvento.Tipo.GetSigla()}",
                                protocol = $"{pagamentoEvento.Viagem.ViagemExternoId}/{pagamentoEvento.Tipo.GetSigla()}"
                            })
                        };

                        lLog.Info("Json de cancelamento: " + JsonConvert.SerializeObject(transferencia));
                        transacao.JsonEnvioDockCancelamento = JsonConvert.SerializeObject(transferencia);

                        var retornoTransferencia =
                            await _transferenciaRepository.RealizaTransferenciaEntreContas(transferencia);
                        transacao.JsonRespostaDockCancelamento = retornoTransferencia.RetornoJson;
                        transacao.ResponseCodeDockCancelamento = retornoTransferencia.Code.ToInt();
                        if (retornoTransferencia.Sucesso)
                        {
                            transacao.Status = StatusPagamento.Cancelado;
                            transacao.DataCancelamento = DateTime.Now;
                        }
                        else
                        {
                            mensagemNotificacao =
                                $"Estorno da transação cód {transacao.Id} com conta destino {transacao.Origem} e conta origem {transacao.
                                    Destino} no valor de {transacao.Valor.FormatMonetario()} com pagamento evento cód {transacao.
                                    IdPagamentoEvento} não realizada: {retornoTransferencia.message}";
                            await RegistrarNotificacaoPagamento(mensagemNotificacao, pagamentoEvento.Id);
                            break;
                        }

                        await Engine.CommandBus.SendCommandAsync(transacao);
                    }

                    catch (Exception e)
                    {
                        return new CancelamentoEventoViagemResponse(false, e.Message);
                    }
                }

                #endregion

                #region Salva status do pagamento + retorna resposta

                // Verifica se todas as transações foram canceladas
                if (lPagamentosExtorno.All(x => x.Status == StatusPagamento.Cancelado))
                {
                    pagamentoEvento.StatusAntecipacaoParcelaProprietario =
                        pagamentoEvento.StatusAntecipacaoParcelaProprietario is StatusAntecipacaoParcelaProprietario
                            .Disponivel or null
                            ? StatusAntecipacaoParcelaProprietario.NaoDisponivel
                            : pagamentoEvento.StatusAntecipacaoParcelaProprietario;
                    pagamentoEvento.Status = StatusPagamento.Cancelado;
                    pagamentoEvento.DataCancelamento = DateTime.Now;
                    await Engine.CommandBus.SendCommandAsync<Domain.Models.PagamentoEvento.PagamentoEvento>(
                        pagamentoEvento);
                }

                return new CancelamentoEventoViagemResponse()
                {
                    Sucesso = true,
                    Mensagem = mensagemNotificacao,
                    PagamentoEvento = new CancelamentoEventoViagemPagamentoResponse()
                    {
                        Id = pagamentoEvento.Id,
                        Tipo = Tipo.Cancelamento.ToString(),
                        Status = pagamentoEvento.Status.ToString()
                    },
                    Transacoes = lPagamentosExtorno.Select(x => new CancelamentoEventoViagemTransacaoResponse()
                    {
                        Sucesso = x.Status == StatusPagamento.Cancelado,
                        Mensagem = x.Status == StatusPagamento.Cancelado
                            ? "Sucesso ao cancelar a transação do pagamento!"
                            : "Erro ao processar estorno da transação.",
                        Valor = x.Valor,
                        ContaOrigem = x.Origem,
                        ContaDestino = x.Destino,
                        Agencia = x.Agencia,
                        Conta = x.Conta,
                        CodigoBanco = x.CodigoBanco,
                        Status = x.Status.ToString(),
                        StatusEnum = x.Status,
                        Id = x.Id
                    }).ToList()
                };

                #endregion
            }
            catch (Exception e)
            {
                Console.WriteLine(e.Message);
                throw;
            }
        }

        #endregion

        public async Task<Notificacao> RegistrarNotificacaoPagamento(string notificacaoPagamento, int idPagamentoEvento)
        {
            var commandNotificacao = new NotificacaoSalvarComRetornoCommand()
            {
                Descricao = notificacaoPagamento,
                PagamentoEventoId = idPagamentoEvento
            };

            var retornoNotificacao = await Engine.CommandBus
                .SendCommandAsync<Notificacao>(commandNotificacao);

            return retornoNotificacao;
        }

        public async Task<RespPadrao> VerificarSaldoContaOrigem(int idContaOrigem, decimal valorTransacao)
        {
            try
            {
                var lSaldoResponse = await _cartaoRepository.ConsultarSaldo(idContaOrigem.ToString());

                if (lSaldoResponse.saldoDisponivelGlobal > 0)
                {
                    if (lSaldoResponse.saldoDisponivelGlobal >= valorTransacao)
                    {
                        return new RespPadrao
                        {
                            sucesso = true,
                            mensagem = "Conta ID: " + idContaOrigem + " Saldo disponivel para pagamento"
                        };
                    }

                    return new RespPadrao
                    {
                        sucesso = false,
                        mensagem = "Conta ID: " + idContaOrigem + " Insuficiência de fundos"
                    };
                }

                return new RespPadrao
                {
                    sucesso = false,
                    mensagem = "Erro ao avaliar saldo de conta " + idContaOrigem
                };
            }
            catch (Exception e)
            {
                return new RespPadrao
                {
                    sucesso = false,
                    mensagem = e.Message
                };
            }
        }

        private async Task<Domain.Models.PagamentoEvento.PagamentoEvento> RegistrarPendenciaPagamento(
            int pagamentoEventoId, string pendenciaPagamento,
            StatusPagamento status, Tipo? tipoEvento, decimal? valorCancelamentoReq, DateTime? dataSolicitacao,
            bool contador = false, bool cancelamento = false, bool reenvioAutomatico = false)
        {
            var commandPendenciaPagamento = new PagamentoEventoSalvarComRetornoCommand()
            {
                Id = pagamentoEventoId,
                MotivoPendencia = pendenciaPagamento
            };

            var newPagamentoEvento = _pagamentoEventoReadRepository
                .Include(x => x.Transacao)
                .FirstOrDefault(x => x.Id == pagamentoEventoId);

            var contReenvio = 0;

            if (newPagamentoEvento?.ContadorReenvio != null)
                contReenvio = newPagamentoEvento.ContadorReenvio ?? 0;

            if (cancelamento)
            {
                commandPendenciaPagamento.Tipo = tipoEvento;
                commandPendenciaPagamento.ValorCancelamento = valorCancelamentoReq;
                commandPendenciaPagamento.DataSolicitacaoCancelamento = dataSolicitacao;

                if (status == StatusPagamento.Cancelado)
                {
                    commandPendenciaPagamento.Status = StatusPagamento.Cancelado;
                    commandPendenciaPagamento.DataCancelamento = DateTime.Now;
                }
                else
                {
                    var numeroTentativas = (await _parametrosAppService.GetParametrosAsync(-1,
                        Domain.Models.Parametros.Parametros.TipoDoParametro
                            .LimiteMaximoRetentativaCancelamentoPagamentoFrete,
                        Domain.Models.Parametros.Parametros.TipoDoValor.Number)).Valor.ToInt();

                    if (contReenvio == numeroTentativas)
                    {
                        commandPendenciaPagamento.MotivoPendencia =
                            "Número de tentativas atingido! Entrar em contato com contratado.";
                    }

                    commandPendenciaPagamento.ContadorReenvio = contReenvio + 1;
                }
            }

            if (contador && (status == StatusPagamento.Erro || status == StatusPagamento.Processando))
            {
                status = reenvioAutomatico ? StatusPagamento.Processando : StatusPagamento.Pendente;
                commandPendenciaPagamento.ContadorReenvio = contReenvio + 1;
            }
            else
                commandPendenciaPagamento.ContadorReenvio = contReenvio;

            if (status == StatusPagamento.Fechado)
                commandPendenciaPagamento.ContadorReenvio = 0;

            commandPendenciaPagamento.Status = status;

            var retornoPendenciaPagamento = await Engine.CommandBus
                .SendCommandAsync<Domain.Models.PagamentoEvento.PagamentoEvento>(commandPendenciaPagamento);

            //Incluir as transações para o retorno
            retornoPendenciaPagamento.Transacao = newPagamentoEvento?.Transacao;

            return retornoPendenciaPagamento;
        }

        #region Integrar
        /// <summary>
        /// Método responsável por integrar uma viagem completa na versão 2 da API
        /// Inclui controle de CIOT, múltiplos pagamentos, veículos e portadores
        /// </summary>
        /// <param name="viagemIntegrarRequest">Dados completos da viagem para integração</param>
        /// <param name="token">Token de autenticação</param>
        /// <returns>Resposta da integração completa</returns>
        public async Task<RespPadrao> Integrar(ViagemIntegrarV2Request viagemIntegrarRequest, string token = "")
        {
            try
            {
                #region Log Inicial

                _logger.Info($"Iniciando integração completa V2 - Empresa: {Engine.User.EmpresaId}, " +
                             $"ViagemExternoId: {viagemIntegrarRequest.ViagemExternoId}, " +
                             $"Proprietário: {viagemIntegrarRequest.Proprietario?.CpfCnpj}, " +
                             $"Motorista: {viagemIntegrarRequest.Motorista?.CpfCnpj}, " +
                             $"Quantidade Pagamentos: {viagemIntegrarRequest.Pagamentos?.Count ?? 0}");

                #endregion

                #region Validações iniciais

                var validacao = await ValidarRequestV2(viagemIntegrarRequest);
                if (!validacao.sucesso)
                {
                    return new ViagemIntegrarV2Response(false, validacao.mensagem);
                }

                var responseData = new ViagemIntegrarV2Data();

                #endregion
               
                #region 1. Controlar e cadastrar portadores automaticamente

                var resultadoPortadores = await ProcessarPortadores(viagemIntegrarRequest);
                if (!resultadoPortadores.sucesso)
                {
                    return new ViagemIntegrarV2Response(false, resultadoPortadores.mensagem);
                }

                #endregion

                #region 2. Controlar e cadastrar veículos automaticamente

                var resultadoVeiculos = await ProcessarVeiculos(viagemIntegrarRequest, responseData);
                if (!resultadoVeiculos.sucesso)
                {
                    return new ViagemIntegrarV2Response(false, resultadoVeiculos.mensagem);
                }

                #endregion

                #region 3. Controlar viagem (registrar ou atualizar)

                var resultadoViagem = await ProcessarViagem(viagemIntegrarRequest, responseData,
                    resultadoPortadores.data as PortadoresProcessados);
                if (!resultadoViagem.sucesso)
                {
                    return new ViagemIntegrarV2Response(false, resultadoViagem.mensagem);
                }

                #endregion

                #region 3.1. Vincular veículos à viagem

                var resultadoVinculoVeiculos = await ProcessarVinculoVeiculos(viagemIntegrarRequest, responseData);
                if (!resultadoVinculoVeiculos.sucesso)
                {
                    return new ViagemIntegrarV2Response(false, resultadoVinculoVeiculos.mensagem);
                }

                #endregion

                #region 4. Controlar CIOT (declarar automaticamente se necessário)
                
                var resultadoCiot = await ProcessarCiot(viagemIntegrarRequest, responseData);
                if (!resultadoCiot.sucesso)
                {
                    return new ViagemIntegrarV2Response(false, resultadoCiot.mensagem);
                }
                
                #endregion

                #region 5. Controlar múltiplos pagamentos

                var resultadoPagamentos = await ProcessarPagamentos(viagemIntegrarRequest, responseData, token);
                if (!resultadoPagamentos.sucesso)
                {
                    return new ViagemIntegrarV2Response(false, resultadoPagamentos.mensagem);
                }

                #endregion

                _logger.Info($"Integração completa V2 finalizada com sucesso - ViagemId: {responseData.Id}");
                return new ViagemIntegrarV2Response(true, "Integração realizada com sucesso!", responseData);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"Erro na integração completa V2 - Empresa: {Engine.User.EmpresaId}, " +
                                  $"ViagemExternoId: {viagemIntegrarRequest.ViagemExternoId}");
                return new ViagemIntegrarV2Response(false, $"Erro na integração V2: {ex.Message}");
            }
        }

        private async Task<ConsultarTacAgregadoResponse> ConsultarTacAgregado(string aCpfCnpjProprietario, string aCpfCnpjContratante)
        {
            return  await _operacoesAppService.ConsultarTacAgregado(new ConsultarTacAgregadoRequest()
            {
                CpfCnpjProprietario = aCpfCnpjProprietario,
                CpfCnpjContratante = aCpfCnpjContratante
            });
        }
        
        public async Task<RespPadrao> ConsultarTacAgregado(ConsultarTacAgregadoRequest request)
        {
            var resultado = await ConsultarTacAgregado(request.CpfCnpjProprietario, request.CpfCnpjContratante);
            return new RespPadrao(!resultado.CIOT.IsEmpty(), "", resultado);
        }

        private async Task CarregarInfoCiot(Domain.Models.Viagem.Viagem viagem, ViagemIntegrarV2Data responseData)
        {
            var json = viagem.DescricaoCiot;
            if (json != null && viagem.Ciot == null)
            {
                var serializer = new JsonSerializer();
                using var reader = new StringReader(json);
                using var jsonReader = new JsonTextReader(reader);
                {   if (viagem.StatusCiot != StatusCiot.Cancelado)
                    {
                        var resultado = serializer.Deserialize<DeclararOperacaoTransporteResp>(jsonReader);
                        if (resultado.Sucesso)
                        {
                            responseData.Ciot = resultado.CIOT;
                            responseData.MensagemCiot = "CIOT já declarado.";
                            responseData.DataDeclaracaoCiot = viagem.DataDeclaracaoCiot;
                            viagem.CiotId ??= resultado.SenhaAlteracao.ToIntSafe();
                            viagem.StatusCiot ??= StatusCiot.CiotGerado;
                            viagem.VerificadorCiot ??= resultado.CodigoVerificador;
                            viagem.DataDeclaracaoCiot ??= viagem.DataCadastro;
                            Repository.Command.Update(viagem);
                            await Repository.Command.SaveChangesAsync();
                        }
                        else
                        {
                            responseData.MensagemCiot = resultado.Excecao.Mensagem;
                            responseData.DataDeclaracaoCiot = viagem.DataDeclaracaoCiot;
                        }
                    }
                    else
                    {
                        responseData.Ciot = viagem.Ciot;
                        responseData.StatusCiot = viagem.StatusCiot.ToInt();
                        responseData.MensagemCiot = viagem.DescricaoCiot;
                    }
               
                }
            }
            else
            {
                responseData.Ciot = viagem.Ciot;
                responseData.StatusCiot = viagem.StatusCiot.ToInt();
                responseData.MensagemCiot = "CIOT declarado e vinculado automaticamente.";
                responseData.DataDeclaracaoCiot = viagem.DataDeclaracaoCiot;
            }
            
        }

        private async Task<RespPadrao> ValidarRequestV2(ViagemIntegrarV2Request request)
        {
            try
            {
                if (request.ViagemExternoId <= 0)
                    return new RespPadrao(false, "ViagemExternoId deve ser maior que zero.");

                if (request.DataTerminoFrete <= request.DataInicioFrete)
                    return new RespPadrao(false, "DataTerminoFrete deve ser maior que DataInicioFrete.");

                if (request.Valores == null)
                    return new RespPadrao(false, "Valores são obrigatórios.");

                var pagamentosSaldo = request.Pagamentos.Where(p => p.Tipo == Tipo.Saldo).ToList();

                if (pagamentosSaldo.Count != 1)
                    return new RespPadrao(false, "É obrigatório ter exatamente um pagamento do tipo Saldo para realizar os pagamentos.");
                
                // Verificar se há valores de complemento informado ou calcular avulsos dos pagamentos
                var valorComplemento = request.Valores.Complementos ?? 0;
                var valorAvulso = request.Pagamentos
                    .Where(p => p.Tipo == Tipo.Avulso)
                    .Sum(p => p.Valor);
                var totalComplementosAvulsos = valorComplemento + valorAvulso;

                if (totalComplementosAvulsos > 0)
                {
                    // Validação com complementos/avulsos: ValorFrete = Adiantamentos + Saldo + ValorComplemento + ValorAvulso
                    var totalEsperado = request.Valores.Adiantamentos + request.Valores.Saldo + totalComplementosAvulsos;

                    if (request.Valores.ValorFrete != totalEsperado)
                        return new RespPadrao(false, $"ValorFrete ({request.Valores.ValorFrete:C}) deve ser igual à soma de Adiantamentos ({request.Valores.Adiantamentos:C}) + Saldo ({request.Valores.Saldo:C}) + ValorComplemento ({valorComplemento:C}) + ValorAvulso ({valorAvulso:C}) = {totalEsperado:C}.");
                }
                else
                {
                    // Validação original: ValorFrete = Adiantamentos + Saldo
                    if (request.Valores.ValorFrete != (request.Valores.Adiantamentos + request.Valores.Saldo))
                        return new RespPadrao(false, "ValorFrete deve ser igual à soma de Adiantamentos + Saldo.");
                }

                if (request.Pagamentos == null || !request.Pagamentos.Any())
                    return new RespPadrao(false, "Pelo menos um pagamento deve ser informado.");
                
                var somaPagamentos = request.Pagamentos.Where(p => p.Status != 4).Sum(p => p.Valor);
                if (request.Valores.ValorFrete != somaPagamentos)
                {
                    return new RespPadrao(false,
                        $"ValorFrete ({request.Valores.ValorFrete:C}) deve ser igual à soma dos valores dos pagamentos ({somaPagamentos:C}). " +
                        $"Diferença: {Math.Abs(request.Valores.ValorFrete - somaPagamentos):C}");
                }
                
                var viagemExistente = await BuscarViagemExistente(request);
                if (viagemExistente != null)
                {
                    // Verificar se há apenas complementos/avulsos na request (permite adicionar sem incluir parcelas existentes)
                    var apenasComplementosAvulsos = request.Pagamentos.All(p => p.Tipo == Tipo.Complemento || p.Tipo == Tipo.Avulso);

                    if (!apenasComplementosAvulsos)
                    {
                        var pagamentosExistentes =
                            viagemExistente.PagamentoEvento.Select(p => p.PagamentoExternoId).ToList();
                        var pagamentosRequest = request.Pagamentos.Select(p => p.PagamentoExternoId).ToList();

                        var pagamentosFaltando = pagamentosExistentes.Except(pagamentosRequest).ToList();
                        if (pagamentosFaltando.Any())
                        {
                            return new RespPadrao(false,
                                $"Todas as parcelas da viagem devem estar na requisição. Faltando: {string.Join(", ", pagamentosFaltando)}");
                        }
                    }
                }

                // Validar pagamentos
                foreach (var pagamento in request.Pagamentos)
                {

                    if (pagamento.DataPrevisaoPagamento != null && pagamento.DataPrevisaoPagamento <= DateTime.Now)
                    {
                        return new RespPadrao(false, "DataPrevisaoPagamento deve ser maior que a data atual.");
                    }

                    pagamento.DataPrevisaoPagamento = DateTime.Now;

                    if (!string.IsNullOrEmpty(pagamento.HashValidacao) &&
                        pagamento.Status is 1 or 4) // Pagamento ou cancelamento
                    {
                        if (string.IsNullOrEmpty(pagamento.HashValidacao))
                            return new RespPadrao(false,
                                "HashValidacao é obrigatório para pagamentos ou cancelamentos.");
                    }
                }

                return new RespPadrao(true, "Validação realizada com sucesso.");
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Erro na validação da requisição V2");
                return new RespPadrao(false, $"Erro na validação: {ex.Message}");
            }
        }

        private async Task<RespPadrao> ProcessarPortadores(ViagemIntegrarV2Request request)
        {
            try
            {
                #region Processar Proprietário

                var resultadoProprietario = await CadastrarOuAtualizarPortador(request.Proprietario);
                if (!resultadoProprietario.sucesso)
                    return resultadoProprietario;

                var proprietario = resultadoProprietario.data as Domain.Models.Portador.Portador; 

                #endregion
                
                #region Processar Motorista

                var resultadoMotorista = await CadastrarOuAtualizarPortador(request.Motorista);
                if (!resultadoMotorista.sucesso)
                    return resultadoMotorista;

                var motorista = resultadoMotorista.data as Domain.Models.Portador.Portador;

                #endregion
                
                #region Processar Contratante

                var resultadoContratante = await CadastrarOuAtualizarPortador(request.Contratante);
                if (!resultadoContratante.sucesso)
                    return resultadoContratante;

                var contratante = resultadoContratante.data as Domain.Models.Portador.Portador;

                #endregion 
                
                #region Processar Remetente (opcional)
                
                Domain.Models.Portador.Portador rementete = null;
                if (request.Remetente != null)
                {
                    var resultadoRemetente = await CadastrarOuAtualizarPortador(request.Remetente);
                    if (!resultadoRemetente.sucesso)
                        return resultadoRemetente;
                    rementete = resultadoRemetente.data as Domain.Models.Portador.Portador;
                }

                #endregion 
                
                #region Processar Destinatário (opcional)
                Domain.Models.Portador.Portador destinatario = null;
                if (request.Destinatario != null)
                {
                    var resultadoDestinatario = await CadastrarOuAtualizarPortador(request.Destinatario);
                    if (!resultadoDestinatario.sucesso)
                        return resultadoDestinatario;

                    destinatario = resultadoDestinatario.data as Domain.Models.Portador.Portador;
                }
                #endregion 
                
                var portadores = new PortadoresProcessados
                {
                    Proprietario = proprietario,
                    Motorista = motorista,
                    Contratante = contratante,
                    Remetente = rementete,
                    Destinatario = destinatario
                };

                return new RespPadrao(true, "Portadores processados com sucesso.", portadores);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Erro no processamento de portadores");
                return new RespPadrao(false, $"Erro no processamento de portadores: {ex.Message}");
            }
        }
        
        private async Task<RespPadrao> CadastrarOuAtualizarPortador(PortadorV2Request portadorRequest)
        {
            try
            {
                if (portadorRequest == null || string.IsNullOrEmpty(portadorRequest.CpfCnpj))
                    return new RespPadrao(false, "Dados do portador são obrigatórios.");

                #region ATUALIZA portador

                var portadorExistente = await _portadorReadRepository.GetByCpfCnpjAsync(portadorRequest.CpfCnpj);
                if (portadorExistente != null)
                {
                    // Atualizar nome e RNTRC se fornecidos segundo a documentação
                    if (portadorExistente.Atualizar(portadorRequest.NomeRazaoSocial, portadorRequest.RNTRC))
                    {
                        var commandUpdate = Mapper.Map<PortadorAtualizarV2Command>(portadorRequest);
                        commandUpdate.Id =portadorExistente.Id;
                        var retornoUpdate =
                            Engine.CommandBus.SendCommand<Domain.Models.Portador.Portador>(commandUpdate);
                        return new RespPadrao(true, "Portador atualizado com sucesso.", retornoUpdate);
                    }
                    else
                    {
                        return new RespPadrao(true, "Não foi necessário atualziar o portador", portadorExistente);
                    }
                }

                #endregion

                #region SALVA Portador

                var result = await CreatePortadorRequestCadastro(portadorRequest);
                var command = Mapper.Map<PortadorSalvarComRetornoCommand>(result);
                var retorno = Engine.CommandBus.SendCommand<Domain.Models.Portador.Portador>(command);

                #endregion

                return new RespPadrao(true, "Portador salvo com sucesso.", retorno);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"Erro ao processar portador: {portadorRequest?.CpfCnpj}");
                return new RespPadrao(false, $"Erro ao processar portador: {ex.Message}");
            }
        }
        
        private async Task<PortadorRequest> CreatePortadorRequestCadastro(PortadorV2Request portadorRequest)
        {
            var cidade = portadorRequest.Endereco != null
                ? await _cidadeReadRepository.GetCidadeByIbgeAsync(portadorRequest.Endereco.CodigoMunicipio)
                : await _cidadeReadRepository.FirstOrDefaultAsync();

            var portador = Mapper.Map<PortadorRequest>(portadorRequest);
            portador.CidadeId = cidade.Id;
            portador.EstadoId = cidade.EstadoId;
            portador.EmpresaId = User.EmpresaId;
            return portador;
        }

        private async Task<RespPadrao> ProcessarCiot(ViagemIntegrarV2Request request, ViagemIntegrarV2Data responseData)
        {
            try
            {
                var situacao = await _operacoesAppService.ConsultarSituacaoTransportador(Mapper.Map<ConsultarSituacaoTransportadorReq>(request));
                
                // Verificar se empresa utiliza CIOT
                var utilizaCiot = await VerificarEmpresaUtilizaCiot(Engine.User.EmpresaId);
                switch (utilizaCiot)
                {
                    case true when !situacao.EquiparadoTAC:
                        responseData.MensagemCiot = "Empresa não é equiparado ao TAC";
                        return new RespPadrao(true, "Empresa não é equiparado ao TAC");
                    case false:
                        responseData.MensagemCiot = "Empresa não utiliza CIOT.";
                        return new RespPadrao(true, "Empresa não utiliza CIOT.");
                }
                
                var viagem = await BuscarViagemExistente(request);
                if (viagem.DeclarouCiot()) {
                    await CarregarInfoCiot(viagem, responseData);
                    return new RespPadrao(true, "Processamento de CIOT concluído.", responseData.Ciot);
                }
                
                var validacaoCiot = request.ValidarCamposObrigatoriosCiot();
                if (!validacaoCiot.sucesso)
                {
                    return validacaoCiot;
                }
                
                var proprietario = await _portadorReadRepository.GetByCpfCnpjAsync(request.Proprietario.CpfCnpj);
                if (proprietario == null)
                {
                    return new RespPadrao(false, "Proprietário não encontrado para declaração de CIOT.");
                }
                
                var resultadoDeclaracao = await DeclararCiotAutomatico(request, responseData);
                if (resultadoDeclaracao.sucesso)
                    return new RespPadrao(true, "Processamento de CIOT concluído.", resultadoDeclaracao.data);
               
                responseData.MensagemCiot = resultadoDeclaracao.mensagem;
                return new RespPadrao(false, $"Erro no processamento de CIOT: {resultadoDeclaracao.mensagem}");
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Erro no processamento de CIOT");
                return new RespPadrao(false, $"Erro no processamento de CIOT: {ex.Message}");
            }
        }

        private async Task<bool> VerificarEmpresaUtilizaCiot(int empresaId)
        {
            var empresa = await _empresaReadRepository.GetByIdAsync(empresaId);
            if (empresa == null)
            {
                throw new Exception("Empresa não encontrada.");
            }

            return empresa.UsaCiot();
        }

        private async Task<RespPadrao> DeclararCiotAutomatico(ViagemIntegrarV2Request request, ViagemIntegrarV2Data responseData)
        {
            try
            {
                var viagemExistente = await BuscarViagemExistente(request);
                _logger.Info($"Declarando CIOT automaticamente para viagem {request.ViagemExternoId}");
                if (!request.DeclaraTacAgregado())
                    return await DeclararViaOperacaoTransporte(viagemExistente, request, responseData);
                
                var opTac = await ConsultarTacAgregado(request.Proprietario.CpfCnpj, request.Contratante.CpfCnpj);
                if (!opTac.CIOT.IsEmpty())
                    return await AtualizarViagemComCiotTac(viagemExistente, opTac, responseData, "CIOT declarado via TAC");
                return await DeclararViaOperacaoTransporte(viagemExistente, request, responseData);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Erro na declaração automática de CIOT");
                return new RespPadrao(false, $"Erro na declaração de CIOT: {ex.Message}");
            }
        }

        private async Task<RespPadrao> DeclararViaOperacaoTransporte(Domain.Models.Viagem.Viagem viagem, ViagemIntegrarV2Request request, ViagemIntegrarV2Data responseData)
        {
            var operacaoTransporte = await MapearParaDeclararOperacaoTransporte(request);
            var resultadoCiot = await _operacoesAppService.DeclararOperacaoTransporte(operacaoTransporte);

            viagem.DescricaoCiot = JsonConvert.SerializeObject(resultadoCiot);
            Repository.Command.Update(viagem);
            await Repository.Command.SaveChangesAsync();

            if (resultadoCiot.Sucesso)
            {
                responseData.StatusCiot = StatusCiot.CiotGerado.ToInt();
                responseData.Ciot = resultadoCiot.CIOT;
                responseData.MensagemCiot = "CIOT declarado e vinculado automaticamente.";
                return await AtualizarViagemComCiot(viagem, resultadoCiot, responseData, "CIOT declarado com sucesso");
            }

            _logger.Error($"Erro na declaração de CIOT: {resultadoCiot.Excecao?.Mensagem}");
            return new RespPadrao(false, $"Erro na declaração de CIOT: {resultadoCiot.Excecao?.Mensagem}");
        }
        
        private async Task<RespPadrao> AtualizarViagemComCiotTac(Domain.Models.Viagem.Viagem viagem, ConsultarTacAgregadoResponse ciotResult, ViagemIntegrarV2Data responseData, string logMessage)
        {
            viagem.Ciot = ciotResult.CIOT;
            viagem.CiotId = ciotResult.SenhaAlteracao.ToIntSafe();
            viagem.DataDeclaracaoCiot = ciotResult.DataDeclaracao;
            viagem.VerificadorCiot = ciotResult.CodigoVerificador;
            viagem.StatusCiot = ConverterStatusCiot(ciotResult.StatusCiot);
            responseData.Ciot = ciotResult.CIOT;
            responseData.DataDeclaracaoCiot = ciotResult.DataDeclaracao;
            responseData.MensagemCiot = logMessage;
            responseData.StatusCiot = viagem.StatusCiot.ToInt();
            await AtualizarViagem(viagem);
            _logger.Info($"{logMessage}. CIOT: {ciotResult.CIOT}, Código Verificador: {ciotResult.CodigoVerificador}");
            return new RespPadrao(true, $"{logMessage}. CIOT: {ciotResult.CIOT}", ciotResult);
        }
        
        private async Task<RespPadrao> AtualizarViagemComCiot(Domain.Models.Viagem.Viagem viagem, DeclararOperacaoTransporteResp ciotResult, ViagemIntegrarV2Data responseData, string logMessage)
        {
            viagem.Ciot = ciotResult.CIOT;
            viagem.CiotId = ciotResult.SenhaAlteracao.ToIntSafe();
            viagem.DataDeclaracaoCiot = DateTime.Now;
            viagem.VerificadorCiot = ciotResult.CodigoVerificador;
            viagem.StatusCiot = StatusCiot.CiotGerado;
            responseData.DataDeclaracaoCiot = viagem.DataDeclaracaoCiot;
            await AtualizarViagem(viagem);
            _logger.Info($"{logMessage}. CIOT: {ciotResult.CIOT}, Código Verificador: {ciotResult.CodigoVerificador}");
            return new RespPadrao(true, $"{logMessage}. CIOT: {ciotResult.CIOT}", ciotResult);
        }

        private async Task AtualizarViagem(Domain.Models.Viagem.Viagem viagem)
        {
            Repository.Command.Update(viagem);
            await Repository.Command.SaveChangesAsync();
        }
        
        private async Task<Domain.Models.Viagem.Viagem> BuscarViagemExistente(ViagemIntegrarV2Request request)
        {
            var viagemExistente = await Repository.Query
                .Include(v => v.PagamentoEvento)
                .Include(v => v.PortadorMotorista)
                .Include(v => v.PortadorProprietario)
                .FirstOrDefaultAsync(v => v.ViagemExternoId == request.ViagemExternoId && v.EmpresaId == Engine.User.EmpresaId);

            // Carregar transações separadamente se a viagem existir
            if (viagemExistente?.PagamentoEvento != null && viagemExistente.PagamentoEvento.Any())
            {
                var pagamentoIds = viagemExistente.PagamentoEvento.Select(p => p.Id).ToList();
                var transacoes = await _transacaoReadRepository
                    .Where(t => pagamentoIds.Contains(t.IdPagamentoEvento))
                    .ToListAsync();

                // Associar transações aos pagamentos
                foreach (var pagamento in viagemExistente.PagamentoEvento)
                {
                    var transacoesPagamento = transacoes.Where(t => t.IdPagamentoEvento == pagamento.Id).ToList();
                    // Assumindo que existe uma propriedade Transacao no PagamentoEvento
                    if (pagamento.Transacao == null)
                        pagamento.Transacao = new List<Domain.Models.Transacao.Transacao>();

                    foreach (var transacao in transacoesPagamento)
                    {
                        pagamento.Transacao.Add(transacao);
                    }
                }
            }

            return viagemExistente;
        }

        private async Task<RespPadrao> ProcessarPagamentos(ViagemIntegrarV2Request request,
            ViagemIntegrarV2Data responseData, string token)
        {
            try
            {
                responseData.PagamentosEventos = new List<PagamentoV2Response>();
                
                foreach (var pagamentoRequest in request.Pagamentos)
                {
                    var pagamentoExistente = await BuscarPagamentoExistente(pagamentoRequest.PagamentoExternoId.Value, request.ViagemExternoId);
                    if (pagamentoRequest.Status == 0) // Baixa
                    {
                        var resultadoPagamento = await ProcessarBaixaComIdempotencia(pagamentoRequest, request, token, pagamentoExistente, responseData);
                        responseData.PagamentosEventos.Add(resultadoPagamento);
                    }
                    else if (pagamentoRequest.Status == 1) // Abertura
                    {
                        var resultadoPagamento = await ProcessarAberturaComIdempotencia(pagamentoRequest, request, token, pagamentoExistente);
                        responseData.PagamentosEventos.Add(resultadoPagamento);
                    }
                    else if (pagamentoRequest.Status == 4) // Cancelamento
                    {
                        var resultadoPagamento = await ProcessarCancelamentoComIdempotencia(pagamentoRequest, request, token, pagamentoExistente);
                        responseData.PagamentosEventos.Add(resultadoPagamento);
                    }

                }
                return new RespPadrao(true, "Pagamentos processados com sucesso.");
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Erro no processamento de pagamentos");
                return new RespPadrao(false, $"Erro no processamento de pagamentos: {ex.Message}");
            }
        }

        #region Métodos de Idempotência

        private async Task<Domain.Models.PagamentoEvento.PagamentoEvento> BuscarPagamentoExistente(int pagamentoExternoId, int viagemExternoId)
        {
            return await _pagamentoEventoReadRepository
                .Where(x => x.PagamentoExternoId == pagamentoExternoId &&
                           x.Viagem.ViagemExternoId == viagemExternoId &&
                           x.EmpresaId == Engine.User.EmpresaId)
                .Include(x => x.Viagem)
                .Include(x => x.Empresa)
                .Include(x => x.Transacao)
                .FirstOrDefaultAsync();
        }

        private async Task<PagamentoV2Response> ProcessarBaixaComIdempotencia(
            PagamentoV2Request pagamentoRequest, ViagemIntegrarV2Request request, string token,
            Domain.Models.PagamentoEvento.PagamentoEvento pagamentoExistente, ViagemIntegrarV2Data responseData )
        {
            if (pagamentoExistente == null ||
                (pagamentoExistente.Status != StatusPagamento.Fechado &&
                 pagamentoExistente.Status != StatusPagamento.Processando))
                return await ProcessarBaixaPagamento(pagamentoRequest, request, token, pagamentoExistente?.Id ?? 0, responseData);

            _logger.Info($"Pagamento {pagamentoRequest.PagamentoExternoId} já está baixado (Status: {pagamentoExistente.Status}). Retornando dados existentes.");

            var response = CriarRespostaPagamentoV2("Pagamento já foi baixado anteriormente.",
                pagamentoRequest, CriarViagemIntegrarResponseExistente(pagamentoExistente));
            
            return response;
        }

        private async Task<PagamentoV2Response> ProcessarAberturaComIdempotencia(
            PagamentoV2Request pagamentoRequest, ViagemIntegrarV2Request request, string token,
            Domain.Models.PagamentoEvento.PagamentoEvento pagamentoExistente)
        {
            if (pagamentoExistente is not { Status: StatusPagamento.Aberto })
                return await AbrirParcelaPagamento(pagamentoRequest, request, token, pagamentoExistente?.Id ?? 0);
            
            _logger.Info($"Pagamento {pagamentoRequest.PagamentoExternoId} já está aberto. Retornando dados existentes.");
            return CriarRespostaPagamentoV2("Pagamento já foi aberto anteriormente.",
                pagamentoRequest, CriarViagemIntegrarResponseExistente(pagamentoExistente));
        }

        private async Task<PagamentoV2Response> ProcessarCancelamentoComIdempotencia(
            PagamentoV2Request pagamentoRequest, ViagemIntegrarV2Request request, string token,
            Domain.Models.PagamentoEvento.PagamentoEvento pagamentoExistente)
        {
            if (pagamentoExistente is not { Status: StatusPagamento.Cancelado })
                return await CancelarParcelaPagamento(pagamentoRequest, request, token, pagamentoExistente?.Id ?? 0);
            _logger.Info($"Pagamento {pagamentoRequest.PagamentoExternoId} já está cancelado. Retornando dados existentes.");

            return CriarRespostaPagamentoV2("Pagamento já foi cancelado anteriormente.",
                pagamentoRequest, CriarViagemIntegrarResponseExistente(pagamentoExistente));
        }

        private ViagemIntegrarV2InternalResponse CriarViagemIntegrarResponseExistente(Domain.Models.PagamentoEvento.PagamentoEvento pagamentoExistente)
        {
            return new ViagemIntegrarV2InternalResponse
            {
                Sucesso = true,
                ViagemId = pagamentoExistente.ViagemId,
                ViagemExternoId = pagamentoExistente.Viagem.ViagemExternoId,
                StatusViagem = pagamentoExistente.Viagem.Status.ToInt(),
                Mensagem = "Operação já foi processada anteriormente",
                Pagamento = new PagamentoViagemV2InternalResponse
                {
                    PagamentoEventoId = pagamentoExistente.Id,
                    PagamentoExternoId = pagamentoExistente.PagamentoExternoId,
                    ValorParcela = pagamentoExistente.Valor,
                    StatusPagamento = pagamentoExistente.Status?.ToInt(),
                    FormaPagamento = pagamentoExistente.FormaPagamento?.ToInt(),
                    CodigoTransacao = pagamentoExistente.CodigoTransacao,
                    Transacoes = pagamentoExistente.Transacao?
                        .Where(t => t.Tipo != Tipo.Tarifas)
                        .Select(t => Mapper.Map<PagamentoV2TransacaoResponse>(t))
                        .ToList() ?? new List<PagamentoV2TransacaoResponse>()
                }
            };
        }

        #endregion

        private async Task<PagamentoViagemIntegrarRequest> CriarIntegrarRequest(
            PagamentoV2Request pagamentoRequest,
            ViagemIntegrarV2Request request,
            EStatusCliente statusCliente)
        {
            var viagem = await BuscarViagemExistente(request);
            return new PagamentoViagemIntegrarRequest
            {
                // IDs e referência
                ViagemExternoId = request.ViagemExternoId,
                PagamentoExternoId = pagamentoRequest.PagamentoExternoId,

                // Contratado e Motorista
                CpfCnpjContratado = request.Proprietario.CpfCnpj,
                NomeContratado = request.Proprietario.NomeRazaoSocial,
                CpfMotorista = request.Motorista.CpfCnpj,
                NomeMotorista = request.Motorista.NomeRazaoSocial,

                // Pagamento
                Tipo = pagamentoRequest.Tipo,
                FormaPagamento = pagamentoRequest.FormaPagamento,
                Valor = pagamentoRequest.Valor,
                Agencia = pagamentoRequest.Agencia,
                Conta = pagamentoRequest.Conta,
                TipoConta = pagamentoRequest.TipoConta,
                ChavePix = pagamentoRequest.ChavePix,
                HashValidacao = pagamentoRequest.HashValidacao,
                DataPrevisaoPagamento = pagamentoRequest.DataPrevisaoPagamento,
                RecebedorAutorizado = pagamentoRequest.RecebedorAutorizado,
                WebhookUrl = pagamentoRequest.WebhookUrl,

                // Dados da viagem
                Ciot = viagem.Ciot,
                VerificadorCiot = viagem.VerificadorCiot,
                PesoCarga = viagem.PesoCarga,
                CodigoNaturezaCarga = viagem.CodigoNaturezaCarga,
                TipoViagem = viagem.TipoCiot.ToIntSafe(1),

                // Localidade e filial
                IbgeOrigem = request.CodigoMunicipioOrigem,
                IbgeDestino = request.CodigoMunicipioDestino,
                FilialId = request.Filial,

                // Status do cliente
                StatusCliente = statusCliente
            };
        }
        
        private async Task<PagamentoV2Response> CancelarParcelaPagamento(PagamentoV2Request pagamentoRequest,
            ViagemIntegrarV2Request request, string token, int pagamentoEventoId = 0)
        {
            try
            {
                // Converter para o formato do sistema existente
                var pagamentoViagemRequest = await CriarIntegrarRequest(pagamentoRequest, request, EStatusCliente.Cancelar);

                // Usar o método existente para processar o pagamento
                var resultadoV1 = await _viagemAppService.PagamentoViagemCancelar(pagamentoViagemRequest, token, false, pagamentoEventoId);
                var resultado = ConverterV1ParaV2(resultadoV1);

                return CriarRespostaPagamentoV2(
                    resultado.Sucesso ? "Parcela pagamento cancelada com sucesso." : "Erro no cancelamento do pagamento.",
                    pagamentoRequest, resultado);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"Erro ao processar pagamento: {pagamentoRequest?.PagamentoExternoId}");
                return CriarRepostaPagamentoV2Erro(pagamentoRequest, ex);
            }
        }
        
         private async Task<PagamentoV2Response> AbrirParcelaPagamento(PagamentoV2Request pagamentoRequest,
            ViagemIntegrarV2Request request, string token, int pagamentoEventoId = 0)
        {
            try
            {
                var pagamentoViagemRequest = await CriarIntegrarRequest(pagamentoRequest, request, EStatusCliente.Programar);
                var resultadoV1 = await _viagemAppService.PagamentoViagemAbrir(pagamentoViagemRequest, token, false, pagamentoEventoId);
                var resultado = ConverterV1ParaV2(resultadoV1);
                return CriarRespostaPagamentoV2(
                    resultado.Sucesso ? "Parcela pagamento processada com sucesso." : "Erro no processamento do pagamento.",
                    pagamentoRequest, resultado);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"Erro ao processar pagamento: {pagamentoRequest?.PagamentoExternoId}");
                return CriarRepostaPagamentoV2Erro(pagamentoRequest, ex);
            }
        }
        
        private async Task<PagamentoV2Response> ProcessarBaixaPagamento(PagamentoV2Request pagamentoRequest,
            ViagemIntegrarV2Request request, string token, int pagamentoEventoId = 0, ViagemIntegrarV2Data responseData = null)
        {
            try
            {
                if (pagamentoRequest.Tipo == Tipo.Saldo)
                {
                    _logger.Info($"Iniciando validação para baixa de Saldo - ViagemExternoId: {request.ViagemExternoId}, PagamentoExternoId: {pagamentoRequest.PagamentoExternoId}");

                    var validacaoSaldo = await ValidarBaixaSaldo(request.ViagemExternoId, pagamentoRequest.PagamentoExternoId.Value);
                    if (!validacaoSaldo.sucesso)
                    {
                        _logger.Warn($"Validação de baixa de Saldo falhou: {validacaoSaldo.mensagem}");
                        return new PagamentoV2Response
                        {
                            Sucesso = false,
                            Mensagem = validacaoSaldo.mensagem,
                            PagamentoExternoId = pagamentoRequest.PagamentoExternoId,
                            ValorParcela = pagamentoRequest.Valor,
                            Tipo = (int)pagamentoRequest.Tipo,
                            FormaPagamento = (int)pagamentoRequest.FormaPagamento
                        };
                    }
                    _logger.Info("Validação de baixa de Saldo aprovada");
                }

                var pagamentoViagemRequest = await CriarIntegrarRequest(pagamentoRequest, request, EStatusCliente.Baixar);
                var resultadoV1 = await _viagemAppService.PagamentoViagemBaixa(pagamentoViagemRequest, token, false, pagamentoEventoId);
                var resultado = ConverterV1ParaV2(resultadoV1);

                if (resultadoV1.Pagamento == null)
                {
                    return CriarRespostaPagamentoV2(
                        resultadoV1.Mensagem,
                        pagamentoRequest, resultado);
                }
                
                if (pagamentoRequest.Tipo == Tipo.Saldo && resultadoV1.Pagamento.StatusPagamento == 0)
                {
                    _logger.Info($"Saldo baixado com sucesso, iniciando fechamento da viagem {request.ViagemExternoId}");
                    await FecharViagemAposBaixaSaldo(request.ViagemExternoId);
                    resultado.StatusViagem = (int) StatusViagem.Baixado; // Status = 2
                    if (responseData != null) responseData.Status = StatusViagem.Baixado.ToInt();
                    _logger.Info($"StatusViagem atualizado na resposta para: {resultado.StatusViagem}");
                }

                return CriarRespostaPagamentoV2(
                    resultadoV1.Pagamento.StatusPagamento == 0 ? "Baixa pagamento processado com sucesso." : $"{resultadoV1.Pagamento.Mensagem}",
                    pagamentoRequest, resultado);

            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"Erro ao processar pagamento: {pagamentoRequest?.PagamentoExternoId}");
                return CriarRepostaPagamentoV2Erro(pagamentoRequest, ex);
            }
        }

        /// <summary>
        /// Valida se é possível baixar um pagamento do tipo Saldo
        /// Regra: Não pode ter nenhum Adiantamento com status "Aberto" na viagem
        /// </summary>
        private async Task<RespPadrao> ValidarBaixaSaldo(int viagemExternoId, int pagamentoExternoIdSaldo)
        {
            try
            {
                var viagem = await Repository.Query
                    .Include(v => v.PagamentoEvento)
                    .FirstOrDefaultAsync(v => v.ViagemExternoId == viagemExternoId && v.EmpresaId == Engine.User.EmpresaId);

                if (viagem == null)
                {
                    return new RespPadrao(false, "Viagem não encontrada.");
                }
                
                var pagamentosAbertos = viagem.PagamentoEvento
                    .Where(p => p.PagamentoExternoId != pagamentoExternoIdSaldo &&
                               p.Status == StatusPagamento.Aberto &&
                               p.Tipo == Tipo.Adiantamento)  // Apenas Adiantamentos impedem baixa de Saldo
                    .ToList();

                _logger.Info($"Adiantamentos em aberto que impedem Saldo: {pagamentosAbertos.Count}");
                if (pagamentosAbertos.Any())
                {
                    var adiantamentosAbertos = pagamentosAbertos.Select(p => $"Id:{p.PagamentoExternoId} Tipo:{p.Tipo} Status:{p.Status}");
                    _logger.Warn($"Adiantamentos que impedem baixa do Saldo: {string.Join(", ", adiantamentosAbertos)}");
                    return new RespPadrao(false,
                        "Não é possível baixar o Saldo pois existem Adiantamentos em aberto na viagem.");
                }

                return new RespPadrao(true, "Validação de baixa de Saldo aprovada.");
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"Erro ao validar baixa de Saldo para viagem {viagemExternoId}");
                return new RespPadrao(false, "Erro interno ao validar baixa de Saldo.");
            }
        }

        /// <summary>
        /// Fecha a viagem após baixar o Saldo com sucesso
        /// Status = 2 (Baixado) conforme esperado
        /// </summary>
        private async Task FecharViagemAposBaixaSaldo(int viagemExternoId)
        {
            try
            {
                var viagem = await Repository.Query
                    .FirstOrDefaultAsync(v => v.ViagemExternoId == viagemExternoId && v.EmpresaId == Engine.User.EmpresaId);

                if (viagem != null)
                {
                    viagem.Status = StatusViagem.Baixado; // Status = 2 (Baixado)
                    viagem.DataBaixa = DateTime.Now;

                    Repository.Command.Update(viagem);
                    await Repository.Command.SaveChangesAsync();

                    _logger.Info($"Viagem {viagemExternoId} fechada com Status = 2 (Baixado) após baixa do Saldo.");
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"Erro ao fechar viagem {viagemExternoId} após baixa do Saldo");
                // Não propagar o erro para não afetar o sucesso da baixa do pagamento
            }
        }
        
        /// <summary>
        /// Converte ViagemIntegrarResponse da V1 para ViagemIntegrarV2InternalResponse
        /// </summary>
        private ViagemIntegrarV2InternalResponse ConverterV1ParaV2(ViagemIntegrarResponse resultadoV1)
        {
            return new ViagemIntegrarV2InternalResponse
            {
                Sucesso = resultadoV1.Sucesso,
                ViagemId = resultadoV1.ViagemId,
                ViagemExternoId = resultadoV1.ViagemExternoId,
                StatusViagem = resultadoV1.StatusViagem,
                Mensagem = resultadoV1.Mensagem,
                Pagamento = resultadoV1.Pagamento != null ? new PagamentoViagemV2InternalResponse
                {
                    PagamentoEventoId = resultadoV1.Pagamento.PagamentoEventoId,
                    PagamentoExternoId = resultadoV1.Pagamento.PagamentoExternoId,
                    ValorParcela = resultadoV1.Pagamento.ValorParcela,
                    ValorMotorista = resultadoV1.Pagamento.ValorMotorista,
                    StatusPagamento = resultadoV1.Pagamento.StatusPagamento,
                    CodigoTransacao = resultadoV1.Pagamento.CódTransacao,
                    FormaPagamento = resultadoV1.Pagamento.FormaPagamento,
                    Mensagem = resultadoV1.Pagamento.Mensagem,
                    Transacoes = resultadoV1.Pagamento.Transacoes?.Select(t => Mapper.Map<PagamentoV2TransacaoResponse>(t)).ToList() ?? new List<PagamentoV2TransacaoResponse>()
                } : null
            };
        }

        private PagamentoV2Response CriarRepostaPagamentoV2Erro(PagamentoV2Request pagamentoRequest, Exception ex)
        {
           return new PagamentoV2Response
            {
                Sucesso = false,
                PagamentoExternoId = pagamentoRequest?.PagamentoExternoId ?? 0,
                Tipo = pagamentoRequest?.Tipo != null ? pagamentoRequest.Tipo.ToInt() : Tipo.Saldo.ToInt(),
                FormaPagamento = pagamentoRequest?.FormaPagamento != null ? pagamentoRequest.FormaPagamento.ToInt() : FormaPagamentoEvento.Deposito.ToInt(),
                ValorParcela = pagamentoRequest?.Valor ?? 0,
                Mensagem = $"Erro ao processar pagamento: {ex.Message}",
                PagamentoAgendado = false,
                DataPrevisaoPagamento = pagamentoRequest?.DataPrevisaoPagamento,
                Transacoes = new List<PagamentoV2TransacaoResponse>()
            };
        }

        private PagamentoV2Response CriarRespostaPagamentoV2(string mensagem, PagamentoV2Request pagamentoRequest, ViagemIntegrarV2InternalResponse resultado)
        {
            // Buscar o PagamentoEvento para obter o Tipo correto e DataPrevisaoPagamento
            var pagamentoEvento = resultado.Pagamento?.PagamentoEventoId.HasValue == true
                ? _pagamentoEventoReadRepository.GetByIdIncludeTransacoesEViagemAsync(resultado.Pagamento.PagamentoEventoId.Value).Result
                : null;

            var response = new PagamentoV2Response
            {
                Sucesso = resultado.Sucesso,
                Id = resultado.Pagamento?.PagamentoEventoId,
                PagamentoExternoId = pagamentoRequest.PagamentoExternoId,
                Tipo = pagamentoEvento?.Tipo.HasValue == true ? pagamentoEvento.Tipo.Value.ToInt() : pagamentoRequest.Tipo.ToInt(),
                FormaPagamento = pagamentoRequest.FormaPagamento.ToInt(),
                ValorParcela = pagamentoRequest.Valor,
                ValorMotorista = resultado.Pagamento?.ValorMotorista,
                StatusPagamento = resultado.Pagamento?.StatusPagamento,
                CodigoTransacao = resultado.Pagamento?.CodigoTransacao,
                Mensagem = resultado.Sucesso ? mensagem : resultado.Mensagem,
                PagamentoAgendado = pagamentoRequest.DataPrevisaoPagamento > DateTime.Now,
                DataPrevisaoPagamento = pagamentoEvento?.DataPrevisaoPagamento ?? pagamentoRequest.DataPrevisaoPagamento,
                Transacoes = resultado.Pagamento?.Transacoes?.Any() == true
                    ? resultado.Pagamento.Transacoes.Select(t => Mapper.Map<PagamentoV2TransacaoResponse>(t)).ToList()
                    : pagamentoEvento?.Transacao?
                        .Where(t => t.Tipo != Tipo.Tarifas)
                        .Select(t => Mapper.Map<PagamentoV2TransacaoResponse>(t))
                        .ToList() ?? new List<PagamentoV2TransacaoResponse>()
            };

            return response;
        }
        
        /// <summary>
        /// Converte string de tipo para enum Tipo
        /// Trata conversão por nome ou por valor numérico
        /// </summary>
        /// <param name="tipo">Tipo como string (pode ser nome ou número)</param>
        /// <returns>Tipo enum correspondente</returns>
        private Tipo ConverterTipoPagamento(string tipo)
        {
            if (string.IsNullOrWhiteSpace(tipo))
            {
                _logger.Warn("Tipo de pagamento veio vazio, usando valor padrão Saldo");
                return Tipo.Saldo;
            }

            // Tentar converter por valor numérico primeiro
            if (int.TryParse(tipo.Trim(), out var tipoInt))
            {
                if (Enum.IsDefined(typeof(Tipo), tipoInt))
                {
                    _logger.Info($"Tipo convertido de número: {tipo} -> {(Tipo)tipoInt}");
                    return (Tipo)tipoInt;
                }
                else
                {
                    _logger.Warn($"Tipo número inválido: {tipo}, usando valor padrão Saldo");
                    return Tipo.Saldo;
                }
            }

            // Tentar converter por nome (case insensitive)
            var tipoLower = tipo.ToLower().Trim();
            Tipo? resultado = tipoLower switch
            {
                "adiantamento" => Tipo.Adiantamento,
                "saldo" => Tipo.Saldo,
                "complemento" => Tipo.Complemento,
                "avulso" => Tipo.Avulso,
                "tarifaantt" => Tipo.TarifaANTT,
                "cancelamento" => Tipo.Cancelamento,
                "tarifas" => Tipo.Tarifas,
                _ => null
            };

            if (resultado.HasValue)
            {
                _logger.Info($"Tipo convertido de nome: {tipo} -> {resultado.Value}");
                return resultado.Value;
            }

            // Tentar converter por nome do enum diretamente
            if (Enum.TryParse<Tipo>(tipo.Trim(), true, out var tipoEnum))
            {
                _logger.Info($"Tipo convertido por enum: {tipo} -> {tipoEnum}");
                return tipoEnum;
            }

            // Se não conseguiu converter, usar valor padrão
            _logger.Warn($"Tipo não reconhecido: '{tipo}', usando valor padrão Saldo");
            return Tipo.Saldo;
        }
        
        private async Task<RespPadrao> ProcessarVeiculos(ViagemIntegrarV2Request request,
            ViagemIntegrarV2Data responseData)
        {
            try
            {
                if (request.Veiculos == null || !request.Veiculos.Any())
                {
                    return new RespPadrao(true, "Nenhum veículo informado.");
                }

                foreach (var veiculoRequest in request.Veiculos)
                {
                    var resultadoVeiculo = await ProcessarVeiculo(veiculoRequest, request.Proprietario.CpfCnpj);
                    responseData.Veiculos.Add(resultadoVeiculo);
                }
                return new RespPadrao(true, "Veículos processados com sucesso.");
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Erro no processamento de veículos");
                return new RespPadrao(false, $"Erro no processamento de veículos: {ex.Message}");
            }
        }

        private async Task<VeiculoV2Response> ProcessarVeiculo(VeiculoV2Request veiculoRequest,
            string cpfCnpjProprietario)
        {
            try
            {
                var veiculoExistente = await _veiculoAppService.Repository.Query
                    .Include(v => v.Portador)
                    .FirstOrDefaultAsync(v => v.Placa.ToUpper() == veiculoRequest.Placa.ToUpper());

                if (veiculoExistente != null)
                {
                    return new VeiculoV2Response
                    {
                        VeiculoId = veiculoExistente.Id,
                        Placa = veiculoExistente.Placa,
                        RNTRC = veiculoExistente.Portador?.RNTRC,
                        VeiculoCadastrado = true,
                        VeiculoVinculado = true,
                        Mensagem = "Veículo existente vinculado à viagem."
                    };
                }
                else
                {
                    // Cadastrar novo veículo
                    var proprietario = await _portadorReadRepository.GetByCpfCnpjAsync(cpfCnpjProprietario);
                    if (proprietario == null)
                    {
                        return new VeiculoV2Response
                        {
                            Placa = veiculoRequest.Placa,
                            VeiculoCadastrado = false,
                            VeiculoVinculado = false,
                            Mensagem = "Proprietário não encontrado para cadastro do veículo."
                        };
                    }
                    
                    var novoVeiculo = new Domain.Models.Veiculo.Veiculo
                    {
                        Placa = veiculoRequest.Placa.ToUpper(),
                        PortadorProprietarioId = proprietario.Id,
                        EmpresaId = Engine.User.EmpresaId,
                        Status = StatusVeiculo.Ativo,
                        Ano = DateTime.Now.Year, // Valor padrão
                        TipoVeiculo = 1, // Valor padrão
                        UsuarioCadastroId = Engine.User.Id,
                        DataCadastro = DateTime.Now
                    };

                    await _veiculoAppService.Repository.Command.AddAsync(novoVeiculo);
                    await _veiculoAppService.Repository.Command.SaveChangesAsync();

                    _logger.Info($"Veículo cadastrado automaticamente: {veiculoRequest.Placa} - ID: {novoVeiculo.Id}");

                    return new VeiculoV2Response
                    {
                        VeiculoId = novoVeiculo.Id,
                        Placa = novoVeiculo.Placa,
                        RNTRC = veiculoRequest.RNTRC,
                        VeiculoCadastrado = true,
                        VeiculoVinculado = true,
                        Mensagem = "Veículo cadastrado e vinculado à viagem."
                    };
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"Erro ao processar veículo: {veiculoRequest?.Placa}");
                return new VeiculoV2Response
                {
                    Placa = veiculoRequest?.Placa,
                    VeiculoCadastrado = false,
                    VeiculoVinculado = false,
                    Mensagem = $"Erro ao processar veículo: {ex.Message}"
                };
            }
        }
        
        private Pagamento MapearPagamentoCIOT(ViagemIntegrarV2Request request)
        {
            var primeiroPagamento = request.Pagamentos.First(); // Regra definida via chat
            var parcelas = MapearParaParcelasPagamentoCIOT(request.Pagamentos);
            return new Pagamento
            {
                AgenciaPagamento = primeiroPagamento.Agencia,
                ContaPagamento = primeiroPagamento.Conta,
                FormaPagamento = RetornarPagamentoCaruana(primeiroPagamento.FormaPagamento.ToIntSafe(1)),
                ParcelaUnica = request.Pagamentos.Count <= 1,
                Parcelas = parcelas.ToArray()
            };
        }

        private List<ParcelaPagamento> MapearParaParcelasPagamentoCIOT(List<PagamentoV2Request> pagamentos)
        {
            return pagamentos.Select((p, index) => new ParcelaPagamento
            {
                CodigoParcela = $"PARC{index + 1:000}",
                ValorParcela = p.Valor,
                Vencimento = p.DataPrevisaoPagamento ?? DateTime.Now
            }).ToList();
        }
        
        private Task<DeclararOperacaoTransporteReq> MapearParaDeclararOperacaoTransporte(
            ViagemIntegrarV2Request request)
        {
            try
            {
                var operacaoTransporte = Mapper.Map<DeclararOperacaoTransporteReq>(request);

                operacaoTransporte.Frete.PesoCarga = request.PesoCarga;
                operacaoTransporte.Frete.CodigoNaturezaCarga = request.CodigoNaturezaCarga;
                if (request.Pagamentos == null || !request.Pagamentos.Any())
                    throw new Exception("É necessário informar ao menos um pagamento para viagens do tipo 1.");
                    
                return Task.FromResult(operacaoTransporte);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Erro ao mapear dados para DeclararOperacaoTransporte");
                throw;
            }
        }

        private void LimparDeclaracaoTac(DeclararOperacaoTransporteReq operacao)
        {
            operacao.Frete.CodigoMunicipioDestino = null;
            operacao.Frete.CodigoMunicipioOrigem = null;
            operacao.Remetente = null;
            operacao.Destinatario = null;
            operacao.Frete.DataInicioFrete = null;
        }
        
        private int RetornarPagamentoCaruana(int formaPagamento)
        {
            switch (formaPagamento)
            {
                case 0:
                    return 2;
                case 1:
                    return 1;
                case 2:
                    return 3;
                case 3:
                    return 3;
                case 4:
                    return 1;
            }
            throw new DeclararCiotException("Erro ao efetuar transação!");
        }
        
        private async Task<int> BuscarCidadePorIbge(int ibge)
        {
            var cidadeId = await _cidadeReadRepository
                .GetIdByIbgeAsync(ibge.ToIntSafe());
            return cidadeId;
        }
        
        /// <summary>
        /// Processa a viagem (registra ou atualiza)
        /// </summary>
        private async Task<RespPadrao> ProcessarViagem(ViagemIntegrarV2Request request, ViagemIntegrarV2Data responseData, PortadoresProcessados portadores)
        {
            try
            {
                var viagemExistente = await BuscarViagemExistente(request);
                if (viagemExistente != null)
                {
                    if (viagemExistente.PortadorProprietario.CpfCnpj != portadores.Proprietario.CpfCnpj)
                    {
                        return new RespPadrao(false, "Proprietário (portador contratado) informado não pertence a esta viagem.");
                    }

                    if (viagemExistente.PortadorMotorista.CpfCnpj != portadores.Motorista.CpfCnpj)
                    {
                        return new RespPadrao(false, "Motorista informado não pertence a esta viagem!");
                    }
                    
                    Mapper.Map(request, viagemExistente);
                    viagemExistente.VersaoIntegracaoViagem = EVersaoIntegracao.V2;
                    viagemExistente.UsuarioAlteracaoId = Engine.User.Id;
                    
                    Repository.Command.Update(viagemExistente);
                    await Repository.Command.SaveChangesAsync();

                    responseData.Id = viagemExistente.Id;
                    responseData.Status = viagemExistente.Status.ToInt();
                    responseData.ViagemExternoId = viagemExistente.ViagemExternoId;
                    responseData.ValorFrete = viagemExistente.ValorFrete.Value;
                    _logger.Info($"Viagem atualizada: ID {viagemExistente.Id}, ViagemExternoId: {request.ViagemExternoId}");
                }
                else
                {
                    var viagemIntegrar = Mapper.Map<ViagemSalvarComRetornoCommand>(request);
                    viagemIntegrar.PortadorMotoristaId = portadores.Motorista.Id;
                    viagemIntegrar.PortadorProprietarioId = portadores.Proprietario.Id;
                    viagemIntegrar.VersaoIntegracaoViagem = EVersaoIntegracao.V2;
                    
                    viagemIntegrar.Status = StatusViagem.Pendente;
                    if (request.CodigoMunicipioOrigem != null)
                    {
                        viagemIntegrar.CidadeOrigemId = await BuscarCidadePorIbge(request.CodigoMunicipioOrigem!.Value);
                    }

                    if (request.CodigoMunicipioDestino != null)
                    {
                        viagemIntegrar.CidadeDestinoId = await BuscarCidadePorIbge(request.CodigoMunicipioDestino!.Value);
                    }
                    
                    var viagemResult = await Engine.CommandBus.SendCommandAsync<Domain.Models.Viagem.Viagem>(viagemIntegrar);
                    if (viagemResult.Id == 0)
                    {
                        return new RespPadrao(false, "Não foi possível criar a viagem.");
                    }
                   
                    responseData.Id = viagemResult.Id;
                    responseData.Status = viagemResult.Status.ToInt();
                    responseData.ViagemExternoId = viagemResult.ViagemExternoId;
                    responseData.ValorFrete = viagemResult.ValorFrete.Value;
                    _logger.Info($"Nova viagem criada: ID {viagemResult.Id}, ViagemExternoId: {request.ViagemExternoId}");
                }
                return new RespPadrao(true, "Viagem processada com sucesso.");
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"Erro no processamento da viagem: {request.ViagemExternoId}");
                return new RespPadrao(false, $"Erro no processamento da viagem: {ex.Message}");
            }
        }

        /// <summary>
        /// Processa o vínculo dos veículos com a viagem na tabela ViagemVeiculos
        /// </summary>
        private async Task<RespPadrao> ProcessarVinculoVeiculos(ViagemIntegrarV2Request request, ViagemIntegrarV2Data responseData)
        {
            try
            {
                if (request.Veiculos == null || !request.Veiculos.Any())
                {
                    _logger.Info($"Nenhum veículo para vincular à viagem {responseData.Id}");
                    return new RespPadrao(true, "Nenhum veículo para vincular.");
                }
                
                foreach (var veiculoResponse in responseData.Veiculos)
                {
                    if (veiculoResponse.VeiculoId.HasValue)
                    {
                        var viagemVeiculoCommand = new Domain.Models.ViagemVeiculos.Commands.ViagemVeiculosSalvarCommand
                        {
                            ViagemId = responseData.Id,
                            VeiculoId = veiculoResponse.VeiculoId.Value
                        };

                        await Engine.CommandBus.SendCommandAsync(viagemVeiculoCommand);

                        _logger.Info($"Veículo {veiculoResponse.VeiculoId} vinculado à viagem {responseData.Id}");
                    }
                }

                return new RespPadrao(true, "Veículos vinculados com sucesso à viagem.");
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"Erro ao vincular veículos à viagem {responseData.Id}");
                return new RespPadrao(false, $"Erro ao vincular veículos: {ex.Message}");
            }
        }

        /// <summary>
        /// Converte string de status CIOT para enum StatusCiot
        /// Trata casos onde vem número como string da API externa
        /// </summary>
        /// <param name="statusString">Status como string (pode ser número ou nome)</param>
        /// <returns>StatusCiot enum correspondente</returns>
        private StatusCiot ConverterStatusCiot(string statusString)
        {
            if (string.IsNullOrWhiteSpace(statusString))
            {
                _logger.Warn("StatusCiot veio vazio, usando valor padrão CiotGerado");
                return StatusCiot.CiotGerado;
            }

            // Tentar converter por valor numérico primeiro (caso mais comum)
            if (int.TryParse(statusString.Trim(), out var statusInt))
            {
                // Verificar se o valor numérico existe no enum
                if (Enum.IsDefined(typeof(StatusCiot), statusInt))
                {
                    _logger.Info($"StatusCiot convertido de número: {statusString} -> {(StatusCiot)statusInt}");
                    return (StatusCiot)statusInt;
                }
                else
                {
                    _logger.Warn($"StatusCiot número inválido: {statusString}, usando valor padrão CiotGerado");
                    return StatusCiot.CiotGerado;
                }
            }

            // Tentar converter por nome do enum
            if (Enum.TryParse<StatusCiot>(statusString.Trim(), true, out var statusEnum))
            {
                _logger.Info($"StatusCiot convertido de nome: {statusString} -> {statusEnum}");
                return statusEnum;
            }

            // Se não conseguiu converter, usar valor padrão
            _logger.Warn($"StatusCiot não reconhecido: '{statusString}', usando valor padrão CiotGerado");
            return StatusCiot.CiotGerado;
        }

        private class PortadoresProcessados
        {
            public Domain.Models.Portador.Portador Proprietario { get; set; }
            public Domain.Models.Portador.Portador Motorista { get; set; }
            public Domain.Models.Portador.Portador Contratante { get; set; }
            public Domain.Models.Portador.Portador Remetente { get; set; }
            public Domain.Models.Portador.Portador Destinatario { get; set; }
        }
        
        #endregion
    }
}
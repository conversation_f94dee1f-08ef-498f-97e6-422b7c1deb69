using SistemaInfo.BBC.Application.Objects.Web.ClientSecretAdm;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.BBC.Domain.Models.ClientSecretAdm;
using SistemaInfo.BBC.Domain.Models.ClientSecretAdm.Commands;


namespace SistemaInfo.BBC.Application.AutoMapper.ClientSecretAdmMapper
{
    public class ClientSecretAdmMappingProfile : SistemaInfoMappingProfile
    {
        public ClientSecretAdmMappingProfile()
        {
            CreateMap<ConsultarClientSecretAdmResponse, ClientSecretAdmEditarCommand>();
            
            CreateMap<ClientSecretAdmAlterarStatusRequest, ClientSecretAdmAlterarStatusCommand>();
            
            CreateMap<ClientSecretAdmEditarCommand, ClientSecretAdm>();
            
            CreateMap<ClientSecretAdm, ClientSecretAdmEditarCommand>();
            
            CreateMap<ClientSecretAdm, ClientSecretAdmAdicionarCommand>();
            
            CreateMap<ClientSecretAdmAdicionarCommand, ClientSecretAdm>();
            
            CreateMap<ClientSecretAdmRequest, ClientSecretAdmAdicionarCommand>();
            
            CreateMap<ClientSecretAdmRequest, ClientSecretAdmEditarCommand>();
            
            CreateMap<ClientSecretAdm, ConsultarClientSecretAdmResponse>()
                .ForMember(dest => dest.ClientSecret, opts => opts.MapFrom(s => s.ClientSecret.gerarMascaraClientSecret()));
            
            CreateMap<ClientSecretAdm, ConsultarClientSecretAdmGrid>()
                .ForMember(dest => dest.ClientSecret, opts => opts.MapFrom(s => s.ClientSecret.gerarMascaraClientSecret()))
                .ForMember(dest => dest.SecretKeySearch, opts => opts.MapFrom(s => s.ClientSecret))
                .ForMember(dest => dest.DataCadastro, opts => opts.MapFrom(s => s.DataCadastro.ToString("dd/MM/yyyy HH:mm")))
                .ForMember(dest => dest.DataAlteracao, opts => opts.MapFrom(s => s.DataAlteracao.HasValue ? s.DataAlteracao.Value.ToString("dd/MM/yyyy HH:mm") : ""))
                .ForMember(dest => dest.UsuarioCadastro, opts => opts.MapFrom(s => s.Usuario != null ? s.Usuario.Nome : ""))
                .ForMember(dest => dest.UsuarioAlteracao, opts => opts.MapFrom(s => s.UsuarioAlteracao != null ? s.UsuarioAlteracao.Nome : ""));
        }
    }
}

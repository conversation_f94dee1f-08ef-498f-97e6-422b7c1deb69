using System;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Reflection;
using System.Security.Authentication;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc.Controllers;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using SistemaInfo.BBC.Application.Interface.Usuario;
using SistemaInfo.BBC.Domain.Models.AuthSessionApi.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using SistemaInfo.Framework.DomainDrivenDesign.Infra.DataTransferObjects;
using SistemaInfo.Framework.DomainDrivenDesign.Web.Secutiry.UserSession;
using SistemaInfo.Framework.Utils;
using SistemaInfo.Framework.Web.Security;

namespace SistemaInfo.BBC.ApiIntegracao.Filters
{
    namespace SistemaInfo.Auth.Bus.Common.Infra.Security
{
    /// <summary>
    /// Validação de sessão utilizada por front end
    /// </summary>
    public class JwtAuthorizationHandler : AuthorizationHandler<AppTokenAuthorizationRequirement>
    {
        private readonly IAppEngine _engine;
        private readonly IServiceProvider _serviceProvider;
        private readonly IUsuarioAppService _usuarioAppService;
        private readonly IAuthSessionApiReadRepository _authSessionApiReadRepository;

        /// <summary>
        /// Injeção de dependencias utilizadas na controller
        /// </summary>
        /// <param name="engine"></param>
        /// <param name="serviceProvider"></param>
        /// <param name="usuarioAppService"></param>
        /// <param name="authSessionApiReadRepository"></param>
        public JwtAuthorizationHandler(
            IAppEngine engine,
            IServiceProvider serviceProvider,
            IUsuarioAppService usuarioAppService,
            IAuthSessionApiReadRepository authSessionApiReadRepository)
        {
            _engine = engine;
            _serviceProvider = serviceProvider;
            _usuarioAppService = usuarioAppService;
            _authSessionApiReadRepository = authSessionApiReadRepository;
        }

        /// <summary>
        /// Solicitação de serviço em formato Async
        /// </summary>
        /// <param name="context"></param>
        /// <param name="requirement"></param>
        /// <returns></returns>
        /// <exception cref="AuthenticationException"></exception>
        protected override async Task HandleRequirementAsync(AuthorizationHandlerContext context,
            AppTokenAuthorizationRequirement requirement)
        {
            try
            {
                if (context.Resource is AuthorizationFilterContext mvcContext)
                {
                    // Caso o método não necessite de autenticação
                    var controllerActionDesc = mvcContext.ActionDescriptor as ControllerActionDescriptor;
                    if (controllerActionDesc != null)
                    {
                        var methodInfo = controllerActionDesc.MethodInfo;
                        if (methodInfo.GetCustomAttributes<IgnoreAuthAttribute>().Any())
                        {
                            context.Succeed(requirement);
                            return;
                        }
                    }

                    // Autenticar pelo barramento
                    var sessionToken = mvcContext.HttpContext.Request?.Headers["x-web-auth-token"].FirstOrDefault();

                    if (sessionToken.IsNullOrWhiteSpace())
                        throw new AuthenticationException("Token não identificado!");

                    // DESSERIALIZAR JWT

                    var handler = new JwtSecurityTokenHandler();
                    var tokenS = handler.ReadToken(sessionToken) as JwtSecurityToken;

                    var expDate = tokenS.ValidTo;

                    if (expDate < DateTime.UtcNow)
                        context.Fail();

                    var token = _authSessionApiReadRepository.FirstOrDefault(x => x.Token == sessionToken);

                    var usuario = await _usuarioAppService.Repository.Query
                        .Where(u => u.Id == 1)
                        .Select(u => new
                        {
                            u.Id,
                            u.Nome,
                            u.EmpresaId,
                            u.GrupoEmpresaId
                        })
                        .FirstOrDefaultAsync();

                    if (usuario == null)
                        throw new AuthenticationException($"Usuário não localizado!");


                    var sessionUser = _serviceProvider.GetRequiredService<ISessionUser>() as SessionUser;
                    sessionUser.EmpresaId = token.EmpresaId ?? 0;
                    sessionUser.Id = usuario.Id;
                    sessionUser.Nome = usuario.Nome;
                    sessionUser.AdministradoraId = token.GrupoEmpresaId ?? 0;
                    // Validar no barramento

                    context.Succeed(requirement);
                }
            }
            catch (Exception)
            {
                context.Fail();
                context.Succeed(requirement);
            }
        }
    }
}
}
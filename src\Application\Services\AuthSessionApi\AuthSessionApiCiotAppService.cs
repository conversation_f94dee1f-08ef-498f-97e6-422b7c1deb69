using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using Newtonsoft.Json;
using NLog;
using SistemaInfo.BBC.Application.Helpers;
using SistemaInfo.BBC.Application.Interface.AuthSessionApi;
using SistemaInfo.BBC.Application.Interface.ClientSecret;
using SistemaInfo.BBC.Application.Objects.Api.Token;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Domain.Models.AuthSession.Repository;
using SistemaInfo.BBC.Domain.Models.AuthSessionApi.Repository;
using SistemaInfo.BBC.Domain.Models.Empresa.Repository;
using SistemaInfo.BBC.Domain.Models.GrupoEmpresa.Repository;
using SistemaInfo.BBC.Domain.Models.Parametros.Repository;
using SistemaInfo.BBC.Domain.Models.Usuario.Repository;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.Services.AuthSessionApi;

public class AuthSessionApiCiotAppService : IAuthSessionApiCiotAppService
{
    private readonly IEmpresaReadRepository _empresaReadRepository;
    private readonly IConfiguration _config;
    private readonly IGrupoEmpresaReadRepository _grupoEmpresaReadRepository;
    private readonly IClientSecretAppService _clientSecretAppService;
    private readonly IAuthSessionApiReadRepository _authSessionReadRepository;
    private readonly IUsuarioReadRepository _usuarioReadRepository;
    private readonly IAuthSessionApiWriteRepository _authSessionApiWriteRepository;
    private readonly IParametrosReadRepository _parametrosReadRepository;

    public AuthSessionApiCiotAppService(IEmpresaReadRepository empresaReadRepository, IConfiguration config,
        IGrupoEmpresaReadRepository grupoEmpresaReadRepository, IClientSecretAppService clientSecretAppService,
        IAuthSessionApiReadRepository authSessionReadRepository,
        IAuthSessionApiWriteRepository authSessionApiWriteRepository, IUsuarioReadRepository usuarioReadRepository,
        IParametrosReadRepository parametrosReadRepository)
    {
        _empresaReadRepository = empresaReadRepository;
        _config = config;
        _grupoEmpresaReadRepository = grupoEmpresaReadRepository;
        _clientSecretAppService = clientSecretAppService;
        _authSessionReadRepository = authSessionReadRepository;
        _authSessionApiWriteRepository = authSessionApiWriteRepository;
        _usuarioReadRepository = usuarioReadRepository;
        _parametrosReadRepository = parametrosReadRepository;
    }

    public async Task<RespPadrao> GerarToken(TokenRequestIntegracao tokenRequest)
    {
        var log = LogManager.GetCurrentClassLogger();

        log.Info(JsonConvert.SerializeObject(tokenRequest));

        try
        {
            if (string.IsNullOrWhiteSpace(tokenRequest.Cnpj))
                return new RespPadrao(false, "Autenticação inválida!");

            if (string.IsNullOrWhiteSpace(tokenRequest.Empresa) && string.IsNullOrWhiteSpace(tokenRequest.Cnpj))
                return new RespPadrao(false, "Autenticação inválida!");

            var lEmpresa = new Domain.Models.Empresa.Empresa();
            var lGrupoEmpresa = new Domain.Models.GrupoEmpresa.GrupoEmpresa();
            var lSenhaApiHash = tokenRequest.SenhaApi.GetHashSha1();

            if (!tokenRequest.Cnpj.IsNullOrWhiteSpace() && !tokenRequest.Empresa.IsNullOrWhiteSpace())
            {
                var grupoEmpresa = await _grupoEmpresaReadRepository.AsNoTracking()
                    .Include(x => x.Empresa)
                    .Include(x => x.ClientSecret)
                    .FirstOrDefaultAsync(x => x.Cnpj == tokenRequest.Cnpj && x.Ativo == 1);

                if (grupoEmpresa !=
                    null) //Cnpj é de grupo de empresa, campos especificos de Empresa se tornam obrigatórios
                {
                    var lValidacaoGrupoEmpresa = ValidaGrupoEmpresa(grupoEmpresa, tokenRequest, lSenhaApiHash);
                    lGrupoEmpresa = grupoEmpresa;
                    lEmpresa = null;

                    if (!lValidacaoGrupoEmpresa.sucesso)
                        return lValidacaoGrupoEmpresa;
                }
            }
            else
            {
                var empresa = await _empresaReadRepository.AsNoTracking()
                    .Include(x => x.ClientSecret)
                    .FirstOrDefaultAsync(x => x.Cnpj == tokenRequest.Cnpj && x.Ativo == 1);

                if (empresa != null) //Cnpj não é de Grupo de Empresa, usar os campos Cnpj, SenhaAPI e ClientSecret para validações
                {
                    var lValidacaoEmpresa = ValidaEmpresa(empresa, tokenRequest.ClientSecret, lSenhaApiHash);

                    lEmpresa = empresa;
                    lGrupoEmpresa = null;

                    if (!lValidacaoEmpresa.sucesso)
                    {
                        return lValidacaoEmpresa;
                    }
                }
            }

            var empresaLogada = lEmpresa ?? lGrupoEmpresa?.Empresa.FirstOrDefault(a => a.Cnpj == tokenRequest.Empresa);
            var jwtToken = GerarJwtToken();

            await SaveToken(empresaLogada,
                jwtToken);
            await _clientSecretAppService.DesativarClientSecretExpirada();
            return new RespPadrao()
            {
                sucesso = true,
                mensagem = "",
                data = new { jwtToken }
            };
        }
        catch (Exception e)
        {
            log.Error(e);
            return new RespPadrao(false, "Autenticação invalida!");
        }
    }

    private string GerarJwtToken()
    {
        var claims = new List<Claim>()
        {
            new(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString("N"))
        };

        var secretKey = Encoding.UTF8.GetString(Convert.FromBase64String(_config["Authentication:SecretKey"]));
        var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(secretKey));
        var credential = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);
        var intervaloValidade = 600;
        var dataCriacao = DateTime.UtcNow;
        var dataExpiracao = dataCriacao + TimeSpan.FromSeconds(intervaloValidade);

        var token = new JwtSecurityToken
        (
            claims: claims,
            signingCredentials: credential,
            expires: dataExpiracao,
            issuer: "MyServer",
            audience: "EveryApplication"
        );

        return new JwtSecurityTokenHandler().WriteToken(token);
    }

    public string GerarJwtTokenInterno()
    {
        try
        {
            new LogHelper().LogOperationStart("GerarJwtTokenInterno");
            var claims = new[]
            {
                new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString("N")),
                new Claim("EmpresaId", "1")
            };

            var secretKey =
                Encoding.UTF8.GetString(
                    Convert.FromBase64String(_config["Authentication:SecretKey"] ?? "I0FHSntGa3FkRzEscEtwRUphR0o="));
            var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(secretKey));
            var credential = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);
            var intervaloValidade = 120;
            var dataCriacao = DateTime.UtcNow;
            var dataExpiracao = dataCriacao + TimeSpan.FromSeconds(intervaloValidade);

            var token = new JwtSecurityToken
            (
                claims: claims,
                signingCredentials: credential,
                expires: dataExpiracao,
                issuer: "MyServer",
                audience: "EveryApplication"
            );

            return new JwtSecurityTokenHandler().WriteToken(token);
        }
        catch (Exception ex)
        {
            new LogHelper().Error(ex, "Erro ao executar GerarJwtTokenInterno");
            throw;
        }
        finally
        {
            new LogHelper().LogOperationEnd("GerarJwtTokenInterno");
        }
    }

    private async Task SaveToken(Domain.Models.Empresa.Empresa empresa, string token)
    {
        var lAuthSession = _authSessionReadRepository.Where(x => x.EmpresaId == empresa.Id)?.FirstOrDefault();

        if (lAuthSession == null)
        {
            lAuthSession = new BBC.Domain.Models.AuthSessionApi.AuthSessionApi()
            {
                Token = token,
                EmpresaId = empresa.Id,
                DataCadastro = DateTime.Now,
                GrupoEmpresaId = empresa.GrupoEmpresaId
            };

            await _authSessionApiWriteRepository.AddAsync(lAuthSession);
        }
        else
        {
            lAuthSession.Token = token;
            lAuthSession.DataUltimaReq = DateTime.Now;
            _authSessionApiWriteRepository.Update(lAuthSession);
        }

        await _authSessionApiWriteRepository.SaveChangesAsync();
    }

    private RespPadrao ValidaEmpresa(Domain.Models.Empresa.Empresa empresa, string clientSecretEmpresa,
        string tokenSenha)
    {
        if (empresa.GrupoEmpresaId != null)
        {
            return new RespPadrao()
            {
                mensagem = "Autenticação inválida!",
                sucesso = false
            };
        }

        var lClientSecret = empresa.ClientSecret.FirstOrDefault(x =>
            x.SecretKey == clientSecretEmpresa && x.Senha == tokenSenha && x.Ativo == 1 &&
            x.IdEmpresa == empresa.Id);


        if (lClientSecret == null)
        {
            return new RespPadrao()
            {
                mensagem = "Autenticação inválida!",
                sucesso = false
            };
        }

        if (lClientSecret.DataExpiracao.HasValue)
        {
            if (lClientSecret.DataExpiracao < DateTime.Now)
            {
                return new RespPadrao()
                {
                    mensagem = "Autenticação inválida!",
                    sucesso = false
                };
            }
        }

        return new RespPadrao()
        {
            sucesso = true,
            mensagem = "Autenticado com sucesso!"
        };
    }

    private RespPadrao ValidaGrupoEmpresa(Domain.Models.GrupoEmpresa.GrupoEmpresa grupoEmpresa,
        TokenRequestIntegracao tokenRequest, string tokenSenha)
    {
        //Se tem algum ClientSecret expirado para o grupo
        var lClientSecret = grupoEmpresa.ClientSecret.FirstOrDefault(x =>
            x.SecretKey == tokenRequest.ClientSecret && x.Senha == tokenSenha &&
            x.Ativo == 1);

        if (lClientSecret == null)
        {
            return new RespPadrao()
            {
                mensagem = "Autenticação inválida!",
                sucesso = false
            };
        }

        if (lClientSecret.DataExpiracao.HasValue)
        {
            if (lClientSecret.DataExpiracao < DateTime.Now)
            {
                return new RespPadrao()
                {
                    mensagem = "Autenticação inválida!",
                    sucesso = false
                };
            }
        }

        var empresa = grupoEmpresa.Empresa
            .FirstOrDefault(x => x.Cnpj == tokenRequest.Empresa && x.Ativo == 1);

        if (empresa == null)
        {
            return new RespPadrao()
            {
                mensagem = "Autenticação invalida!",
                sucesso = false
            };
        }

        return new RespPadrao()
        {
            sucesso = true,
            mensagem = "Autenticado com sucesso!"
        };
    }
}
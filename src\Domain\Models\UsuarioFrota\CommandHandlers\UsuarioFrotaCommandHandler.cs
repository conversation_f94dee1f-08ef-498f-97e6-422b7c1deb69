using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using NLog;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Domain.Models.Portador.Exeptions;
using SistemaInfo.BBC.Domain.Models.Portador.Repository;
using SistemaInfo.BBC.Domain.Models.UsuarioFrota.Commands;
using SistemaInfo.Framework.CQRS;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using SistemaInfo.Framework.DomainDrivenDesign.Infra.CQRS;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Domain.Models.UsuarioFrota.CommandHandlers
{
    public class UsuarioFrotaCommandHandler :
        CommandHandler<Portador.Portador, IPortadorReadRepository, IPortadorWriteRepository>,
        I<PERSON>andler<UsuarioFrotaSalvarCommand>,
        IHandler<UsuarioFrotaAlterarStatusCommand>, 
        <PERSON><PERSON><PERSON>ler<UsuarioFrotaBloquearCommand>,
        <PERSON><PERSON><PERSON>ler<UsuarioFrotaCancelarCommand>, 
        <PERSON><PERSON><PERSON>ler<UsuarioFrotaResetarTentativasCommand>,
        <PERSON><PERSON><PERSON><PERSON><UsuarioFrotaSenhaErradaCommand>,
        IHandler<UsuarioFrotaSalvarComRetornoCommand, Portador.Portador>
    {
        public UsuarioFrotaCommandHandler(IAppEngine engine, IPortadorReadRepository readRepository, IPortadorWriteRepository writeRepository) : base(engine, readRepository, writeRepository)
        {
        }

        public async Task HandlerAsync(UsuarioFrotaSalvarCommand request)
        {
            var lLog = LogManager.GetCurrentClassLogger();
            try
            {
                var lPortador = Mapper.Map<Portador.Portador>(request);

                if (lPortador.TipoPessoa <= 0)
                {
                    if (lPortador.CpfCnpj.OnlyNumbers().Length <= 11)
                    {
                        lPortador.TipoPessoa = ETipoPessoa.Fisica;
                    }
                    else
                    {
                        lPortador.TipoPessoa = ETipoPessoa.Juridica;
                    }
                }
                
                lPortador.PortadorRepresentanteLegal = new List<PortadorRepresentanteLegal.PortadorRepresentanteLegal>();
                lPortador.PortadorCentroCusto = new List<PortadorCentroCusto.PortadorCentroCusto>();
                
                lPortador.UsuarioCadastroId = User.Id > 0 ? User.Id : 1;
    
                if (lPortador.Id == 0)
                {
                    //recurso para codificar senha para uso em api mobile
                    lPortador.SenhaApi = request.SenhaApi.GetHashSha1();
                    lPortador.DataCadastro = DateTime.Now;
                    lPortador.Ativo = 1;
                    lPortador.SenhaProvisoria = request.SenhaProvisoria == 1 ? 1 : 0;
                    
                    foreach (var lPortadorCentroCusto in request.PortadorCentroCusto)
                    {
                        lPortador.PortadorCentroCusto.Add(new PortadorCentroCusto.PortadorCentroCusto()
                        {
                            CentroCustoId = lPortadorCentroCusto.CentroCustoId
                        });
                    }
                    
                    Repository.Command.Add(lPortador);
                }
                else
                {
                    Repository.Command.RemoveRepresentanteLegal(request.Id);
                    Repository.Command.RemoveCentroCusto(request.Id);
                    lPortador = Repository.Query.GetById(request.Id);
                    
                    // request.Visibilidade = lPortador.Visibilidade;
                    // request.CiotTacAgregado = lPortador.CiotTacAgregado;
                    
                    Mapper.Map(request, lPortador);
                    lPortador.Ativo = 1;
                    lPortador.SenhaProvisoria = request.SenhaProvisoria == 1 ? 1 : 0;
                    if (Engine.User.EmpresaId != request.EmpresaIdFrota &&
                                !Engine.User.IsNivelSuperUsuario &&
                                !Engine.User.IsNivelAdministradora)
                    {
                        lPortador.EmpresaIdFrota = Engine.User.EmpresaId;
                    }
                    if (!lPortador.DataUltimoAcesso.HasValue)
                    {
                        lPortador.DataUltimoAcesso = DateTime.Now;
                    }
                    
                    //recurso para remover bloqueio de acesso ao login da api mobile
                    if (request.QuantidadeErroSenha != 3)
                    {
                        lPortador.DataDesbloqueioMobile = DateTime.Now;
                        lPortador.UsuarioDesbloqueioMobileId = 1;
                    }
                    
                    lPortador.PortadorCentroCusto = new List<PortadorCentroCusto.PortadorCentroCusto>();

                    if (request.PortadorCentroCusto != null)
                    {
                        foreach (var lPortadorCentroCusto in request.PortadorCentroCusto)
                        {
                            lPortador.PortadorCentroCusto.Add(new PortadorCentroCusto.PortadorCentroCusto()
                            {
                                PortadorId = lPortador.Id,
                                CentroCustoId = lPortadorCentroCusto.CentroCustoId
                            });
                        }
                    }
                    Repository.Command.Update(lPortador);
                }
    
                await SaveChangesAsync();
            
            }
            catch (Exception e)
            {
                lLog.Error(e,"Erro ao salvar Usuário Frota, erro: ");
                throw new PortadorInvalidException("Erro ao salvar Usuário Frota!");
            }
        }
        
        public async Task HandlerAsync(UsuarioFrotaAlterarStatusCommand request)
        {
            var lPortador =  await Repository.Query.GetByIdAsync(request.Id);
            if( !Engine.User.IsNivelSuperUsuario &&
                !Engine.User.IsNivelAdministradora)
                lPortador.Ativo = 
                    lPortador.EmpresaIdFrota == Engine.User.EmpresaId ? lPortador.Ativo : 0;
            
            if (lPortador.Ativo == 1)
            {
                lPortador.Ativo = 0;
                lPortador.DataBloqueio = DateTime.Now;
                lPortador.UsuarioBloqueioId = User.Id > 0 ? User.Id : 1;
            }
            else
            {
                lPortador.Ativo = 1;
                lPortador.DataDesbloqueio = DateTime.Now;
                lPortador.UsuarioDesbloqueioId = User.Id > 0 ? User.Id : 1;
                lPortador.QuantidadeErroSenha = 0;
                if (Engine.User.EmpresaId != request.EmpresaIdFrota &&
                    !Engine.User.IsNivelSuperUsuario &&
                    !Engine.User.IsNivelAdministradora)
                {
                    lPortador.EmpresaIdFrota = Engine.User.EmpresaId;
                }
            }
            
            await SaveChangesAsync();
        }
        
        public async Task HandlerAsync(UsuarioFrotaBloquearCommand request)
        {
            var lPortador =  await Repository.Query.GetByIdAsync(request.Id);
            if (lPortador.Status == EStatusPortador.Bloqueado)
            {
                //lPortador.Ativo = 1;
                lPortador.Status = EStatusPortador.Normal;
                lPortador.QuantidadeErroSenha = 0;
                if (!request.Mobile)
                {
                    lPortador.DataDesbloqueio = DateTime.Now;
                }
                else
                {
                    lPortador.DataDesbloqueioMobile = DateTime.Now;
                }
                lPortador.UsuarioDesbloqueioId = User.Id > 0 ? User.Id : 1;
                if (Engine.User.EmpresaId != request.EmpresaIdFrota &&
                    !Engine.User.IsNivelSuperUsuario &&
                    !Engine.User.IsNivelAdministradora)
                {
                    lPortador.EmpresaIdFrota = Engine.User.EmpresaId;
                }
            }
            else
            {
                //lPortador.Ativo = 0;
                lPortador.Status = EStatusPortador.Bloqueado;
                if (!request.Mobile)
                {
                    lPortador.DataBloqueio = DateTime.Now;
                }
                else
                {
                    lPortador.DataBloqueioMobile = DateTime.Now;
                }
                lPortador.UsuarioBloqueioId = User.Id > 0 ? User.Id : 1;
            }
            await SaveChangesAsync();
        }

        
        public async Task HandlerAsync(UsuarioFrotaCancelarCommand request)
        {
            var lPortador =  await Repository.Query.GetByIdAsync(request.Id);
            
            if (lPortador.Status == EStatusPortador.Cancelado)
            {
                lPortador.QuantidadeErroSenha = 0;
                lPortador.Status = EStatusPortador.Normal;
                lPortador.MotivoCancelamento = request.Motivo;
                lPortador.UsuarioCancelamentoId = User.Id > 0 ? User.Id : 1;
                if (Engine.User.EmpresaId != request.EmpresaIdFrota &&
                    !Engine.User.IsNivelSuperUsuario &&
                    !Engine.User.IsNivelAdministradora)
                {
                    lPortador.EmpresaIdFrota = Engine.User.EmpresaId;
                }
            }
            else
            {
                lPortador.Status = EStatusPortador.Cancelado;
                lPortador.DataCancelamento = DateTime.Now;
                lPortador.MotivoCancelamento = request.Motivo;
                lPortador.UsuarioCancelamentoId = User.Id > 0 ? User.Id : 1;
            }
            
            await SaveChangesAsync();
        }
        
        public async Task HandlerAsync(UsuarioFrotaResetarTentativasCommand command)
        {
            var lPortador = await Repository.Query.GetByIdAsync(command.Id);
            lPortador.QuantidadeErroSenha = 0;
            await SaveChangesAsync();
        }
        
        public async Task HandlerAsync(UsuarioFrotaSenhaErradaCommand command)
        {
            var lPortador = await Repository.Query.GetByIdAsync(command.Id);
            
            lPortador.QuantidadeErroSenha++;

            if (lPortador.QuantidadeErroSenha >= command.TotalTentativas)
            {
                lPortador.DataBloqueioMobile = DateTime.Now;
                lPortador.Status = EStatusPortador.Bloqueado;
                lPortador.UsuarioBloqueioId = User.Id > 0 ? User.Id : 1;
            }
                
            await SaveChangesAsync();
        }
        
        public async Task<Portador.Portador> HandlerAsync(UsuarioFrotaSalvarComRetornoCommand request)
        {
            var lLog = LogManager.GetCurrentClassLogger();
            try
            {
                var lPortador = Mapper.Map<Portador.Portador>(request);
                lPortador.TipoPessoa = ETipoPessoa.Fisica;
                lPortador.Atividade = EAtividade.Frota;
                lPortador.Bairro = "";
                lPortador.NumeroCNH = lPortador.CNH;
                
                lPortador.PortadorRepresentanteLegal = new List<PortadorRepresentanteLegal.PortadorRepresentanteLegal>();
                lPortador.UsuarioCadastroId = User.Id > 0 ? User.Id : 1;
    
                if (lPortador.Id == 0)
                {
                    //recurso para codificar senha para uso em api mobile
                    if (request.SenhaApi.IsNullOrWhiteSpace())
                    {
                        Random rdn = new Random();
                        
                        var senhaNova = rdn.Next(100000, 999999);
                        
                        request.SenhaApi = senhaNova.ToString();
                    }
                    lPortador.SenhaApi = request.SenhaApi.GetHashSha1();
                    
                    lPortador.DataCadastro = DateTime.Now;
                    lPortador.Ativo = 1;
                    lPortador.SenhaProvisoria = request.SenhaProvisoria == 1 ? 1 : 0;
                  
                    Repository.Command.Add(lPortador);
                }
                else
                {
                    Repository.Command.RemoveRepresentanteLegal(request.Id);
                    Repository.Command.RemoveCentroCusto(request.Id);
                    lPortador = Repository.Query.GetById(request.Id);
                    
                    if (Engine.User.EmpresaId != request.EmpresaIdFrota &&
                        !Engine.User.IsNivelSuperUsuario &&
                        !Engine.User.IsNivelAdministradora)
                    {
                        lPortador.EmpresaIdFrota = Engine.User.EmpresaId;
                    }
                    Mapper.Map(request, lPortador);
                    lPortador.Ativo = 1;
                    lPortador.SenhaProvisoria = request.SenhaProvisoria == 1 ? 1 : 0;
                    
                    //recurso para remover bloqueio de acesso ao login da api mobile
                    if (request.QuantidadeErroSenha != 3)
                    {
                        lPortador.DataDesbloqueioMobile = DateTime.Now;
                        lPortador.UsuarioDesbloqueioMobileId = 1;
                    }
                    
                    if (lPortador.Status == EStatusPortador.Cancelado)
                    {
                        lPortador.Ativo = 1;
                        lPortador.Status = EStatusPortador.Normal;
                        lPortador.MotivoCancelamento = "";
                        lPortador.DataCancelamento = null;
                        lPortador.QuantidadeErroSenha = 0;
                        lPortador.UsuarioBloqueioId = null;
                        lPortador.UsuarioCancelamentoId = null;
                        lPortador.UsuarioCadastroId = User.Id > 0 ? User.Id : 1;
                        lPortador.DataCadastro = DateTime.Now;
                        lPortador.UsuarioDesbloqueioId = null;
                        lPortador.DataDesbloqueio = null;
                    }
                    
                    lPortador.PortadorRepresentanteLegal = new List<PortadorRepresentanteLegal.PortadorRepresentanteLegal>();
                    
                    lPortador.PortadorCentroCusto = new List<PortadorCentroCusto.PortadorCentroCusto>();
                    if (request.PortadorCentroCusto != null)
                    {
                        foreach (var lPortadorCentroCusto in request.PortadorCentroCusto)
                        {
                            lPortador.PortadorCentroCusto.Add(new PortadorCentroCusto.PortadorCentroCusto()
                            {
                                PortadorId = lPortador.Id,
                                CentroCustoId = lPortadorCentroCusto.CentroCustoId
                            });
                        }
                    }
                    Repository.Command.Update(lPortador);
                }
                await SaveChangesAsync();
                return lPortador;
            }
            catch (Exception e)
            {
                lLog.Error(e,"Erro ao salvar Usuário Frota, erro: ");
                throw new PortadorInvalidException("Erro ao salvar Usuário Frota!");
            }
        }
    }
}
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using SistemaInfo.BBC.Domain.Models.Viagem;
using SistemaInfo.Framework.EntityFramework.Configuration;

namespace SistemaInfo.BBC.Infra.Data.Mappings
{
    public class ViagemMapping : EntityTypeConfiguration<Viagem>
    {
        public override void Map(EntityTypeBuilder<Viagem> builder)
        {
            builder.ToTable("Viagem");
            builder.HasKey(b => b.Id);
            
            builder.Property(b => b.Id).IsRequired().HasColumnName("Id").ValueGeneratedOnAdd();
            builder.Property(b => b.EmpresaId).IsRequired().HasColumnName("EmpresaId").HasColumnType("int");
            builder.Property(b => b.Status).HasColumnName("Status").HasColumnType("int");
            builder.Property(b => b.FilialId).HasColumnName("FilialId").HasColumnType("varchar(100)");
            
            builder.Property(b => b.PortadorProprietarioId).IsRequired().HasColumnName("PortadorProprietarioId").HasColumnType("int");
            builder.Property(b => b.NomeProprietario).HasColumnName("NomeProprietario").HasColumnType("varchar(200)");

            builder.Property(b => b.PortadorMotoristaId).IsRequired().HasColumnName("PortadorMotoristaId").HasColumnType("int");
            builder.Property(b => b.NomeMotorista).HasColumnName("NomeMotorista").HasColumnType("varchar(200)");

            builder.Property(b => b.CiotViagemId).HasColumnName("CiotViagemId").HasColumnType("int");
            builder.Property(b => b.ViagemExternoId).HasColumnName("ViagemExternoId").HasColumnType("int");
            
            builder.Property(b => b.CidadeOrigemId).HasColumnName("CidadeIdOrigem").HasColumnType("int");
            builder.Property(b => b.CidadeDestinoId).HasColumnName("CidadeIdDestino").HasColumnType("int");
            
            builder.Property(b => b.TipoBanco).HasColumnName("TipoBanco").HasColumnType("int");
            builder.Property(b => b.PagamentoExternoId).HasColumnName("PagamentoExternoId").HasColumnType("int");
            builder.Property(b => b.Agencia).HasColumnName("Agencia").HasColumnType("varchar(150)");
            builder.Property(b => b.Conta).HasColumnName("Conta").HasColumnType("varchar(150)");
            builder.Property(b => b.TipoConta).HasColumnName("TipoConta").HasColumnType("int");

            builder.Property(b => b.DataBaixa).HasColumnName("DataBaixa").HasColumnType("timestamp");
            
            builder.Property(b => b.UsuarioCadastroId).HasColumnName("UsuarioCadastroId").HasColumnType("int");
            builder.Property(b => b.DataCadastro).IsRequired().HasColumnName("DataCadastro").HasColumnType("timestamp");
            builder.Property(b => b.UsuarioAlteracaoId).HasColumnName("UsuarioAlteracaoId").HasColumnType("int");
            builder.Property(b => b.DataAlteracao).HasColumnName("DataAlteracao").HasColumnType("timestamp");
            builder.Property(b => b.Ciot).HasColumnName("Ciot").HasColumnType("varchar(36)");
            builder.Property(b => b.VerificadorCiot).HasColumnName("VerificadorCiot").HasColumnType("varchar(12)");
            builder.Property(b => b.CodigoNaturezaCarga).HasColumnName("CodigoNaturezaCarga").HasColumnType("varchar(15)");
            builder.Property(b => b.PesoCarga).HasColumnName("PesoCarga").HasColumnType("numeric(13,2)");

            builder.HasOne(b => b.UsuarioCadastro).WithMany().HasForeignKey(b => b.UsuarioCadastroId);
            builder.HasOne(b => b.UsuarioAlteracao).WithMany().HasForeignKey(b => b.UsuarioAlteracaoId);
            builder.HasOne(b => b.Empresa).WithMany().HasForeignKey(b => b.EmpresaId);
            builder.HasOne(b => b.PortadorMotorista).WithMany().HasForeignKey(b => b.PortadorMotoristaId);
            builder.HasOne(b => b.PortadorProprietario).WithMany().HasForeignKey(b => b.PortadorProprietarioId);
            builder.HasOne(b => b.CiotViagem).WithMany().HasForeignKey(b => b.CiotViagemId);
            builder.HasOne(b => b.CidadeOrigem).WithMany().HasForeignKey(b => b.CidadeOrigemId);
            builder.HasOne(b => b.CidadeDestino).WithMany().HasForeignKey(b => b.CidadeDestinoId);
        }
    }
}
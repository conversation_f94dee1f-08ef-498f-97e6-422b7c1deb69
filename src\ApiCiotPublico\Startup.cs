﻿using AutoMapper;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc.Authorization;
using Microsoft.AspNetCore.Mvc.Formatters;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using SistemaInfo.BBC.Infra.CrossCutting.IoC;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using SistemaInfo.Framework.DomainDrivenDesign.Web.Filters;
using SistemaInfo.Framework.DomainDrivenDesign.Web.Secutiry.UserSession;
using SistemaInfo.Framework.DomainDrivenDesign.Web.Secutiry.UserSession.Swagger;
using SistemaInfo.Framework.DomainDrivenDesign.Web.Swagger;
using SistemaInfo.Framework.Web.Filters;
using Swashbuckle.AspNetCore.Swagger;
using System;
using System.IO;
using System.Text;
using DinkToPdf;
using DinkToPdf.Contracts;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.Extensions.FileProviders;
using Microsoft.IdentityModel.Tokens;
using Newtonsoft.Json.Converters;
using SistemaInfo.BBC.Infra.Bus;
using SistemaInfo.Framework.Utils.AppConfiguration;
using SistemaInfo.Framework.Web.Utils;

namespace SistemaInfo.BBC.ApiCiotPublico
{
    /// <summary>
    ///
    /// </summary>
    public class Startup
    {
        /// <inheritdoc />
        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        /// <summary>
        ///
        /// </summary>
        public IConfiguration Configuration { get; }

        /// <summary>
        ///
        /// </summary>
        /// <param name="services"></param>
        public void ConfigureServices(IServiceCollection services)
        {
            services
                .AddMvc(opts =>
                {
                    opts.Filters.Add<ApiActionExceptionFilter>();
                    opts.Filters.Add<ApiFaultResultCodeFilter>();
                    opts.OutputFormatters.Add(new XmlDataContractSerializerOutputFormatter());
                    opts.InputFormatters.Add(new XmlDataContractSerializerInputFormatter());
                })
                .AddJsonOptions(opts => opts.SerializerSettings.Converters.Add(new StringEnumConverter()));


            services.AddAutoMapper();
            services.AddSwaggerGen(c =>
            {
                c.SwaggerDoc("v1", new Info {Title = "/BBC/ApiCiotPublico", Version = "v1"});
                c.OperationFilter<FileUploadOperation>();
                c.OperationFilter<WebSessionTokenHeaderParameter>();
                //c.OperationFilter<AuditUserDocHeaderParameter>(); // AuditUserDocHeaderParameter está momentaneamente, o correto é gerar o web session token para idnetificação da sessão do usuário, e envia-lo no campo acima
                c.SchemaFilter<SwaggerExcludeFilter>();
                c.DescribeAllEnumsAsStrings();

                var basePath = AppContext.BaseDirectory;
                var lXmlPath = Path.Combine(basePath, "App_Data",
                    System.Reflection.Assembly.GetExecutingAssembly().GetName().Name + ".xml");
                c.IncludeXmlComments(lXmlPath);
            });
            
            
            services.AddCors(options =>
            {
                options.AddPolicy("AllowAngularDevOrigin",
                    builder => builder
                        .AllowAnyOrigin()  // Permite qualquer origem durante o desenvolvimento
                        .AllowAnyHeader()
                        .AllowAnyMethod()
                        .SetIsOriginAllowed(origin => true));
            });
            //
            services.AddSingleton(typeof(IConverter), new SynchronizedConverter(new PdfTools()));
            RegisterServices(services, Configuration);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="app"></param>
        /// <param name="env"></param>
        /// <param name="httpAccessor"></param>
        /// <param name="appConfiguration"></param>
        /// <param name="serviceProvider"></param>
        public void Configure(IApplicationBuilder app, IHostingEnvironment env, IHttpContextAccessor httpAccessor, AppConfiguration appConfiguration, IServiceProvider serviceProvider)
        {
            app.UseCors("AllowAngularDevOrigin");
            app.UseLogForApplicationEvents();
            app.UseSistemaInfoFramework(() => httpAccessor?.HttpContext?.RequestServices ?? serviceProvider);

            app.UsePathBase("/BBC/ApiCiotPublico");
            
            app.Use(async (context, next) =>
            {
                if (context.Request.Method == "OPTIONS")
                {
                    context.Response.StatusCode = 200;
                    await context.Response.Body.FlushAsync();
                    return;
                }

                await next();
            });
            
            app.UseCustomStatusCodePages();
            app.UseCustomDefaultFiles();
            app.UseCustomStaticFiles();
            
            app.UseMiddleware<AuthenticationExceptionHandlingMiddleware>();
            
            app.UseMvc(routes =>
            {
                routes.MapRoute("DefaultApi", "BBC/ApiCiotPublico/{controller}/{action}/{id}");
            });
            
            SetSwagger(app);
            //app.UseAuthentication();
        }

        private void SetSwagger(IApplicationBuilder app)
        {
#if DEBUG
            app.UseSwagger();
                var swaggerEndpoint = "/Web/swagger/v1/swagger.json";

                // appsettings 
                // "Swagger": {
                //     "SwaggerEndpoint": "/BBC/ApiCiot/swagger/v1/swagger.json"
                // }
                if (!string.IsNullOrWhiteSpace(Configuration["Swagger:SwaggerEndpoint"]))
                    swaggerEndpoint = Configuration["Swagger:SwaggerEndpoint"];
                app.UseSwaggerUI(c => { c.SwaggerEndpoint(swaggerEndpoint, "/BBC/ApiCiotPublico"); });
#endif
        }

        private static void RegisterServices(IServiceCollection serviceCollection, IConfiguration configuration)
        {
            DependencyInjector.RegisterServices(serviceCollection);
            MessageBusDependencyInjector.RegisterServices(serviceCollection, configuration);
            DependencyInjectorApiCiotPublico.RegisterServices(serviceCollection);
        }
    }
}
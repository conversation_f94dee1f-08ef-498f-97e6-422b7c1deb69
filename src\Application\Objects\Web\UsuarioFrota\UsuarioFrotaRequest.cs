﻿using System;
using System.Collections.Generic;
using SistemaInfo.BBC.Application.Objects.Web.PortadorCentroCusto;
using SistemaInfo.BBC.Domain.Enum;

namespace SistemaInfo.BBC.Application.Objects.Web.UsuarioFrota;

public class UsuarioFrotaRequest
{
        public string Id { get; set; }
        public int TipoPessoa { get; set; }
        public EAtividade? Atividade { get; set; }
        public string CpfCnpj { get; set; }
        public string Nome { get; set; }
        public string Email { get; set; }
        public string Cnh { get; set; }
        public string Celular { get; set; }
        public DateTime? DataEmissaoCNH { get; set; }
        public DateTime? DataVencimentoCNH { get; set; }
        public string Placa { get; set; }
        
        public bool ControlaAbastecimentoCentroCusto { get; set; }
        public int? EmpresaId { get; set; }
        public int? CarretaId { get; set; }
        public int? Carreta2Id { get; set; }
        public int? Carreta3Id { get; set; }
        public List<PortadorCentroCustoRequest> PortadorCentroCusto { get; set; }
        
        public DateTime? DataCriacaoSenha { get; set; }
        public DateTime? DataUltimoAcesso { get; set; }
        //campos adicionais api mobile
        public string SenhaApi { get; set; }
        public int QuantidadeErroSenha { get; set; } = 0;
        
        // public string Id { get; set; }
        // public int TipoPessoa { get; set; }
        // public string CpfCnpj { get; set; }
        // public string Nome { get; set; }
        // public string Celular { get; set; }
        // public string Telefone { get; set; }
        // public string Email { get; set; }
        // public string Cep { get; set; }
        // public int EstadoId { get; set; }
        // public int CidadeId { get; set; }
        // public string UfEstado { get; set; }
        // public string NomeCidade { get; set; }
        // public string Endereco { get; set; }
        // public string Bairro { get; set; }
        // public string EnderecoNumero { get; set; }
        // public string Complemento { get; set; }
        // public string RNTRC { get; set; }
        // public string NomeMae { get; set; }
        // public string NomePai { get; set; }
        // public int? Sexo { get; set; }
        // public string NumeroIdentidade { get; set; }
        // public string OrgaoEmissor { get; set; }
        // public int? UfEmissao { get; set; }
        // public string UfEmissaoSigla { get; set; }
        // public string EmissaoIdentidade { get; set; }
        // public string RazaoSocial { get; set; }
        // public string InscricaoEstadual { get; set; }
        // public DateTime? DataAberturaEmpresa { get; set; }
        // public string FormaConstituicao { get; set; }
        // public string Cnae { get; set; }
        // public string DataNascimento { get; set; }
        // public int? CartaoId { get; set; }
        // public string Motivo { get; set; }
        // public List<PortadorRepLegalRequest> RepLegaisList { get; set; }
        // public string NaturezaJuridica { get; set; }
        // public bool? IsPep { get; set; }
        // public int? EmpresaId { get; set; }
        // public EAtividade? Atividade { get; set; }
        // public string Placa { get; set; }
        // public int? CarretaId { get; set; }
        // public int? Carreta2Id { get; set; }
        // public int? Carreta3Id { get; set; }
        // public bool ControlaAbastecimentoCentroCusto { get; set; }
        // public List<PortadorCentroCustoRequest> portadorCentroCusto { get; set; }
        // public EVisibilidadePortador Visibilidade { get; set; }
        //
        // //campos adicionais api mobile
        // public string SenhaApi { get; set; }
        // public int QuantidadeErroSenha { get; set; } = 0;
        //
        // //Colunas adicionais para controle de acesso mobile
        // public DateTime? DataCriacaoSenha { get; set; }
        // public DateTime? DataUltimoAcesso { get; set; }
}
using System.Linq;
using Microsoft.AspNetCore.Mvc.ApiExplorer;
using Swashbuckle.AspNetCore.Swagger;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace SistemaInfo.BBC.ApiIntegracao.Filters
{
    /// <summary>
    /// Filtro para organizar endpoints do Swagger por versão usando o ApiVersionDescriptionProvider
    /// </summary>
    public class ApiVersionDocumentFilter : IDocumentFilter
    {
        /// <summary>
        /// Aplica o filtro para separar as versões no Swagger baseado nos atributos ApiVersion dos controllers
        /// </summary>
        /// <param name="swaggerDoc">Documento do Swagger</param>
        /// <param name="context">Contexto da documentação</param>
        public void Apply(SwaggerDocument swaggerDoc, DocumentFilterContext context)
        {
            var version = swaggerDoc.Info.Version;
            var pathsToRemove = swaggerDoc.Paths
                .Where(pathItem => ShouldRemovePath(pathItem.Key, pathItem.Value, version, context))
                .Select(x => x.Key)
                .ToList();

            foreach (var path in pathsToRemove)
            {
                swaggerDoc.Paths.Remove(path);
            }
        }

        /// <summary>
        /// Determina se um path deve ser removido baseado na versão e nos atributos ApiVersion
        /// </summary>
        /// <param name="path">Caminho da API</param>
        /// <param name="pathItem">Item do caminho</param>
        /// <param name="version">Versão atual do documento</param>
        /// <param name="context">Contexto da documentação</param>
        /// <returns>True se deve remover, False caso contrário</returns>
        private bool ShouldRemovePath(string path, PathItem pathItem, string version, DocumentFilterContext context)
        {
            if (version.Equals("v1.0", System.StringComparison.OrdinalIgnoreCase))
            {
                return path.StartsWith("/v2/", System.StringComparison.OrdinalIgnoreCase);
            }
            if (version.Equals("v2.0", System.StringComparison.OrdinalIgnoreCase))
            {
                return !path.StartsWith("/v2/", System.StringComparison.OrdinalIgnoreCase);
            }
            return false;
        }
    }
}

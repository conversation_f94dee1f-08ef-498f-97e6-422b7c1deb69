using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using AutoMapper.QueryableExtensions;
using DinkToPdf;
using DinkToPdf.Contracts;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using NLog;
using SistemaInfo.BBC.Application.Helpers.Ciot;
using SistemaInfo.BBC.Application.Interface.Cidade;
using SistemaInfo.BBC.Application.Interface.Cliente;
using SistemaInfo.BBC.Application.Interface.DeclaracaoCiot;
using SistemaInfo.BBC.Application.Interface.Empresa;
using SistemaInfo.BBC.Application.Interface.NaturezaCarga;
using SistemaInfo.BBC.Application.Interface.Operacoes;
using SistemaInfo.BBC.Application.Interface.Parametros;
using SistemaInfo.BBC.Application.Interface.Portador;
using SistemaInfo.BBC.Application.Objects.Api.Ciot;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.DeclaracaoCiot;
using SistemaInfo.BBC.Domain.Contracts.Operacoes;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Domain.External.CIOT.DTO;
using SistemaInfo.BBC.Domain.External.CIOT.Repository;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.BBC.Domain.Models.Banco.Repository;
using SistemaInfo.BBC.Domain.Models.Cidade.Repository;
using SistemaInfo.BBC.Domain.Models.Cliente.Repository;
using SistemaInfo.BBC.Domain.Models.DeclaracaoCiot.Commands;
using SistemaInfo.BBC.Domain.Models.DeclaracaoCiot.Exeptions;
using SistemaInfo.BBC.Domain.Models.DeclaracaoCiot.Repository;
using SistemaInfo.BBC.Domain.Models.Empresa.Repository;
using SistemaInfo.BBC.Domain.Models.OperacaoTransporteCiot.Commands;
using SistemaInfo.BBC.Domain.Models.Portador.Repository;
using SistemaInfo.BBC.Domain.Models.Usuario.Repository;
using SistemaInfo.BBC.Domain.Models.Veiculo.Repository;
using SistemaInfo.BBC.Domain.Models.Viagem.Repository;
using SistemaInfo.BBC.Infra.Data.External.Caruana.Repository;
using SistemaInfo.BBC.Infra.Data.External.JSLLeasing.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.Services.DeclaracaoCiot
{
    public class DeclaracaoCiotAppService : AppService<Domain.Models.DeclaracaoCiot.DeclaracaoCiot,
        IDeclaracaoCiotReadRepository, IDeclaracaoCiotWriteRepository>, IDeclaracaoCiotAppService
    {
        private readonly ICidadeAppService _cidadeAppService;
        private readonly ICidadeReadRepository _cidadeReadRepository;
        private readonly IClienteAppService _clienteAppService;
        private readonly IClienteReadRepository _clienteReadRepository;
        private readonly IEmpresaAppService _empresaAppService;
        private readonly IEmpresaReadRepository _empresaReadRepository;
        private readonly INaturezaCargaAppService _naturezaCargaAppService;
        private readonly IParametrosAppService _parametrosAppService;
        private readonly IPortadorAppService _portadorAppService;
        private readonly IVeiculoReadRepository _veiculoReadRepository;
        private readonly IBancoReadRepository _bancoReadRepository;
        private readonly IPortadorReadRepository _portadorReadRepository;
        private ICiotClientEngine _ciotClientEngine;
        private readonly IOperacoesAppService _operacoesAppService;
        private readonly IViagemReadRepository _viagemReadRepository;
        private readonly IUsuarioReadRepository _usuarioReadRepository;

        private readonly Domain.Models.DeclaracaoCiot.DeclaracaoCiot.ETipoEmissaoCiotCad lTipoEmissao = 0;

        public DeclaracaoCiotAppService(IAppEngine engine, IDeclaracaoCiotReadRepository readRepository,
            IDeclaracaoCiotWriteRepository writeRepository, IVeiculoReadRepository veiculoReadRepository,
            IClienteReadRepository clienteReadRepository, ICidadeReadRepository cidadeReadRepository,
            IPortadorAppService portadorAppService, INaturezaCargaAppService naturezaCargaAppService,
            ICidadeAppService cidadeAppService, IClienteAppService clienteAppService,
            ICiotClientEngine ciotClientEngine, IEmpresaAppService empresaAppService,
            IParametrosAppService parametrosAppService, IBancoReadRepository bancoReadRepository, 
            IPortadorReadRepository portadorReadRepository, IOperacoesAppService operacoesAppService,
            IViagemReadRepository viagemReadRepository, IUsuarioReadRepository usuarioReadRepository, IEmpresaReadRepository empresaReadRepository) : base(
            engine, readRepository, writeRepository)
        {
            _veiculoReadRepository = veiculoReadRepository;
            _clienteReadRepository = clienteReadRepository;
            _cidadeReadRepository = cidadeReadRepository;
            _portadorAppService = portadorAppService;
            _naturezaCargaAppService = naturezaCargaAppService;
            _cidadeAppService = cidadeAppService;
            _clienteAppService = clienteAppService;
            _ciotClientEngine = ciotClientEngine;
            _empresaAppService = empresaAppService;
            _parametrosAppService = parametrosAppService;
            _bancoReadRepository = bancoReadRepository;
            _portadorReadRepository = portadorReadRepository;
            _operacoesAppService = operacoesAppService;
            _viagemReadRepository = viagemReadRepository;
            _usuarioReadRepository = usuarioReadRepository;
            _empresaReadRepository = empresaReadRepository;

            // var lParametro = _parametrosAppService.GetParametrosAsync(User.EmpresaId,
            //     Domain.Models.Parametros.Parametros.TipoDoParametro.CodigoTipoEmissaoCiot,
            //     Domain.Models.Parametros.Parametros.TipoDoValor.Number).Result;
            //
            // var lParametroGeral = _parametrosAppService.GetParametrosAsync(-1,
            //     Domain.Models.Parametros.Parametros.TipoDoParametro.CodigoTipoEmissaoCiot,
            //     Domain.Models.Parametros.Parametros.TipoDoValor.Number).Result;
            //
            // if (lParametro == null)
            // {
            //     if (lParametroGeral?.Valor.ToIntSafe().ToEnum<ETipoEmissaoCiot>() == ETipoEmissaoCiot.Caruana)
            //     {
            //         _ciotClientEngine = new CaruanaCiotEngineRepository(_parametrosAppService.Repository.Query);
            //         lTipoEmissao = Domain.Models.DeclaracaoCiot.DeclaracaoCiot.ETipoEmissaoCiotCad.Caruana;
            //     }
            //     else
            //     {
            //         _ciotClientEngine = new JslLeasingEngineRepository(_parametrosAppService.Repository?.Query);
            //         lTipoEmissao = Domain.Models.DeclaracaoCiot.DeclaracaoCiot.ETipoEmissaoCiotCad.Leasing;
            //     }
            // }
            // else
            // {
            //     switch (lParametro.Valor.ToIntSafe().ToEnum<ETipoEmissaoCiot>())
            //     {
            //         case ETipoEmissaoCiot.Geral:
            //             if (lParametroGeral.Valor.ToIntSafe() == 1)
            //             {
            //                 _ciotClientEngine = new CaruanaCiotEngineRepository(_parametrosAppService.Repository.Query);
            //                 lTipoEmissao = Domain.Models.DeclaracaoCiot.DeclaracaoCiot.ETipoEmissaoCiotCad.Caruana;
            //                 }
            //             else
            //             {
            //                 _ciotClientEngine = new JslLeasingEngineRepository(_parametrosAppService.Repository.Query);
            //                 lTipoEmissao = Domain.Models.DeclaracaoCiot.DeclaracaoCiot.ETipoEmissaoCiotCad.Leasing;
            //             }
            //
            //             break;
            //         case ETipoEmissaoCiot.Caruana:
            //             _ciotClientEngine = new CaruanaCiotEngineRepository(_parametrosAppService.Repository.Query);
            //             lTipoEmissao = Domain.Models.DeclaracaoCiot.DeclaracaoCiot.ETipoEmissaoCiotCad.Caruana;
            //             break;
            //         case ETipoEmissaoCiot.Leasing:
            //             _ciotClientEngine = new JslLeasingEngineRepository(_parametrosAppService.Repository.Query);
            //             lTipoEmissao = Domain.Models.DeclaracaoCiot.DeclaracaoCiot.ETipoEmissaoCiotCad.Leasing;
            //             break;
            //         default:
            //             throw new Exception(
            //                 "Parâmetro para emissão de CIOT inesperado {0}".FormatEx(lParametro.Valor.ToIntSafe()));
            //     }
            // }
        }

        public ConsultarGridCiotResponse ConsultarGridPainelCiot(int take, int page, OrderFilters orderFilters,
            List<QueryFilters> filters)
        {
            var idEmpresa = Engine.User.EmpresaId;

            var lCiot = Repository.Query.GetAll();

            if (idEmpresa > 0) lCiot = lCiot.Include(a => a.PortadorProp).Where(p => p.EmpresaId == idEmpresa);

            lCiot = lCiot.AplicarFiltrosDinamicos(filters);
            lCiot = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                ? lCiot.OrderByDescending(o => o.Id)
                //FEITO ISSO POIS NÃO ORDENAVA CORRETO, O ENUM DE TIPO É 1-PADRÃO, 3-AGREGADO
                : orderFilters.Campo == "Tipo"
                    ? orderFilters.Operador == EOperadorOrder.Ascending
                        ? lCiot.OrderBy($"{orderFilters.Campo} {EOperadorOrder.Descending.GetDescription()}")
                        : lCiot.OrderBy($"{orderFilters.Campo} {EOperadorOrder.Ascending.GetDescription()}")
                    : lCiot.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            return new ConsultarGridCiotResponse
            {
                Items = lCiot.Skip((page - 1) * take)
                    .Take(take)
                    .ProjectTo<ConsultarGridCiot>().ToList(),
                TotalItems = lCiot.Count()
            };
        }
        
        public async Task<ConsultarGridOperacaoTransporteResponse> ConsultarGridOperacaoTransporte(ConsultarGridOperacaoTransporteRequest request)
        {
            try
            {

                #region Visibilidade de informações
                
                var lUser = await _usuarioReadRepository.GetByIdAsync(Engine.User.Id);
                Domain.Models.Empresa.Empresa empresaLogada;

                if (lUser.EmpresaId != null)
                {
                    empresaLogada = await _empresaReadRepository
                        .Include(a => a.GrupoEmpresa) 
                        .FirstOrDefaultAsync(a => a.Id == User.EmpresaId);
                    request.CnpjEmpresa = new List<string> { empresaLogada.Cnpj.OnlyNumbers() };
                }
                    

                if (lUser.EmpresaId == null && lUser.GrupoEmpresaId != null && request.CnpjEmpresa.IsEmpty())
                {
                    var lEmpresasCnpjGrupo = await _empresaReadRepository.GetEmpresasCnpjByGrupoEmpresaId(lUser.GrupoEmpresaId.ToInt());
                    foreach (var cnpj in lEmpresasCnpjGrupo)
                    {
                        request.CnpjEmpresa.Add(cnpj.OnlyNumbers());
                    }
                }
                
                #endregion
                
                var lCommand = Mapper.Map<OperacaoTransporteConsultarMsCommand>(request);
                var lMessageResponse = await Engine.CommandBus.SendCommandAsync<ConsultaGridOperacaoTransporteMessage>(lCommand);

                return new ConsultarGridOperacaoTransporteResponse
                {
                    Items = Mapper.Map<List<ConsultarGridOperacaoTransporteItem>>(lMessageResponse.OperacaoTransporteList),
                    TotalItems = lMessageResponse.Count
                };
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e.Message);
                throw;
            }
        }
        
        public async Task<ConsultarGridOperacaoTransporteHistoricoResponse> ConsultarGridOperacaoTransporteHistorico(ConsultarGridOperacaoTransporteHistoricoRequest request)
        {
            try
            {
                var lCommand = Mapper.Map<OperacaoTransporteConsultarHistoricoMsCommand>(request);
                var lMessageResponse = await Engine.CommandBus.SendCommandAsync<ConsultaGridOperacaoTransporteHistoricoMessage>(lCommand);

                return new ConsultarGridOperacaoTransporteHistoricoResponse
                {
                    Items = Mapper.Map<List<ConsultarGridOperacaoTransporteHistoricoItem>>(lMessageResponse.OperacaoTransporteHistoricoList),
                    TotalItems = lMessageResponse.Count
                };
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e.Message);
                throw;
            }
        }

        public async Task<ConsultarOperacaoTransportePorIdRespMessage> ConsultarOperacaoTransportePorId(int idOperacaoTransporte)
        {
            try
            {
                var lMessageResponse = await Engine.CommandBus.SendCommandAsync<ConsultarOperacaoTransportePorIdRespMessage>(
                    new ConsultarOperacaoTransportePorIdReqMessage { IdOperacaoTransporte = idOperacaoTransporte });

                #region Aba de viagem

                var data = lMessageResponse.Data;
                
                    var viagensBbc = await _viagemReadRepository
                    .Include(v => v.CidadeOrigem)
                    .Include(v => v.CidadeDestino)
                    .Where(v => v.CiotId == idOperacaoTransporte)
                    .ToListAsync();
                
                var viagensBbcConvertidas = Engine.Mapper.Map<List<ViagemOperacao>>(viagensBbc);
                data.ViagensList = data.ViagensList.Concat(viagensBbcConvertidas).ToList();

                if (data.Tipo == (int)TipoCiot.Padrão)
                {
                    data.CidadeOrigemPadrao = await _cidadeReadRepository.GetCidadeByIbgeAsync(data.IbgeCidadeOrigem.ToInt());
                    data.CidadeDestinoPadrao = await _cidadeReadRepository.GetCidadeByIbgeAsync(data.IbgeCidadeDestino.ToInt());
                }
                else
                {
                    foreach (var viagem in data.ViagensList)
                    {
                        viagem.CidadeOrigem = await _cidadeReadRepository.GetCidadeByIbgeAsync(viagem.IdCidadeOrigem);
                        viagem.CidadeDestino = await _cidadeReadRepository.GetCidadeByIbgeAsync(viagem.IdCidadeDestino);
                    }
                }

                #endregion
                
                return lMessageResponse;
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return new ConsultarOperacaoTransportePorIdRespMessage(false, e.Message);
            }
        }

        public async Task<ConsultarVeiculosCiotRespMessage> ConsultaVeiculosCiot(int idOperacaoTransporte)
        {
            try
            {
                var lMessageResponse = await Engine.CommandBus.SendCommandAsync<ConsultarVeiculosCiotRespMessage>(
                    new ConsultarVeiculoCiotReqMessage { IdOperacaoTransporte = idOperacaoTransporte });

                foreach (var veiculo in lMessageResponse.Data.VeiculosList)
                {
                    veiculo.Placa = veiculo.Placa.ToPlacaFormato();
                }

                return lMessageResponse;
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e.Message);
                throw;
            }
        }

        public List<ConsultarDeclaracaoCiotResponse> ConsultarDeclaracaoCiot(int idPortador)
        {
            var lCiots =
                Repository.Query.Where(d => d.PortadorPropId == idPortador && d.Status != StatusCiot.Cancelado);

            if (Engine.User.EmpresaId > 0) lCiots = lCiots.Where(e => e.EmpresaId == Engine.User.EmpresaId);
            return lCiots.ProjectTo<ConsultarDeclaracaoCiotResponse>().ToList();
        }

        public CiotConsultarApiResponse ConsultarDeclaracaoCiot(CiotConsultarApiRequest request)
        {
            var lCiot = Repository
                .Query
                .Include(c => c.Filial)
                .Include(c => c.PortadorMot)
                .Include(c => c.CiotViagem).ThenInclude(cv => cv.CidadeOrigem)
                .Include(c => c.CiotViagem).ThenInclude(cv => cv.CidadeDestino)
                .Include(c => c.CiotViagem).ThenInclude(cv => cv.ClienteRemetente)
                .Include(c => c.CiotViagem).ThenInclude(cv => cv.ClienteDestinatario)
                .Include(c => c.CiotViagem).ThenInclude(cv => cv.ClienteConsignatario)
                .Include(c => c.CiotVeiculo).ThenInclude(v => v.Veiculo)
                .Where(c => c.EmpresaId == Engine.User.EmpresaId);

            if (!string.IsNullOrEmpty(request.Ciot))
                lCiot = lCiot.Where(c => c.Ciot == request.Ciot);
            if (!string.IsNullOrEmpty(request.Verificador))
                lCiot = lCiot.Where(c => c.Verificador == request.Verificador);
            if (!string.IsNullOrEmpty(request.CpfCnpjPortador))
                lCiot = lCiot.Where(c => c.PortadorProp.CpfCnpj == request.CpfCnpjPortador);

            var listCiot = lCiot.ProjectTo<CiotConsultarApiResponseItem>().ToList();

            return new CiotConsultarApiResponse
            {
                Page = request.Page,
                TotalItems = listCiot.Count,
                Items = listCiot
            };
        }

        public async Task<RespPadrao> Save(CiotRequest lModel)
        {
            var lLog = LogManager.GetCurrentClassLogger();
            try
            {
                lLog.Info("Início save Declaração CIOT");
                if (lModel.Id == "Auto")
                {
                    lModel.Id = null;

                    var lPortadorContratado = await _portadorReadRepository.GetByIdAsync(lModel.PortadorPropId);
                    
                    if (string.IsNullOrWhiteSpace(lPortadorContratado.RNTRC))
                    {
                        lLog.Info("RNTRC do Portador vazio ou nulo");
                        return new RespPadrao
                        {
                            sucesso = false,
                            mensagem = "Portador proprietário sem RNTRC cadastrado"
                        };
                    }
                    
                    var lEmpresa = await _empresaAppService.Repository.Query.FirstOrDefaultAsync(a => a.Id == User.EmpresaId);
                    
                    if (string.IsNullOrWhiteSpace(lEmpresa.RNTRC))
                    {
                        lLog.Info("RNTRC da Empresa vazio ou nulo");
                        return new RespPadrao
                        {
                            sucesso = false,
                            mensagem = "Empresa sem RNTRC cadastrado"
                        };
                    }
                    
                    lLog.Info("Chamando geração de CIOT");
                    var lRetorno = await GerarCiot(lModel);

                    if (lRetorno == null)
                    {
                        return new RespPadrao
                        {
                            sucesso = false,
                            mensagem = "Não foi possível realizar essa operação."
                        };
                    }

                    if (!lRetorno.Sucesso)
                    {
                        return new RespPadrao
                        {
                            sucesso = false,
                            mensagem = lRetorno.Excecao.Mensagem
                        };
                    }

                    var lCiot = Mapper.Map<DeclaracaoCiotSalvarCommand>(lModel);

                    lCiot.Ciot = lRetorno.CIOT;
                    lCiot.AvisoTransportador = lRetorno.AvisoTransportador;
                    lCiot.Verificador = lRetorno.CodigoVerificador;
                    lCiot.EmContigencia = lRetorno.EmContingencia;
                    lCiot.QuantidadeTarifas = lModel.QuantidadeTarifas;
                    lCiot.SenhaAlteracao = lRetorno.SenhaAlteracao;

                    lCiot.Status = lCiot.Tipo == TipoCiot.Padrão ? StatusCiot.Contingencia : lCiot.Status;
                    lCiot.DataEncerramento = lCiot.Tipo == TipoCiot.Padrão ? DateTime.Now : lCiot.DataEncerramento;
                    lCiot.TipoEmissao =
                        lTipoEmissao == Domain.Models.DeclaracaoCiot.DeclaracaoCiot.ETipoEmissaoCiotCad.Caruana
                            ? Domain.Models.DeclaracaoCiot.DeclaracaoCiot.ETipoEmissaoCiotCad.Caruana
                            : Domain.Models.DeclaracaoCiot.DeclaracaoCiot.ETipoEmissaoCiotCad.Leasing;

                    await Engine.CommandBus.SendCommandAsync(lCiot);
                }
                else
                {
                    lLog.Info("Retificação de CIOT");
                    //
                    // var lRetorno = RetificarCiot(lModel);
                    //
                    // if (lRetorno == null)
                    //     return new RespPadrao
                    //     {
                    //         sucesso = false,
                    //         mensagem = "Não foi possível realizar essa operação."
                    //     };
                    //
                    // if (!lRetorno.Sucesso)
                    //     return new RespPadrao
                    //     {
                    //         sucesso = false,
                    //         mensagem = lRetorno.Excecao.Mensagem
                    //     };

                    var lCiot = Mapper.Map<DeclaracaoCiotSalvarCommand>(lModel);

                    await Engine.CommandBus.SendCommandAsync(lCiot);
                }

                return new RespPadrao
                {
                    sucesso = true,
                    mensagem = ""
                };
            }
            catch (Exception e)
            {
                lLog.Error(e, "Erro na Declaração do CIOT");
                return new RespPadrao
                {
                    sucesso = false,
                    mensagem = e.Message
                };
            }
        }

        public async Task<RespPadraoApi> Save(CiotRetificarApiRequest request)
        {
            var ciotConsulta = BuscarPorNumero(request.Numero, request.Verificador);

            if (ciotConsulta == null)
                return new RespPadraoApi
                {
                    sucesso = false,
                    mensagem = "Ciot não encontrado"
                };

            if (ciotConsulta.Tipo == (int) TipoCiot.Agregado)
            {
                var veiculosQuery = _veiculoReadRepository.GetAll();

                foreach (var veiculo in request.Veiculos)
                    veiculosQuery = veiculosQuery.Where(v => v.Placa == veiculo.Placa && v.Renavam == veiculo.Renavam);

                ciotConsulta.veiculosList = Mapper.Map<List<CiotVeiculoResponse>>(veiculosQuery.ToList());
            }

            var cidadeQuery = _cidadeAppService.Repository.Query.GetAll();
            var clienteQuery = _clienteAppService.Repository.Query.GetAll();

            foreach (var viagem in request.Viagens)
            {
                cidadeQuery = cidadeQuery.Where(c =>
                    c.Ibge == viagem.IbgeCidadeOrigem || c.Ibge == viagem.IbgeCidadeDestino);
                clienteQuery = clienteQuery.Where(c => c.CpfCnpj == viagem.CpfCnpjClienteRemetente
                                                       || c.CpfCnpj == viagem.CpfCnpjClienteDestinatario
                                                       || c.CpfCnpj == viagem.CpfCnpjClienteConsignatario);
            }

            var cidades = cidadeQuery.ToList();
            var clientes = clienteQuery.ToList();
            var viagemApiRequest = new List<CiotViagemResponse>();

            try
            {
                foreach (var viagem in request.Viagens)
                    viagemApiRequest.Add(new CiotViagemResponse
                    {
                        NaturezaCargaId = viagem.CodigoNaturezaCarga,
                        ConsignatarioId = clientes.Find(c => c.CpfCnpj == viagem.CpfCnpjClienteConsignatario)?.Id,
                        DestinatarioId = clientes.Find(c => c.CpfCnpj == viagem.CpfCnpjClienteDestinatario)?.Id,
                        CidadeDestinoId = cidades.Find(c => c.Ibge == viagem.IbgeCidadeDestino)?.Id,
                        CidadeOrigemId = cidades.Find(c => c.Ibge == viagem.IbgeCidadeOrigem)?.Id,
                        RemetenteId = clientes.Find(c => c.CpfCnpj == viagem.CpfCnpjClienteRemetente)?.Id,
                        Peso = viagem.PesoCarga.ToString(),
                        ValorCombustivel = viagem.ValorCombustivel?.ToString(),
                        ValorDespesas = viagem.ValorDespesas?.ToString(),
                        ValorFrete = viagem.ValorFrete.ToString(),
                        ValorImposto = viagem.ValorImposto.ToString(),
                        ValorPedagio = viagem.ValorPedagio?.ToString()
                    });
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                throw;
            }

            ciotConsulta.viagensList = viagemApiRequest;
            var ciotSalvar = Mapper.Map<CiotRequest>(ciotConsulta);
            return new RespPadraoApi(await Save(ciotSalvar));
        }

        public string Valida(string dataEmissao, string dataFim, string ciot, bool cancel)
        {
            var viagemCiot = _viagemReadRepository.FirstOrDefault(x => x.Ciot.Equals(ciot));
            
            if (cancel)
            { 
                var lDataFutura = dataEmissao.ToDateTime().AddHours(23).AddMinutes(30);

                if (lDataFutura < DateTime.Now) return "CIOT já está vigente a mais de 24 horas";

                if (viagemCiot != null) return "CIOT tem viagem vinculada.";
            }
            else
            {
                if (dataFim.ToDateTime() > DateTime.Now) return "Data fim do CIOT é maior que a data atual.";
            }

            return "";
        }

        public async Task<RespPadrao> AlterarStatusCancelado(CiotStatusRequest request)
        {
            try
            {
                var cancelaOpTrans = new CancelarOperacaoTransporteReq();

                cancelaOpTrans.MotivoCancelamento = request.Motivo;
                cancelaOpTrans.CIOT = request.Ciot;
                cancelaOpTrans.SenhaAlteracao = request.Id.ToString();
                if (request.CpfCnpjClienteAdmOrCompanyGroup != null) cancelaOpTrans.CpfCnpjClienteAdmOrCompanyGroup = request.CpfCnpjClienteAdmOrCompanyGroup.OnlyNumbers();

                var lRetorno = await CancelarOperacao(cancelaOpTrans);

                if (!lRetorno.Sucesso)
                {
                    return new RespPadrao
                    {
                        sucesso = false,
                        mensagem = lRetorno.Excecao.Mensagem
                    }; 
                }
                
                return new RespPadrao
                {
                    sucesso = true,
                    mensagem = "CIOT cancelado com sucesso!"
                };
            }
            catch (Exception e)
            {
                return new RespPadrao
                {
                    sucesso = false,
                    mensagem = e.Message
                };
            }
        }

        public async Task<RespPadraoApi> AlterarStatusCancelado(CiotCancelarApiRequest request)
        {
            var num = request.Numero;
            var ver = request.Verificador;
            var ciot = Repository.Query.First(c => c.Ciot == num && c.Verificador == ver);

            if (ciot == null) throw new Exception("Ciot não encontrado");

            var status = new CiotStatusRequest
            {
                Id = ciot.Id,
                Motivo = request.Motivo
            };

            if (ciot.TipoEmissao == Domain.Models.DeclaracaoCiot.DeclaracaoCiot.ETipoEmissaoCiotCad.Caruana)
                _ciotClientEngine = new CaruanaCiotEngineRepository(_parametrosAppService.Repository.Query);
            else
                _ciotClientEngine = new JslLeasingEngineRepository(_parametrosAppService.Repository.Query);

            var response = await AlterarStatusCancelado(status);
            var responseApi = new RespPadraoApi(response);
            if (response.sucesso && string.IsNullOrEmpty(responseApi.mensagem))
                responseApi.mensagem = "Cancelado com sucesso";
            return responseApi;
        }

        private async Task AlterarStatusCanceladoServico(List<CiotStatusRequest> lCiotStatusList)
        {
            var lLog = LogManager.GetCurrentClassLogger();
            foreach (var lCiotStatus in lCiotStatusList)
            {
                var lCiot = Mapper.Map<DeclaracaoCiotAlterarStatusCommand>(lCiotStatus);

                var lCiotId = await Repository.Query.FirstOrDefaultAsync(a => a.Id == lCiotStatus.Id);

                if (lCiotId.TipoEmissao == Domain.Models.DeclaracaoCiot.DeclaracaoCiot.ETipoEmissaoCiotCad.Caruana)
                    _ciotClientEngine = new CaruanaCiotEngineRepository(_parametrosAppService.Repository.Query);
                else
                    _ciotClientEngine = new JslLeasingEngineRepository(_parametrosAppService.Repository.Query);

                lCiot.Status = StatusCiot.Cancelado;

                var lCancelaOpTrans = new CancelarOperacaoTransporteReq();

                lCancelaOpTrans.MotivoCancelamento = "Cancelado automaticamente";
                lCancelaOpTrans.SenhaAlteracao = lCiotId.SenhaAlteracao;
                lCancelaOpTrans.CIOT = lCiotId.Ciot;

                var lRetorno = await CancelarOperacao(lCancelaOpTrans);

                if (!lRetorno.Sucesso) lLog.Error($"BAT_CIOT_02 ERRO: CIOT Id {lRetorno.Excecao.Codigo} não cancelado. Mensagem: {lRetorno.Excecao.Mensagem}");
               
                else await Engine.CommandBus.SendCommandAsync(lCiot);
            }
        }

        public async Task<RespPadrao> AlterarStatusEncerrado(CiotStatusRequest request)
        {
            try
            {
                var encerraOpTrans = new EncerrarOperacaoTransporteReq();
                var result = new List<EncerrarOperacaoTransporteViagem>();
                var valoresFrete = new ValoresFrete();
                
                var viagensCiot = request.Viagens;
                var pesoTotal = decimal.Zero;

                if (viagensCiot != null)
                {
                    await UpdateViagemToCiot(viagensCiot, request.Ciot, request.VerificadorCiot);
                    var totalViagens = viagensCiot.Count();
                    pesoTotal = viagensCiot.Sum(o => o.Peso.ToDecimal());
                    
                    valoresFrete.ValorFrete = viagensCiot.Sum(vl => vl.ValorFrete.ToDecimal());
                    valoresFrete.ValorCombustivel = viagensCiot.Sum(vl => vl.ValorCombustivel.ToDecimal());
                    valoresFrete.ValorDespesas = viagensCiot.Sum(vl => vl.ValorDespesas.ToDecimal());
                    valoresFrete.TotalImposto = viagensCiot.Sum(vl => vl.ValorImposto.ToDecimal());
                    valoresFrete.QuantidadeTarifas = 1;
                    valoresFrete.ValorTarifas = viagensCiot.Sum(vl => vl.ValorTarifa.ToDecimal());

                    result.AddRange(viagensCiot.Select(viagem => new EncerrarOperacaoTransporteViagem
                    {
                        CodigoMunicipioOrigem = viagem.CodigoMunicipioOrigem,
                        CodigoMunicipioDestino = viagem.CodigoMunicipioDestino,
                        CodigoNaturezaCarga = viagem.CodigoNaturezaCarga,
                        PesoCarga = viagem.Peso.ToDecimal(),
                        QuantidadeViagens = totalViagens
                    }));
                }

                encerraOpTrans.CIOT = request.Ciot;
                encerraOpTrans.PesoCarga = pesoTotal;
                encerraOpTrans.ViagensOperacaoTransporte = result.ToArray();
                encerraOpTrans.ValoresEfetivos = valoresFrete;
                encerraOpTrans.SenhaAlteracao = request.Id.ToString();
                if (request.CpfCnpjClienteAdmOrCompanyGroup != null) encerraOpTrans.CpfCnpjClienteAdmOrCompanyGroup = request.CpfCnpjClienteAdmOrCompanyGroup.OnlyNumbers();

                var lRetorno = await EncerrarOperacaoTransporte(encerraOpTrans);

                if (!lRetorno.Sucesso)
                    return new RespPadrao
                    {
                        sucesso = false,
                        mensagem = lRetorno.Excecao.Mensagem
                    };

                return new RespPadrao
                {
                    sucesso = true,
                    mensagem = ""
                };
            }
            catch (Exception e)
            {
                return new RespPadrao
                {
                    sucesso = false,
                    mensagem = e.Message
                };
            }
        }

        private async Task UpdateViagemToCiot(List<CiotViagemRequest> viagensCiotList, string ciot, string verificador)
        {
            try
            {
                foreach (var viagemCiot in viagensCiotList)
                {
                    var viagem = await _viagemReadRepository.GetByIdAsync(viagemCiot.ViagemId);
                    if (viagem == null) continue;
                    viagem.CodigoNaturezaCarga = viagemCiot.CodigoNaturezaCarga;
                    viagem.PesoCarga = viagemCiot.Peso.ToDecimalSafe();
                    viagem.VerificadorCiot = verificador;
                    viagem.Ciot = ciot;
                    await _viagemReadRepository.SaveChangesAsync();
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(e.Message);
                throw;
            }
        }

        public async Task<RespPadraoApi> AlterarStatusEncerrado(CiotEncerrarApiRequest request)
        {
            var num = request.Numero;
            var ver = request.Verificador;
            var ciot = Repository.Query.First(c => c.Ciot == num && c.Verificador == ver);

            if (ciot == null) throw new Exception("Ciot não encontrado");

            var status = new CiotStatusRequest
            {
                Id = ciot.Id
            };

            if (ciot.TipoEmissao == Domain.Models.DeclaracaoCiot.DeclaracaoCiot.ETipoEmissaoCiotCad.Caruana)
                _ciotClientEngine = new CaruanaCiotEngineRepository(_parametrosAppService.Repository.Query);
            else
                _ciotClientEngine = new JslLeasingEngineRepository(_parametrosAppService.Repository.Query);

            var response = await AlterarStatusEncerrado(status);
            var responseApi = new RespPadraoApi(response);
            if (response.sucesso && string.IsNullOrEmpty(responseApi.mensagem))
                responseApi.mensagem = "Encerrado com sucesso";
            return responseApi;
        }

        private async Task AlterarStatusEncerradoServico(List<CiotStatusRequest> lCiotStatusList)
        {
            var lLog = LogManager.GetCurrentClassLogger();
            foreach (var lCiotStatus in lCiotStatusList)
            {
                var lCiotMapper = Mapper.Map<DeclaracaoCiotAlterarStatusCommand>(lCiotStatus);
                lCiotMapper.Status = StatusCiot.Contingencia;

                var lEncerrarOpTranspReq = new EncerrarOperacaoTransporteReq();
                var lViagensOpTransp = new List<EncerrarOperacaoTransporteViagem>();
                var lValoresFrete = new ValoresFrete();

                var lCiot = Repository.Query.Where(x => x.Id == lCiotStatus.Id)
                    .Include(x => x.Empresa)
                    .Include(x => x.Empresa.Cidade)
                    .Include(x => x.PortadorMot)
                    .Include(x => x.CiotViagem)
                    .Include(x => x.CiotVeiculo)
                    .Include(x => x.PortadorProp)
                    .FirstOrDefault();

                if (lCiot == null) continue;

                if (lCiot.TipoEmissao == Domain.Models.DeclaracaoCiot.DeclaracaoCiot.ETipoEmissaoCiotCad.Caruana)
                    _ciotClientEngine = new CaruanaCiotEngineRepository(_parametrosAppService.Repository.Query);
                else
                    _ciotClientEngine = new JslLeasingEngineRepository(_parametrosAppService.Repository.Query);

                var lCiotViagens = lCiot.CiotViagem;
                
                var lViagens = lCiotViagens.GroupBy(o => new {o.CidadeOrigemId, o.CidadeDestinoId, o.NaturezaCargaId});

                decimal? lPesoGeral = 0;

                foreach (var lViagem in lViagens)
                {
                    var lTotalViagens = lViagem.Count();
                    var lPesoTotal = lViagem.Sum(o => o.Peso) ?? 0;
                    var lViagemCiot = lViagem.FirstOrDefault();

                    lPesoGeral = lPesoGeral + lPesoTotal;

                    var lCodigoNaturezaCarga = _naturezaCargaAppService.Repository.Query
                        .FirstOrDefault(a => a.Id == lViagemCiot.NaturezaCargaId).Codigo;

                    lViagensOpTransp.Add(new EncerrarOperacaoTransporteViagem
                    {
                        CodigoMunicipioOrigem = GetCodigoCidade(lViagemCiot.CidadeOrigemId).Value,
                        CodigoMunicipioDestino = GetCodigoCidade(lViagemCiot.CidadeDestinoId).Value,
                        CodigoNaturezaCarga = lCodigoNaturezaCarga,
                        PesoCarga = lPesoTotal,
                        QuantidadeViagens = lTotalViagens
                    });
                }

                lValoresFrete.ValorFrete = lCiot.ValorFrete.Round();
                lValoresFrete.ValorCombustivel = lCiot.ValorCombustivel?.Round();
                lValoresFrete.ValorDespesas = lCiot.ValorDespesas?.Round();
                lValoresFrete.TotalImposto = lCiot.ValorImposto.Round();
                lValoresFrete.QuantidadeTarifas = lCiot.QuantidadeTarifas;
                lValoresFrete.ValorTarifas = lCiot.ValorTarifas?.Round();

                lEncerrarOpTranspReq.CIOT = lCiot.Ciot;
                lEncerrarOpTranspReq.PesoCarga = lPesoGeral;
                lEncerrarOpTranspReq.ViagensOperacaoTransporte = lViagensOpTransp.ToArray();
                lEncerrarOpTranspReq.ValoresEfetivos = lValoresFrete;
                lEncerrarOpTranspReq.SenhaAlteracao = lCiot.SenhaAlteracao;

                var lRetorno = await EncerrarOperacaoTransporte(lEncerrarOpTranspReq);
                
                if (!lRetorno.Sucesso) 
                    lLog.Error($"BAT_CIOT_01 ERRO: CIOT Id {lRetorno.Excecao.Codigo} não encerrado. Mensagem: {lRetorno.Excecao.Mensagem}");
                
                else await Engine.CommandBus.SendCommandAsync(lCiot);
            }
        }

        public async Task<RespEquiparadoTAC> ConsultarSituacaoTransportador(int proprietarioId)
        {
            try
            {
                var consSitTrans = new ConsultarSituacaoTransportadorReq();

                var lEmpresa = await _empresaAppService.Repository.Query.FirstOrDefaultAsync(a => a.Id == User.EmpresaId);
                var lProprietario = await _portadorAppService.Repository.Query.FirstOrDefaultAsync(a => a.Id == proprietarioId);

                consSitTrans.CpfCnpjInteressado = lEmpresa?.Cnpj;
                consSitTrans.CpfCnpjTransportador = lProprietario?.CpfCnpj;
                consSitTrans.RNTRCTransportador = lProprietario?.RNTRC;

                var lResultConsSitTransp = await ConsultarSituacaoTransportador(consSitTrans);
                
                var lParametroVerificaContingencia = _parametrosAppService.GetParametrosAsync(-1,
                    Domain.Models.Parametros.Parametros.TipoDoParametro.CodigoTipoEmissaoCiot,
                    Domain.Models.Parametros.Parametros.TipoDoValor.Number).Result;

                var verificaContingencia = Convert.ToBoolean(lParametroVerificaContingencia.Valor.ToInt());

                if (!verificaContingencia)
                {
                    lResultConsSitTransp.Sucesso = true;
                    lResultConsSitTransp.EquiparadoTAC = true;
                }

                if (!lResultConsSitTransp.Sucesso)
                {
                    if (lResultConsSitTransp.Excecao != null)
                        return new RespEquiparadoTAC
                        {
                            sucesso = false,
                            mensagem = lResultConsSitTransp.Excecao.Mensagem
                        };

                    return new RespEquiparadoTAC
                    {
                        sucesso = false,
                        mensagem = "Não foi possível realizar a operação."
                    };
                }

                var lMsg = lResultConsSitTransp.EquiparadoTAC ? "ESTÁ" : "NÃO ESTÁ";
                var lPessoaEmpresa = lProprietario.CpfCnpj.Length == 11 ? "pessoa" : "empresa";

                var lMsgRetorno = "Esta " + lPessoaEmpresa + " " + lMsg +
                                  " equiparada ao Transportador Autônomo de Cargas, e, portanto, " + lMsg +
                                  " OBRIGADA a cumprir as disposições da Resolução ANTT 3658/11.";

                if (lResultConsSitTransp.EquiparadoTAC)
                    return new RespEquiparadoTAC
                    {
                        sucesso = true,
                        mensagem = lMsgRetorno,
                        equiparadoTAC = true
                    };

                return new RespEquiparadoTAC
                {
                    sucesso = true,
                    mensagem = lMsgRetorno,
                    equiparadoTAC = false
                };
            }
            catch (Exception e)
            {
                return new RespEquiparadoTAC
                {
                    sucesso = false,
                    mensagem = e.Message
                };
            }
        }

        public async Task ServiceEncerrarCiots()
        {
            try
            {
                var lCiotsEnc = await Repository.Query
                    .GetCiotsEncerrados()
                    .ProjectTo<CiotStatusRequest>()
                    .ToListAsync();

                if (lCiotsEnc == null || !lCiotsEnc.Any()) return;
                
                await AlterarStatusEncerradoServico(lCiotsEnc);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, "BAT_CIOT_01 ERRO");
            }
        }

        public async Task ServiceCancelarCiots()
        {
            try
            {
                var lCiotsParaCancelar = await Repository.Query
                    .GetCiotsCancelados()
                    .ProjectTo<CiotStatusRequest>()
                    .ToListAsync();

                await AlterarStatusCanceladoServico(lCiotsParaCancelar);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, "BAT_CIOT_02 ERRO");
            }
        }
        
        public async Task ServiceEncerrarCiotsTacAgregado()
        {
            var lLog = LogManager.GetCurrentClassLogger();
            lLog.Info("Início do método ServiceEncerrarCiotsTacAgregado");
            try
            {
                var operacoesTransporte = await ObterOperacoesTransporte();
                lLog.Info($"Operações de transporte obtidas: {operacoesTransporte.Count()}");

                foreach (var grupo in operacoesTransporte)
                {
                    var viagens = grupo.ToList();
                    var ciotId = grupo.Key;

                    var requisicaoEncerramento = CriarRequestEncerramento(viagens, ciotId);
                    lLog.Info($"Request de encerramento criado para CIOT ID: {ciotId}, Viagens: {viagens.Count}, " +
                              $"JSON: {JsonConvert.SerializeObject(requisicaoEncerramento, Formatting.Indented)}");

                    try
                    {
                        var retorno = await EncerrarOperacaoTransporteBbcService(requisicaoEncerramento);
                        lLog.Info($"Response recebido para CIOT ID: {ciotId}, Sucesso: {retorno.Sucesso}, Mensagem: {retorno.Excecao?.Mensagem}");

                        if (retorno.Sucesso)
                        {
                            await UpdateStatusViagem(viagens, StatusCiot.Encerrado);
                            lLog.Info($"Status atualizado para 'Encerrado' para CIOT ID: {ciotId}");
                        }
                        else
                        {
                            await UpdateStatusViagem(viagens, exceptionMessage: retorno.Excecao?.Mensagem);
                            lLog.Warn($"Falha no encerramento para CIOT ID: {ciotId}, Mensagem: {retorno.Excecao?.Mensagem}");
                        }
                        
                    }
                    catch (Exception ex)
                    {
                        lLog.Error(ex, $"Erro ao encerrar operação de transporte para CIOT ID: {ciotId}");
                        await UpdateStatusViagem(viagens, exceptionMessage: ex.Message);
                    }
                }
            }
            catch (Exception e)
            {
                lLog.Error(e, "Erro no método ServiceEncerrarCiotsTacAgregado");
                LogManager.GetCurrentClassLogger().Error(e, "BAT_CIOT_01 ERRO");
            }
            finally
            {
                lLog.Info("Fim do método ServiceEncerrarCiotsTacAgregado");
            }
        }
        
        private async Task<List<IGrouping<int?, Domain.Models.Viagem.Viagem>>> ObterOperacoesTransporte()
        {
            var viagens = await _viagemReadRepository
                .Include(v => v.CidadeOrigem)
                .Include(v => v.CidadeDestino)
                .Where(v =>
                    v.VersaoIntegracaoViagem == EVersaoIntegracao.V2 &&
                    v.TipoCiot == TipoCiot.Agregado &&
                    v.StatusCiot == StatusCiot.CiotGerado &&
                    v.DataDeclaracaoCiot <= DateTime.Now.AddDays(-30) &&
                    v.CiotId != null)
                .ToListAsync();

            return viagens.GroupBy(v => v.CiotId).ToList();
        }
        
        private EncerrarOperacaoTransporteReq CriarRequestEncerramento(List<Domain.Models.Viagem.Viagem> viagens, int? ciotId)
        {
            var valoresFrete = new ValoresFrete
            {
                ValorFrete = viagens.Sum(v => v.ValorFrete.ToDecimal()),
                ValorTarifas = viagens.Sum(v => v.ValorTarifas.ToDecimal()),
                ValorCombustivel = viagens.Sum(v => v.ValorCombustivel.ToDecimal()),
                ValorDespesas = viagens.Sum(v => v.ValorDespesa.ToDecimal()),
                TotalImposto = viagens.Sum(v => v.TotalImposto.ToDecimal()),
                TotalPegadio = viagens.Sum(v => v.TotalPedagio.ToDecimal()),
                QuantidadeTarifas = viagens.Sum(v => v.QuantidadeTarifas)
            };

            return new EncerrarOperacaoTransporteReq
            {
                CIOT = viagens.FirstOrDefault()?.Ciot,
                SenhaAlteracao = ciotId?.ToString(),
                PesoCarga =  Math.Round(viagens.Sum(v => v.PesoCarga ?? 0), 2),
                ValoresEfetivos = valoresFrete,
                ViagensOperacaoTransporte = Mapper.Map<EncerrarOperacaoTransporteViagem[]>(viagens)
            };
        }

        
        private async Task UpdateStatusViagem(List<Domain.Models.Viagem.Viagem> viagens, 
            StatusCiot? status = null, string exceptionMessage = null)
        {
            foreach (var viagem in viagens)
            {
                if (!string.IsNullOrWhiteSpace(exceptionMessage))
                {
                    if (exceptionMessage.Contains("Rejeição: Operação de Transporte já está encerrada.") ||
                        exceptionMessage.Contains("Operação de transporte já encerrada"))
                    {
                        viagem.StatusCiot = StatusCiot.Encerrado;
                        viagem.DescricaoCiot = "Operação de Transporte já encerrada.";
                    }
                    else { viagem.DescricaoCiot = exceptionMessage; }
                    continue;
                }

                if (!status.HasValue) continue;
                
                viagem.StatusCiot = status.Value;
                viagem.DescricaoCiot = "Ciot encerrado com sucesso!";
            }
            await _viagemReadRepository.SaveChangesAsync();
        }

        public IDocument ImprimirCiot(int ciotId)
        {
            var declaracaoCiot = ConsultarOperacaoTransportePorId(ciotId);
            

            var htmlReport = declaracaoCiot == null
                ? $@"<div>CIOT DE ID {ciotId} NÃO ENCONTRADO NA BASE DE DADOS</div>"
                : HtmlCiotReportHelper.GetHtmlImpressaoCiot(declaracaoCiot.Result.Data);

            var doc = new HtmlToPdfDocument
            {
                GlobalSettings =
                {
                    ColorMode = ColorMode.Color,
                    Orientation = Orientation.Portrait,
                    PaperSize = PaperKind.A4Plus
                },
                Objects =
                {
                    new ObjectSettings
                    {
                        PagesCount = true,
                        HtmlContent = htmlReport,
                        WebSettings = {DefaultEncoding = "utf-8"},
                        HeaderSettings = {FontSize = 9, Right = "Page [page] of [toPage]", Line = true, Spacing = 2.812}
                    }
                }
            };

            return doc;
        }

        public ConsultarPorIdCiotResponse BuscarPorId(int id)
        {
            var lDados = Repository.Query
                .Include(a => a.PortadorProp)
                .Include(a => a.PortadorMot)
                .Include(a => a.Empresa)
                .Include(a => a.CiotVeiculo)
                .Include(a => a.CiotViagem).FirstOrDefault(a => a.Id == id);

            lDados.ValorFrete = !lDados.CiotViagem.IsEmpty() ? 0 : lDados.ValorFrete;
            lDados.ValorCombustivel = !lDados.CiotViagem.IsEmpty() ? 0 : lDados.ValorCombustivel;
            lDados.ValorDespesas = !lDados.CiotViagem.IsEmpty() ? 0 : lDados.ValorDespesas;
            lDados.ValorImposto = !lDados.CiotViagem.IsEmpty() ? 0 : lDados.ValorImposto;
            lDados.ValorPedagio = !lDados.CiotViagem.IsEmpty() ? 0 : lDados.ValorPedagio;

            foreach (var item in lDados.CiotViagem)
            {
                lDados.ValorFrete = lDados.ValorFrete + (item.ValorFrete ?? 0);
                lDados.ValorCombustivel = lDados.ValorCombustivel != null ? lDados.ValorCombustivel + item.ValorCombustivel : 0;
                lDados.ValorDespesas = lDados.ValorDespesas != null ? lDados.ValorDespesas + item.ValorDespesas : 0;
                lDados.ValorImposto = lDados.ValorImposto + (item.ValorImposto ?? 0);
                lDados.ValorPedagio = lDados.ValorPedagio != null ? lDados.ValorPedagio + item.ValorPedagio : 0;
            }

            var lCiot = Mapper.Map<ConsultarPorIdCiotResponse>(lDados);

            lCiot.NomeBanco = _bancoReadRepository.FirstOrDefault(b => b.Id == lDados.Banco)?.Nome;
            
            lCiot.veiculosList = Repository.Query.GetVeiculos(id).ProjectTo<CiotVeiculoResponse>().ToList();
            lCiot.viagensList = Repository.Query.GetCiotViagens(id).ProjectTo<CiotViagemResponse>().ToList();

            return lCiot;
        }

        public Task<IEnumerable<ConsultarPorIdCiotResponse>> BuscarTodos()
        {
            throw new NotImplementedException();
        }

        public bool Existe(int id)
        {
            throw new NotImplementedException();
        }

        public async Task<ConsultarSituacaoTransportadorResp> ConsultarSituacaoTransportador(ConsultarSituacaoTransportadorReq req)
        {
            return await _operacoesAppService.ConsultarSituacaoTransportador(req);
        }

        public ConsultarSituacaoTransportadorResp ConsultarSituacaoTransportadorAPI(ConsultarSituacaoTransportadorReq req)
        {
            if (req.CpfCnpjInteressado.IsNullOrWhiteSpace() || req.CpfCnpjTransportador.IsNullOrWhiteSpace() ||
                req.RNTRCTransportador.IsNullOrWhiteSpace())
            {
                return new ConsultarSituacaoTransportadorResp
                {
                    Sucesso = false,
                    Excecao = new Excecao
                    {
                        Mensagem = "Campo(s) obrigatório(s) não informado."  
                    }
                };
            }

            var empresa = _empresaAppService.Repository.Query.FirstOrDefault(e => e.Id == User.EmpresaId);

            if (empresa.Cnpj != req.CpfCnpjInteressado)
            {
                return new ConsultarSituacaoTransportadorResp
                {
                    Sucesso = false,
                    Excecao = new Excecao
                    {
                        Mensagem = "CNPJ do interessado tem que ser o mesmo da empresa logada."  
                    }
                };
            }
            
            var portador = _portadorAppService.Repository.Query.FirstOrDefault(p =>
                p.CpfCnpj == req.CpfCnpjTransportador && p.RNTRC == req.RNTRCTransportador);

            if (portador == null)
            {
                return new ConsultarSituacaoTransportadorResp
                {
                    Sucesso = false,
                    Excecao = new Excecao
                    {
                        Mensagem = "Não foi encontrado nenhum transportador para o CPF/CNPJ e RNTRC informado."  
                    }
                };
            }
            
            return _ciotClientEngine.ConsultarSituacaoTransportador(req);
        }
        
        public ConsultarPorIdCiotResponse BuscarPorNumero(string ciot, string verificador)
        {
            var lDados = Repository.Query
                .Include(a => a.PortadorProp)
                .Include(a => a.PortadorMot)
                .Include(a => a.Empresa)
                .Include(a => a.CiotVeiculo)
                .Include(a => a.CiotViagem)
                .FirstOrDefault(a => a.Ciot == ciot && a.Verificador == verificador);

            var lCiot = Mapper.Map<ConsultarPorIdCiotResponse>(lDados);

            lCiot.veiculosList = Repository.Query.GetVeiculos(lDados.Id).ProjectTo<CiotVeiculoResponse>().ToList();
            lCiot.viagensList = Repository.Query.GetCiotViagens(lDados.Id).ProjectTo<CiotViagemResponse>().ToList();

            return lCiot;
        }

        //CIOT
        public async Task<CancelarOperacaoTransporteResp> CancelarOperacao(CancelarOperacaoTransporteReq req)
        {
            return await _operacoesAppService.CancelarOperacaoTransporte(req);
        }

        public async Task<DeclararOperacaoTransporteResp> DeclararOperacaoTransporte(DeclararOperacaoTransporteReq req)
        {
            return await _operacoesAppService.DeclararOperacaoTransporte(req);
        }

        public async Task<EncerrarOperacaoTransporteResp> EncerrarOperacaoTransporte(EncerrarOperacaoTransporteReq req)
        {
            return await _operacoesAppService.EncerrarOperacaoTransporte(req);
        }
        
        public async Task<EncerrarOperacaoTransporteResp> EncerrarOperacaoTransporteBbcService(EncerrarOperacaoTransporteReq req)
        {
            return await _operacoesAppService.EncerrarOperacaoTransporteBbcService(req);
        }

        public async Task<RetificarOperacaoTransporteResp> RetificarOperacaoTransporte(RetificarOperacaoTransporteReq req)
        {
            return await _operacoesAppService.RetificarOperacaoTransporte(req);
        }

        public async Task<DeclararOperacaoTransporteResp> GerarCiot(CiotRequest lModel)
        {
            var lLog = LogManager.GetCurrentClassLogger();
            try
            {
                #region Declaração de variáveis e consultas iniciais no banco
                
                var lOperacaoTransporte = new DeclararOperacaoTransporteReq();
                var lFrete = new Frete();
                var lPortadorContratadoBanco = _portadorAppService.Repository.Query.GetById(lModel.PortadorPropId);
                var lPortadorMotoristaBanco = _portadorAppService.Repository.Query.GetById(lModel.PortadorMotId);
                var lEmpresaBanco = _empresaAppService.Repository.Query.GetById(User.EmpresaId);
                var lNaturezaCargaId = 0;
                var lDestinatarioId = 0;
                var lConsignatarioId = 0;
                var lRemetenteId = 0;
                var lCidadeOrigem = 0;
                var lCidadeDestino = 0;
                var lPeso = "";
                
                foreach (var lViagemRequest in lModel.viagensList)
                {
                    lNaturezaCargaId = lViagemRequest.NaturezaCargaId ?? 0;
                    lDestinatarioId = lViagemRequest.DestinatarioId ?? 0;
                    lConsignatarioId = lViagemRequest.ConsignatarioId ?? 0;
                    lRemetenteId = lViagemRequest.RemetenteId ?? 0;
                    lCidadeOrigem = lViagemRequest.CidadeOrigemId ?? 0;
                    lCidadeDestino = lViagemRequest.CidadeDestinoId ?? 0;
                    lPeso = lViagemRequest.Peso;
                }
                
                var lNaturezaCargaBanco = _naturezaCargaAppService.Repository.Query.GetById(lNaturezaCargaId);
                var lDestinararioBanco = _clienteAppService.Repository.Query.GetById(lDestinatarioId);
                var lConsignatarioBanco = _clienteAppService.Repository.Query.GetById(lConsignatarioId);
                var lRemetenteBanco = _clienteAppService.Repository.Query.GetById(lRemetenteId);

                #endregion
                
                if (lModel.Tipo == TipoCiot.Padrão.ToInt())
                {
                    #region Destinatário

                    var lDestinatario = new Destinatario
                    {
                        NomeRazaoSocial = lDestinararioBanco.RazaoSocial,
                        CpfCnpj = lDestinararioBanco.CpfCnpj,
                        NomeFantasia = lDestinararioBanco.NomeFantasia,
                        TipoPessoa = lDestinararioBanco.CpfCnpj.Length == 11 ? "F" : "J"
                    };
                    
                    var lDestinatarioEndereco = new Endereco
                    {
                        CEP = lDestinararioBanco.Cep,
                        CodigoMunicipio = GetCodigoCidade(lDestinararioBanco.CidadeId),
                        Logradouro = lDestinararioBanco.Endereco,
                        Complemento = lDestinararioBanco.Complemento,
                        Bairro = lDestinararioBanco.Bairro,
                        Telefone = lDestinararioBanco.Telefone,
                        Email = lDestinararioBanco.Email
                    };
                    
                    #endregion

                    #region Consignatário
                    
                    var lConsignatario = new Consignatario()
                    {
                        NomeRazaoSocial = lConsignatarioBanco.RazaoSocial,
                        CpfCnpj = lConsignatarioBanco.CpfCnpj,
                        NomeFantasia = lConsignatarioBanco.NomeFantasia,
                        TipoPessoa = lConsignatarioBanco.CpfCnpj.Length == 11 ? "F" : "J"
                    };

                    var lConsignatarioEndereco = new Endereco
                    {
                        CEP = lConsignatarioBanco.Cep,
                        CodigoMunicipio = GetCodigoCidade(lConsignatarioBanco.CidadeId),
                        Logradouro = lConsignatarioBanco.Endereco,
                        Complemento = lConsignatarioBanco.Complemento,
                        Bairro = lConsignatarioBanco.Bairro,
                        Telefone = lConsignatarioBanco.Telefone,
                        Email = lConsignatarioBanco.Email
                    };

                    #endregion

                    #region Remetente

                    var lRemetente = new Remetente
                    {
                        NomeRazaoSocial = lRemetenteBanco.RazaoSocial,
                        CpfCnpj = lRemetenteBanco.CpfCnpj,
                        NomeFantasia = lRemetenteBanco.NomeFantasia,
                        TipoPessoa = lRemetenteBanco.CpfCnpj.Length == 11 ? "F" : "J"
                    };

                    var lRemetenteEndereco = new Endereco
                    {
                        CEP = lRemetenteBanco.Cep,
                        CodigoMunicipio = GetCodigoCidade(lRemetenteBanco.CidadeId),
                        Logradouro = lRemetenteBanco.Endereco,
                        Complemento = lRemetenteBanco.Complemento,
                        Bairro = lRemetenteBanco.Bairro,
                        Telefone = lRemetenteBanco.Telefone,
                        Email = lRemetenteBanco.Email
                    };
                    
                    #endregion
                    
                    #region Preenchendo as variáveis da Operação de Transporte

                    lOperacaoTransporte.Destinatario = lDestinatario;
                    lOperacaoTransporte.Destinatario.Endereco = lDestinatarioEndereco;
                    lOperacaoTransporte.Consignatario = lConsignatario;
                    lOperacaoTransporte.Consignatario.Endereco = lConsignatarioEndereco;
                    lOperacaoTransporte.Remetente = lRemetente;
                    lOperacaoTransporte.Remetente.Endereco = lRemetenteEndereco;

                    #endregion

                    #region Preenchendo as variáveis do Frete

                    lFrete.CodigoNaturezaCarga = lNaturezaCargaBanco.Codigo;
                    lFrete.DataInicioFrete = DateTime.Now.ToLocalTime();
                    lFrete.CodigoMunicipioOrigem = GetCodigoCidade(lCidadeOrigem);
                    lFrete.CodigoMunicipioDestino = GetCodigoCidade(lCidadeDestino);
                    lFrete.PesoCarga = 
                        decimal.Parse(lPeso, NumberStyles.Any, new CultureInfo("pt-BR")) == 0
                            ? (decimal?) null
                            : decimal.Parse(lPeso, NumberStyles.Any, new CultureInfo("pt-BR"));
                    
                    #endregion
                }

                #region Frete

                lFrete.DataTerminoFrete = lModel.DataFim.ToLocalTime();
                lFrete.TipoViagem = lModel.Tipo;
                
                #endregion

                #region Frete Contratado

                var lFreteContratado = new Proprietario
                {
                    RNTRC = lPortadorContratadoBanco.RNTRC,
                    NomeRazaoSocial = lPortadorContratadoBanco.Nome,
                    CpfCnpj = lPortadorContratadoBanco.CpfCnpj,
                    NomeFantasia = lPortadorContratadoBanco.Nome,
                    TipoPessoa = lPortadorContratadoBanco.CpfCnpj.Length == 11 ? "F" : "J"
                };

                var lFreteContratadoEndereco = new Endereco()
                {
                    CEP = lPortadorContratadoBanco.Cep,
                    CodigoMunicipio = GetCodigoCidade(lPortadorContratadoBanco.CidadeId),
                    Logradouro = lPortadorContratadoBanco.Endereco,
                    Numero = lPortadorContratadoBanco.EnderecoNumero.ToString(),
                    Complemento = lPortadorContratadoBanco.Complemento,
                    Bairro = lPortadorContratadoBanco.Bairro,
                    Telefone = lPortadorContratadoBanco.Telefone,
                    Email = lPortadorContratadoBanco.Email
                };
                
                #endregion

                #region Contratante
                
                var lContratante = new Contratante()
                {
                    RNTRC = lModel.Tipo == TipoCiot.Padrão.ToInt() ? null : lEmpresaBanco.RNTRC,
                    NomeRazaoSocial = lEmpresaBanco.RazaoSocial,
                    CpfCnpj = lEmpresaBanco.Cnpj,
                    NomeFantasia = lEmpresaBanco.NomeFantasia,
                    TipoPessoa = lEmpresaBanco.Cnpj.Length == 11 ? "F" : "J"
                };

                var lContratanteEndereco = new Endereco
                {
                    CEP = lEmpresaBanco.Cep,
                    CodigoMunicipio = GetCodigoCidade(lEmpresaBanco.CidadeId),
                    Logradouro = lEmpresaBanco.Endereco,
                    Complemento = lEmpresaBanco.Complemento,
                    Bairro = lEmpresaBanco.Bairro,
                    Telefone = lEmpresaBanco.Telefone,
                    Email = lEmpresaBanco.Email
                };

                #endregion

                #region Valores Frete

                var lValores = new ValoresFrete
                {
                    ValorFrete = lModel.ValorFrete != null ? decimal.Parse(lModel.ValorFrete, NumberStyles.Any, new CultureInfo("pt-BR")) : 0,
                    ValorCombustivel = lModel.ValorCombustivel != null ? decimal.Parse(lModel.ValorCombustivel, NumberStyles.Any, new CultureInfo("pt-BR")) : 0,
                    ValorDespesas = lModel.ValorDespesas != null? decimal.Parse(lModel.ValorDespesas, NumberStyles.Any, new CultureInfo("pt-BR")) : 0,
                    TotalImposto = lModel.ValorImposto != null ? decimal.Parse(lModel.ValorImposto, NumberStyles.Any, new CultureInfo("pt-BR")) : 0,
                    TotalPegadio = lModel.ValorPedagio != null ? decimal.Parse(lModel.ValorPedagio, NumberStyles.Any, new CultureInfo("pt-BR")) : 0,
                    ValorTarifas = lModel.ValorTarifas != null ? decimal.Parse(lModel.ValorTarifas, NumberStyles.Any, new CultureInfo("pt-BR")) : 0,
                    QuantidadeTarifas = lModel.QuantidadeTarifas
                };
                
                #endregion

                #region Veículos
                
                var lVeiculos = GetVeiculos(lModel, lPortadorContratadoBanco);
                
                #endregion

                #region Pagamento

                var lFormaPagtoCaruana = RetornarPagamentoCaruana(lModel.FormaPagamento);
                
                var lPagamento = new Pagamento
                {
                    FormaPagamento = lFormaPagtoCaruana,
                    Parcelas = GetParcelas(lModel),
                    ParcelaUnica = true,
                    InfoPagamento = lModel.TipoConta,
                    BancoPagamento = lModel.Banco,
                    AgenciaPagamento = lModel.Agencia,
                    ContaPagamento = lModel.Conta,
                };

                #endregion
                
                
                #region Frete Motorista

                var lFreteMotorista = new Motorista()
                {
                    Nome = lPortadorMotoristaBanco.Nome,
                    CpfCnpj = lPortadorMotoristaBanco.CpfCnpj,
                    NumeroCNH = lPortadorMotoristaBanco.NumeroCNH
                };
                
                #endregion

                #region Preenchendo as variáveis da Operação de Transporte

                lOperacaoTransporte.Frete = lFrete;
                lOperacaoTransporte.Frete.Proprietario = lFreteContratado;
                lOperacaoTransporte.Frete.Proprietario.Endereco = lFreteContratadoEndereco;
                lOperacaoTransporte.Frete.Motorista = lFreteMotorista;
                lOperacaoTransporte.Contratante = lContratante;
                lOperacaoTransporte.Contratante.Endereco = lContratanteEndereco;
                lOperacaoTransporte.Valores = lValores;
                lOperacaoTransporte.Veiculos = lVeiculos;
                lOperacaoTransporte.Pagamento = lPagamento;
                
                #endregion

                var request = JsonConvert.SerializeObject(lOperacaoTransporte);
                var lRetorno = await DeclararOperacaoTransporte(lOperacaoTransporte);

                if (lRetorno.Excecao.Codigo != null) 
                    return lRetorno;
                
                await UpdateViagemToCiot(lModel.viagensList, lRetorno.CIOT, lRetorno.CodigoVerificador);
                return lRetorno;
            }
            catch (Exception e)
            {
                lLog.Error(e, "Erro ao efetuar declaracao de CIOT, erro: ");
                throw new DeclararCiotException("Erro ao efetuar declaracao de CIOT!");
            }
        }

        public int RetornarPagamentoCaruana(int formaPagamento)
        {
            switch (formaPagamento)
            {
                case 0:
                    return 2;
                case 1:
                    return 1;
                case 2:
                    return 3;
                case 3:
                    return 3;
            }

            throw new DeclararCiotException("Erro ao efetuar transação!");
        }

        public async Task<RespPadrao> RetificarCiotOperacaoTransporte(RetificarOperacaoTransporteReq request)
        {
            var lLog = LogManager.GetCurrentClassLogger();
        
            try
            {
                if (request.CpfCnpjClienteAdmOrCompanyGroup != null)
                    request.CpfCnpjClienteAdmOrCompanyGroup = request.CpfCnpjClienteAdmOrCompanyGroup.OnlyNumbers();
                
                var lRetorno = await RetificarOperacaoTransporte(request);

                if (!lRetorno.Sucesso)
                    return new RespPadrao
                    {
                        sucesso = false,
                        mensagem = lRetorno.Excecao.Mensagem
                    };

                return new RespPadrao
                {
                    sucesso = true,
                    mensagem = ""
                };
            }
            catch (Exception e)
            {
                lLog.Error(e, "Erro ao efetuar retificação de CIOT, erro: ");
                throw new DeclararCiotException("Erro ao efetuar retificação!");
            }
        }

        public int? GetCodigoCidade(int? CidadeId)
        {
            if (CidadeId == null) return null;

            return _cidadeAppService.Repository.Query.FirstOrDefault(a => a.Id == CidadeId).Ibge.ToInt();
        }

        public ParcelaPagamento[] GetParcelas(CiotRequest lModel)
        {
            var result = new List<ParcelaPagamento>();

            result.Add(new ParcelaPagamento
            {
                CodigoParcela = "1",
                ValorParcela = lModel.ValorFrete != null ? decimal.Parse(lModel.ValorFrete, NumberStyles.Any, new CultureInfo("pt-BR")) : 0,                
                Vencimento = lModel.DataFim
            });
            return result.ToArray();
        }

        public Domain.External.CIOT.DTO.Veiculo[] GetVeiculos(CiotRequest lModel, Domain.Models.Portador.Portador lProprietario)
        {
            var result = new List<Domain.External.CIOT.DTO.Veiculo>();

            foreach (var lVeiculoslist in lModel.veiculosList)
                result.Add(new Domain.External.CIOT.DTO.Veiculo
                    {Placa = lVeiculoslist.Placa.Replace("-", ""), RNTRC = lProprietario.RNTRC});

            return result.ToArray();
        }
    }
}
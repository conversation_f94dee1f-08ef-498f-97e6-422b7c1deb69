using System;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using SistemaInfo.BBC.ApiIntegracao.Controllers.Base;
using SistemaInfo.BBC.Application.Interface.Viagem;
using SistemaInfo.BBC.Application.Objects.Api.Viagem;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Domain.External.CIOT.DTO;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;

namespace SistemaInfo.BBC.ApiIntegracao.Controllers
{
    /// <summary>
    /// Versão 2 da API de Viagem - Novas implementações e funcionalidades
    /// Acessível diretamente via /v2/Viagem/ sem necessidade de parâmetros de versão
    /// </summary>
    [ApiVersion("2.0")]
    [Route("v2/Viagem")]
    public class ViagemV2Controller : ApiControllerBase
    {
        private readonly IViagemIntegracaoAppService _viagemIntegracaoAppService;

        /// <summary>
        /// Injeção de dependecias e herança
        /// </summary>
        /// <param name="engine"></param>
        /// <param name="viagemIntegracaoAppService"></param>
        public ViagemV2Controller(IAppEngine engine, IViagemIntegracaoAppService viagemIntegracaoAppService) : base(engine)
        {
            _viagemIntegracaoAppService = viagemIntegracaoAppService;
        }
        
        /// <summary>
        /// Metodo responsavel pelo cancelamento de Eventos de pagamento relacionado a viagem
        /// </summary>
        /// <param name="cancelarEventoViagemRequest"></param>
        /// <returns></returns>
        [Produces("application/json")]
        [HttpPost("Cancelar")]
        public async Task<JsonResult> CancelamentoPagamento([FromBody] CancelamentoEventoViagemV2Request cancelarEventoViagemRequest)
        {
            try
            {
                var lIntegrarPag =
                    await _viagemIntegracaoAppService.Cancelar(cancelarEventoViagemRequest, Engine.User.EmpresaId);

                return lIntegrarPag.Sucesso
                    ? ResponseBaseApi.ResponderSucesso(lIntegrarPag.Data.Viagem)
                    : ResponseBaseApi.ResponderErro(lIntegrarPag.Mensagem);
            }
            catch (Exception e)
            {
                return ResponseBaseApi.ResponderErro("Não foi possível realizar a operação. Mensagem: " + e.Message);
            }
        }

        /// <summary>
        /// Método responsável por integrar uma viagem completa na versão 2
        /// Inclui controle de CIOT, múltiplos pagamentos, veículos e portadores
        /// </summary>
        /// <param name="viagemIntegrarRequest"></param>
        /// <returns></returns>
        [Produces("application/json")]
        [HttpPost("Integrar")]
        public async Task<JsonResult> Integrar([FromBody] ViagemIntegrarV2Request viagemIntegrarRequest)
        {
            try
            {
                var lIntegrarViagem = await _viagemIntegracaoAppService
                    .Integrar(viagemIntegrarRequest, HttpContext.Request.Headers["x-web-auth-token"]);

                return lIntegrarViagem.sucesso ?
                    ResponseBaseApi.ResponderSucesso(lIntegrarViagem.data) :
                    ResponseBaseApi.ResponderErro(lIntegrarViagem.mensagem);
            }
            catch (Exception e)
            {
                return ResponseBaseApi.ResponderErro("Não foi possível realizar a operação V2. Mensagem: " + e.Message);
            }
        }
        
        /// <summary>
        /// Método responsável por consultar informações de TAC Agregado
        /// Verifica se existe operação de transporte TAC Agregado para os CNPJs informados
        /// </summary>
        /// <param name="request">Dados de consulta contendo CNPJ do proprietário e contratante</param>
        /// <returns>Informações do TAC Agregado encontrado</returns>
        [ApiExplorerSettings(IgnoreApi = true)] 
        [Produces("application/json")]
        [HttpPost("ConsultarTacAgregado")]
        public async Task<JsonResult> ConsultarTacAgregado([FromBody] ConsultarTacAgregadoRequest request)
        {
            try
            {
                var resultado = await _viagemIntegracaoAppService
                    .ConsultarTacAgregado(request);
        
                return resultado.sucesso ?
                    ResponseBaseApi.ResponderSucesso(resultado.data) :
                    ResponseBaseApi.ResponderErro("Nenhum TAC Agredado em aberto");
            }
            catch (Exception e)
            {
                return ResponseBaseApi.ResponderErro("Não foi possível verificar se possui tac agregado. Mensagem: " + e.Message);
            }
        }
    }
}
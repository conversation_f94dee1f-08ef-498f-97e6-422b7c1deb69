﻿using System.Linq;
using System.Reflection;
using BBC.Test.Tests.Viagem.Fixture;
using Moq;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Services.Viagem;
using SistemaInfo.BBC.Domain.Models.Transacao.Repository;
using Xunit;

namespace BBC.Test.Tests.Viagem;


[Collection(nameof(ViagemCollection))]
public class ViagemAppServiceConsultarTransacaoPorIdTest
{
    private readonly ViagemFixture _fixture;
    private readonly ViagemAppService _appService;
    private readonly Mock<ITransacaoReadRepository> _readRepository;
    public ViagemAppServiceConsultarTransacaoPorIdTest(ViagemFixture fixture)
    {
        _fixture = fixture;
        _appService = fixture.Mocker.CreateInstance<ViagemAppService>();
        _readRepository = fixture.Mocker.GetMock<ITransacaoReadRepository>();
    }
    
    [Fact(DisplayName = "Consulta Transacao por Id")]
    [Trait("ViagemAppService", "ConsultarTransacaoPorId")]
    public void ViagemConsultarTransacaoPorId_ConsultaPadrao_DeveRetornarPagamentoEventoResponse()
    {
        var idReq = 1;
        //Arrange
        var fakeReturn = _fixture.GerarTransacao();

        _readRepository.Setup(c => c.FirstOrDefault(a => a.Id == idReq)).Returns(fakeReturn);
        
        //Action
        var lResponse =  _appService.ConsultarTransacaoPorId(idReq).Result;
            
        //Assert
        Assert.True(TestHelper.TemMesmaEstrutura(lResponse, new RespPadrao()));
    }
    
    [Fact(DisplayName = "Consulta Trasacao por Id inválido")]
    [Trait("ViagemAppService", "ConsultarTransacaoPorId")]
    public void ViagemConsultarPorId_IdInvalido_DeveRetornarNull()
    {
        // Arrange
        var transacaoId = -1; // ID negativo inválido
        _readRepository.Setup(c => c.FirstOrDefault(a => a.Id == transacaoId)).Returns(null as SistemaInfo.BBC.Domain.Models.Transacao.Transacao);
        
        // Action
        var response = _appService.ConsultarTransacaoPorId(transacaoId);

        // Assert
        if (response.Exception != null) Assert.Equal("Id inválido.", response.Exception.Message);
    }
}
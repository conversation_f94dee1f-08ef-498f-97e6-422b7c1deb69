

using System;
using System.ComponentModel;

namespace SistemaInfo.BBC.Domain.Enum
{
    public enum StatusEvento
    {
        Bloqueado = 0,
        Aberto = 1,
        Baixado = 2,
        Cancelado = 3,
        <PERSON>dente = 4,
        <PERSON>rro = 5
    }

    public enum StatusPagamento
    {
        [Description("EXECUTED")] 
        Fechado = 0,
        Aberto = 1,
        [Description("PENDING")] 
        Pendente = 2,
        Erro = 3,
        [Description("Cancelado")] 
        Cancelado = 4,
        Processando = 5,
        [Description("NOT_EXECUTED")] 
        NaoExecutado = 6
        //Removido a pedido do cliente
        //[Description("SCHEDULED_RELEASES")] LancamentoProgramado = 7
    }

    public enum TipoOperacao
    {
        Notificacao = 0,
        Pendencia = 1,
        Erro = 3
    }


    public enum StatusAntecipacaoParcelaProprietario
    {
        [Description("Disponível")]
        Disponivel = 0,
        [Description("AguardandoProcessamento")]
        AguardandoProcessamento = 1,
        [Description("Aprovado")]
        Aprovado = 2,
        [Description("Erro")]
        Erro = 3,
        [Description("Não Disponível")]
        NaoDisponivel = 4
    }
}
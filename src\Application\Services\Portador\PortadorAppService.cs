using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Net.Mail;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using AutoMapper.QueryableExtensions;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using NLog;
using SistemaInfo.BBC.Application.Email.Usuario;
using SistemaInfo.BBC.Application.External.Conductor.Interface;
using SistemaInfo.BBC.Application.Helpers;
using SistemaInfo.BBC.Application.Interface.Cidade;
using SistemaInfo.BBC.Application.Interface.Portador;
using SistemaInfo.BBC.Application.Objects.Api.Portador;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Mobile.Posto;
using SistemaInfo.BBC.Application.Objects.Web.Portador;
using SistemaInfo.BBC.Application.Objects.Web.PortadorCentroCusto;
using SistemaInfo.BBC.Application.Objects.Web.Transportador;
using SistemaInfo.BBC.Domain.Components.Email;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Domain.External.Conductor.DTO.Cartao;
using SistemaInfo.BBC.Domain.External.Conductor.DTO.Token;
using SistemaInfo.BBC.Domain.External.Conductor.Interface;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.BBC.Domain.Models.Cidade.Repository;
using SistemaInfo.BBC.Domain.Models.Empresa.Repository;
using SistemaInfo.BBC.Domain.Models.Estado.Repository;
using SistemaInfo.BBC.Domain.Models.Parametros.Repository;
using SistemaInfo.BBC.Domain.Models.Portador.Commands;
using SistemaInfo.BBC.Domain.Models.Portador.Repository;
using SistemaInfo.BBC.Domain.Models.PortadorCentroCusto.Commands;
using SistemaInfo.BBC.Domain.Models.PortadorEmpresa;
using SistemaInfo.BBC.Domain.Models.PortadorEmpresa.Commands;
using SistemaInfo.BBC.Domain.Models.PortadorEmpresa.Repository;
using SistemaInfo.BBC.Domain.Models.Usuario.Repository;
using SistemaInfo.BBC.Domain.Models.UsuarioFrota.Commands;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using SistemaInfo.Framework.Utils;
using PortadorRepLegalRequest = SistemaInfo.BBC.Application.Objects.Api.Portador.PortadorRepLegalRequestApi;

namespace SistemaInfo.BBC.Application.Services.Portador
{
    public class PortadorAppService :
        AppService<Domain.Models.Portador.Portador, IPortadorReadRepository, IPortadorWriteRepository>,
        IPortadorAppService
    {
        private readonly ICartaoAppService _cartaoAppService;
        private readonly ICidadeAppService _cidadeAppService;
        private readonly IEmpresaReadRepository _empresaReadRepository;
        private readonly IPortadorEmpresaReadRepository _portadorEmpresaReadRepository;
        private readonly IAceiteTermoRepository _aceiteTermoRepository;
        private readonly ICidadeReadRepository _cidadeReadRepository;
        private readonly IEstadoReadRepository _estadoReadRepository;
        private readonly ICartaoRepository _cartaoRepository;
        private readonly IPortadorReadRepository _portadorReadRepository;
        private readonly IParametrosReadRepository _parametrosReadRepository;
        private readonly IUsuarioReadRepository _usuarioReadRepository;
        private INotificationEmailExecutor _notificationEmailExecutor;

        public PortadorAppService(IAppEngine engine,
            IPortadorReadRepository readRepository,
            IPortadorWriteRepository writeRepository,
            ICartaoAppService cartaoAppService,
            ICidadeAppService cidadeAppService,
            IEmpresaReadRepository empresaReadRepository,
            IPortadorEmpresaReadRepository portadorEmpresaReadRepository,
            IAceiteTermoRepository aceiteTermoRepository,
            ICidadeReadRepository cidadeReadRepository,
            IEstadoReadRepository estadoReadRepository,
            ICartaoRepository cartaoRepository,
            IPortadorReadRepository portadorReadRepository,
            INotificationEmailExecutor notificationEmailExecutor,
            IParametrosReadRepository parametrosReadRepository,
            IUsuarioReadRepository usuarioReadRepository)
            : base(engine, readRepository, writeRepository)
        {
            _cartaoAppService = cartaoAppService;
            _cidadeAppService = cidadeAppService;
            _empresaReadRepository = empresaReadRepository;
            _portadorEmpresaReadRepository = portadorEmpresaReadRepository;
            _aceiteTermoRepository = aceiteTermoRepository;
            _cidadeReadRepository = cidadeReadRepository;
            _estadoReadRepository = estadoReadRepository;
            _cartaoRepository = cartaoRepository;
            _portadorReadRepository = portadorReadRepository;
            _notificationEmailExecutor = notificationEmailExecutor;
            _parametrosReadRepository = parametrosReadRepository;
            _usuarioReadRepository = usuarioReadRepository;
        }

        #region Operações portador

        public ConsultarGridPortadorResponse ConsultarGridPortador(int take, int page, OrderFilters orderFilters,
            List<QueryFilters> filters)
        {
            
            #region Consulta personalizada filtros da grid

            foreach (var item in filters)
            {
                item.Valor = item.Campo switch
                {
                    "cpfCnpj" => item.Valor.Replace(".", "").Replace("/", "").Replace("-", ""),
                    _ => item.Valor
                };
            }

            #endregion
            
            var lPortador = Repository.Query.GetAll();

            //Portador x empresa
            if (Engine.User.EmpresaId > 0)
            {
                var lPortadorEmp = _portadorEmpresaReadRepository.Where(x => x.EmpresaId == Engine.User.EmpresaId)
                    .ToList();

                lPortador = lPortador.Where(x => lPortadorEmp.Select(c => c.PortadorId).Contains(x.Id));
            }

            if (!Engine.User.IsNivelAdministradora &&
                !Engine.User.IsNivelSuperUsuario)
            {
                var lPortadorlist = lPortador.ToList();

                lPortadorlist.ForEach(portador =>
                {
                    portador.Ativo = portador.EmpresaIdFrota == Engine.User.EmpresaId ? portador.Ativo : 0;
                });
                lPortador = lPortadorlist.AsQueryable();
            }

            lPortador = lPortador.Where(x => x.Atividade != EAtividade.Frota);
            
            lPortador = lPortador.AplicarFiltrosDinamicos(filters);
            lPortador = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                ? lPortador.OrderByDescending(o => o.Id)
                : lPortador.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            var lRetorno = lPortador.Skip((page - 1) * take)
                .Take(take)
                .ToList();

            var retorno = Mapper.Map<List<ConsultarGridPortador>>(lRetorno);
            var lCount = lPortador.Count();

            return new ConsultarGridPortadorResponse()
            {
                items = retorno,
                totalItems = lCount
            };
        }

        public ConsultarGridPortadorResponse ConsultarGridPortadorCombo(int take, int page, OrderFilters orderFilters,
            List<QueryFilters> filters)
        {
            var lPortador = Repository.Query.GetAll();

            //Portador x empresa
            if (Engine.User.EmpresaId > 0)
            {
                var lPortadorEmp = _portadorEmpresaReadRepository.Where(x => x.EmpresaId == Engine.User.EmpresaId)
                    .ToList();

                lPortador = lPortador.Where(x => lPortadorEmp.Select(c => c.PortadorId).Contains(x.Id));
            }

            if (!Engine.User.IsNivelAdministradora &&
                !Engine.User.IsNivelSuperUsuario)
            {
                var lPortadorlist = lPortador.ToList();

                lPortadorlist.ForEach(portador =>
                {
                    portador.Ativo = portador.EmpresaIdFrota == Engine.User.EmpresaId ? portador.Ativo : 0;
                });
                lPortador = lPortadorlist.AsQueryable();
            }

            lPortador = lPortador.AplicarFiltrosDinamicos(filters);
            lPortador = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                ? lPortador.OrderByDescending(o => o.Id)
                : lPortador.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");
            lPortador = lPortador.Where(c => c.Ativo == 1);

            var lRetorno = lPortador.Skip((page - 1) * take)
                .Take(take)
                .ToList();

            var retorno = Mapper.Map<List<ConsultarGridPortador>>(lRetorno);
            var lCount = lPortador.Count();

            return new ConsultarGridPortadorResponse()
            {
                items = retorno,
                totalItems = lCount
            };
        }

        public async Task<ConsultarGridPortadorResponse> ConsultarGridPortadorEmpresaCombo(int take, int page,
            OrderFilters orderFilters,
            List<QueryFilters> filters)
        {
            var lPortador = _portadorEmpresaReadRepository.Where(pe => pe.EmpresaId == User.EmpresaId)
                .Select(x => x.Portador).AsQueryable().Where(z => z.Ativo == 1);

            lPortador = lPortador.AplicarFiltrosDinamicos(filters);
            lPortador = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                ? lPortador.OrderByDescending(o => o.Id)
                : lPortador.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            var lRetorno = await lPortador.Skip((page - 1) * take).Take(take).ToListAsync();

            var retorno = Mapper.Map<List<ConsultarGridPortador>>(lRetorno);
            var lCount = lPortador.Count();

            return new ConsultarGridPortadorResponse()
            {
                items = retorno,
                totalItems = lCount
            };
        }

        public async Task<ConsultarGridPortadorResponse> ConsultarGridPortadorPessoaFisica(int take, int page,
            OrderFilters orderFilters,
            List<QueryFilters> filters)
        {
            var lPortador = _portadorEmpresaReadRepository.Where(pe => pe.EmpresaId == User.EmpresaId)
                .Select(x => x.Portador).AsQueryable().Where(z => z.TipoPessoa == ETipoPessoa.Fisica && z.Ativo == 1);

            lPortador = lPortador.AplicarFiltrosDinamicos(filters);
            lPortador = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                ? lPortador.OrderByDescending(o => o.Id)
                : lPortador.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            var retorno = await lPortador.Skip((page - 1) * take)
                .Take(take)
                .ProjectTo<ConsultarGridPortador>().ToListAsync();

            var lCount = lPortador.Count();

            return new ConsultarGridPortadorResponse()
            {
                items = retorno,
                totalItems = lCount
            };
        }

        public ConsultarPorIdPortadorResponse BuscarPorId(int id)
        {
            try
            {
                new LogHelper().LogOperationStart("BuscarPorId");

                if (id < 0)
                {
                    throw new Exception("ID inválido!");
                }

                var lDados = Repository.Query.GetByIdIncludes(id);

                var lPortador = Mapper.Map<ConsultarPorIdPortadorResponse>(lDados);

                lPortador.PortadorCentroCusto = Repository.Query.GetCentroCusto(id)
                    .ProjectTo<PortadorCentroCustoResp>().ToList();

                lPortador.RepLegaisList = Repository.Query.GetRepLegais(id)
                    .ProjectTo<PortadorRepLegalResponse>().ToList();

                if (lDados.EmpresaIdFrota > 0)
                {
                    lPortador.EmpresaId = lDados?.EmpresaIdFrota;

                    lPortador.EmpresaNome = lDados?.EmpresaFrota.NomeFantasia;
                }
                else
                {
                    lPortador.EmpresaId = _portadorEmpresaReadRepository
                        .FirstOrDefault(x => x.PortadorId == lPortador.Id)?
                        .EmpresaId;

                    lPortador.EmpresaNome = _empresaReadRepository
                        .FirstOrDefault(x => x.Id == lPortador.EmpresaId)?
                        .NomeFantasia;
                }

                var lConta = _cartaoAppService.ConsultarContas(null, null, null, null, lPortador.CpfCnpj, true);

                if (lConta != null)
                {
                    if (lConta.Result.content != null)
                    {
                        if (!lConta.Result.content.FirstOrDefault().Sucesso)
                        {
                            throw new Exception("");
                        }

                        var lContaId = lConta.Result.content.FirstOrDefault().id;

                        var lDadosConta = _cartaoAppService.ConsultarContasPorId(lContaId).Result;

                        lPortador.ContaConductor = new List<PortadorContaResponse>();

                        lPortador.ContaConductor.Add(new PortadorContaResponse
                        {
                            ContaId = lDadosConta.Id.ToIntSafe(),
                            NumeroConta = lDadosConta.NumeroContaCorrente,
                            Agencia = lDadosConta.NumeroAgencia.ToIntSafe(),
                            StatusConta = lDadosConta.StatusConta
                        });
                    }
                }

                return lPortador;
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar BuscarPorId");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("BuscarPorId");
            }
        }

        public async Task<RespPadrao> EnviarEmailAvisoDesvinculacaoPortador(PortadorSalvarCommand portador,
            int empresaAntigaId)
        {
            if (portador == null || empresaAntigaId == 0)
            {
                return new RespPadrao
                {
                    sucesso = false,
                    mensagem = "Parâmetros portador ou empresaAntigaId inválidos."
                };
            }

            var lEmpresa = await _empresaReadRepository.GetByIdAsync(empresaAntigaId);

            var lGestoresDeCadastrosDaAntigaEmpresa = await _usuarioReadRepository
                .Where(u => u.EmpresaId == empresaAntigaId)
                .Include(u => u.GrupoUsuario)
                .ThenInclude(g => g.GrupoUsuarioMenu)
                .ThenInclude(m => m.Menu)
                .Where(u => u.GrupoUsuario.GrupoUsuarioMenu.FirstOrDefault(m => m.Menu.Descricao == "Portador") != null)
                .ToListAsync();

            foreach (var lGestor in lGestoresDeCadastrosDaAntigaEmpresa)
            {
                EmailUsuarioGestorNotificacaoDesvinculoPortador.EnviarEmail(_notificationEmailExecutor,
                    lGestor.Email, portador.CpfCnpj, portador.Nome, lGestor.Nome, lEmpresa.NomeFantasia);
            }

            return new RespPadrao
            {
                sucesso = true,
                mensagem = "Emails enviados com sucesso!"
            };
        }

        public async Task<RespPadrao> Save(PortadorRequest request)
        {
            try
            {
                var lRetornoRecuperarSenha = false;

                //Se um usuario não administrador está tentando cadastrar pra outra empresa que não a dele
                if (Engine.User.EmpresaId != 0 && request.EmpresaId != Engine.User.EmpresaId)
                {
                    return new RespPadrao
                    {
                        sucesso = false,
                        mensagem = "Empresa selecionada diferente da empresa do usuário atual."
                    };
                }

                var lEmpresaId = Engine.User.EmpresaId != 0 ? Engine.User.EmpresaId : request.EmpresaId;

                var lPortadorJaExistente = await _portadorReadRepository.GetByCpfCnpjAsync(request.CpfCnpj);

                int? lEmpresaAntigaId = lPortadorJaExistente?.EmpresaIdFrota;

                List<PortadorEmpresa> lJaCadastradoNessaEmpresa;
                //Se for um cadastro novo
                if (request.Id == "Auto" || request.Id.IsNullOrWhiteSpace())
                {
                    //Consultas para cadastros existentes do CPF/CNPJ
                    var lPortadorEmpresa = _portadorEmpresaReadRepository
                        .Include(x => x.Portador);

                    var lCadastradoEmOutraEmpresa = lPortadorEmpresa
                        .Where(x => x.EmpresaId != lEmpresaId && x.Portador.CpfCnpj == request.CpfCnpj)
                        .ToList();

                    lJaCadastradoNessaEmpresa = lPortadorEmpresa
                        .Where(x => x.EmpresaId == lEmpresaId && x.Portador.CpfCnpj == request.CpfCnpj)
                        .ToList();

                    //Se não encontrou empresa
                    if (lEmpresaId == 0 || lEmpresaId == null)
                    {
                        return new RespPadrao
                        {
                            sucesso = false,
                            mensagem = "Empresa não encontrada."
                        };
                    }

                    //Se ta cadastrado em outra empresa
                    if (lCadastradoEmOutraEmpresa.Count > 0 && lJaCadastradoNessaEmpresa.Count == 0)
                    {
                        if (request.Id == "Auto" && request.Atividade == EAtividade.Frota)
                        {
                            lRetornoRecuperarSenha = RecuperarSenha(new PortadorRecuperarSenhaRequest()
                            {
                                CpfCnpj = request.CpfCnpj,
                                Mobile = false
                            }).Result.sucesso;
                        }
                    }

                    request.Id = null;

                    request.CpfCnpj.OnlyNumbers();

                    var lCpfCnpjCadastrado = await Repository.Query
                        .FirstOrDefaultAsync(o => o.CpfCnpj == request.CpfCnpj);

                    if (lCpfCnpjCadastrado != null && lCpfCnpjCadastrado.Id > 0)
                    {
                        request.Id = lCpfCnpjCadastrado.Id.ToString();
                    }
                }

                var lRequestIdSafe = request.Id.ToIntSafe();

                var lEmpresaPortadorExistente = await _portadorEmpresaReadRepository
                    .GetByPortadorIdAndEmpresaId(lRequestIdSafe, request.EmpresaId ?? 0);

                var lRetornoCadastraPessoa = await CadastraPessoaConductorWeb(request);

                if (request.Id.ToInt() > 0)
                    request.SenhaApi = !request.SenhaApi.IsNullOrWhiteSpace()
                        ? request.SenhaApi.GetHashSha1()
                        : Repository.Query.GetById(request.Id.ToInt()).SenhaApi;

                var lPortador = Mapper.Map<PortadorSalvarComRetornoCommand>(request);

                lPortador.ValidarCadastro();

                lPortador.EmpresaIdFrota = request.EmpresaId;

                var lRetorno = await Engine.CommandBus.SendCommandAsync<Domain.Models.Portador.Portador>(lPortador);

                if (lEmpresaAntigaId != null && lRetorno.EmpresaIdFrota != lEmpresaAntigaId)
                {
                    await EnviarEmailAvisoDesvinculacaoPortador(lPortador, (int)lEmpresaAntigaId);
                }

                if (lEmpresaPortadorExistente == null)
                {
                    await SalvarPortadorEmpresa(new PortadorEmpresaRequest
                    {
                        EmpresaId = lEmpresaId ?? 0,
                        PortadorId = lRetorno?.Id ?? 0
                    });
                }

                if (string.IsNullOrWhiteSpace(request.Id) && request.Atividade == EAtividade.Frota)
                {
                    lRetornoRecuperarSenha = RecuperarSenha(new PortadorRecuperarSenhaRequest()
                    {
                        CpfCnpj = request.CpfCnpj,
                        Mobile = false
                    }).Result.sucesso;
                }

                if (!lRetornoRecuperarSenha && string.IsNullOrWhiteSpace(request.Id) &&
                    request.Atividade == EAtividade.Frota)
                {
                    return new RespPadrao
                    {
                        sucesso = false,
                        mensagem = "Cadastro realizado, porém senha não enviada para o portador!"
                    };
                }

                if (lRetornoCadastraPessoa == null)
                    return new RespPadrao
                    {
                        sucesso = true,
                        mensagem = "Operação realizada com sucesso!"
                    };

                if (!lRetornoCadastraPessoa.sucesso)
                    return new RespPadrao
                    {
                        sucesso = true,
                        mensagem =
                            "Portador salvo com sucesso, porém não foi possível salvar a pessoa na conductor."
                    };

                return new RespPadrao
                {
                    sucesso = true,
                    mensagem = "Operação realizada com sucesso!"
                };
            }
            catch (Exception e)
            {
                return new RespPadrao
                {
                    sucesso = false,
                    mensagem = e.Message
                };
            }
        }

        public async Task<RespPadrao> CadastrarPortadorAutomatico(CadastroPortadorPainelCiotRequest request)
        {
            var lLog = LogManager.GetCurrentClassLogger();
            try
            {
                var portadorExistente = await _portadorReadRepository.GetByCpfCnpjAsync(request.CpfCnpj);
                bool jaCadastradoNaEmpresaLogada;
                var lEmpresaId = User.EmpresaId;

                if (portadorExistente != null)
                {
                    //Consultas para cadastros existentes do CPF/CNPJ
                    var lPortadorEmpresa = _portadorEmpresaReadRepository
                        .Include(x => x.Portador);

                    jaCadastradoNaEmpresaLogada = lPortadorEmpresa.Any(x =>
                        x.EmpresaId == lEmpresaId && x.Portador.CpfCnpj == request.CpfCnpj);

                    if (jaCadastradoNaEmpresaLogada)
                    {
                        return new RespPadrao(false, "Portador ja cadastrado para essa empresa!", null);
                    }

                    {
                        await SalvarPortadorEmpresa(new PortadorEmpresaRequest()
                        {
                            EmpresaId = User.EmpresaId,
                            PortadorId = portadorExistente.Id
                        });
                    }

                    await Engine.CommandBus.SendCommandAsync(Mapper.Map<PortadorSalvarCommand>(portadorExistente));

                    lLog.Info(
                        $"Portador Vinculado Automaticamente na empresa {lEmpresaId} com o CNPJ {request.CpfCnpj} com Id {portadorExistente.Id}.");

                    return new RespPadrao(true, "Portador vinculado a empresa com sucesso!");
                }

                var portador = new PortadorRequest();
                portador.CpfCnpj = request.CpfCnpj;
                portador.Nome = request.Nome;
                portador.Email = request.Email;
                portador.RNTRC = request.Rntrc;
                portador.Cep = request.Cep;
                portador.Bairro = request.Bairro;
                portador.CidadeId = request.CidadeId;
                portador.EstadoId = request.EstadoId;
                portador.Celular = request.Celular;
                portador.Telefone = request.Telefone;
                portador.Endereco = request.Endereco;
                portador.Complemento = request.Complemento;
                portador.TipoPessoa = request.CpfCnpj.Length > 11 ? 2 : 1;

                var command = Mapper.Map<PortadorSalvarComRetornoCommand>(portador);
                var retorno = Engine.CommandBus.SendCommand<Domain.Models.Portador.Portador>(command);

                {
                    await SalvarPortadorEmpresa(new PortadorEmpresaRequest
                    {
                        EmpresaId = User.EmpresaId,
                        PortadorId = retorno.Id
                    });
                }

                lLog.Info(
                    $"Portador Cadastrado Automaticamente na empresa {lEmpresaId} com o CNPJ {request.CpfCnpj} com Id {retorno.Id}.");

                return new RespPadrao(true, "Portador cadastrado com sucesso!", retorno);
            }
            catch (Exception e)
            {
                lLog.Error(e);
                return new RespPadrao(false, "Não foi possível salvar o portador. " + e.Message);
            }
        }

        public async Task<RespPadrao> AlterarStatus(PortadorStatusRequest lPortadorStatus)
        {
            try
            {
                var lPortador = Mapper.Map<PortadorAlterarStatusCommand>(lPortadorStatus);
                await Engine.CommandBus.SendCommandAsync(lPortador);
                return new RespPadrao
                {
                    sucesso = true,
                    mensagem = "Operação realizada com sucesso!"
                };
            }
            catch (Exception e)
            {
                return new RespPadrao
                {
                    sucesso = false,
                    mensagem = "Erro interno ao tentar alterar o status do portador: " + e.Message
                };
            }
        }

        private int? GetIdUF(string ufEmissaoSigla)
        {
            var log = LogManager.GetCurrentClassLogger();
            log.Info("GetIdUF");
            return ufEmissaoSigla.IsNullOrWhiteSpace()
                ? null
                : Repository.Query.Include(e => e.Estado).FirstOrDefault(p => p.UfEmissao == ufEmissaoSigla.ToUpper())
                    ?.EstadoId;
        }

        public async Task<RespPadrao> CadastraPessoaConductorWeb(PortadorRequest lPortador)
        {
            try
            {
                new LogHelper().LogOperationStart("CadastraPessoaConductorWeb");
                lPortador.UfEstado = lPortador.UfEmissaoSigla;
                lPortador.UfEmissao = lPortador.UfEmissaoSigla != null
                    ? GetIdUF(lPortador.UfEmissaoSigla)
                    : lPortador.UfEmissao;

                if (lPortador.CidadeId != 0)
                    lPortador.NomeCidade = Repository.Query.Include(e => e.Cidade)
                        .FirstOrDefault(p => p.CidadeId == lPortador.CidadeId)?.Cidade.Nome;

                var lToken = await _aceiteTermoRepository.ConsultarTokens();

                if (lToken?.result != null)
                {
                    var lTokensList = new List<string>();
                    foreach (var token in lToken.result.regulatoryDocuments)
                    {
                        lTokensList.Add(token.token);
                    }

                    var request = new AutenticarTokensRequest
                    {
                        tokens = lTokensList
                    };
                    var autenticar = _aceiteTermoRepository.AutenticarTokens(request).Result;

                    if (autenticar.message != null)
                    {
                        if (autenticar.message.Equals("Agreements accepted successfully"))
                        {
                            return CadastrarPessoa(lPortador, lTokensList);
                        }
                        else
                        {
                            return new RespPadrao
                            {
                                sucesso = false,
                                mensagem = "Não foi possível validar as tokens"
                            };
                        }
                    }
                }

                return new RespPadrao
                {
                    sucesso = true,
                    mensagem = ""
                };
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar CadastraPessoaConductorWeb");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("CadastraPessoaConductorWeb");
            }
        }

        public RespPadrao CadastraPessoaConductorApi(PortadorRequest lPortador)
        {
            try
            {
                new LogHelper().LogOperationStart("CadastraPessoaConductorApi");
                lPortador.UfEmissao =
                    lPortador.UfEmissaoSigla.IsNullOrWhiteSpace() ? null : GetIdUF(lPortador.UfEmissaoSigla);

                var cidade = _cidadeReadRepository.Include(x => x.Estado)
                    .FirstOrDefault(x => x.Ibge == lPortador.CidadeId);

                if (cidade != null)
                {
                    lPortador.UfEstado = cidade.Estado.Uf;
                    lPortador.NomeCidade = cidade.Nome;
                }

                var lToken = _aceiteTermoRepository.ConsultarTokens();
                RespPadrao responseConductor = new RespPadrao();

                if (lToken.Result.result != null)
                {
                    var lTokensList = new List<string>();

                    foreach (var token in lToken.Result.result.regulatoryDocuments)
                    {
                        lTokensList.Add(token.token);
                    }

                    var request = new AutenticarTokensRequest
                    {
                        tokens = lTokensList
                    };
                    var autenticar = _aceiteTermoRepository.AutenticarTokens(request).Result;

                    if (autenticar.message != null)
                    {
                        if (autenticar.message.Equals("Agreements accepted successfully"))
                        {
                            responseConductor = CadastrarPessoa(lPortador, lTokensList);
                        }
                        else
                        {
                            return new RespPadrao
                            {
                                sucesso = false,
                                mensagem = "Não foi possível validar as tokens"
                            };
                        }
                    }
                }

                if (responseConductor.sucesso)
                {
                    return new RespPadrao
                    {
                        sucesso = true,
                        mensagem = ""
                    };
                }

                return new RespPadrao
                {
                    sucesso = false,
                    mensagem = responseConductor.mensagem
                };
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar CadastraPessoaConductorApi");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("CadastraPessoaConductorApi");
            }
        }

        public RespPadrao CadastrarPessoa(PortadorRequest lPortador, List<string> tokenList)
        {
            try
            {
                new LogHelper().LogOperationStart("CadastrarPessoa");
                if (lPortador.TipoPessoa == (int)ETipoPessoa.Fisica || lPortador.CpfCnpj.Length == 11)
                {
                    var lRetornoPessoaFisica = _cartaoRepository.ConsultarPortador(lPortador.CpfCnpj);

                    if (lRetornoPessoaFisica?.Result?.items == null)
                    {
                        var lPessoa = Mapper.Map<ContaPessoaFisicaReq>(lPortador);
                        var lCidade = _cidadeReadRepository.FirstOrDefault(x => x.Id == lPortador.CidadeId);
                        var lUfEmissaoSigla = _estadoReadRepository.FirstOrDefault(x => x.Id == lPortador.UfEmissao).Uf;

                        lPessoa.idBusinessSource = 1;
                        lPessoa.idProduct = 1;
                        lPessoa.dueDate = 10;
                        lPessoa.idOccupationType = 2;

                        lPessoa.address.idAddressType = 1;
                        lPessoa.address.country = "Brasil";
                        lPessoa.address.zipCode = lPortador.Cep;
                        lPessoa.address.street = lPortador.Endereco;
                        lPessoa.address.city = lCidade?.Nome;
                        lPessoa.address.federativeUnit = lPortador.UfEmissaoSigla ?? lUfEmissaoSigla;
                        lPessoa.phone.idPhoneType = 1;
                        lPessoa.gender = GetSexo(lPortador.Sexo);
                        lPessoa.birthDate = Convert.ToDateTime(lPortador.DataNascimento).ToString("yyyy-MM-dd");
                        lPessoa.termsAndConditionsTokens = tokenList;
                        lPessoa.idMaritalStatus = 1;
                        lPessoa.idNationality = 1;
                        lPessoa.phone.areaCode = "0" + lPortador.Celular?.Substring(0, 2);
                        lPessoa.phone.number = lPortador.Celular?.Substring(2);
                        lPessoa.issuingDateIdentity = DateTime.Now.ToString("yyyy-MM-dd");
                        lPessoa.isPep = lPortador.IsPep;

                        var lRetornoSavePessoaFisica = _cartaoAppService.CadastrarPessoaFisica(lPessoa).Result;

                        if (lRetornoSavePessoaFisica == null || (!lRetornoSavePessoaFisica.Sucesso))
                        {
                            if (lRetornoSavePessoaFisica != null)
                                return new RespPadrao
                                {
                                    sucesso = false,
                                    mensagem = "Não foi possível cadastrar a pessoa na Conductor! Mensagem: " +
                                               lRetornoSavePessoaFisica.message
                                };

                            return new RespPadrao
                            {
                                sucesso = false,
                                mensagem = "Não foi possível cadastrar a pessoa na Conductor!"
                            };
                        }
                    }
                }
                else
                {
                    var lRetornoPessoaJuridica = _cartaoRepository.ConsultarPortador(lPortador.CpfCnpj);

                    if (lRetornoPessoaJuridica?.Result?.results == null)
                    {
                        MainPhone mainPhone = null;
                        MainPhone mainPhoneSocio = null;
                        var listaSocios = new List<Individuals>();

                        var listaTelefones = new List<Phones>
                        {
                            new Phones
                            {
                                countryCode = "55",
                                number = lPortador.Telefone.Substring(2),
                                area = lPortador.Telefone.Substring(0, 2),
                                type = "MOBILE"
                            }
                        };

                        if (lPortador.Celular != null)
                        {
                            mainPhone = new MainPhone
                            {
                                countryCode = "55",
                                number = lPortador.Celular.Substring(2),
                                area = lPortador.Celular.Substring(0, 2),
                                type = "MOBILE"
                            };
                        }

                        var listaEnderecos = new List<Addresses>
                        {
                            new Addresses
                            {
                                neighborhood = lPortador.Bairro,
                                zip = lPortador.Cep.OnlyNumbers(),
                                city = lPortador.NomeCidade, complement = lPortador.Complemento,
                                street = lPortador.Bairro,
                                number = lPortador.EnderecoNumero,
                                state = lPortador.UfEstado,
                                country = "BRASIL"
                            }
                        };

                        var mainAddresses = new MainAddress
                        {
                            neighborhood = lPortador.Bairro,
                            zip = lPortador.Cep.OnlyNumbers(),
                            city = lPortador.NomeCidade, complement = lPortador.Complemento,
                            street = lPortador.Bairro,
                            number = lPortador.EnderecoNumero,
                            state = lPortador.UfEstado,
                            country = "BRASIL"
                        };

                        var telefoneRepLegal = new List<Phones>();

                        foreach (var repLegais in lPortador.RepLegaisList)
                        {
                            var lSocio = Repository.Query.Include(x => x.Estado)
                                .FirstOrDefault(p => p.CpfCnpj == repLegais.CpfCnpj);

                            if (lSocio != null)
                            {
//                            lSocio.UfEmissao = GetUF(lSocio.EstadoId);
                                var lCidade = _cidadeReadRepository.FirstOrDefault(x => x.Id == lSocio.CidadeId);

                                var mainAddressesSocio = new MainAddress
                                {
                                    neighborhood = lSocio.Bairro,
                                    zip = lPortador.Cep.OnlyNumbers(),
                                    city = lCidade.Nome,
                                    street = lSocio.Bairro,
                                    number = lSocio.EnderecoNumero != null ? lSocio.EnderecoNumero.ToString() : "0",
                                    state = lSocio.Estado.Uf,
                                    country = "BRASIL"
                                };

                                if (lPortador.Celular != null)
                                {
                                    mainPhoneSocio = new MainPhone
                                    {
                                        countryCode = "55",
                                        number = lPortador.Celular.Substring(2),
                                        area = lPortador.Celular.Substring(0, 2),
                                        type = "MOBILE"
                                    };

                                    telefoneRepLegal.Add(new Phones()
                                    {
                                        countryCode = "55",
                                        number = lPortador.Celular.Substring(2),
                                        area = lPortador.Celular.Substring(0, 2),
                                        type = "MOBILE"
                                    });
                                }


                                listaSocios.Add(new Individuals()
                                {
                                    profile = "OWNER",
                                    type = new List<string>() { "MASTER" },
                                    name = lSocio.Nome,
                                    motherName = lSocio.NomeMae,
                                    nationalRegistration = lSocio.CpfCnpj,
                                    dateBirth = Convert.ToDateTime(lSocio.DataNascimento).ToString("yyyy-MM-dd"),
                                    phones = telefoneRepLegal,
                                    mainPhone = mainPhoneSocio,
                                    mainAddress = mainAddressesSocio,
                                    isPep = repLegais.IsPep,
                                    email = lPortador.Email
                                });
                            }
                        }

                        var nomePortador = Regex.Replace(lPortador.Nome, "[^0-9a-zA-Z]+", "").Split(' ');

                        var lPessoa = new ContaPessoaJuridicaReq
                        {
                            company =
                            {
                                tradeName = lPortador.Nome,
                                legalName = lPortador.RazaoSocial,
                                nationalRegistration = lPortador.CpfCnpj.OnlyNumbers(),
                                legalStatus = "ATIVO",
                                stateRegistration = lPortador.InscricaoEstadual,
                                dateEstablishment = lPortador.DataAberturaEmpresa?.ToString("yyyy-MM-dd"),
                                establishmentFormat = lPortador.FormaConstituicao,
                                email = lPortador.Email,
                                //   phones = listaTelefones,
                                mainPhone = mainPhone,
                                addresses = listaEnderecos,
                                mainCnae = lPortador.Cnae,
                                mainAddress = mainAddresses,
                                legalNature = lPortador.NaturezaJuridica,
                                partners =
                                {
                                    individuals = listaSocios
                                }
                            },
                            productSettings =
                            {
                                printedCardName = nomePortador.Length > 1
                                    ? nomePortador[0] + nomePortador[1]
                                    : nomePortador[0],
                                termsAndConditionsTokens = tokenList,
                                deviceIdentification =
                                {
                                    fingerprint = "Iphone"
                                },
                                idProduct = 1,
                                idBusinessSource = 1
                            }
                        };

                        var lRetornoSavePessoaJuridica = _cartaoAppService.CadastrarContaPessoaJuridica(lPessoa).Result;

                        if (lRetornoSavePessoaJuridica.Sucesso == false)
                        {
                            return new RespPadrao
                            {
                                sucesso = false,
                                mensagem = "Não foi possível cadastrar a pessoa na Conductor! Mensagem: " +
                                           lRetornoSavePessoaJuridica.message
                            };
                        }
                    }
                }

                return new RespPadrao
                {
                    sucesso = true,
                    mensagem = ""
                };
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar CadastrarPessoa");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("CadastrarPessoa");
            }
        }

        public async Task<RespPadrao> AdicionarCartao(PortadorRequest lPortador)
        {
            try
            {
                var log = LogManager.GetCurrentClassLogger();

                log.Info("AdicionarCartao");

                /*if (User.EmpresaId > 0)
                {
                    log.Info("AdicionarCartao empresa: " + User.EmpresaId);
                    var cnpj = _empresaReadRepository.GetCnpj(User.EmpresaId);
                    /*var retCardList = _transacaoAppService.Card(cnpj);

                    if (retCardList.Result != null)
                    {
                        log.Info("Trouxe result");
                        foreach (var retCard in retCardList.Result)
                        {
                            foreach (var cardIntervals in retCard.card_intervals)
                            {
                                log.Info("Trouxe interval");
                                log.Info("Trouxe interval idinformado" +  lPortador.CartaoId);
                                log.Info("Trouxe interval inicio" +  cardIntervals.start_id.ToIntSafe());
                                log.Info("Trouxe interval fim" +  cardIntervals.final_id.ToIntSafe());
                                if (lPortador.CartaoId < cardIntervals.start_id.ToIntSafe() ||
                                    lPortador.CartaoId > cardIntervals.final_id.ToIntSafe())
                                {
                                    return new RespPadrao
                                    {
                                        sucesso = false,
                                        mensagem = "O cartão informado não está vinculado a empresa logada!"
                                    };
                                }
                            }
                        }
                    }
                    else
                    {
                        return new RespPadrao
                        {
                            sucesso = false,
                            mensagem = "Não existe cartões vinculados a empresa logada!"
                        };
                    }#1#
                }*/

                var lRetornoCadastroPessoa = await CadastraPessoaConductorWeb(lPortador);
                log.Info("AdicionarCartao - CadastraPessoaConductorWeb");
                if (!lRetornoCadastroPessoa.sucesso)
                {
                    return new RespPadrao
                    {
                        sucesso = false,
                        mensagem = lRetornoCadastroPessoa.mensagem
                    };
                }

                if (lPortador.TipoPessoa == (int)ETipoPessoa.Fisica)
                {
                    if (lPortador.CartaoId == null)
                    {
                        return new RespPadrao
                        {
                            sucesso = false,
                            mensagem = "Cartão informado inválido!"
                        };
                    }

                    var lConta = await _cartaoAppService.ConsultarContas(null, null, null, null, lPortador.CpfCnpj);
                    log.Info("AdicionarCartao - ConsultarContas");

                    if (lConta.content == null)
                    {
                        log.Info("AdicionarCartao - ConsultarContasContent");
                        var lRetornoAtribuirTitular = await _cartaoAppService
                            .AtribuirTitular(lPortador.CartaoId.Value, lPortador.CpfCnpj.OnlyNumbers());

                        if (!lRetornoAtribuirTitular.Sucesso)
                        {
                            log.Info("AdicionarCartao - ConsultarContasErro1");
                            return new RespPadrao
                            {
                                sucesso = false,
                                mensagem = "Não foi possível atribuir titular! " + lRetornoAtribuirTitular.message
                            };
                        }
                    }
                    else
                    {
                        log.Info("AdicionarCartao - ConsultarContasConsultaCartao");
                        var lCartao = await _cartaoAppService.ConsultaCartaoPorConta(lConta.content.First().id);
                        var lCartaoId = 0;
                        if (lCartao?.content != null)
                        {
                            foreach (var content in lCartao.content)
                            {
                                if (content.idStatus != 6)
                                {
                                    lCartaoId = content.id;
                                }
                            }
                        }

                        if (lCartaoId == lPortador.CartaoId.Value)
                        {
                            log.Info("AdicionarCartao - cartão ja vinculado");
                            return new RespPadrao
                            {
                                sucesso = false,
                                mensagem = "Cartão informado já vinculado com o Portador!"
                            };
                        }

                        if (lCartaoId > 0)
                        {
                            log.Info("AdicionarCartao - Cancelamento");
                            var lRetornoCancelarCartao = _cartaoAppService.CancelarCartao(lCartaoId,
                                6, lPortador.Motivo).Result;

                            if (lRetornoCancelarCartao.idStatus != 6)
                            {
                                return new RespPadrao
                                {
                                    sucesso = false,
                                    mensagem = "Não foi possível cancelar o cartão atual do portador!"
                                };
                            }

                            if (!lRetornoCancelarCartao.Sucesso)
                            {
                                return new RespPadrao
                                {
                                    sucesso = false,
                                    mensagem = lRetornoCancelarCartao.message
                                };
                            }
                        }

                        log.Info("AdicionarCartao - PrePago");
                        var lRetornoAtribuirCartaoPrePago =
                            _cartaoAppService.AtribuirCartaoPrePago(lPortador.CartaoId.Value,
                                lConta.content.First().id, lPortador.CpfCnpj.OnlyNumbers()).Result;

                        // if (lRetornoAtribuirCartaoPrePago != null)
                        // {
                        //     log.Info("AdicionarCartao - PrePagoStatus" + lRetornoAtribuirCartaoPrePago.Status);
                        // }

                        if (lRetornoAtribuirCartaoPrePago != null)
                        {
                            log.Info("AdicionarCartao - PrePagoStatus" + lRetornoAtribuirCartaoPrePago.Status);

                            var objetoMessage =
                                JsonConvert.DeserializeObject<PortadorGenericDockResponse>(lRetornoAtribuirCartaoPrePago
                                    .Message);

                            if (objetoMessage?.Code == 400)
                            {
                                return new RespPadrao
                                {
                                    sucesso = false,
                                    mensagem = "Não foi possível realizar essa operação! " + objetoMessage.Message + "."
                                };
                            }
                        }

                        if (lRetornoAtribuirCartaoPrePago == null)
                        {
                            return new RespPadrao
                            {
                                sucesso = true,
                                mensagem = "Operação realizada com sucesso!"
                            };
                        }

                        return new RespPadrao
                        {
                            sucesso = false,
                            mensagem = "Não foi possível realizar essa operação!"
                        };
                    }
                }
                else
                {
                    log.Info("AdicionarCartao - consultaConta de novo");
                    var lRetornoConsultaContas =
                        _cartaoAppService.ConsultarContas(null, null, null, null, lPortador.CpfCnpj);
                    if (lRetornoConsultaContas == null)
                    {
                        return new RespPadrao
                        {
                            sucesso = false,
                            mensagem = "Conta para vínculo de cartão não encontrada!"
                        };
                    }

                    var lConta = lRetornoConsultaContas.Result;
                    log.Info("AdicionarCartao - consultaCartao de novo");
                    var lCartao = _cartaoAppService.ConsultaCartaoPorConta(lConta.content.First().id).Result;
                    var lCartaoId = 0;

                    if (lCartao.content != null)
                    {
                        foreach (var content in lCartao.content)
                        {
                            if (content.idStatus != 6)
                            {
                                lCartaoId = content.id;
                            }
                        }
                    }

                    if (lCartaoId > 0)
                    {
                        log.Info("AdicionarCartao - Cancelamento de novo");
                        var lRetornoCancelarCartao = _cartaoAppService.CancelarCartao(lCartaoId,
                            6, lPortador.Motivo).Result;

                        if (lRetornoCancelarCartao.idStatus != 6)
                        {
                            return new RespPadrao
                            {
                                sucesso = false,
                                mensagem = "Não foi possível cancelar o cartão atual do portador!"
                            };
                        }

                        if (!lRetornoCancelarCartao.Sucesso)
                        {
                            return new RespPadrao
                            {
                                sucesso = false,
                                mensagem = lRetornoCancelarCartao.message
                            };
                        }
                    }

                    log.Info("AdicionarCartao - prepago de novo");

                    var lRetornoAtribuirCartaoPrePago =
                        _cartaoAppService.AtribuirCartaoPrePago(lPortador.CartaoId.Value,
                            lConta.content.First().id, lPortador.CpfCnpj.OnlyNumbers()).Result;

                    if (lRetornoAtribuirCartaoPrePago == null)
                    {
                        return new RespPadrao
                        {
                            sucesso = true,
                            mensagem = "Operação realizada com sucesso!"
                        };
                    }
                    else
                    {
                        return new RespPadrao
                        {
                            sucesso = false,
                            mensagem = "Não foi possível realizar essa operação!"
                        };
                    }
                }

                return new RespPadrao
                {
                    sucesso = true,
                    mensagem = ""
                };
            }
            catch (Exception e)
            {
                return new RespPadrao
                {
                    sucesso = false,
                    mensagem = e.Message
                };
            }
        }

        public string GetSexo(int? Sexo)
        {
            try
            {
                new LogHelper().LogOperationStart("GetSexo");
                switch (Sexo)
                {
                    case (int)ESexo.Masculino: return "M";

                    case (int)ESexo.Feminino: return "F";

                    case (int)ESexo.Outros: return "O";

                    case (int)ESexo.Indefinido: return "N";

                    default: return "N";
                }
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar GetSexo");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("GetSexo");
            }
        }

        public Domain.Models.Portador.Portador ConsultarPorCpfCnpj(string cpfCnpj)
        {
            return Repository.Query.Where(p => p.CpfCnpj == cpfCnpj).FirstOrDefault();
        }

        public async Task<RespPadraoApi> IntegrarPortador(IntegrarPortadorRequest lPortadorRequest)
        {
            var lLog = LogManager.GetCurrentClassLogger();
            try
            {
                SalvarRepresentanteLegalTabelaPortador(lPortadorRequest.RepLegaisList, lPortadorRequest);
                var lPortadorEnvioConductor = Mapper.Map<PortadorRequest>(lPortadorRequest);
                var possuiConta = await _cartaoRepository
                    .ConsultarPortador(lPortadorRequest.CpfCnpj);
                var possuiContaBbc = await _portadorReadRepository
                    .FirstOrDefaultAsync(x => x.CpfCnpj == lPortadorRequest.CpfCnpj);

                int? lEmpresaAntigaId = possuiContaBbc?.EmpresaIdFrota;

                if (lPortadorRequest.DataNascimento != null)
                {
                    var idadePortador =
                        StringHelper.GetIdadeDataNascimento(lPortadorRequest.DataNascimento.ToDateTime());
                    if (idadePortador < 18)
                    {
                        return new RespPadraoApi()
                        {
                            sucesso = false,
                            mensagem = "Portador não pode ter menos que 18 anos de idade!"
                        };
                    }
                }

                if (possuiConta == null || (possuiConta.items == null && possuiConta.results == null))
                {
                    if (lPortadorRequest.CriarPessoaDock)
                    {
                        var responseConductor = CadastraPessoaConductorApi(lPortadorEnvioConductor);

                        if (!responseConductor.sucesso)
                        {
                            return new RespPadraoApi()
                            {
                                sucesso = false,
                                mensagem = responseConductor.mensagem
                            };
                        }
                    }
                }

                var lEmpresaId = Engine.User.EmpresaId;

                var lPortadorEmp = _portadorEmpresaReadRepository.Include(x => x.Portador);
                var lJaCadastradoParaEmpresa = lPortadorEmp.Where(x => x.EmpresaId == lEmpresaId &&
                                                                       x.Portador.CpfCnpj ==
                                                                       lPortadorRequest.CpfCnpj).ToList();

                if (possuiContaBbc != null)
                {
                    //Portador x empresa
                    var lCadastradaOutraEmp = lPortadorEmp.Where(x => x.EmpresaId != lEmpresaId &&
                                                                      x.Portador.CpfCnpj == lPortadorRequest.CpfCnpj)
                        .ToList();

                    if (lEmpresaId == 0)
                        return new RespPadraoApi
                        {
                            sucesso = false,
                            mensagem = "Empresa não encontrada."
                        };

                    if (lCadastradaOutraEmp.Count > 0 && lJaCadastradoParaEmpresa.Count == 0)
                    {
                        await SalvarPortadorEmpresa(new PortadorEmpresaRequest()
                        {
                            EmpresaId = (int)lEmpresaId,
                            PortadorId = lCadastradaOutraEmp.Select(x => x.PortadorId).FirstOrDefault()
                        });
                    }

                    lPortadorRequest.Id = null;
                    lPortadorRequest.CpfCnpj.OnlyNumbers();

                    var lCpfCnpjCadastrado =
                        Repository.Query.FirstOrDefault(o => o.CpfCnpj == lPortadorRequest.CpfCnpj);

                    if (lCpfCnpjCadastrado != null)
                        lPortadorRequest.Id = lCpfCnpjCadastrado.Id.ToString();
                }

                if (possuiContaBbc != null)
                    lPortadorRequest.Id = possuiContaBbc.Id.ToString();


                var lPortador = Mapper.Map<PortadorSalvarCommand>(lPortadorRequest);
                lPortador.EmpresaIdFrota = lEmpresaId;
                var lCidade = _cidadeAppService.Repository.Query.FirstOrDefault(x => x.Ibge == lPortador.CidadeId);

                if (lCidade != null)
                {
                    lPortador.CidadeId = lCidade.Id;
                    lPortador.EstadoId = lCidade.EstadoId;
                }

                //recuperar representante
                if (lPortadorRequest.RepLegaisList != null && lPortadorRequest.RepLegaisList.Count > 0)
                {
                    foreach (var representante in lPortadorRequest.RepLegaisList)
                    {
                        var lRepresentante =
                            Repository.Query.FirstOrDefault(a => a.CpfCnpj == representante.CpfCnpj);

                        if (lRepresentante != null)
                        {
                            lPortador.RepLegaisList = new List<PortadorRepLegalCommand>();

                            lPortador.RepLegaisList.Add(new PortadorRepLegalCommand()
                            {
                                Id = lRepresentante.Id,
                                Nome = lRepresentante.Nome,
                                CpfCnpj = lRepresentante.CpfCnpj
                            });
                        }
                    }
                }

                lLog.Info("Antes de validar o portador!");
                lPortador.ValidarIntegrar();
                lPortador.ValidarCadastro();
                lLog.Info("Antes de Salvar o portador!");
                var command = Mapper.Map<PortadorSalvarComRetornoCommand>(lPortador);
                var retorno = Engine.CommandBus.SendCommandAsync<Domain.Models.Portador.Portador>(command).Result;
                lLog.Info("Depois de salvar o portador!");

                if (lEmpresaAntigaId != null && retorno.EmpresaIdFrota != lEmpresaAntigaId)
                {
                    await EnviarEmailAvisoDesvinculacaoPortador(lPortador, (int)lEmpresaAntigaId);
                }

                if (lPortadorRequest.Id == "Auto" ||
                    lPortadorRequest.Id.IsNullOrWhiteSpace() && !lJaCadastradoParaEmpresa.Any())
                {
                    var lRetornoSalvarPortadorEmpresa = SalvarPortadorEmpresa(new PortadorEmpresaRequest
                    {
                        EmpresaId = lEmpresaId,
                        PortadorId = retorno.Id
                    }).Result;
                }

                lLog.Info("Salvar portador empresa!");
                return new RespPadraoApi()
                {
                    sucesso = true,
                    mensagem = "Portador cadastrado com sucesso!"
                };
            }
            catch (Exception e)
            {
                return new RespPadraoApi()
                {
                    sucesso = false,
                    mensagem = e.Message
                };
            }
        }

        public void SalvarRepresentanteLegalTabelaPortador(List<PortadorRepLegalRequest> repLegaisList,
            IntegrarPortadorRequest lPortadorRequest)
        {
            if (repLegaisList != null && repLegaisList.Count > 0)
            {
                foreach (var representante in repLegaisList)
                {
                    var existePortador =
                        Repository.Query.FirstOrDefault(a => a.CpfCnpj == representante.CpfCnpj);

                    if (existePortador == null)
                    {
                        var lNovoPortador = new PortadorSalvarCommand();
                        lNovoPortador.CpfCnpj = representante.CpfCnpj.OnlyNumbers();
                        lNovoPortador.Nome = representante.Nome;
                        lNovoPortador.DataNascimento = representante.DataNascimento;
                        lNovoPortador.NomeMae = representante.NomeMae;
                        if (lPortadorRequest.CidadeIbgeId != 0)
                        {
                            var lCidadeIdForIbge =
                                _cidadeReadRepository.FirstOrDefault(x => x.Ibge == lPortadorRequest.CidadeIbgeId);
                            if (lCidadeIdForIbge != null)
                            {
                                lNovoPortador.CidadeId = lCidadeIdForIbge.Id;
                                lNovoPortador.EstadoId = lCidadeIdForIbge.EstadoId;
                            }
                            else
                            {
                                lNovoPortador.CidadeId = null;
                                lNovoPortador.EstadoId = null;
                            }
                        }

                        lNovoPortador.Bairro = "Portador cadastrado via importação";
                        lNovoPortador.RepLegaisList = new List<PortadorRepLegalCommand>();
                        Engine.CommandBus.SendCommand<Domain.Models.Portador.Portador>(lNovoPortador);
                    }
                    else
                    {
                        existePortador.Nome = representante.Nome;
                        existePortador.DataNascimento = representante.DataNascimento;
                        existePortador.NomeMae = representante.NomeMae;

                        var editarPortador = Mapper.Map<PortadorSalvarCommand>(existePortador);
                        Engine.CommandBus.SendCommand<Domain.Models.Portador.Portador>(editarPortador);
                    }
                }
            }
        }

        public Task<IEnumerable<ConsultarPorIdPortadorResponse>> BuscarTodos()
        {
            throw new NotImplementedException();
        }

        public bool Existe(int id)
        {
            try
            {
                new LogHelper().LogOperationStart("Existe");
                throw new NotImplementedException();
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar Existe");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("Existe");
            }
        }

        public async Task<RespPadrao> SalvarPortadorEmpresa(PortadorEmpresaRequest lPortadorEmpresa)
        {
            try
            {
                lPortadorEmpresa.EmpresaId =
                    Engine.User.EmpresaId == 0 ? lPortadorEmpresa.EmpresaId : Engine.User.EmpresaId;

                await Engine.CommandBus.SendCommandAsync(Mapper.Map<PortadorEmpresaSalvarCommand>(lPortadorEmpresa));

                return new RespPadrao
                {
                    sucesso = true,
                    mensagem = "Registro salvo com sucesso!"
                };
            }
            catch (Exception e)
            {
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = e.Message
                };
            }
        }

        public async Task<RespPadrao> SalvarPortadorCentroCusto(List<PortadorCentroCustoRequest> lPortadorCentroCusto,
            int portadorId)
        {
            try
            {
                foreach (var lista in lPortadorCentroCusto)
                {
                    var command = Mapper.Map<PortadorCentroCustoSalvarComRetornoCommand>(lista);
                    command.PortadorId = portadorId;

                    var retorno = await Engine.CommandBus
                        .SendCommandAsync<Domain.Models.PortadorCentroCusto.PortadorCentroCusto>(command);

                    if (retorno.Id <= 0)
                    {
                        throw new Exception();
                    }
                }

                return new RespPadrao
                {
                    sucesso = true,
                    mensagem = "Registro salvo com sucesso!"
                };
            }
            catch (Exception e)
            {
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = e.Message
                };
            }
        }

        public async Task<VerificaPortadorCadastradoResponse> VerificaPortadorCadastradoEmOutraEmp(string cpfCnpj)
        {
            try
            {
                new LogHelper().LogOperationStart("VerificaPortadorCadastradoEmOutraEmp");
                var lPortadorEmp = _portadorEmpresaReadRepository.Include(x => x.Portador);

                var lCadastradaOutraEmp = await lPortadorEmp.FirstOrDefaultAsync(x =>
                    x.EmpresaId != Engine.User.EmpresaId && x.Portador.CpfCnpj == cpfCnpj);

                var lJaCadastradoParaEmpresa = await lPortadorEmp.FirstOrDefaultAsync(x =>
                    x.EmpresaId == Engine.User.EmpresaId && x.Portador.CpfCnpj == cpfCnpj);

                if (lJaCadastradoParaEmpresa != null)
                    return new VerificaPortadorCadastradoResponse
                    {
                        sucesso = false,
                        mensagem = "Portador ja esta cadastrado para esta empresa.",
                        PortadorPertenceEmpresaLogada = true
                    };

                if (lCadastradaOutraEmp != null)
                {
                    var lPortador = await _portadorReadRepository.GetByIdAsync(lCadastradaOutraEmp.PortadorId);

                    return new VerificaPortadorCadastradoResponse
                    {
                        sucesso = true,
                        mensagem = "Portador ja está cadastrado em outra empresa.",
                        data = lPortador,
                        PortadorPertenceEmpresaLogada = false
                    };
                }

                return new VerificaPortadorCadastradoResponse { sucesso = false, mensagem = "" };
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar VerificaPortadorCadastradoEmOutraEmp");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("VerificaPortadorCadastradoEmOutraEmp");
            }
        }

        public async Task<RespPadrao> RecuperarSenha(PortadorRecuperarSenhaRequest model)
        {
            var lLog = LogManager.GetCurrentClassLogger();

            #region Valida parâmetros de e-mail

            var lParametroEmail = await _parametrosReadRepository
                .GetParametrosAsync(-1, Domain.Models.Parametros.Parametros.TipoDoParametro.EmailUsuario,
                    Domain.Models.Parametros.Parametros.TipoDoValor.Criptografia);

            if (lParametroEmail?.ValorCriptografado == null)
                return new RespPadrao
                {
                    sucesso = false,
                    data = null,
                    mensagem = "Dados inválidos, não foi possível recuperar a senha!"
                };

            var lParametroEmailSenha = await _parametrosReadRepository
                .GetParametrosAsync(-1, Domain.Models.Parametros.Parametros.TipoDoParametro.EmailSenha,
                    Domain.Models.Parametros.Parametros.TipoDoValor.Criptografia);

            if (lParametroEmailSenha?.ValorCriptografado == null)
                return new RespPadrao
                {
                    sucesso = false,
                    data = null,
                    mensagem = "Dados inválidos, não foi possível recuperar a senha!"
                };

            #endregion

            #region Busca o portador e altera a senha

            var lPortador = await _portadorReadRepository.GetByCpfCnpjAsync(model.CpfCnpj);

            if (lPortador == null)
            {
                return new RespPadrao
                {
                    sucesso = false,
                    data = null,
                    mensagem = "Dados inválidos, não foi possível recuperar a senha!"
                };
            }

            if (!string.IsNullOrWhiteSpace(model.Email))
                if (lPortador.Email != model.Email)
                    return new RespPadrao(false, "Dados inválidos, não foi possível recuperar a senha!");

            var lRandom = new Random();

            var lSenhaNova = SenhaGenerator.Gerar();//lRandom.Next(100000, 999999);
            var lSenhaAntiga = lPortador.SenhaApi;

            try
            {
                await Repository.Command.AlterarSenhaBase(lSenhaNova, lPortador);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return new RespPadrao(false, "Não foi possivel alterar sua senha!" + e.Message);
            }

            #endregion

            #region Envio de email

            var lCaminhoAplicacao = AppDomain.CurrentDomain.BaseDirectory;

            var lRemetente = "<EMAIL>";

            var lPortadorNovo = lPortador.DataUltimoAcesso is null;

            var lUrlAplicativo = await _parametrosReadRepository.GetByTipoDoParametroAsync(Domain.Models.Parametros
                .Parametros.TipoDoParametro.LinkAplicativoCadastroPortador);
            var styleUrlApp = string.IsNullOrWhiteSpace(lUrlAplicativo?.Valor) || !lPortadorNovo
                ? "style='display: none'"
                : $"href='{lUrlAplicativo.Valor}'";
            var lCpfCnpjMascarado = lPortador.CpfCnpj.MascaraCpfCnpjEmail();
            try
            {
                using (var lStreamReader =
                       new StreamReader(lCaminhoAplicacao + @"\Content\Email\Usuario\recuperar-senha-portador.html"))
                {
                    var lEmailHtml = await lStreamReader.ReadToEndAsync();

                    var lEmailbody = lPortadorNovo
                        ? $"&nbsp;&nbsp;&nbsp; Olá, {lPortador.Nome}, seja bem-vindo! " +
                          "Abaixo suas informações de acesso ao (app) BBC Frota: "
                        : $"&nbsp;&nbsp;&nbsp; Olá, {lPortador.Nome}! " +
                          "Sua senha foi redefinida automaticamente. " +
                          "Para sua segurança, altere-a novamente ao realizar o login no sistema.";
                    lEmailHtml = lEmailHtml
                        .Replace("{NOME_USUARIO}", lPortador.Nome)
                        .Replace("{NOVA_SENHA}", lSenhaNova.ToString())
                        .Replace("{STYLE-LINK}", styleUrlApp)
                        .Replace("{NOVO_USUARIO}", lCpfCnpjMascarado)
                        .Replace("{STYLE_USUARIO}", lPortadorNovo ? "" : "style='display: none'")
                        .Replace("{CORPO}", "corpoNovoUsuario")
                        .Replace("{EMAIL_TITLE}", lPortadorNovo ? "PRIMEIRO ACESSO" : "RECUPERAÇÃO DE SENHA")
                        .Replace("{EMAIL_BODY}", lEmailbody);

                    var lView = AlternateView.CreateAlternateViewFromString(lEmailHtml, null, "text/html");

                    var lResultEmail = await _notificationEmailExecutor.ExecuteAsync(new Domain.Components.Email.Email
                    {
                        To = new[] { new Domain.Components.Email.Email.EmailAddress { Address = lPortador.Email } },
                        Priority = MailPriority.High,
                        Subject = lPortadorNovo ? "PRIMEIRO ACESSO" : "RECUPERAÇÃO DE SENHA",
                        IsBodyHtml = true,
                        AlternateView = lView,
                        From = new Domain.Components.Email.Email.EmailAddress { Address = lRemetente }
                    });

                    if (!lResultEmail.Sucesso)
                    {
                        throw new Exception("Falha ao enviar e-mail.");
                    }
                }
            }
            catch (Exception e)
            {
                lLog.Error(e);

                if (!string.IsNullOrEmpty(lSenhaAntiga))
                    await Repository.Command.AlterarSenhaBase(lSenhaAntiga, lPortador, false);

                return new RespPadrao
                {
                    sucesso = false,
                    data = lPortador.Id,
                    mensagem = "Não foi possível alterar sua senha. E-mail não enviado."
                };
            }

            #endregion

            return new RespPadrao
            {
                sucesso = true,
                data = lPortador.Id,
                mensagem = "Senha alterada com sucesso! E-mail enviado."
            };
        }
        
        public RespPadrao ConsultarMobilePortadorCpfCnpj(string cpfCnpj)
        {
            try
            {
                new LogHelper().LogOperationStart("ConsultarMobilePortadorCpfCnpj");
                var retornoPortador = Mapper.Map<PortadorConsultarCpfCnpjApiResponse>(ConsultarPorCpfCnpj(cpfCnpj));

                if (retornoPortador != null)
                {
                    return new RespPadrao()
                    {
                        sucesso = true,
                        data = retornoPortador
                    };
                }

                return new RespPadrao()
                {
                    sucesso = false,
                    data = null,
                    mensagem = "Credenciais inválidas"
                };
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar ConsultarMobilePortadorCpfCnpj");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("ConsultarMobilePortadorCpfCnpj");
            }
        }

        public async Task<RespPadrao> CriarNovaSenha(CriarNovaSenhaRequest request)
        {
            try
            {
                if (request.mobile)
                {
                    return new RespPadrao()
                    {
                        sucesso = false,
                        mensagem = "Recurso em desenvolvimento!"
                    };
                }

                var lPortador = Repository.Query.GetById(request.id);
                var lPortadorEmail = Mapper.Map<PortadorSalvarCommand>(lPortador);

                if (lPortador.EmpresaIdFrota != Engine.User.EmpresaId && !Engine.User.IsNivelSuperUsuario)
                {
                    await EnviarEmailAvisoDesvinculacaoPortador(lPortadorEmail, (int)lPortador.EmpresaIdFrota);
                }

                var gerarNovaSenhaRequest = new PortadorRecuperarSenhaRequest()
                {
                    CpfCnpj = lPortador.CpfCnpj,
                    Telefone = lPortador.Telefone,
                    Email = lPortador.Email,
                    TipoConsulta = lPortador.Email.IsNullOrWhiteSpace() ? "0" : "1",
                    Mobile = false
                };

                var retornoCriarNovaSenha = await RecuperarSenha(gerarNovaSenhaRequest);

                if (!retornoCriarNovaSenha.sucesso)
                {
                    return new RespPadrao()
                    {
                        sucesso = false,
                        mensagem = retornoCriarNovaSenha.mensagem
                    };
                }

                return new RespPadrao()
                {
                    sucesso = true,
                    mensagem = "Portador reativado com sucesso!"
                };
            }
            catch (Exception e)
            {
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = "Falha ao gerar nova senha para Portador! Mensagem: " + e.Message
                };
            }
        }

        public async Task<RespPadrao> ValidarPortadorLogin(string cpfCnpj, string tokenSenha)
        {
            try
            {
                var lPortador = await _portadorReadRepository.GetByCpfCnpjAsync(cpfCnpj);
                // Valida existência e status do usuário
                if (lPortador == null || lPortador.Cancelado())
                {
                    return RespPadrao.Erro("Autenticação inválida!");
                }

                var numeroTentativas = await _parametrosReadRepository.GetTotalTentativaErroSenhaPortadorFrota();
                //Valida se a senha inserida está correta
                //Pode bloquear o usuário
                if (!lPortador.SenhaCorreta(tokenSenha))
                {
                    await AlterarQuantidadeTentativas(lPortador, numeroTentativas);
                    return RespPadrao.Erro("Autenticação inválida!");
                }

                //Valida usuário está bloqueado ou não
                if (lPortador.Bloqueado())
                {
                    return RespPadrao.Erro("Usuário inativo, ou com acesso bloqueado.");
                }
                
                //A partir daqui o usuário está obrigatóriamente ativo.
                //Command para bloquear o usuario caso falhar em certas validações
                
                //Valida quantidade máxima de tentativas de login atingida
                //Pode bloquear o usuário
                if (lPortador.TentativasExcedida(numeroTentativas))
                {
                    await AlterarStatus(lPortador);
                    return RespPadrao.Erro("Limite de tentativas de login excedido! Acesso bloqueado.");
                }

                //Valida o período de inatividade do usuário com a senha provisória.
                //Pode bloquear o usuário
                if (lPortador.UsaSenhaProvisoria())
                {
                    var lPeriodoMaximoSenha = await _parametrosReadRepository
                        .GetPeriodoMaximoInatividadeSenhaProvisoria();
                    if (lPortador.UltrapassouPeriodoSenhaProvisoria(lPeriodoMaximoSenha))
                    {
                        await AlterarStatus(lPortador);
                        return RespPadrao.Erro("Período máximo de utilização da senha provisoria alcançado!");
                    }
                }

                //Valida período de inatividade do usuário
                //Pode bloquear o usuário
                var lPeriodoMaximoInatividade =
                    await _parametrosReadRepository.GetPeriodoMaximoInatividadePortador();
                if (lPortador.Inativo(lPeriodoMaximoInatividade))
                {
                    await AlterarStatus(lPortador);
                    return RespPadrao.Erro("Período máximo de inatividade alcançado!");
                }

                //Passou todas as validações
                //Seta tentativas de senha errada para zero
                await ResetarTentativasErroSenha(lPortador);
                return RespPadrao.Sucesso("Portador validado com sucesso!", lPortador);
            }
            catch (Exception e)
            {
                return RespPadrao.Erro("Falha ao validar portador! Mensagem: " + e.Message);
            }
        }

        private async Task AlterarQuantidadeTentativas(Domain.Models.Portador.Portador aPortador, int numeroTentativas) 
        {
            if (aPortador.Atividade == EAtividade.Frota)
            {
                var lUsuarioFrotaSenhaErradaCommand = new UsuarioFrotaSenhaErradaCommand
                {
                    Id = aPortador.Id,
                    TotalTentativas = numeroTentativas
                };

                await Engine.CommandBus.SendCommandAsync(lUsuarioFrotaSenhaErradaCommand);
            }
            else
            {
                var lPortadorSenhaErradaCommand = new PortadorSenhaErradaCommand
                {
                    Id = aPortador.Id,
                    TotalTentativas = numeroTentativas
                };

                await Engine.CommandBus.SendCommandAsync(lPortadorSenhaErradaCommand);
            }
           
        }

        private async Task ResetarTentativasErroSenha(Domain.Models.Portador.Portador aPortador)
        {
            if (aPortador.Atividade == EAtividade.Frota)
            {
                var lUsuarioFrotaSenhaCorretaCommand = new UsuarioFrotaResetarTentativasCommand()
                {
                    Id = aPortador.Id
                };
                await Engine.CommandBus.SendCommandAsync(lUsuarioFrotaSenhaCorretaCommand);
            }
            else
            {
                var lPortadorSenhaCorretaCommand = new PortadorSenhaCorretaCommand
                {
                    Id = aPortador.Id
                };
                await Engine.CommandBus.SendCommandAsync(lPortadorSenhaCorretaCommand);
            }
        }

        private async Task AlterarStatus(Domain.Models.Portador.Portador aPortador)
        {
            if (aPortador.Atividade == EAtividade.Frota)
            {
                await AlterarStatusPortadorFrota(aPortador);
            }
            else
            {
                await AlterarStatusPortador(aPortador);
            }
        }
        
        private async Task AlterarStatusPortadorFrota(Domain.Models.Portador.Portador aPortador)
        {
            await Engine.CommandBus.SendCommandAsync(new UsuarioFrotaBloquearCommand {
                Id = aPortador.Id,
                Mobile = true
            });
        }
        
        private async Task AlterarStatusPortador(Domain.Models.Portador.Portador aPortador)
        {
            await Engine.CommandBus.SendCommandAsync(new PortadorAlterarStatusCommand {
                Id = aPortador.Id 
            });
        }
        
        
        public async Task<RespPadrao> AtualizarUltimoAcesso(int id)
        {
            try
            {
                var lUltimoAcessoCommand = new PortadorUltimoAcessoCommand
                {
                    Id = id
                };

                await Engine.CommandBus.SendCommandAsync(lUltimoAcessoCommand);

                return new RespPadrao
                {
                    mensagem = "Último acesso atualizado com sucesso!",
                    sucesso = true
                };
            }
            catch (Exception e)
            {
                return new RespPadrao
                {
                    sucesso = false,
                    mensagem = "Erro ao atualizar o último acesso do portador. Mensagem: " + e.Message
                };
            }
        }

        #endregion

        #region Transportador

        public ConsultarGridTransportadorResponse ConsultarGridTransportador(int take, int page,
            OrderFilters orderFilters,
            List<QueryFilters> filters)
        {
            
            #region Consulta personalizada filtros da grid

            foreach (var item in filters)
            {
                item.Valor = item.Campo switch
                {
                    "cpfCnpj" => item.Valor.Replace(".", "").Replace("/", "").Replace("-", ""),
                    _ => item.Valor
                };
            }

            #endregion
            
            var lTransportador = Repository.Query.Where(x => x.Visibilidade == EVisibilidadePortador.Transportador);

            //Portador x empresa
            if (Engine.User.EmpresaId > 0)
            {
                lTransportador = lTransportador.Where(c => c.EmpresaIdFrota == Engine.User.EmpresaId);
            }

            lTransportador = lTransportador.AplicarFiltrosDinamicos(filters);
            lTransportador = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                ? lTransportador.OrderByDescending(o => o.Id)
                : lTransportador.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            var retorno = lTransportador.Skip((page - 1) * take)
                .Take(take)
                .ProjectTo<ConsultarGridTransportador>(Engine.Mapper.ConfigurationProvider).ToList();

            return new ConsultarGridTransportadorResponse()
            {
                items = retorno,
                totalItems = lTransportador.Count()
            };
        }

        public async Task<RespPadrao> SaveTransportador(TransportadorRequest lmodel)
        {
            try
            {
                var command = Mapper.Map<TransportadorSalvarComRetornoCommand>(lmodel);
                command.Bairro = "Bairro Padrão";
                var retorno = await Engine.CommandBus.SendCommandAsync<Domain.Models.Portador.Portador>(command);

                return new RespPadrao
                {
                    id = retorno.Id,
                    sucesso = true,
                    mensagem = "Registro salvo com sucesso!"
                };
            }
            catch (Exception e)
            {
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = e.Message
                };
            }
        }

        public TransportadorResponse ConsultarTransportadorPorId(int id)
        {
            try
            {
                new LogHelper().LogOperationStart("ConsultarTransportadorPorId");
                return Mapper.Map<TransportadorResponse>(Repository.Query.GetTransportadorById(id).Result);
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar ConsultarTransportadorPorId");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("ConsultarTransportadorPorId");
            }
        }

        public TransportadorResponse ConsultarTransportadorPorCpfCnpj(string cpfCnpj)
        {
            try
            {
                new LogHelper().LogOperationStart("ConsultarTransportadorPorCpfCnpj");
                return Mapper.Map<TransportadorResponse>(Repository.Query.GetTransportadorByCpfCnpj(cpfCnpj).Result);
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar ConsultarTransportadorPorCpfCnpj");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("ConsultarTransportadorPorCpfCnpj");
            }
        }

        #endregion
    }
}
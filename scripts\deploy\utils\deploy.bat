:: Init
echo off
setlocal enabledelayedexpansion
if %ambiente%=="" (
echo Sem ambiente definido!
pause
goto :fim)

echo sorce_dir: %sorce_dir%
echo publish_dir: %publish_dir%
echo src_project: %src_project%
echo src_front: %src_front%
echo src_pack: %src_pack%
echo src_pack_back: %src_pack_back%
echo origemBat: %origemBat%
echo tipo_pacote: %tipo_pacote%

if "%src_project%"=="ApiCiotPublico" (
	set sitename=%site%
) else (
	set sitename=%site%-%ambiente%
)
call :info %sitename%

if "%origemBat%"=="Web" (
	call :info Web
	if %tipo_pacote% lss 4 (
		call :Api
	)
	if %tipo_pacote% gtr 1 (
		call :Front
	)
	goto :Compress
) else (
	call :Api
	goto :Compress
)

:Api
	call :info "%sorce_dir%\%src_project%\bin\%tipo%\%targetFrameWork%" "%publish_dir%\%src_pack_back%"
	call :CopyApi "%sorce_dir%\%src_project%\bin\%tipo%\%targetFrameWork%" "%publish_dir%\%src_pack_back%"
	exit /b 0


:Front
	call :info "%sorce_dir%\%src_front%\dist" "%publish_dir%\%src_pack%"
	call :CopyFront "%sorce_dir%\%src_front%\dist" "%publish_dir%\%src_pack%"
	exit /b 0

:: 0 - dll padrao
:: 1 - full dll
:: 2 - pacote completo
:: 3 - dll padrao + front
:: 4 - front

:CopyApi
if %tipo_pacote%==0 (
	robocopy "%~1" "%~2" "*%dllPadrao%*.dll" /XD "publish" "win-x64" /S /Z /R:2 /MT:10 /LEV:3
	)
if %tipo_pacote%==1 (
	robocopy "%~1" "%~2" "*.dll" /XD "publish" "win-x64" /S /Z /R:2 /MT:10 /LEV:3
	)
if %tipo_pacote%==2 (
	robocopy "%~1" "%~2" * /XD "publish" "win-x64" /S /Z /R:2 /MT:10 /LEV:3
	)
if %tipo_pacote%==3 (
	robocopy "%~1" "%~2" "*%dllPadrao%*.dll" /XD "publish" "win-x64" /S /Z /R:2 /MT:10 /LEV:3
	)
exit /b 0

:CopyFront
if %tipo_pacote%==2 (
	robocopy "%~1" "%~2" * /S /Z /R:2 /MT:10 /LEV:3
	)
if %tipo_pacote% gtr 2 (
	robocopy "%~1" "%~2" index.html
	robocopy "%~1" "%~2" *.css /XD "app" "assets" "fonts" "shims" /S /Z /R:2 /MT:10 /LEV:3
	robocopy "%~1" "%~2" *.js /XD "app" "assets" "fonts" "shims" /S /Z /R:2 /MT:10 /LEV:3
	)
exit /b 0
	
:Compress
call :info Gerando pacote
pause
cd %publish_dir%/%src_pack%
call %projeto%\scripts\autobuild\remove-config.bat
call :info extensao: %extensao%
set pacote=%publish_dir%\%src_pack%.zip
echo %pacote%
call %Seven% a -y -tzip "%pacote%" %extensao%
call :info pacote: %pacote%
cd %pasta_raiz%
if exist "%publish_dir%/%src_pack%" rd /S /Q "%publish_dir%/%src_pack%"

call :warn Digite [1] para iniciar o deploy no site %sitename%
echo.
set /p confirm=""
set confirm=%confirm%
call :info confirm selecionado: %confirm%
if %confirm%==1 call :deploy
call :log Processo finalizado!
goto :fim
	
:deploy	
	set computername=https://%server%:%porta%/msdeploy.axd?site=%sitename%

	call "%msdeploypath%" -verb:sync -source:recycleApp -dest:recycleApp="%sitename%",computerName="%computername%",userName=%username%,password=%pass%,authtype="Basic",includeAcls="False",recycleMode="StopAppPool" -allowUntrusted=true	
	call "%msdeploypath%" ^
	  -source:package="%pacote%" ^
	  -dest:auto="%server%",computerName="%computername%",username=%username%,password=%pass%,authtype="Basic",includeAcls="False" ^
	  -verb:sync ^
	  -disableLink:AppPoolExtension ^
	  -disableLink:ContentExtension ^
	  -disableLink:CertificateExtension ^
	  -allowUntrusted=true ^
	  -setParam:kind=ProviderPath,scope=contentPath,value="%sitename%" ^
	  -enableRule:DoNotDeleteRule ^
	  -verbose
	call "%msdeploypath%" -verb:sync -source:recycleApp -dest:recycleApp="%sitename%",computerName="%computername%",username=%username%,password=%pass%,authtype="Basic",includeAcls="False",recycleMode="StartAppPool" -allowUntrusted=true
	exit /B 0

:: Sets up the ESC string for use later in this script
:setESC
    for /F "tokens=1,2 delims=#" %%a in ('"prompt #$H#$E# & echo on & for %%b in (1) do rem"') do (
      set ESC=%%b
      exit /B 0
    )
    exit /B 0
	
:opt
	call :setESC
	echo !ESC![92m %* !!ESC![0m
	exit /B 0
	
:log
	:: %~n0 = nome arquivo | %~x0 = extensão arquivo
	echo.
	call :setESC
	echo !ESC![95m===================================================================================================!!ESC![0m
	echo !ESC![95m	%*
	echo !ESC![95m===================================================================================================!!ESC![0m
	echo.
	exit /B 0
	
:info
	:: %~n0 = nome arquivo | %~x0 = extensão arquivo
	echo.
	call :setESC
	echo !ESC![94m===================================================================================================!!ESC![0m
	echo !ESC![95m	%*
	echo !ESC![94m===================================================================================================!!ESC![0m
	echo.
	exit /B 0

:warn
	:: %~n0 = nome arquivo | %~x0 = extensão arquivo
	echo.
	call :setESC
	echo !ESC![93m===================================================================================================!!ESC![0m
	echo !ESC![93m	%*
	echo !ESC![93m===================================================================================================!!ESC![0m
	echo.
	exit /B 0
	
:erro
	echo.
	call :setESC
	echo !ESC![91m===================================================================================================!!ESC![0m
	echo !ESC![91m	%~n0%~x0^> %*
	echo !ESC![91m===================================================================================================!!ESC![0m
	echo.
	if %NoStop%==False (pause)
	exit 1
	
:fim
exit /b %errorlevel%
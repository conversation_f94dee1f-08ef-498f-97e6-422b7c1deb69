﻿using Microsoft.EntityFrameworkCore.Migrations;
using System;
using System.Collections.Generic;

namespace SistemaInfo.BBC.Infra.Data.Migrations
{
    public partial class SPRINT_55_PAGAMENTO_EVENTO_ALTER_TABLE_DEFAULT_VALUE : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<int>(
                name: "StatusAntecipacaoParcelaProprietario",
                schema: "BBC",
                table: "PagamentoEvento",
                type: "int",
                nullable: true,
                defaultValue: 4,
                oldClrType: typeof(int),
                oldType: "int",
                oldNullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<int>(
                name: "StatusAntecipacaoParcelaProprietario",
                schema: "BBC",
                table: "PagamentoEvento",
                type: "int",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "int",
                oldNullable: true,
                oldDefaultValue: 4);
        }
    }
}

using System;
using System.Linq;
using SistemaInfo.BBC.Domain.Contracts.Operacoes;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.Helpers.Ciot
{
    public static class HtmlCiotPublicoReportHelper
    {
        private static string ValidateStringField(string field)
        {
            return string.IsNullOrEmpty(field) ? "-" : field;
        }

        private static string ValidateObjectPersonField<T>(T obj, string name, string document)
        {
            if (obj == null)
                return "-";

            var hasName = !name.IsNullOrWhiteSpace();
            var hasDocument = !document.IsNullOrWhiteSpace();

            if (hasName && hasDocument)
                return $"{document.ToCpfOrCnpj()} - {name}";
            if (hasDocument)
                return document.ToCpfOrCnpj();

            return hasName ? name : "-";
        }

        private static string ValidateObjectCityField<T>(T obj, string name, string uf)
        {
            if (obj == null)
                return "-";

            var hasName = !name.IsNullOrWhiteSpace();
            var hasUf = !uf.IsNullOrWhiteSpace();

            if (hasName && hasUf)
                return $"{name} / {uf}";
            if (hasUf)
                return uf;

            return hasName ? name : "-";
        }

        public static string GetHtmlImpressaoCiotPublico(RelatorioGestaoCiotResponseMessageInfo declaracaoCiot)
        {
            var css = GetCssImpressaoCiot();
            var htmlViagens = "";
            var quantidadeViagens = 0;
            
            decimal totalPesoViagens;
            var pesoCiot = decimal.TryParse(declaracaoCiot.Peso, out var parsedPeso) ? parsedPeso : 0;
            decimal pesoTotal = 0;

            if (declaracaoCiot.ViagensList != null)
            {
                quantidadeViagens = declaracaoCiot.ViagensList?.Sum(v => v.QtdViagens) ?? 0;
                totalPesoViagens = declaracaoCiot.ViagensList?.Sum(v => v.PesoCarga) ?? 0;
                pesoTotal = totalPesoViagens + pesoCiot;
                htmlViagens += @"
                    <table class='table-viagens'>
                        <thead>
                            <tr>
                                <th colspan='5' class='table-title'>Viagens</th>
                            </tr>
                            <tr>
                                <th>Cidade origem</th>
                                <th>Cidade destino</th>
                                <th>Natureza carga</th>
                                <th>Peso carga</th>
                                <th>Quantidade viagens</th>
                            </tr>
                        </thead>
                        <tbody>";


                htmlViagens = declaracaoCiot.ViagensList?.Aggregate(htmlViagens, (current, viagem) => current + $@"
                           <tr>
                              <td>{viagem.CidadeOrigem?.Nome}</td>
                              <td>{viagem.CidadeDestino?.Nome}</td>
                              <td>{viagem.CodigoNaturezaCarga}</td>
                              <td>{viagem.PesoCarga:N2}</td>
                              <td>{viagem.QtdViagens}</td>
                           </tr>");

                htmlViagens += @"     </tbody>
                    </table>";
            }

            var base64Image = Convert.ToBase64String(System.IO.File.ReadAllBytes("wwwroot/images/Logo.png"));
            var imgSrc = $"data:image/png;base64,{base64Image}";

            var html = $@"<!DOCTYPE html>
                <html>
                    <head>
                        <meta charset='utf-8'>
                        <title>Relatório de CIOT - BBC Leasing</title>
                        {css}
                    </head>
                    <body>
                        <div class='cabecalho'>
                             <div class='logo'>
                                <img src='{imgSrc}' alt='BBC Leasing Logo'>
                             </div>

                              <div class='titulo'>
                                RELATÓRIO DE CIOT
                              </div>

                              <div class='info-caixa'>
                                <p><strong>Data:</strong> {DateTime.Now:dd/MM/yyyy}</p>
                                <p><strong>Hora de emissão:</strong> {DateTime.Now:HH:mm}</p>
                              </div>
                        </div>

                       <div class='bloco-dados'>
                          <div class='coluna'>
                            <p><strong>Cliente:</strong> Privado</p>
                            <p><strong>CIOT:</strong> {ValidateStringField(declaracaoCiot.Ciot)}</p>
                            <p><strong>Senha:</strong> {ValidateStringField(declaracaoCiot.SenhaAlteracao)}</p>
                            <p><strong>Cidade origem:</strong> {ValidateObjectCityField(declaracaoCiot.CidadeOrigemPadrao, declaracaoCiot.CidadeOrigemPadrao?.Nome, declaracaoCiot.CidadeOrigemPadrao?.Uf)}</p>
                            <p><strong>Cidade destino:</strong> {ValidateObjectCityField(declaracaoCiot.CidadeDestinoPadrao, declaracaoCiot.CidadeDestinoPadrao?.Nome, declaracaoCiot.CidadeDestinoPadrao?.Uf)}</p>
                            <p><strong>Data inicial:</strong> {declaracaoCiot.DataInicioFrete:dd/MM/yyyy}</p>
                            <p><strong>Data final:</strong> {declaracaoCiot.DataFim:dd/MM/yyyy}</p>
                          </div>

                          <div class='coluna'>
                            <p><strong>Contratante:</strong> {ValidateObjectPersonField(declaracaoCiot.Contratante, declaracaoCiot.Contratante?.NomeRazaoSocial, declaracaoCiot.Contratante?.CpfCnpj)}</p>
                            <p><strong>Motorista:</strong>  {ValidateObjectPersonField(declaracaoCiot.Motorista, declaracaoCiot.Motorista?.CpfCnpj, declaracaoCiot.Motorista?.Nome)}</p>
                            <p><strong>Proprietário:</strong>  {ValidateObjectPersonField(declaracaoCiot.Proprietario, declaracaoCiot.Proprietario?.NomeRazaoSocial, declaracaoCiot.Proprietario?.CpfCnpj)}</p>
                            <p><strong>Natureza carga:</strong> {ValidateStringField(declaracaoCiot.CodNaturezaCarga)}</p>
                            <p><strong>Peso carga:</strong> {ValidateStringField(declaracaoCiot.Peso)}</p>
                            <p><strong>Tipo viagem:</strong> {ValidateStringField(declaracaoCiot.Tipo)}</p>
                            <p><strong>Status:</strong> {ValidateStringField(declaracaoCiot.Status)}</p>
                          </div>

                          <div class='coluna'>
                            <p><strong>Encerrado:</strong> {declaracaoCiot.Encerrado}</p>
                            <p><strong>Valor frete:</strong> {ValidateStringField(declaracaoCiot.ValorFrete)}</p>
                            <p><strong>Valor combustível:</strong> {ValidateStringField(declaracaoCiot.ValorCombustivel)}</p>
                            <p><strong>Valor pedágio:</strong>{ValidateStringField(declaracaoCiot.ValorPedagio)}</p>
                            <p><strong>Valor imposto:</strong>{ValidateStringField(declaracaoCiot.ValorImposto)}</p>
                            <p><strong>Qtde tarifas:</strong> {(declaracaoCiot.QuantidadeTarifas != null ? $"{declaracaoCiot.QuantidadeTarifas}" : "-")}</p>
                            <p><strong>Valor tarifas:</strong> {ValidateStringField(declaracaoCiot.ValorTarifas)}</p>
                          </div>
                        </div>

                        {htmlViagens}

                        <hr style='border: none; border-bottom: 2px solid #000; margin: 20px 0;'>                       

                        <table>
                            <thead>
                                <tr>
                                    <th colspan='5' class='table-title'>Totalizador</th>
                                </tr>
                                <tr>
                                    <th>Valor frete</th>
                                    <th>Valor pedágio</th>
                                    <th>Peso Carga</th>
                                    <th>Quantidade Viagens</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>{ValidateStringField(declaracaoCiot.ValorFrete)}</td>
                                    <td>{ValidateStringField(declaracaoCiot.ValorPedagio)}</td>
                                    <td>{pesoTotal}</td>
                                    <td>{quantidadeViagens}</td>
                                </tr>
                            </tbody>
                        </table>
                     </body>
                </html>";


            return html;
        }

        private static string GetCssImpressaoCiot()
        {
            return @"<style>
                    body {
                        font-family: Arial, sans-serif;
                        margin: 40px;
                        font-size: 13px;
                    }

                   .cabecalho {
                      width: 100%;
                      font-size: 13px;
                      padding-bottom: 10px;
                      margin-bottom: 20px;
                      white-space: nowrap;
                    }

                    .logo,
                    .titulo,
                    .info-caixa {
                      display: inline-block;
                      vertical-align: middle;
                    }

                    .logo {
                      width: 20%;
                    }

                    .logo img {
                      width: 100%;
                      height: auto;
                    }

                    .titulo {
                      width: 55%;
                      text-align: center;
                      font-size: 18px;
                      font-weight: bold;
                    }

                    .info-caixa {
                      width: 23%;
                      text-align: left;
                      font-size: 12px;
                      line-height: 1.4;
                      border: 1px solid #000;
                      padding: 8px 12px;
                    }

                    .bloco-dados {
                      width: 100%;
                      margin-top: 20px;
                      font-size: 18px;
                    }

                    .coluna {
                      display: inline-block;
                      width: 32%;
                      vertical-align: top;
                      padding: 0 10px;
                      box-sizing: border-box;
                    }

                    .coluna p {
                          margin: 8px 0; 
                          line-height: 1.6; 
                    }

                    table {
                        width: 100%;
                        border-collapse: collapse;
                        margin-top: 10px;
                    }

                    table th, table td {
                        border: 1px solid #000;
                        padding: 8px;
                        text-align: center;
                    }

                    table th {
                        background-color: #f0f0f0;
                    }

                    .table-title {
                        font-weight: bold;
                        background-color: #dcdcdc;
                        text-align: center;
                    }
                    
                    .table-viagens {
                        width: 100%;
                        border-collapse: collapse;
                        margin: 20px 0;
                        font-size: 13px;
                    }

                    .table-viagens th,
                    .table-viagens td {
                        border: 1px solid #000;
                        padding: 6px 8px;
                        text-align: center;
                    }

                    .table-viagens th {
                        background-color: #e8e8e8;
                        font-weight: bold;
                    }

                </style>";
        }
    }
}
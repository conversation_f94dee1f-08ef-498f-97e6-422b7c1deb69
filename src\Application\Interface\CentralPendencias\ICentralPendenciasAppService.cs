using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.CentralPendencias;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Models.PagamentoEvento.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;

namespace SistemaInfo.BBC.Application.Interface.CentralPendencias
{
    public interface ICentralPendenciasAppService : IAppService<Domain.Models.PagamentoEvento.PagamentoEvento, IPagamentoEventoReadRepository, IPagamentoEventoWriteRepository>
    {
        Task<ConsultarGridCentralPendenciasResponse> ConsultarGridCentralPendencias(int EmpresaId, String dataInicial, String dataFinal, int Perfil, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
        Task<ConsultarGridCentralPendenciasValePedagioResponse> ConsultarGridCentralPendenciasValePedagio(ConsultarGridCentralPendenciasValePedagioRequest request);
        Task<ConsultarGridCentralPendenciasMovidaResponse> ConsultarGridCentralPendenciasMovida(ConsultarGridCentralPendenciasMovida request);
        CentralPendenciasResponse ConsultarPorId(int idCentralPendencias);
        CentralPendenciasDetalhesResponse ConsultarDetalhesPorId(int idCentralPendencias);
        Task<RespPadrao> ReenviarPagamentoEvento(int PagamentoEventoId, bool automatico = false, bool pixCompletado = false);
        Task<RespPadrao> Save(CentralPendenciasRequest lFabricanteReq, bool integracao = false);
        Task<RespPadrao> ReenviarAbastecimentoMovida(int abastecimentoId);

        #region Tarefas

        Task ServiceReenviarPagamentosPendentes();
        Task ServiceReenviarAbastecimentosMovida();

        #endregion
    }
}
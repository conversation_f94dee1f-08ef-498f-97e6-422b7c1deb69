using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic;
using System.Net.Http;
using System.Net.Http.Headers;
using System.ServiceModel;
using System.Text;
using System.Threading.Tasks;
using AutoMapper.QueryableExtensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using NLog;
using SistemaInfo.BBC.Application.Helpers;
using SistemaInfo.BBC.Application.Interface.AuthSessionApi;
using SistemaInfo.BBC.Application.Interface.MonitoramentoCiot;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.ServidorCiot;
using SistemaInfo.BBC.Domain.Contracts.Servidor;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.BBC.Domain.Models.ServidorCiot;
using SistemaInfo.BBC.Domain.Models.ServidorCiot.Commands;
using SistemaInfo.BBC.Domain.Models.ServidorCiot.Repository;
using SistemaInfo.BBC.Domain.Models.Usuario.Repository;
using SistemaInfo.BBC.Infra.Data.External.JSL.JSLLeasing;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using ConsultarSituacaoTransportadorReq = SistemaInfo.BBC.Domain.External.CIOT.DTO.ConsultarSituacaoTransportadorReq;

namespace SistemaInfo.BBC.Application.Services.MonitoramentoCiot;

public class MonitoramentoCiotAppService :
    AppService<ServidorCiot, IServidorCiotReadRepository, IServidorCiotWriteRepository>, IMonitoramentoCiotAppService
{
    private readonly IConfiguration _configuration;

    public MonitoramentoCiotAppService(IAppEngine engine, IServidorCiotReadRepository readRepository,
        IServidorCiotWriteRepository writeRepository, IConfiguration configuration) :
        base(engine, readRepository, writeRepository)
    {
        _configuration = configuration;
    }

    public async Task<RespPadrao> AtivarDesativarContigencia(DadosServidorGrid dadosDadosServidor)
    {
        try
        {
            var command = Mapper.Map<ServidorCiotSalvarCommand>(dadosDadosServidor);

            command.Status = command.Status != StatusServidorCiot.ContigenciaManual
                ? StatusServidorCiot.ContigenciaManual
                : StatusServidorCiot.Ok;
            command.StatusComunicacaoAntt = command.Status != StatusServidorCiot.Ok
                ? StatusComunicacaoAntt.SemComunicacao
                : StatusComunicacaoAntt.Ok;

            await Engine.CommandBus.SendCommandAsync<ServidorCiot>(command);

            return new RespPadrao(true, "Sucesso");
        }
        catch (Exception e)
        {
            LogManager.GetCurrentClassLogger().Error(e);
            return new RespPadrao(false, e.Message);
        }
    }

    public async Task<RespPadrao> AtivarDesativarServidor(DadosServidorGrid dadosDadosServidor)
    {
        try
        {
            var command = Mapper.Map<ServidorCiotSalvarCommand>(dadosDadosServidor);

            command.Ativo = command.Ativo == 1 ? 0 : 1;

            await Engine.CommandBus.SendCommandAsync<ServidorCiot>(command);

            return new RespPadrao(true, "Sucesso");
        }
        catch (Exception e)
        {
            LogManager.GetCurrentClassLogger().Error(e);
            return new RespPadrao(false, e.Message);
        }
    }

    public async Task SincronizarServidorComBbc(ServidorSincronizarComBbcMessage message)
    {
        try
        {
            var lServidor = await Repository.Query.FindAsync(message.Id);
            lServidor.Status = message.Status;
            lServidor.StatusComunicacaoAntt = message.StatusComunicacaoAntt;
            lServidor.Status = message.Status;
            await Repository.Command.SaveChangesAsync();
        }
        catch (Exception e)
        {
            LogManager.GetCurrentClassLogger().Error(e);
        }
    }

    public async Task<RespPadrao> ConsultarGridServidoresCiot(int requestTake, int requestPage,
        OrderFilters requestOrder, List<QueryFilters> requestFilters)
    {
        var lLog = LogManager.GetCurrentClassLogger();
        try
        {
            var lCommand = new ServidoresConsultarMSCommand { IdPedagio = 0 };
            var lMessage = await Engine.CommandBus.SendCommandAsync<ConsultarServidorCiotGridMessage>(lCommand);

            var lServidorCiotQuery = lMessage.ServerList.AsQueryable();

            var totalItems = lServidorCiotQuery.Count();

            lServidorCiotQuery = string.IsNullOrWhiteSpace(requestOrder?.Campo)
                ? lServidorCiotQuery.OrderByDescending(o => o.Id)
                : lServidorCiotQuery.OrderBy($"{requestOrder?.Campo} {requestOrder?.Operador.DescriptionAttr()}");

            lServidorCiotQuery = lServidorCiotQuery.AplicarFiltrosDinamicos(requestFilters);

            lServidorCiotQuery = lServidorCiotQuery.Skip((requestPage - 1) * requestTake).Take(requestTake);

            var lServidorCiotList = lServidorCiotQuery.ToList();

            lServidorCiotList = await VerificaContigencia(lServidorCiotList);

            return new RespPadrao()
            {
                sucesso = true,
                mensagem = "sucesso",
                data = new
                {
                    items = lServidorCiotList,
                    totalItems
                }
            };
        }
        catch (Exception e)
        {
            lLog.Error(e);
            return new RespPadrao(false, e.Message);
        }
    }

    public async Task<RespPadrao> SaveServidorCiot(CadastrarServidorCiotRequest aModel)
    {
        var lLog = LogManager.GetCurrentClassLogger();
        try
        {
            if (aModel is null)
                throw new InvalidOperationException("Requisição enviada com dados inválidos.");

            var command = Mapper.Map<ServidorCiotSalvarCommand>(aModel);
            await Engine.CommandBus.SendCommandAsync<ServidorCiot>(command);

            return new RespPadrao
            {
                sucesso = true,
                mensagem = "Servidor salvo com sucesso."
            };
        }
        catch (Exception e)
        {
            lLog.Error(e);
            return new RespPadrao(false, "Não foi possível salvar o servidor. " + e.Message);
        }
    }

    public async Task<RespPadrao> ConsultarGridServidoresCiotHistorico(int requestTake, int requestPage,
        OrderFilters requestOrder, List<QueryFilters> requestFilters)
    {
        var lLog = LogManager.GetCurrentClassLogger();
        try
        {
            var lCiotServerHistoric = Repository.Query.GetAll();

            var totalItems = await lCiotServerHistoric.CountAsync();

            lCiotServerHistoric = lCiotServerHistoric.AplicarFiltrosDinamicos(requestFilters);

            lCiotServerHistoric = lCiotServerHistoric.OrderByDescending(o => o.DataAlteracao);

            var lCiotServerQuery =
                lCiotServerHistoric.ProjectTo<ConsultarServidorCiotHistoricoResponse>(Engine.Mapper
                    .ConfigurationProvider);

            lCiotServerQuery = lCiotServerQuery.OrderByDescending(c => c.Id);

            lCiotServerQuery = lCiotServerQuery.Skip((requestPage - 1) * requestTake).Take(requestTake);

            var lCiotServerHistoryList = await lCiotServerQuery.ToListAsync();


            return new RespPadrao()
            {
                sucesso = true,
                mensagem = "sucesso",
                data = new
                {
                    items = lCiotServerHistoryList,
                    totalItems
                }
            };
        }
        catch (Exception e)
        {
            lLog.Error(e);
            return new RespPadrao(false, e.Message);
        }
    }

    public List<ConsultarServidorCiotHistoricoResponse> ConsultarServidoresHistorico()
    {
        try
        {
            new LogHelper().LogOperationStart("ConsultarServidoresHistorico");
            var lQuery = Repository.Query.GetAll();

            return lQuery.ProjectTo<ConsultarServidorCiotHistoricoResponse>().ToList();
        }
        catch (Exception ex)
        {
            new LogHelper().Error(ex, "Erro ao executar ConsultarServidoresHistorico");
            throw;
        }
        finally
        {
            new LogHelper().LogOperationEnd("ConsultarServidoresHistorico");
        }
    }

    #region Servico

    public async Task<List<ConsultarServidorCiotGrid>> VerificaContigencia(List<ConsultarServidorCiotGrid> servidores)
    {
        var lLog = LogManager.GetCurrentClassLogger();
        try
        {
            var retornoStatus = new List<ConsultarServidorCiotGrid>();
            var lIpsServidoresSoap = servidores.Where(x => x.TipoServidor == TipoServidor.Soap.GetHashCode()).ToList();
            var lIpsServidoresJson = servidores.Where(x => x.TipoServidor == TipoServidor.Json.GetHashCode()).ToList();
            if (lIpsServidoresSoap.Count > 0)
            {
                var statusServidorSoap = await VerificaContigenciaServidoresSoap(lIpsServidoresSoap);
                retornoStatus.AddRange(statusServidorSoap);
            }

            if (lIpsServidoresJson.Count > 0)
            {
                var statusServidorJson = await VerificaContigenciaServidoresJson(lIpsServidoresJson);
                retornoStatus.AddRange(statusServidorJson);
            }

            return retornoStatus;
        }
        catch (Exception e)
        {
            lLog.Error(e);
            return servidores;
        }
    }

    private async Task<List<ConsultarServidorCiotGrid>> VerificaContigenciaServidoresJson(
        List<ConsultarServidorCiotGrid> ipsServidoresJson)
    {
        var lLog = LogManager.GetCurrentClassLogger();
        var lToken = _configuration["TokenComunicacaoInterna"];
        foreach (var servidor in ipsServidoresJson)
        {
            var lRequest = new ConsultarSituacaoTransportadorReq
            {
                CpfCnpjTransportador = "06372005948",
                CpfCnpjInteressado = "06372005948",
                RNTRCTransportador = "00000002"
            };

            try
            {
                if (servidor.Status != StatusServidorCiot.ContigenciaManual.GetHashCode())
                {
                    using var client = new HttpClient();
                    client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
                    client.DefaultRequestHeaders.Add("x-web-auth-token", lToken);
                    var content = new StringContent(JsonConvert.SerializeObject(lRequest), Encoding.UTF8,
                        "application/json");
                    var apiResult =
                        await client.PostAsync($"{servidor.Link}{"/Operacoes/ConsultarSituacaoTransportador"}",
                            content);

                    apiResult.EnsureSuccessStatusCode();
                    var lJsonResponse = await apiResult.Content.ReadAsStringAsync();
                    if (lJsonResponse.Contains("ADM002"))
                    {
                        servidor.StatusConsulta = StatusServidorCiot.ContigenciaForcada.GetHashCode();
                        servidor.Status = StatusServidorCiot.ContigenciaForcada.GetHashCode();
                        servidor.StatusComunicacaoAntt = StatusComunicacaoAntt.SemComunicacao.GetHashCode();
                    }
                    else if (lJsonResponse.Contains("ADM024"))
                    {
                        servidor.StatusConsulta = StatusServidorCiot.ContigenciaManual.GetHashCode();
                        servidor.Status = StatusServidorCiot.ContigenciaManual.GetHashCode();
                        servidor.StatusComunicacaoAntt = StatusComunicacaoAntt.SemComunicacao.GetHashCode();
                    }
                    else
                    {
                        servidor.StatusConsulta = StatusServidorCiot.Ok.GetHashCode();
                        servidor.Status = StatusServidorCiot.Ok.GetHashCode();
                        servidor.StatusComunicacaoAntt = StatusComunicacaoAntt.Ok.GetHashCode();
                    }
                }
            }
            catch (CommunicationException e)
            {
                lLog.Error(e);
                servidor.StatusConsulta = StatusServidorCiot.Offline.GetHashCode();
                servidor.Status = StatusServidorCiot.Offline.GetHashCode();
                servidor.StatusComunicacaoAntt = StatusComunicacaoAntt.SemComunicacao.GetHashCode();
            }
            catch (Exception e)
            {
                lLog.Error(e);
                servidor.StatusConsulta = StatusServidorCiot.Offline.GetHashCode();
                servidor.Status = StatusServidorCiot.Offline.GetHashCode();
                servidor.StatusComunicacaoAntt = StatusComunicacaoAntt.SemComunicacao.GetHashCode();
            }
        }

        return ipsServidoresJson;
    }

    private async Task<List<ConsultarServidorCiotGrid>> VerificaContigenciaServidoresSoap(
        List<ConsultarServidorCiotGrid> ipsServidoresSoap)
    {
        var lLog = LogManager.GetCurrentClassLogger();
        foreach (var servidor in ipsServidoresSoap)
        {
            try
            {
                var lRequest = new SistemaInfo.BBC.Infra.Data.External.JSL.JSLLeasing.ConsultarSituacaoTransportadorReq
                {
                    CpfCnpjTransportador = "06372005948",
                    CpfCnpjInteressado = "06372005948",
                    RNTRCTransportador = "50898955"
                };


                if (servidor.Status != StatusServidorCiot.ContigenciaManual.GetHashCode())
                {
                    using var lCiotClient =
                        new CiotServiceClient(CiotServiceClient.EndpointConfiguration.BasicHttpBinding_CiotService,
                            servidor.Link);
                    var lResponse = await lCiotClient.ConsultarSituacaoTransportadorAsync(lRequest);

                    if (lResponse.Excecao.Codigo.Contains("ADM002"))
                    {
                        servidor.StatusConsulta = StatusServidorCiot.ContigenciaForcada.GetHashCode();
                        servidor.Status = StatusServidorCiot.ContigenciaForcada.GetHashCode();
                        servidor.StatusComunicacaoAntt = StatusComunicacaoAntt.SemComunicacao.GetHashCode();
                    }
                    else if (lResponse.Excecao.Codigo.Contains("ADM024"))
                    {
                        servidor.StatusConsulta = StatusServidorCiot.ContigenciaManual.GetHashCode();
                        servidor.Status = StatusServidorCiot.ContigenciaManual.GetHashCode();
                        servidor.StatusComunicacaoAntt = StatusComunicacaoAntt.SemComunicacao.GetHashCode();
                    }
                    else
                    {
                        servidor.StatusConsulta = StatusServidorCiot.Ok.GetHashCode();
                        servidor.Status = StatusServidorCiot.Ok.GetHashCode();
                        servidor.StatusComunicacaoAntt = StatusComunicacaoAntt.Ok.GetHashCode();
                    }
                }
            }
            catch (CommunicationException e)
            {
                lLog.Error(e);
                servidor.StatusConsulta = StatusServidorCiot.Offline.GetHashCode();
                servidor.Status = StatusServidorCiot.Offline.GetHashCode();
                servidor.StatusComunicacaoAntt = StatusComunicacaoAntt.SemComunicacao.GetHashCode();
            }
            catch (Exception e)
            {
                lLog.Error(e);
                servidor.StatusConsulta = StatusServidorCiot.Offline.GetHashCode();
                servidor.Status = StatusServidorCiot.Offline.GetHashCode();
                servidor.StatusComunicacaoAntt = StatusComunicacaoAntt.SemComunicacao.GetHashCode();
            }
        }

        return ipsServidoresSoap;
    }

    #endregion
}
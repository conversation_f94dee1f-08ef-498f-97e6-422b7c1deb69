using Microsoft.EntityFrameworkCore;
using SistemaInfo.BBC.Domain.Models.ClientSecretAdm.Repository;
using SistemaInfo.BBC.Infra.Data.Context;
using SistemaInfo.Framework.DomainDrivenDesign.Infra.Repository;

namespace SistemaInfo.BBC.Infra.Data.Repository.ClientSecretAdm
{
    
    public class ClientSecretAdmBaseWriteRepository<TContext, TClientSecretAdmEntity> : WriteOnlyRepository<TClientSecretAdmEntity, TContext>
        where TContext : DbContext
        where TClientSecretAdmEntity : Domain.Models.ClientSecretAdm.ClientSecretAdm
    {
        public ClientSecretAdmBaseWriteRepository(TContext context) : base(context)
        {
        }
    }
    
    public class ClientSecretAdmWriteRepository : ClientSecretAdmBaseWriteRepository<ConfigContext, Domain.Models.ClientSecretAdm.ClientSecretAdm>, IClientSecretAdmWriteRepository
    {
        public ClientSecretAdmWriteRepository(ConfigContext context) : base(context)
        {
        }

       
    }
}

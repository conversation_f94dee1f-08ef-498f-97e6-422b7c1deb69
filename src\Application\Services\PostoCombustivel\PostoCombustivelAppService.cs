using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using AutoMapper.QueryableExtensions;
using SistemaInfo.BBC.Application.Helpers;
using SistemaInfo.BBC.Application.Interface.PostoCombustivel;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.PostoCombustivel;
using SistemaInfo.BBC.Application.Objects.Web.PostoCombustivel.Request;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.BBC.Domain.Models.PostoCombustivel.Commands;
using SistemaInfo.BBC.Domain.Models.PostoCombustivel.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using PostoCombustivelAlterarStatusCommand =
    SistemaInfo.BBC.Domain.Models.PostoCombustivel.Command.PostoCombustivelAlterarStatusCommand;

namespace SistemaInfo.BBC.Application.Services.PostoCombustivel
{
    public class PostoCombustivelAppService : AppService<Domain.Models.PostoCombustivel.PostoCombustivel,
            IPostoCombustivelReadRepository, IPostoCombustivelWriteRepository>,
        IPostoCombustivelAppService
    {
        public PostoCombustivelAppService(IAppEngine engine, IPostoCombustivelReadRepository readRepository,
            IPostoCombustivelWriteRepository writeRepository) : base(engine, readRepository, writeRepository)
        {
        }

        public ConsultarGridPostoCombustivelResponse ConsultarGridPostoCombustivel(int take, int page,
            OrderFilters orderFilters, List<QueryFilters> filters)
        {
            try
            {
                new LogHelper().LogOperationStart("ConsultarGridPostoCombustivel");
                var lPostoCombustivel = Repository.Query.GetAll();

                var lCount = lPostoCombustivel.Count();

                lPostoCombustivel = lPostoCombustivel.AplicarFiltrosDinamicos(filters);
                lPostoCombustivel = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                    ? lPostoCombustivel.OrderByDescending(o => o.Id)
                    : lPostoCombustivel.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

                var retorno = lPostoCombustivel.Skip((page - 1) * take)
                    .Take(take)
                    .ProjectTo<ConsultarPostoCombustivelGrid>(Engine.Mapper.ConfigurationProvider).ToList();

                return new ConsultarGridPostoCombustivelResponse
                {
                    items = retorno,
                    totalItems = lCount
                };
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar ConsultarGridPostoCombustivel");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("ConsultarGridPostoCombustivel");
            }
        }

        public PostoCombustivelResponse ConsultarPorId(int idPostoCombustivel)
        {
            try
            {
                new LogHelper().LogOperationStart("ConsultarPorId");
                var lRetorno = Repository.Query.GetByIdIncludeCombustivel(idPostoCombustivel).Result;
                return Mapper.Map<PostoCombustivelResponse>(lRetorno);
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar ConsultarPorId");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("ConsultarPorId");
            }
        }

        public async Task<RespPadrao> Save(PostoCombustivelRequest request)
        {
            try
            {
                var command = Mapper.Map<PostoCombustivelSaveComRetornoCommand>(request);

                var retorno =
                    await Engine.CommandBus.SendCommandAsync<Domain.Models.PostoCombustivel.PostoCombustivel>(command);

                return new RespPadrao()
                {
                    id = retorno.Id,
                    sucesso = true,
                    mensagem = "Registro salvo com sucesso."
                };
            }
            catch (Exception e)
            {
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = "Ocorreu um erro ao salvar o registro. " + e.Message
                };
            }
        }
    }
}
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using AutoMapper.QueryableExtensions;
using Microsoft.EntityFrameworkCore;
using NLog;
using SistemaInfo.BBC.Application.Interface.CentralNotificacoes;
using SistemaInfo.BBC.Application.Interface.CentralPendencias;
using SistemaInfo.BBC.Application.Interface.Pedagio;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.CentralNotificacoes;
using SistemaInfo.BBC.Domain.Contracts.NotificacaoPedagio;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.BBC.Domain.Models.Notificacao;
using SistemaInfo.BBC.Domain.Models.Notificacao.Commands;
using SistemaInfo.BBC.Domain.Models.Notificacao.Repository;
using SistemaInfo.BBC.Domain.Models.Usuario.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.Services.CentralNotificacoes
{
    public class CentralNotificacoesAppService : AppService<Notificacao,
        INotificacaoReadRepository, INotificacaoWriteRepository>, ICentralNotificacoesAppService
    {
        private readonly ICentralPendenciasAppService _centralPendenciasAppService;
        private readonly IUsuarioReadRepository _usuarioReadRepository;
        private readonly IPedagioAppService _pedagioAppService;

        public CentralNotificacoesAppService(
            IAppEngine engine,
            INotificacaoReadRepository readRepository,
            IUsuarioReadRepository usuarioReadRepository,
            INotificacaoWriteRepository writeRepository, ICentralPendenciasAppService centralPendenciasAppService, IPedagioAppService pedagioAppService) : base(
            engine, readRepository, writeRepository)
        {
            _centralPendenciasAppService = centralPendenciasAppService;
            _pedagioAppService = pedagioAppService;
            _usuarioReadRepository = usuarioReadRepository;
        }

        public async Task<ConsultarGridCentralNotificacoesResponse> ConsultarGridCentralNotificacoes(int empresaId,
            string dataInicial, string dataFinal, int perfil, int take, int page, OrderFilters orderFilters,
            List<QueryFilters> filters)
        {
            var dtIni = dataInicial.ToDateTime();
            var dtFim = dataFinal.ToDateTime().AddDays(1).AddSeconds(-1);
            
            var lCentralNotificacoes = Repository.Query
                .GetAll()
                .Include(a => a.PagamentoEvento)
                .ThenInclude(a => a.Viagem)
                .AsQueryable();
            
            if(User.EmpresaId != 0) 
                lCentralNotificacoes = lCentralNotificacoes.Where(e => e.PagamentoEvento.Viagem.EmpresaId == User.EmpresaId);
            
            var grupoEmpresaId = await _usuarioReadRepository
                .AsNoTracking()
                .Where(x => x.Id == Engine.User.Id)
                .Select(x => x.GrupoEmpresaId)
                .FirstOrDefaultAsync();
            
            if (grupoEmpresaId != null)
                lCentralNotificacoes = lCentralNotificacoes.Where(x => x.PagamentoEvento.Viagem.Empresa.GrupoEmpresaId == grupoEmpresaId);
            
            
            if (empresaId != 0)
            {
                if (User.EmpresaId == empresaId || User.EmpresaId == 0)
                    lCentralNotificacoes = lCentralNotificacoes.Where(e => e.PagamentoEvento.Viagem.EmpresaId == empresaId);

                else
                    throw new InvalidOperationException("Usuário sem permissões para visualizar pagamentos desta empresa.");
            }

            lCentralNotificacoes = lCentralNotificacoes.Where(p => p.DataCadastro >= dtIni && p.DataCadastro <= dtFim);

            lCentralNotificacoes = lCentralNotificacoes.AplicarFiltrosDinamicos(filters);

            lCentralNotificacoes = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                ? lCentralNotificacoes.OrderByDescending(o => o.Id)
                : lCentralNotificacoes.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            var lCount = await lCentralNotificacoes.CountAsync();
            
            var retorno = await lCentralNotificacoes.Skip((page - 1) * take).Take(take)
                .ProjectTo<ConsultarGridCentralNotificacoes>(Engine.Mapper.ConfigurationProvider)
                .ToListAsync();

            return new ConsultarGridCentralNotificacoesResponse
            {
                items = retorno,
                totalItems = lCount
            };
        }

        public async Task<ConsultarGridCentralNotificacoesValePedagioResponse> ConsultarGridCentralNotificacoesValePedagio(ConsultarGridCentralNotificacoesValePedagioRequest request)
        {
            try
            {
                return await _pedagioAppService.ConsultarGridCentralNotificacoesValePedagio(request);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e.Message);
                throw;
            }
        }

        public CentralNotificacoesResponse ConsultarPorId(int idCentralNotificacoes)
        {
            return Mapper.Map<CentralNotificacoesResponse>(Repository.Query.Include(x => x.PagamentoEvento)
                .FirstOrDefault(a => a.Id == idCentralNotificacoes));
        }

        public async Task<RespPadrao> Save(CentralNotificacoesRequest request)
        {
            try
            {
                var command = Mapper.Map<NotificacaoSalvarComRetornoCommand>(request);
                var retorno = await Engine.CommandBus.SendCommandAsync<Notificacao>(command);
                return new RespPadrao(true, "Notificação salva com sucesso.");
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return new RespPadrao(false, "Ocorreu um erro ao salvar a notificação: " + e.Message);
            }
        }
    }
}

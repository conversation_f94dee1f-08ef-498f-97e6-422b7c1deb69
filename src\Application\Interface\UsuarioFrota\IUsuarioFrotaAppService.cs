using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using SistemaInfo.BBC.Application.Interface.Base;
using SistemaInfo.BBC.Application.Objects.Api.Portador;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.Portador;
using SistemaInfo.BBC.Application.Objects.Web.PortadorCentroCusto;
using SistemaInfo.BBC.Application.Objects.Web.Transportador;
using SistemaInfo.BBC.Application.Objects.Web.UsuarioFrota;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Models.Portador.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;

namespace SistemaInfo.BBC.Application.Interface.UsuarioFrota
{
    public interface IUsuarioFrotaAppService : IAppService<Domain.Models.Portador.Portador, 
        IPortadorReadRepository, 
        IPortadorWriteRepository>,
        IBaseGetAppService<ConsultarPorIdUsuarioFrotaResponse>
    {
        ConsultarGridUsuarioFrotaResponse ConsultarGrid(int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
        Task<RespPadrao> Save(UsuarioFrotaRequest lmodel);
        Task<RespPadrao> AlterarStatus(UsuarioFrotaStatusRequest lPortadorStatus);
        Task<RespPadrao> Bloquear(UsuarioFrotaStatusRequest lPortadorStatus);
        Task<RespPadrao> Cancelar(UsuarioFrotaCancelarRequest lPortadorStatus);
        Task<ConsultarPorIdUsuarioFrotaResponse> ConsultarPorId(int id);
    }
}
// Versão para ambiente do desenvolvedor Localhost
// ng serve => Hospedar server localhost para testar
// ng build => Gerar versão com este arquivo de configuração

export const environment = {
  production: true,
  // url: "http://hml-mhservices.sistemainfo.com.br"
   //url: "http://sw12-140:1000"
  // url: "http://hml-mhservices.sistemainfo.com.br"
    // url: "http://hmlciotpws.bancobbcdigital.com.br:8091/"
   //url: "http://***********:1982/"
   url: window.location.hostname === "localhost" ?
       "http://localhost:51001/" :
       window.location.protocol + "//" + window.location.host + "/Web/"
  // url: "http://ciotpws.bancobbcdigital.com.br:8091/"
}


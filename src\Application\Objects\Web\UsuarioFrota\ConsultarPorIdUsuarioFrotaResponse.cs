using System;
using System.Collections.Generic;
using SistemaInfo.BBC.Application.Objects.Web.Portador;
using SistemaInfo.BBC.Application.Objects.Web.PortadorCentroCusto;

namespace SistemaInfo.BBC.Application.Objects.Web.UsuarioFrota
{
    public class ConsultarPorIdUsuarioFrotaResponse
    {
        public int Id { get; set; }
        public int TipoPessoa { get; set; }
        public string CpfCnpj { get; set; }
        public string Nome { get; set; }
        public string Celular { get; set; }
        public string Telefone { get; set; }
        public string Email { get; set; }
        public int? Atividade { get; set; }
        public string Placa { get; set; }
        public int? CarretaId { get; set; }
        public int? Carreta2Id { get; set; }
        public int? Carreta3Id { get; set; }
        public int? EmpresaId { get; set; }
        public string EmpresaNome { get; set; }
        public CarretaResponse Carreta { get; set; }
        public CarretaResponse Carreta2 { get; set; }
        public CarretaResponse Carreta3 { get; set; }
        public bool ControlaAbastecimentoCentroCusto { get; set; }
        public List<PortadorCentroCustoResp> PortadorCentroCusto { get; set; }
        public int? EmpresaIdFrota { get; set; }
        
        //campos adicionais api mobile
        public int QuantidadeErroSenha { get; set; } = 0;
        public int? TipoEmpresaId { get; set; }
        public string Link { get; set; }
        
        //campos adicionais api mobile login
        public DateTime? DataCriacaoSenha { get; set; }
        public DateTime? DataUltimoAcesso { get; set; }
        public int? SenhaProvisoria { get; set; } = 0;
        
        public string Status { get; set; }
        public string CNH { get; set; }
        public DateTime? DataEmissaoCNH { get; set; }
        public DateTime? DataVencimentoCNH { get; set; }
    }
    
}
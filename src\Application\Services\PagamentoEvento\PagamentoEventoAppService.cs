using System;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using AutoMapper.QueryableExtensions;
using JetBrains.Annotations;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using NLog;
using SistemaInfo.BBC.Application.Interface.PagamentoEvento;
using SistemaInfo.BBC.Application.Objects.Api.Viagem;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.PagamentoEvento;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.BBC.Domain.Models.PagamentoEvento.Repository;
using SistemaInfo.BBC.Domain.Models.Usuario.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.Services.PagamentoEvento
{
    public class PagamentoEventoAppService :
        AppService<Domain.Models.PagamentoEvento.PagamentoEvento, IPagamentoEventoReadRepository,
            IPagamentoEventoWriteRepository>,
        IPagamentoEventoAppSerivce
    {
        private readonly IUsuarioReadRepository _usuarioReadRepository;

        public PagamentoEventoAppService(IAppEngine engine, IPagamentoEventoReadRepository readRepository,
            IPagamentoEventoWriteRepository writeRepository, IUsuarioReadRepository usuarioReadRepository) : base(
            engine, readRepository, writeRepository)
        {
            _usuarioReadRepository = usuarioReadRepository;
        }

        public ConsultaGridPagamentoEventoResponse ConsultarGridPagamentoEvento(
            ConsultaGridPagamentoEventoRequest eventoRequest)
        {
            try
            {
                var DtInicial = eventoRequest.dataInicial.ToDateTime();
                var DtFinal = eventoRequest.dataFinal.ToDateTime().AddDays(1).AddSeconds(-1);

                var lPagamentos = Repository.Query
                    .Include(s => s.Viagem.PortadorProprietario)
                    .Include(s => s.Viagem.PortadorMotorista)
                    .AsQueryable();

                if (eventoRequest.Status >= 0)
                {
                    lPagamentos = lPagamentos.Where(x => x.Status == eventoRequest.Status);
                }

                if (!Engine.User.IsNivelSuperUsuario &&
                    !Engine.User.IsNivelAdministradora)
                {
                    var listaEmpresasIds = _usuarioReadRepository.GetEmpresasAcessoUsuario(Engine.User.Id).Result;
                    listaEmpresasIds.Add(11);
                    if (!listaEmpresasIds.IsEmpty())
                        lPagamentos = lPagamentos.Where(o => listaEmpresasIds.Contains(o.EmpresaId ?? 0));
                  
                }

                if (eventoRequest.EmpresaId != 0)
                    lPagamentos = lPagamentos.Where(x => x.EmpresaId == eventoRequest.EmpresaId);


                if (DtInicial > ("01/01/0001 00:00:00").ToDateTime() || DtFinal > ("01/01/0001 00:00:00").ToDateTime())
                    lPagamentos = lPagamentos.Where(p => p.DataAlteracao >= DtInicial && p.DataAlteracao <= DtFinal);


                lPagamentos = lPagamentos.AplicarFiltrosDinamicos(eventoRequest.Filters);

                lPagamentos = string.IsNullOrWhiteSpace(eventoRequest.Order?.Campo)
                    ? lPagamentos.OrderByDescending(o => o.Id)
                    : lPagamentos.OrderBy(
                        $"{eventoRequest.Order.Campo} {eventoRequest.Order.Operador.DescriptionAttr()}");

                var lCount = lPagamentos.Count();

                var retorno = lPagamentos.Skip((eventoRequest.Page - 1) * eventoRequest.Take)
                    .Take(eventoRequest.Take).ProjectTo<ConsultaGridPagamentoEventoItem>().ToList();

                return new ConsultaGridPagamentoEventoResponse()
                {
                    items = retorno,
                    totalItems = lCount
                };
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                throw;
            }
        }

        public async Task EnviaWebHookAtualizacaoStatus(Domain.Models.PagamentoEvento.PagamentoEvento pix)
        {
            try
            {
                if (pix.WebhookUrl.IsNullOrWhiteSpace())
                {
                    LogManager.GetCurrentClassLogger()
                        .Error("Url do webhook não preenchida para o pagamento ID " + pix.Id);
                    return;
                }

                var deserializedObject = JsonConvert.DeserializeObject<ViagemIntegrarResponse>(pix.JsonRetorno);

                var lRequestObject = new
                {
                    sucesso = true,
                    mensagem = "Operação realizada com sucesso!",
                    data = new ViagemIntegrarWebhookResponse()
                    {
                        sucesso = true,
                        viagemId = pix.Viagem.Id,
                        viagemExternoId = pix.Viagem.ViagemExternoId,
                        statusViagem = pix.Viagem.Status?.GetHashCode(),
                        mensagem = pix.Viagem.Status == StatusViagem.Baixado
                            ? "Viagem baixada com sucesso!"
                            : "Pagamento integrado com sucesso à viagem!",
                        pagamento = new PagamentoViagemWebhoookResponse()
                        {
                            pagamentoExternoId = pix.PagamentoExternoId,
                            valorParcela = pix.Valor,
                            valorMotorista = pix.ValorTransferenciaMotorista,
                            statusPagamento = pix.Status.GetHashCode(),
                            códTransacao = pix.CodigoTransacao,
                            formaPagamento = pix.FormaPagamento?.GetHashCode(),
                            pagamentoEventoId = pix.Id,
                            mensagem = deserializedObject.Pagamento.Mensagem
                        }
                    }
                };

                var requestString = JsonConvert.SerializeObject(lRequestObject);
                using var client = new HttpClient();
                var content = new StringContent(requestString, Encoding.UTF8, "application/json");
                await client.PostAsync(pix.WebhookUrl, content);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, "Erro ao enviar o webhook do pagamento ID " + pix.Id);
            }
        }

        public async Task<RespPadrao> SalvarOcorrencia(SalvarOcorrenciaRequest request)
        {
            try
            {
                if (request.PagamentoEventoId == null)
                    return new RespPadrao
                    {
                        sucesso = false,
                        mensagem = "Não foi possível salvar uma ocorrência para um evento de pagamento inexistente."
                    };

                var lPagamentoEvento = await Repository.Query.GetByIdAsync(request.PagamentoEventoId);
                lPagamentoEvento.Ocorrencia = request.Ocorrencia;
                await Repository.Command.SaveChangesAsync();

                return new RespPadrao
                {
                    sucesso = true,
                    mensagem = "Ocorrência atualizada com sucesso!"
                };
            }
            catch (Exception e)
            {
                return new RespPadrao
                {
                    sucesso = false,
                    mensagem = "Erro interno no servidor ao tentar atualizar ocorrência: " + e.Message
                };
            }
        }

        public async Task<RespPadrao> ConsultarPorId(int pagamentoEventoId)
        {
            try
            {
                if (pagamentoEventoId == 0)
                    return new RespPadrao
                    {
                        sucesso = false,
                        mensagem = "Não foi possível efetuar a consulta."
                    };

                var lPagamentoEvento = await Repository.Query.GetByIdAsync(pagamentoEventoId);

                return new RespPadrao
                {
                    sucesso = true,
                    mensagem = "Consulta realizada com sucesso.",
                    data = lPagamentoEvento
                };
            }
            catch (Exception)
            {
                return new RespPadrao
                {
                    sucesso = false,
                    mensagem = "Erro interno no servidor ao tentar realizar a consulta."
                };
            }
        }

        [ItemCanBeNull]
        public async Task<Domain.Models.PagamentoEvento.PagamentoEvento> BuscarPagamentoEventoCompleto(int idPagamentoEvento)
        {
            var pagamentoEventoParaAtualizar = await Repository.Query
            .Include(v => v.Empresa)
            .Include(v => v.Viagem).ThenInclude(v => v.PortadorProprietario)
            .Include(v => v.Viagem).ThenInclude(v => v.PortadorMotorista)
            .Include(v => v.UsuarioCadastro)
            .Include(v => v.Transacao)
            .FirstOrDefaultAsync(p => p.Id ==idPagamentoEvento);
            return pagamentoEventoParaAtualizar;
        }
        
        public async Task<Domain.Models.PagamentoEvento.PagamentoEvento> BuscarPagamentoEvento(int idPagamentoEvento, int viagemId)
        {
            var pagamentoEventoParaAtualizar =  await Repository.Query.AsNoTracking()
                .Include(pe => pe.Transacao)
                .Include(v => v.Viagem)
                .ThenInclude(v => v.PortadorProprietario)
                .FirstOrDefaultAsync(v => v.Viagem.Id == viagemId && v.Id == idPagamentoEvento);
            return pagamentoEventoParaAtualizar;
        }
        
       
    }
}
using BBC.Test.Tests.ESexoEnum.Fixture;
using SistemaInfo.BBC.Domain.Enum;
using System.ComponentModel;
using System.Reflection;
using Xunit;

namespace BBC.Test.Tests.ESexoEnum
{
    [Collection(nameof(ESexoEnumCollection))]
    public class ESexoEnumTest
    {
        private readonly ESexoEnumFixture _fixture;

        public ESexoEnumTest(ESexoEnumFixture fixture)
        {
            _fixture = fixture;
        }

        [Theory(DisplayName = "ESexo - deve conter valores corretos")]
        [Trait("ESexoEnum", "ValoresEnum")]
        [InlineData(1, "Masculino")]
        [InlineData(2, "Feminino")]
        [InlineData(3, "Outros")]
        [InlineData(4, "Indefinido")]
        public void ESexo_DeveConterValoresCorretos(int valorEsperado, string nomeEnum)
        {
            // Arrange & Act
            var enumValue = System.Enum.Parse<ESexo>(nomeEnum);

            // Assert
            Assert.Equal(valorEsperado, (int)enumValue);
        }

        [Theory(DisplayName = "ESexo - deve conter descrições corretas")]
        [Trait("ESexoEnum", "DescricoesEnum")]
        [InlineData("Masculino", "Masculino")]
        [InlineData("Feminino", "Feminino")]
        [InlineData("Outros", "Outros")]
        [InlineData("Indefinido", "Indefinido")]
        public void ESexo_DeveConterDescricoesCorretas(string nomeEnum, string descricaoEsperada)
        {
            // Arrange
            var enumValue = System.Enum.Parse<ESexo>(nomeEnum);
            var field = typeof(ESexo).GetField(enumValue.ToString());
            var attribute = field.GetCustomAttribute<DescriptionAttribute>();

            // Act & Assert
            Assert.NotNull(attribute);
            Assert.Equal(descricaoEsperada, attribute.Description);
        }

        [Fact(DisplayName = "ESexo - deve conter quatro valores")]
        [Trait("ESexoEnum", "QuantidadeValores")]
        public void ESexo_DeveConterQuatroValores()
        {
            // Arrange & Act
            var valores = System.Enum.GetValues(typeof(ESexo));

            // Assert
            Assert.Equal(4, valores.Length);
        }

        [Theory(DisplayName = "ESexo - deve permitir conversão para string")]
        [Trait("ESexoEnum", "ConversaoString")]
        [InlineData("Masculino")]
        [InlineData("Feminino")]
        [InlineData("Outros")]
        [InlineData("Indefinido")]
        public void ESexo_DevePermitirConversaoParaString(string nomeEsperado)
        {
            // Arrange & Act
            var enumValue = System.Enum.Parse<ESexo>(nomeEsperado);
            var resultado = enumValue.ToString();

            // Assert
            Assert.Equal(nomeEsperado, resultado);
        }

        [Theory(DisplayName = "ESexo - deve permitir conversão de string")]
        [Trait("ESexoEnum", "ConversaoDeString")]
        [InlineData("Masculino", ESexo.Masculino)]
        [InlineData("Feminino", ESexo.Feminino)]
        [InlineData("Outros", ESexo.Outros)]
        [InlineData("Indefinido", ESexo.Indefinido)]
        public void ESexo_DevePermitirConversaoDeString(string nomeEnum, ESexo enumEsperado)
        {
            // Arrange & Act
            var resultado = System.Enum.Parse<ESexo>(nomeEnum);

            // Assert
            Assert.Equal(enumEsperado, resultado);
        }

        [Theory(DisplayName = "ESexo - deve verificar se valor é definido")]
        [Trait("ESexoEnum", "ValorDefinido")]
        [InlineData(1, true)]  // Masculino
        [InlineData(2, true)]  // Feminino
        [InlineData(3, true)]  // Outros
        [InlineData(4, true)]  // Indefinido
        [InlineData(999, false)] // Valor inválido
        public void ESexo_DeveVerificarSeValorEDefinido(int valor, bool esperado)
        {
            // Arrange & Act
            var resultado = System.Enum.IsDefined(typeof(ESexo), valor);

            // Assert
            Assert.Equal(esperado, resultado);
        }
    }
}

using Microsoft.AspNetCore.Http;
using System.Threading.Tasks;

namespace SistemaInfo.BBC.ApiIntegracao.Middleware
{
    /// <summary>
    /// Middleware para detectar automaticamente a versão da API baseada na URL
    /// </summary>
    public class ApiVersionDetectionMiddleware
    {
        private readonly RequestDelegate _next;

        public ApiVersionDetectionMiddleware(RequestDelegate next)
        {
            _next = next;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            var path = context.Request.Path.Value?.ToLowerInvariant();
            if (path?.Contains("/v2/") == true)
            {
                context.Items["IsApiV2"] = true;
                context.Items["ApiVersion"] = "2.0";
            }
            else
            {
                context.Items["IsApiV2"] = false;
                context.Items["ApiVersion"] = "1.0";
            }
            await _next(context);
        }
    }
}

export interface ICidade {
    idCidade: number;
    nome: string;
    estadoId: number;
    uf: string;
    ibge: string;
    pais: string;
    nomeSiglaDescricao?: string;
}

export class Cidade implements ICidade {
    idCidade: number = 0;
    nome: string = '';
    estadoId: number = 0;
    uf: string = '';
    ibge: string = null;
    pais: string = null;
    nomeSiglaDescricao: string = '';
    
    constructor() {}
}

export interface CidadeResponse {
    cidades: Cidade[];
    sucesso: boolean;
    excecao: any;
}

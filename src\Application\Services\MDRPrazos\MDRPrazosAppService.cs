using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using AutoMapper.QueryableExtensions;
using Microsoft.EntityFrameworkCore;
using NLog;
using SistemaInfo.BBC.Application.Helpers;
using SistemaInfo.BBC.Application.Interface.MDRPrazos;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.MDRPrazos;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.BBC.Domain.Models.MDRPrazos.Commands;
using SistemaInfo.BBC.Domain.Models.MDRPrazos.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;

namespace SistemaInfo.BBC.Application.Services.MDRPrazos
{
    public class MDRPrazosAppService : AppService<Domain.Models.MDRPrazos.MDRPrazos,
        IMDRPrazosReadRepository, IMDRPrazosWriteRepository>, IMDRPrazosAppService
    {
        public MDRPrazosAppService(
            IAppEngine engine,
            IMDRPrazosReadRepository readRepository,
            IMDRPrazosWriteRepository writeRepository) : base(
            engine, readRepository, writeRepository)
        {
        }

        public ConsultarGridMDRPrazosResponse ConsultarGridMDRPrazos(int take, int page, OrderFilters orderFilters,
            List<QueryFilters> filters)
        {
            IQueryable<Domain.Models.MDRPrazos.MDRPrazos> lMDRPrazos;

            lMDRPrazos = Repository.Query.GetAll();

            lMDRPrazos = lMDRPrazos.AplicarFiltrosDinamicos(filters);
            lMDRPrazos = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                ? lMDRPrazos.OrderByDescending(o => o.Id)
                : lMDRPrazos.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            var lCount = lMDRPrazos.Count();
            var retorno = lMDRPrazos.Skip((page - 1) * take)
                .Take(take).ProjectTo<ConsultarGridMDRPrazos>(Engine.Mapper.ConfigurationProvider).ToList();

            return new ConsultarGridMDRPrazosResponse
            {
                items = retorno,
                totalItems = lCount
            };
        }

        public MDRPrazosResponse ConsultarPorId(int idMDRPrazos)
        {
            try
            {
                new LogHelper().LogOperationStart("ConsultarPorId");
                if (idMDRPrazos < 0)
                {
                    throw new Exception("ID inválido!");
                }

                var lResult = Repository.Query.ConsultarPorId(idMDRPrazos);

                return Mapper.Map<MDRPrazosResponse>(lResult);
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar ConsultarPorId");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("ConsultarPorId");
            }
        }

        public List<MDRPrazosResponse> ConsultarMDRPrazosPorBancoId(string lBancoId)
        {
            try
            {
                new LogHelper().LogOperationStart("ConsultarMDRPrazosPorBancoId");
                var lPrazos = Repository.Query
                    .Where(m => m.BancoId == lBancoId)
                    .Where(m => m.Ativo == 1)
                    .OrderBy(m => m.Prazo)
                    .Include(a => a.Banco)
                    .ToList();

                return Mapper.Map<List<MDRPrazosResponse>>(lPrazos);
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar ConsultarMDRPrazosPorBancoId");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("ConsultarMDRPrazosPorBancoId");
            }
        }


        public async Task<RespPadrao> Save(MDRPrazosRequest request)
        {
            try
            {
                var command = Mapper.Map<MDRPrazosSalvarComRetornoCommand>(request);
                var retorno = await Engine.CommandBus.SendCommandAsync<Domain.Models.MDRPrazos.MDRPrazos>(command);

                return new RespPadrao
                {
                    id = retorno.Id,
                    sucesso = true,
                    mensagem = "Registro salvo com sucesso."
                };
            }
            catch (Exception e)
            {
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = "Ocorreu um erro ao salvar o registro. " + e.Message
                };
            }
        }

        public async Task<RespPadrao> AlterarStatus(MDRPrazosStatusRequest request)
        {
            try
            {
                var command = Mapper.Map<MDRPrazosAlterarStatusCommand>(request);
                var retorno = await Engine.CommandBus.SendCommandAsync<Domain.Models.MDRPrazos.MDRPrazos>(command);
                return new RespPadrao(true, $"Registro {(retorno.Ativo == 1 ? "ativado" : "inativado")} com sucesso.");
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return new RespPadrao(false, "Erro ao alterar status do registro. " + e.Message);
            }
        }
    }
}
﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using SistemaInfo.BBC.Domain.Models.Empresa;
using SistemaInfo.Framework.EntityFramework.Configuration;

namespace SistemaInfo.BBC.Infra.Data.Mappings
{
    public class EmpresaMapping : EntityTypeConfiguration<Empresa>
    {
        public override void Map(EntityTypeBuilder<Empresa> builder)
        {
            builder.ToTable("Empresa");
            builder.<PERSON><PERSON>ey(b => b.Id);

            builder.Property(b => b.Id).IsRequired().HasColumnName("Id").ValueGeneratedOnAdd();
            builder.Property(b => b.NomeFantasia).IsRequired().HasColumnName("NomeFantasia").HasColumnType("varchar(200)");
            builder.Property(b => b.Cnpj).IsRequired().HasColumnName("Cnpj").HasColumnType("varchar(14)");
            builder.Property(b => b.Email).HasColumnName("Email").HasColumnType("varchar(200)");
            builder.Property(b => b.Endereco).HasColumnName("Endereco").HasColumnType("varchar(200)");
            builder.Property(b => b.CidadeId).HasColumnName("CidadeId");
            builder.Property(b => b.Cep).HasColumnName("Cep").HasColumnType("varchar(15)");
            builder.Property(b => b.EnderecoNumero).HasColumnType("int").HasColumnName("EnderecoNumber");
            builder.Property(b => b.Telefone).HasColumnName("Telefone").HasColumnType("varchar(15)");
            builder.Property(b => b.UsuarioCadastroId).HasColumnName("UsuarioCadastroId").HasColumnType("int");
            builder.Property(b => b.DataCadastro).IsRequired().HasColumnName("DataCadastro").HasColumnType("date");
            builder.Property(b => b.Ativo).IsRequired().HasColumnName("Ativo").HasColumnType("int");
            builder.Property(b => b.Celular).HasColumnName("Celular").HasColumnType("varchar(15)");
            builder.Property(b => b.Bairro).IsRequired().HasColumnName("Bairro").HasColumnType("varchar(100)");
            builder.Property(b => b.Complemento).HasColumnName("Complemento").HasColumnType("varchar(200)");
            builder.Property(b => b.RazaoSocial).IsRequired().HasColumnName("RazaoSocial").HasColumnType("varchar(200)");
            builder.Property(b => b.UsuarioBloqueioId).HasColumnName("UsuarioBloqueioId").HasColumnType("int");
            builder.Property(b => b.DataBloqueio).HasColumnName("DataBloqueio").HasColumnType("date");
            builder.Property(b => b.UsuarioDesbloqueioId).HasColumnName("UsuarioDesbloqueioId").HasColumnType("int");
            builder.Property(b => b.DataDesbloqueio).HasColumnName("DataDesbloqueio").HasColumnType("date");
            builder.Property(b => b.StatusCadastro).IsRequired().HasColumnName("StatusCadastro").HasColumnType("int");
            builder.Property(b => b.UsuarioValidacaoId).HasColumnName("UsuarioValidacaoId").HasColumnType("int");
            builder.Property(b => b.DataValidacao).HasColumnName("DataValidacao").HasColumnType("date");
            builder.Property(b => b.ParecerInterno).HasColumnName("ParecerInterno").HasColumnType("varchar(500)");
            builder.Property(b => b.ParecerExterno).HasColumnName("ParecerExterno").HasColumnType("varchar(500)");
            builder.Property(b => b.RNTRC).HasColumnName("RNTRC").HasColumnType("varchar(15)");
            builder.Property(b => b.InscricaoEstadual).HasColumnName("InscricaoEstadual").HasColumnType("varchar(20)");
            builder.Property(b => b.DataAberturaEmpresa).HasColumnName("DataAberturaEmpresa").HasColumnType("date");
            builder.Property(b => b.FormaConstituicao).HasColumnName("FormaConstituicao").HasColumnType("varchar(200)");
            builder.Property(b => b.SenhaApi).HasColumnName("SenhaApi").HasColumnType("varchar(200)");
            builder.Property(b => b.LiberaBloqueioSPD).IsRequired().HasColumnName("LiberaBloqueioSPD").HasColumnType("int");
            builder.Property(b => b.CobrancaTarifa).IsRequired().HasColumnName("CobrancaTarifa").HasColumnType("int");
            builder.Property(b => b.TempoAbastecimento).IsRequired().HasColumnName("TempoAbastecimento").HasColumnType("int");
            builder.Property(b => b.ValorTolerancia).IsRequired().HasColumnName("ValorTolerancia").HasColumnType("decimal");
            builder.Property(b => b.ControlaOdometro).IsRequired().HasColumnName("ControlaOdometro").HasColumnType("int");
            builder.Property(b => b.ControlaAutonomia).IsRequired().HasColumnName("ControlaAutonomia").HasColumnType("int");
            builder.Property(b => b.TaxaAbastecimento).HasColumnName("TaxaAbastecimento").HasColumnType("decimal");
            builder.Property(b => b.Cashback).HasColumnName("Cashback").HasColumnType("decimal");
            builder.Property(b => b.RegistraCiot).HasColumnName("RegistraCiot").HasColumnType("int").HasDefaultValue(0);
            builder.Property(b => b.Link).HasColumnName("Link").HasColumnType("varchar(200)");
            builder.Property(b => b.SenhaLink).HasColumnName("SenhaLink").HasColumnType("varchar(200)");
            builder.Property(b => b.LinkSAP).HasColumnName("LinkSAP").HasColumnType("varchar(200)");
            builder.Property(b => b.UsuarioSAP).HasColumnName("UsuarioSAP").HasColumnType("varchar(200)");
            builder.Property(b => b.SenhaSAP).HasColumnName("SenhaSAP").HasColumnType("varchar(200)");
            builder.Property(b => b.TipoEmpresaId).HasColumnName("TipoEmpresaId");
            builder.Property(b => b.ImpostoPIS).HasColumnName("ImpostoPIS").HasColumnType("decimal");;
            builder.Property(b => b.ImpostoCSLL).HasColumnName("ImpostoCSLL").HasColumnType("decimal");;
            builder.Property(b => b.ImpostoIRRF).HasColumnName("ImpostoIRRF").HasColumnType("decimal");;
            builder.Property(b => b.ImpostoCOFINS).HasColumnName("ImpostoCOFINS").HasColumnType("decimal");;
            builder.Property(b => b.PercentualAutonomiaInferior).HasColumnName("PercentualAutonomiaInferior").HasColumnType("decimal");
            builder.Property(b => b.PercentualAutonomiaSuperior).HasColumnName("PercentualAutonomiaSuperior").HasColumnType("decimal");
            builder.Property(b => b.ControlaContingencia).IsRequired().HasColumnName("ControlaContingencia").HasColumnType("int").HasDefaultValue(1);
            builder.Property(b => b.ContaAbastecimento).HasColumnName("ContaAbastecimento").HasColumnType("int");

            builder.Property(b => b.ContaValePedagio).HasColumnName("ContaValePedagio").HasColumnType("int");
            builder.Property(b => b.DataAlteracaoContaAbastecimento).HasColumnName("DataAlteracaoContaAbastecimento").HasColumnType("timestamp");
            builder.Property(b => b.UsuarioAlteracaoContaAbastecimentoId).HasColumnName("UsuarioAlteracaoContaAbastecimentoId").HasColumnType("int");
            builder.Property(b => b.DataAlteracaoContaValePedagio).HasColumnName("DataAlteracaoContaValePedagio").HasColumnType("timestamp");
            builder.Property(b => b.UsuarioAlteracaoContaValePedagioId).HasColumnName("UsuarioAlteracaoContaValePedagioId").HasColumnType("int");

            builder.Property(b => b.ContaFrete).HasColumnName("ContaFrete").HasColumnType("int");
            builder.Property(b => b.DataAlteracaoContaAbastecimento).HasColumnName("DataAlteracaoContaAbastecimento").HasColumnType("timestamp");
            builder.Property(b => b.UsuarioAlteracaoContaAbastecimentoId).HasColumnName("UsuarioAlteracaoContaAbastecimentoId").HasColumnType("int");
            builder.Property(b => b.DataAlteracaoContaFrete).HasColumnName("DataAlteracaoContaFrete").HasColumnType("timestamp");
            builder.Property(b => b.UsuarioAlteracaoContaFreteId).HasColumnName("UsuarioAlteracaoContaFreteId").HasColumnType("int");

            builder.Property(b => b.Prazo).HasColumnName("Prazo").HasColumnType("int");
            builder.Property(b => b.DataAlteracaoModelo).HasColumnName("DataAlteracaoModelo").HasColumnType("timestamp");
            builder.Property(b => b.DebitoPrazo).HasColumnName("DebitoPrazo").HasColumnType("int");
            builder.Property(b => b.DebitoProtocolo).HasColumnName("DebitoProtocolo").HasColumnType("int");
            builder.Property(b => b.ValorTarifaPix).HasColumnName("ValorTarifaPix").HasColumnType("decimal");
            builder.Property(b => b.ValorTarifaBbc).HasColumnName("ValorTarifaBbc").HasColumnType("decimal");
            builder.Property(b => b.QtdMensalSemTaxaPix).HasColumnName("QtdMensalSemTaxaPix").HasColumnType("int");
            builder.Property(b => b.UtilizaTarifaEmpresa).HasColumnName("UtilizaTarifaEmpresa").HasColumnType("int").HasDefaultValue(1);
            builder.Property(b => b.RecebedorAutorizado).HasColumnName("RecebedorAutorizado").HasColumnType("boolean");
            builder.Property(b => b.PorcentagemTarifaServiceValePedagio).HasColumnName("PorcentagemTarifaServiceValePedagio").HasColumnType("decimal");
            builder.Property(b => b.PermitirPagamentoValePedagio).HasColumnName("PermitirPagamentoValePedagio").HasColumnType("int").HasDefaultValue(0);
            builder.Property(b => b.CobrarTarifaBbcValePedagio).HasColumnName("CobrarTarifaBbcValePedagio").HasColumnType("int").HasDefaultValue(0);
            builder.Property(b => b.UtilizaTarifaEmpresaPagamentoPedagio).HasColumnName("UtilizaTarifaEmpresaPagamentoPedagio").HasColumnType("int").HasDefaultValue(1);
            builder.Property(b => b.NotificacaoContingenciaCiot).HasColumnName("NotificacaoContingenciaCiot").HasColumnType("varchar(1500)");
            builder.Property(b => b.StatusReprocessamentoPagamentoFrete).HasColumnName("StatusReprocessamentoPagamentoFrete").HasColumnType("int").HasDefaultValue(0);
            builder.Property(b => b.HabilitaReprocessamentoValePedagio).IsRequired().HasColumnName("HabilitaReprocessamentoValePedagio").HasColumnType("int").HasDefaultValue(1);
            builder.Property(b => b.PermitirEncerramentoPainelCiot).IsRequired().HasColumnName("PermitirEncerramentoPainelCiot").HasColumnType("int").HasDefaultValue(0);
            builder.Property(b => b.UtilizaCiot).IsRequired().HasColumnName("UtilizaCiot").HasColumnType("int").HasDefaultValue(0);
            
            builder.Property(b => b.HabilitaPainelSaldo).HasColumnName("HabilitaPainelSaldo").HasColumnType("int");
            builder.Property(b => b.ValorAdiantamentoBbc).HasColumnName("ValorAdiantamentoBBC").HasColumnType("decimal");
            builder.Property(b => b.ImagemCartao).HasColumnName("ImagemCartao").HasColumnType("bytea");
            
            builder
                .HasOne(e => e.Cidade)
                .WithMany()
                .HasForeignKey(e => e.CidadeId);
            
            builder
                .HasOne(e => e.Usuario)
                .WithMany()
                .HasForeignKey(e => e.UsuarioCadastroId);
            
            builder
                .HasOne(e => e.UsuarioBloqueio)
                .WithMany()
                .HasForeignKey(e => e.UsuarioBloqueioId);
            
            builder
                .HasOne(e => e.UsuarioDesbloqueio)
                .WithMany()
                .HasForeignKey(e => e.UsuarioDesbloqueioId);
            
            builder
                .HasOne(e => e.TipoEmpresa)
                .WithMany()
                .HasForeignKey(e => e.TipoEmpresaId);
            
            builder
                .HasOne(e => e.UsuarioAlteracaoContaAbastecimento)
                .WithMany()
                .HasForeignKey(e => e.UsuarioAlteracaoContaAbastecimentoId);
            
            builder
                .HasMany(e => e.Documentos)
                .WithOne(x => x.Empresa)
                .HasForeignKey(e => e.EmpresaId);
            
            builder
                .HasOne(e => e.GrupoEmpresa)
                .WithMany(c => c.Empresa)
                .HasForeignKey(e => e.GrupoEmpresaId);
        }
    }
}

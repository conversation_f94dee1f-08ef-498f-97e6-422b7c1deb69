using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;
using NLog;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Domain.External.Conductor.DTO.Transferencia;
using SistemaInfo.BBC.Domain.External.Conductor.Interface;
using SistemaInfo.BBC.Domain.Models.Parametros.Repository;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.Utils
{
    /// <summary>
    /// Utilitários compartilhados para métodos idênticos de Viagem
    /// </summary>
    public static class ViagemUtils
    {
        /// <summary>
        /// Retorna os status de conta válidos para operações
        /// </summary>
        public static List<int> StatusConta()
        {
            return new List<int>()
            {
                0,
                200,
                10,
                109
            };
        }

        /// <summary>
        /// Localiza conta AliasBank pelo ID da conta de destino
        /// </summary>
        public static async Task<RespPadrao> LocalizarContaAliasBank(int contaDestinoId, ICartaoRepository cartaoRepository)
        {
            #region Consultar Conta AliasBank
            var lLog = LogManager.GetCurrentClassLogger();
            try
            {
                lLog.Info($"Inicio LocalizarContaAliasBank, Conta Destino: {contaDestinoId}");
                var lContaAliasBankResponse =
                    await cartaoRepository.ConsultarContaAliasBank(contaDestinoId.ToDecimalSafe());
                var lContaAliasBank = lContaAliasBankResponse?.items?.FirstOrDefault();

                #region Valida Contas Informadas com Conta AliasBank

                if (lContaAliasBank != null)
                {
                    return new RespPadrao
                    {
                        data = lContaAliasBank,
                        sucesso = true
                    };
                }

                return new RespPadrao
                {
                    sucesso = false,
                    mensagem = "AliasBank não localizada."
                };

                #endregion
            }
            catch (Exception e)
            {
                lLog.Error(e, "Erro ao se ConsultarContaAliasBank");
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = "Erro ao realizar processo localizar conta BBC. erro: " + e.Message
                };
            }
            finally
            {
                lLog.Info($"Fim LocalizarContaAliasBank");
            }

            #endregion
        }

        /// <summary>
        /// Qualifica pagamento de frete P2P
        /// </summary>
        public static async Task<int> QualificarPagamentoFreteP2P(TransferenciaEntreContaResp objectJson, string receipt, IParametrosReadRepository parametrosReadRepository)
        {
            var lLog = LogManager.GetCurrentClassLogger();
            try
            {
                lLog.Info($"Inicio QualificarPagamentoFreteP2P {objectJson}");

                #region Coleta de parâmetros + montagem de link

                var url = (await parametrosReadRepository.GetByTipoDoParametroAsync(
                    Domain.Models.Parametros.Parametros.TipoDoParametro.LinkApiQualificacaoFrete))?.Valor;

                var authorizationBase64 = (await parametrosReadRepository.GetByTipoDoParametroAsync(
                    Domain.Models.Parametros.Parametros.TipoDoParametro.AutorizacaoLinkApiQualificacaoFrete))?.Valor;

                #endregion

                using var client = new HttpClient();
                using var request = new HttpRequestMessage(HttpMethod.Post, url);

                #region Autenticação

                if (!string.IsNullOrEmpty(authorizationBase64))
                {
                    var decodedAuth = Encoding.UTF8.GetString(Convert.FromBase64String(authorizationBase64));
                    request.Headers.Add("Authorization", decodedAuth);
                }

                #endregion

                #region Requisição

                var jsonString = objectJson.RetornoJson;
                var jsonObj = JsonConvert.DeserializeObject<dynamic>(jsonString);
                string transactionCode = jsonObj?.transactionCode;

                var jsonContent = new { transactionCode, receipt };
                var json = JsonConvert.SerializeObject(jsonContent);
                request.Content = new StringContent(json, Encoding.UTF8, "application/json");

                #endregion

                #region Resposta

                var response = await client.SendAsync(request);

                var responseString = await response.Content.ReadAsStringAsync();
                var statusCode = (int)response.StatusCode;

                if (statusCode != 204)
                {
                    lLog.Error(
                        "Erro na qualificação de frete. " +
                        $"URL: {url} | " +
                        $"Request: {json} | " +
                        $"Response Code: {statusCode} | " +
                        $"Response Body: {responseString}"
                    );
                }

                #endregion

                return statusCode;
            }
            catch (Exception e)
            {
                lLog.Error(e, "Erro ao se comunicar com a api");
                return 500;
            }
            finally
            {
                lLog.Info($"Fim QualificarPagamentoFreteP2P");
            }
        }
        
        public static string RemoveCaracteresEpeciaisCpfCnpjChavePix(this string valor)
        {
            if (string.IsNullOrWhiteSpace(valor))
                return valor;

            var apenasNumeros = new string(valor.Where(char.IsDigit).ToArray());

            var isCpfFormatado = valor.Contains(".") && apenasNumeros.Length == 11;
            var isCnpjFormatado = valor.Contains("/") && apenasNumeros.Length == 14;
            var isCnpjCompletoFormatado = valor.Contains(".") && valor.Contains("/") && apenasNumeros.Length == 14;

            if (isCpfFormatado || isCnpjFormatado || isCnpjCompletoFormatado)
                return apenasNumeros;

            return valor;
        }
    }
}

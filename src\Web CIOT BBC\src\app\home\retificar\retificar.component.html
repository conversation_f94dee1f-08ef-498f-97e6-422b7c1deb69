<div class="container main-container">
    <br>
    <form novalidate (ngSubmit)="retificar()" [formGroup]="meetupForm">
        <div class="form-horizontal">
            <div class="alert alert-danger" *ngIf="errors.length > 0">
                <h3 id="msgRetorno">Opa! Alguma coisa não deu certo:</h3>
                <ul>
                    <li *ngFor="let error of errors">{{ error }}</li>
                </ul>
            </div>
            <div class="row">
                <div class="col-sm-12 col-md-6 col-lg-3">
                    <div class="form-group required" [ngClass]="{'has-error': displayMessage.ciot }">
                        <label class="control-label" for="ciotRetificar">CIOT</label>
                        <input class="form-control" id="ciotRetificar" type="text" formControlName="ciotRetificar" placeholder="Informe o CIOT" />
                        <span class="text-danger" *ngIf="displayMessage.ciotRetificar">
              <p [innerHTML]="displayMessage.ciotRetificar"></p>
            </span>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12 col-md-6 col-lg-3">
                    <div class="form-group required" [ngClass]="{'has-error': displayMessage.senhaRetificar }">
                        <label class="control-label" for="senhaRetificar">Senha</label>
                        <input class="form-control" id="senhaRetificar" type="password" OnlyNumber formControlName="senhaRetificar" placeholder="Informe a senha" />
                        <span class="text-danger" *ngIf="displayMessage.senhaRetificar">
              <p [innerHTML]="displayMessage.senhaRetificar"></p>
            </span>
                    </div>
                </div>
            </div>
            <!-- <div class="g-recaptcha" data-sitekey="6LdTj0cUAAAAAJMT7t5dVnEU4k6chdIc0m-y-PZF"></div> -->
            <br/>
<!--            <re-captcha (resolved)="resolved($event)" siteKey="6LcqvgEVAAAAAEtksgMX9T9-7zHU9rje34hsJ3_5"></re-captcha>-->
            <br/>
            <button class="btn btn-danger" id="retificar" type="submit" [disabled]='!meetupForm.valid' style="background-color: #00622c; border-color: #00622c;">Retificar</button>
        </div>
    </form>
</div>
using System;
using System.Globalization;
using System.IO;
using System.Net.Mail;
using SistemaInfo.BBC.Application.Objects.Web.ManutencaoAbastecimento;
using SistemaInfo.BBC.Domain.Components.Email;

namespace SistemaInfo.BBC.Application.Email.Abastecimento
{
    public class EmailComprovanteAbastecimento
    {
        public static void EnviarEmailComprovanteAbastecimento(INotificationEmailExecutor notificationEmailExecutor, string destinatario,ComprovanteAbastecimentoResponse data)
        {
            var caminhoAplicacao = Environment.CurrentDirectory;

            using (var streamReader = new StreamReader(caminhoAplicacao + @"\Content\Email\Abastecimento\comprovante-abastecimento.html"))
            {
                var html = streamReader.ReadToEnd();
                html = html.Replace("{Funcionario}", data.Funcionario);
                html = html.Replace("{Litragem}", data.Litragem);
                html = html.Replace("{Placa}", data.Placa);
                html = html.Replace("{ValorAbastecimento}", data.Valor);
                html = html.Replace("{PostoCnpj}", data.CnpjPosto);
                html = html.Replace("{CodAutorizacao}", data.CodAutorizacao);
                html = html.Replace("{CombustivelNome}", data.CombustivelNome);
                html = html.Replace("{PostoNome}", data.NomePosto);
                html = html.Replace("{Data}", data.DataCadastro.ToString(CultureInfo.CurrentCulture));

                var view = AlternateView.CreateAlternateViewFromString(html, null, "text/html");
                
                notificationEmailExecutor.ExecuteAsync(new Domain.Components.Email.Email
                {
                    //From = new Domain.Components.Email.Email.EmailAddress {Address = "<EMAIL>", DisplayName = "Minhazarma"},
                    To = new[] {new Domain.Components.Email.Email.EmailAddress {Address = destinatario}},
                    Priority = MailPriority.High,
                    Subject = "Comprovante de Abastecimento",
                    IsBodyHtml = true,
                    AlternateView = view
                });
            }
        }
    }
}
export class CiotResponse {
    ciot: string;
    codigoVerificador: string;
    situacao: number;
    enviadoEmContingencia: boolean;
    erroContingencia?: any;
    nomeProprietario: string;
    cpfCnpjProprietario: string;
    rntrcProprietario: string;
    dataInicioFrete: Date;
    dataTerminoFrete: Date;
    valorFrete: number;
    valorEfetivoFrete: number;
    valorDespesas: number;
    totalImposto: number;
    totalPegadio: number;
    tipoViagem: number;
    encerrado: boolean;
    sucesso: boolean;
    excecao?: {
        tipo: number;
        codigo: string;
        mensagem: string;
    };
    
}


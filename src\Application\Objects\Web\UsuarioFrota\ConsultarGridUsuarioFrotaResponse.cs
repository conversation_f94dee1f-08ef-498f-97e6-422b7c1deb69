using System;
using System.Collections.Generic;

namespace SistemaInfo.BBC.Application.Objects.Web.UsuarioFrota
{
    public class ConsultarGridUsuarioFrota
    {
        public int id { get; set; }
        public int ativo { get; set; }
        public int PertenceEmpresaFrota { get; set; }
        public string nome { get; set; }
        public string cpfCnpj { get; set; }
        public string telefone { get; set; }
        public string celular { get; set; }
        public string email { get; set; }
        public string tipoPessoa { get; set; }
        public string atividade { get; set; }
        public int? empresaIdFrota { get; set; }
        public int? PertecenEmpresaFrota { get; set; }
        public string Status { get; set; }
        public string MotivoCancelamento { get; set; }
        public int? UsuarioCancelamentoId { get; set; }
        public String DataUltimoAcesso { get; set; }
        public string UsuarioCadastro { get; set; }
        public string DataCadastro { get; set; }
        public string CNH { get; set; }
        
        // public DateTime? DataEmissaoCNH { get; set; }
        // public DateTime? DataVencimentoCNH { get; set; }
        
        public string UsuarioBloqueio { get; set; }
        public String DataBloqueio { get; set; }
        public String DataBloqueioMobile { get; set; }
        
        public string UsuarioDesbloqueio { get; set; }
        public String DataDesbloqueio { get; set; }
        public String DataDesbloqueioMobile { get; set; }
        
        public String DataCancelamento { get; set; }
        public string UsuarioCancelamento { get; set; }
        public string Empresa { get; set; }
        public int QuantidadeErroSenha { get; set; }
        public string QuantidadeErroSenhaVsParametro { get; set; }
        
        public int SenhaProvisoria { get; set; }
        public string Placa { get; set; }
    }
    
    public class ConsultarGridUsuarioFrotaReduzido
    {
        public int Id { get; set; }
        public string Nome { get; set; }
        public string CpfCnpj { get; set; }
    }
    
    public class ConsultarGridUsuarioFrotaResponse
    {
        public int totalItems { get; set; }
        public List<ConsultarGridUsuarioFrota> items { get; set; }
    }
    
    public class ConsultarGridUsuarioFrotaReduzidoResponse
    {
        public int totalItems { get; set; }
        public List<ConsultarGridUsuarioFrotaReduzido> items { get; set; }
    }
}
﻿using Microsoft.EntityFrameworkCore.Migrations;
using System;
using System.Collections.Generic;

namespace SistemaInfo.BBC.Infra.Data.Migrations
{
    public partial class REMOVE_NULLABLE_VERSAOINTEGRACAOVIAGEM_VIAGEM : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            
            // Atualiza todos os registros NULL para 1 antes de aplicar NOT NULL
            migrationBuilder.Sql(@"
                UPDATE ""BBC"".""Viagem""
                SET ""VersaoIntegracaoViagem"" = 1
                WHERE ""VersaoIntegracaoViagem"" IS NULL;
            ");
            
            migrationBuilder.AlterColumn<int>(
                name: "VersaoIntegracaoViagem",
                schema: "BBC",
                table: "Viagem",
                type: "int",
                nullable: false,
                defaultValue: 1,
                oldClrType: typeof(int),
                oldType: "int",
                oldNullable: true,
                oldDefaultValue: 1);
            
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            
            migrationBuilder.AlterColumn<int>(
                name: "VersaoIntegracaoViagem",
                schema: "BBC",
                table: "Viagem",
                type: "int",
                nullable: true,
                defaultValue: 1,
                oldClrType: typeof(int),
                oldType: "int",
                oldDefaultValue: 1);
            
        }
    }
}

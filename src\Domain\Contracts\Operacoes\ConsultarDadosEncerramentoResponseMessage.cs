using System;
using System.Collections.Generic;
using SistemaInfo.BBC.Domain.External.CIOT.DTO;

namespace SistemaInfo.BBC.Domain.Contracts.Operacoes
{
    
    public class ConsultarDadosEncerramentoReqMessage
    {
        public string Ciot { get; set; }
        public string SenhaAlteracao { get; set; }
    }
    
    public class ConsultarDadosEncerramentoRespMessage
    {
        public ConsultarDadosEncerramentoRespMessage(bool sucesso, string excecao)
        {
            Sucesso = sucesso;
            Erro = new Excecao()
            {
                Mensagem = excecao
            };
        }
        public ConsultarDadosEncerramentoRespMessage() { }
        public bool Sucesso { get; set; }
        public Excecao Erro { get; set; }
        public string CIOT { get; set; }
        public string CodigoNaturezaCarga { get; set; }
        public DateTime? DataInicioViagem { get; set; }
        public DateTime DataFimViagem { get; set; }
        public decimal? PesoCarga { get; set; }
        public string SenhaAlteracao { get; set; }
        public string TipoViagem { get; set; }
        public int? QuantidadeTarifas { get; set; }
        public decimal? ValorTarifas { get; set; }
    }
}
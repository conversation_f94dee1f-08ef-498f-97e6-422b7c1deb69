using System;
using Newtonsoft.Json;
using NLog;

namespace SistemaInfo.BBC.Application.Helpers
{
    /// <summary>
    /// Classe utilitária para logging
    /// </summary>
    public class LogHelper
    {
        private readonly Logger _logger;

        public LogHelper(Logger logger = null)
        {
            _logger = logger ?? GetLoggerFromCaller();
        }

        public void LogOperationStart(string operationName, string details = null)
        {
            var message = $"--> Início da operação: {operationName} <--";
            if (!string.IsNullOrEmpty(details))
                message += $" Detalhes: {details}";

            _logger.Info($"{_logger.Name} --> {message}");
        }

        public void LogOperationEnd(string operationName, string details = null)
        {
            var message = $" ----------------- Fim da operação: {operationName} -----------------";
            if (!string.IsNullOrEmpty(details))
                message += $" Detalhes: {details}";

            _logger.Info($"{_logger.Name} --> {message}");
        }

        public void Info(string message)
        {
            _logger.Info($"{_logger.Name} --> {message}");
        }

        public void Info<T>(string message, T obj)
        {
            _logger.Info($"{_logger.Name} --> {message} {JsonConvert.SerializeObject(obj)}");
        }

        public void Error(string message)
        {
            _logger.Error($"{_logger.Name} --> {message}");
        }

        public void Error(Exception ex, string message = null)
        {
            if (string.IsNullOrEmpty(message))
                _logger.Error(ex);
            else
                _logger.Error(ex, $"{_logger.Name} --> {message}");
        }

        public void Error<T>(Exception ex, string message, T obj)
        {
            _logger.Error(ex,
                string.IsNullOrEmpty(message)
                    ? $"{JsonConvert.SerializeObject(obj)}"
                    : $"{_logger.Name} --> {message} --> {JsonConvert.SerializeObject(obj)}");
        }

        public void Error<T>(string message, T obj)
        {
            _logger.Error($"{_logger.Name} --> {message} --> {JsonConvert.SerializeObject(obj)}");
        }

        private static Logger GetLoggerFromCaller()
        {
            var stackTrace = new System.Diagnostics.StackTrace();

            for (int i = 1; i < stackTrace.FrameCount; i++)
            {
                var method = stackTrace.GetFrame(i).GetMethod();
                var declaringType = method.DeclaringType;

                if (declaringType != null && declaringType != typeof(LogHelper))
                {
                    return LogManager.GetLogger(declaringType.FullName);
                }
            }

            return LogManager.GetLogger("Desconhecido");
        }
    }
}

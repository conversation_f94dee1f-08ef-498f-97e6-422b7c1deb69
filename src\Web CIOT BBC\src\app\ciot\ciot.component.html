<div class="container main-container">
    <div class="container">
        <div class="panel-body">
            <fieldset class="scheduler-border">
                <legend class="scheduler-border">Declarar operação de transporte</legend>
                <div>
                    <tabset #staticTabs>
                        <tab heading="Frete" style="padding-left: 25px" id="tab1">
                            <tabset>
                                <tab heading="Viagem">
                                    <app-viagem #viagemComponent style="padding-left: 50px"></app-viagem>
                                </tab>
                                <tab heading="Pagamento">
                                    <app-pagamento #pagamentoComponent></app-pagamento>
                                </tab>
                                <tab heading="Transportador/Veículos">
                                    <app-transportador-veiculos #transportadorVeiculosComponent
                                        [CpfCnpjTransportador]="CpfCnpjTransportador"
                                        [RNTRCTransportador]="RNTRCTransportador"
                                        [NomeRazaoSocialTransportador]="NomeRazaoSocialTransportador"></app-transportador-veiculos>
                                </tab>
                                <tab heading="Motorista">
                                    <app-motorista #motoristaComponent></app-motorista>
                                </tab>
                            </tabset>
                        </tab>
                        <tab heading="Contratante">
                            <app-contratante #contratanteComponent
                                [CpfCnpjInteressado]="CpfCnpjInteressado"></app-contratante>
                        </tab>
                        <tab heading="Remetente">
                            <app-remetente #remetenteComponent [contratanteComponent]="contratanteComponent"
                                [destinatarioComponent]="destinatarioComponent"
                                [consignatarioComponent]="consignatarioComponent"
                                [transportadorVeiculosComponent]="transportadorVeiculosComponent"></app-remetente>
                        </tab>
                        <tab heading="Destinatário">
                            <app-destinatario #destinatarioComponent [contratanteComponent]="contratanteComponent"
                                [remetenteComponent]="remetenteComponent"
                                [consignatarioComponent]="consignatarioComponent"
                                [transportadorVeiculosComponent]="transportadorVeiculosComponent"></app-destinatario>
                        </tab>
                        <tab heading="Consignatário (Opcional)">
                            <app-consignatario #consignatarioComponent [contratanteComponent]="contratanteComponent"
                                [remetenteComponent]="remetenteComponent"
                                [destinatarioComponent]="destinatarioComponent"
                                [transportadorVeiculosComponent]="transportadorVeiculosComponent"></app-consignatario>
                        </tab>
                        <app-salvar-declaracao [contratanteComponent]="contratanteComponent"
                            [remetenteComponent]="remetenteComponent" [destinatarioComponent]="destinatarioComponent"
                            [transportadorVeiculosComponent]="transportadorVeiculosComponent"
                            [consignatarioComponent]="consignatarioComponent" [viagemComponent]="viagemComponent"
                            [pagamentoComponent]="pagamentoComponent" [motoristaComponent]="motoristaComponent">
                        </app-salvar-declaracao>
                    </tabset>
                </div>
            </fieldset>
        </div>
    </div>
</div>
using System;
using BBC.Test.Tests.EnumHelper.Fixture;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Domain.Helper;
using Xunit;

namespace BBC.Test.Tests.EnumHelper
{
    [Collection(nameof(EnumHelperCollection))]
    public class EnumHelperTest
    {
        private readonly EnumHelperFixture _fixture;

        public EnumHelperTest(EnumHelperFixture fixture)
        {
            _fixture = fixture;
        }

        [Fact(DisplayName = "Obter enum por descrição - deve retornar enum correto quando descrição existe")]
        [Trait("EnumHelper", "GetFromDescription")]
        public void ObterEnumPorDescricao_DeveRetornarEnumCorreto_QuandoDescricaoExiste()
        {
            // Arrange
            var descricao = "Masculino";

            // Act
            var resultado = SistemaInfo.BBC.Domain.Helper.EnumHelper.GetFromDescription<ESexo>(descricao);

            // Assert
            Assert.Equal(ESexo.Masculino, resultado);
        }

        [Fact(DisplayName = "Obter enum por descrição - deve retornar padrão quando descrição não existe")]
        [Trait("EnumHelper", "GetFromDescription")]
        public void ObterEnumPorDescricao_DeveRetornarPadrao_QuandoDescricaoNaoExiste()
        {
            // Arrange
            var descricaoInvalida = "DescricaoInexistente";

            // Act
            var resultado =  SistemaInfo.BBC.Domain.Helper.EnumHelper.GetFromDescription<ESexo>(descricaoInvalida);

            // Assert
            Assert.Equal(default(ESexo), resultado);
        }

        [Fact(DisplayName = "Obter enum por descrição - deve retornar enum por nome quando não tem atributo descrição")]
        [Trait("EnumHelper", "GetFromDescription")]
        public void ObterEnumPorDescricao_DeveRetornarEnumPorNome_QuandoNaoTemAtributoDescricao()
        {
            // Arrange
            var nomeEnum = "Ativo";

            // Act
            var resultado =  SistemaInfo.BBC.Domain.Helper.EnumHelper.GetFromDescription<StatusVeiculo>(nomeEnum);

            // Assert
            Assert.Equal(StatusVeiculo.Ativo, resultado);
        }

        [Fact(DisplayName = "Obter descrição enum - deve retornar descrição quando tem atributo descrição")]
        [Trait("EnumHelper", "GetEnumDescription")]
        public void ObterDescricaoEnum_DeveRetornarDescricao_QuandoTemAtributoDescricao()
        {
            // Arrange
            var enumValue = ESexo.Feminino;

            // Act
            var resultado = SistemaInfo.BBC.Domain.Helper.EnumHelper.GetEnumDescription(enumValue);

            // Assert
            Assert.Equal("Feminino", resultado);
        }

        [Fact(DisplayName = "Obter descrição enum - deve retornar nome enum quando não tem atributo descrição")]
        [Trait("EnumHelper", "GetEnumDescription")]
        public void ObterDescricaoEnum_DeveRetornarNomeEnum_QuandoNaoTemAtributoDescricao()
        {
            // Arrange
            var enumValue = StatusVeiculo.Ativo;

            // Act
            var resultado = SistemaInfo.BBC.Domain.Helper.EnumHelper.GetEnumDescription(enumValue);

            // Assert
            Assert.Equal("Ativo", resultado);
        }

        [Theory(DisplayName = "Obter sigla - deve retornar sigla correta para diferentes tipos")]
        [Trait("EnumHelper", "GetSigla")]
        [InlineData("Adiantamento", "A")]
        [InlineData("Saldo", "S")]
        [InlineData("Complemento", "R")]
        [InlineData("Avulso", "V")]
        [InlineData("TarifaANTT", "T")]
        [InlineData("Cancelamento", "C")]
        [InlineData("Tarifas", "F")]
        public void ObterSigla_DeveRetornarSiglaCorreta_ParaDiferentesTipos(string tipoNome, string siglaEsperada)
        {
            // Arrange
            var tipo = System.Enum.Parse<Tipo>(tipoNome);

            // Act
            var resultado = SistemaInfo.BBC.Domain.Helper.EnumHelper.GetSigla(tipo);

            // Assert
            Assert.Equal(siglaEsperada, resultado);
        }

        [Fact(DisplayName = "Obter sigla - deve lançar exceção para tipo inválido")]
        [Trait("EnumHelper", "GetSigla")]
        public void ObterSigla_DeveLancarExcecao_ParaTipoInvalido()
        {
            // Arrange
            Tipo? tipoInvalido = (Tipo)999;

            // Act & Assert
            Assert.Throws<ArgumentOutOfRangeException>(() => tipoInvalido.GetSigla());
        }

        [Fact(DisplayName = "Obter sigla - deve lançar exceção para tipo nulo")]
        [Trait("EnumHelper", "GetSigla")]
        public void ObterSigla_DeveLancarExcecao_ParaTipoNulo()
        {
            // Arrange
            Tipo? tipoNull = null;

            // Act & Assert
            Assert.Throws<ArgumentOutOfRangeException>(() => tipoNull.GetSigla());
        }
    }
}

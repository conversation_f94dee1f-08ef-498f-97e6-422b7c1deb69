using System.Runtime.Serialization;

namespace SistemaInfo.BBC.Application.Objects.Base
{
    [DataContract]
    public class RespPadrao
    {
        [DataMember(Order = 0)]
        public bool sucesso { get; set; } = false;

        [DataMember(Order = 1)]
        public string mensagem { get; set; }
        
        [DataMember(Order = 2)]
        public object data { get; set; }
        
        [DataMember(Order = 3, EmitDefaultValue = false)]
        public int? id { get; set; }

        public RespPadrao()
        {
        }

        public RespPadrao(bool aSucesso, string aMensagem = null)
        {
            sucesso = aSucesso;
            mensagem = aMensagem?.Length > 250 ? aMensagem.Substring(0, 250) : aMensagem;
        }    
        
        public RespPadrao(bool aSucesso, string aMensagem, object aData = null)
        {
            sucesso = aSucesso;
            mensagem = aMensagem?.Length > 250 ? aMensagem.Substring(0, 250) : aMensagem;
            data = aData;
        }
        
        public static RespPadrao <PERSON>rro(string aMensagem = null)
        {
            return new RespPadrao
            {
                sucesso = false,
                mensagem = aMensagem?.Length > 250 ? aMensagem.Substring(0, 250) : aMensagem
            };
        }  
        public static RespPadrao Sucesso(string aMensagem = null, object aData = null)
        {
            return new RespPadrao
            {
                sucesso = true,
                mensagem = aMensagem?.Length > 250 ? aMensagem.Substring(0, 250) : aMensagem,
                data = aData
            };
        }  
    }
}
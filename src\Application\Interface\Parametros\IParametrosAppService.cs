using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using SistemaInfo.BBC.Application.Objects.Api.Parametros;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.ConfiguracaoAtualizacaoAutomaticaPrecoCombustivel;
using SistemaInfo.BBC.Application.Objects.Web.ConfiguracaoMonitoramentoCiot;
using SistemaInfo.BBC.Application.Objects.Web.ConfiguracaoQualificacaoTransacao;
using SistemaInfo.BBC.Application.Objects.Web.ConfiguracaoTelaoSaldo;
using SistemaInfo.BBC.Application.Objects.Web.ConfiguracaoTokenMobile;
using SistemaInfo.BBC.Application.Objects.Web.ConfiguracaoValePedagio;
using SistemaInfo.BBC.Application.Objects.Web.Parametros;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Models.ParametroConfiguracaoSla;
using SistemaInfo.BBC.Domain.Models.Parametros.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;

namespace SistemaInfo.BBC.Application.Interface.Parametros
{
    public interface IParametrosAppService : IAppService<Domain.Models.Parametros.Parametros, 
        IParametrosReadRepository, IParametrosWriteRepository>
    {
       Task<RespPadrao> Cadastrar(ParametrosCadastrarRequest parametrosCadastrarRequest);
       Task<RespPadrao> SalvarConfiguracaoSla(ParametroConfiguracaoSlaRequest parametroConfiguracaoSla);
       
       ConsultarGridParametroResponse ConsultarGridParametro(int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
       Task<Domain.Models.Parametros.Parametros> GetParametrosAsync(int id,
           Domain.Models.Parametros.Parametros.TipoDoParametro tipoDoParametro,
           Domain.Models.Parametros.Parametros.TipoDoValor tipoDoValor);

       Task<int> GetTempoInatividadeUsuario();
       Task<List<Domain.Models.Parametros.Parametros>> GetParametrosListAsync(int id,
           Domain.Models.Parametros.Parametros.TipoDoParametro tipoDoParametro,
           Domain.Models.Parametros.Parametros.TipoDoValor tipoDoValor);

       Domain.Models.Parametros.Parametros SaveParametro(int id,
           Domain.Models.Parametros.Parametros.TipoDoParametro tipoDoParametro, string valor, string valorCriptografado,
           Domain.Models.Parametros.Parametros.TipoDoValor tipoValor);

       Task<TipoFarol> FarolSlaValidar(DateTime dataCadastroPosto);
       
       Task<ParametroConfiguracaoSla> ConsultarParametroConfiguracaoSla();
       
       Task<RespPadrao> ConfiguracaoAtualizacaoAutomaticaPrecoCombustivel();
       Task<RespPadrao> SalvarConfiguracaoAtualizacaoAutomaticaPrecoCombustivel(ConfiguracaoAtualizacaoAutomaticaPrecoCombustivelRequest request);
       Task<int> GetMaxTentativasReenvioPedagio();
       Task<int> GetMaxTentativasReenvioFrete();
       Task<int> GetContaRetencaoAr();
       Task<RespPadrao> ConsultaParametrosConfiguracaoMonitoramentoCiot();
       Task<RespPadrao> ConsultaParametrosConfiguracaoValePedagio();
       Task<RespPadrao> SalvarConfiguracaoMonitoramentoCiot(ConfiguracaoMonitoramentoCiotRequest request);
       Task<RespPadrao> SalvarConfiguracaoValePedagio(ConfiguracaoValePedagioRequest request);
       Task<RespPadrao> ConsultaParametrosConfiguracaoTelaoSaldo();
       Task<RespPadrao> SalvarConfiguracaoTelaoSaldo(ConfiguracaoTelaoSaldoRequest request);
       Task<RespPadrao> SincronizaParametrosComMicroServico();
       Task<RespPadrao> ConsultaParametrosConfiguracaoQualificacaoTransacao();
       Task<RespPadrao> SalvarConfiguracaoQualificacaoTransacao(ConfiguracaoQualificacaoTransacaoRequest request);
    }
}    
using Bogus;
using Bogus.Extensions.Brazil;
using Moq;
using SistemaInfo.BBC.Application.Objects.Web.ClientSecretAdm;
using SistemaInfo.BBC.Domain.Models.GrupoUsuario;
using Xunit;

namespace BBC.Test.Tests.ClientSecretAdm.Fixture
{
    [CollectionDefinition(nameof(ClientSecretAdmCollection))]
    public class ClientSecretAdmCollection : ICollectionFixture<ClientSecretAdmFixture>
    {
        
    }

    public class ClientSecretAdmFixture : MockEngine
    {

        public SistemaInfo.BBC.Domain.Models.ClientSecretAdm.ClientSecretAdm GerarClientSecretAdm(bool idInvalido = false)
        {
            var lClientSecretAdm = new Faker<SistemaInfo.BBC.Domain.Models.ClientSecretAdm.ClientSecretAdm>("pt_BR")
                .CustomInstantiator(f => new SistemaInfo.BBC.Domain.Models.ClientSecretAdm.ClientSecretAdm
                {
                    Id = idInvalido ? f.Random.Int(int.MinValue, 0) : f.Random.Int(1, 99999),
                    Login = f.Internet.UserName(),
                    ClientSecret = f.Random.Guid().ToString(),
                    Descricao = f.Lorem.Sentence(),
                    DataCadastro = f.Date.Past(),
                    DataAlteracao = f.Date.Recent(),
                    UsuarioCadastroId = f.Random.Number(1, 100),
                    UsuarioAlteracaoId = f.Random.Number(1, 100),
                    Senha = f.Internet.Password(),
                    Ativo = f.Random.Number(0, 1)
                });
            return lClientSecretAdm;
        }

        public ClientSecretAdmRequest GerarClientSecretAdmRequest(bool novoRegistro = false)
        {
            return new Faker<ClientSecretAdmRequest>("pt_BR")
                .CustomInstantiator(f => new ClientSecretAdmRequest
                {
                    Id = novoRegistro ? 0 : f.Random.Int(1, 99999),
                    Login = f.Internet.UserName(),
                    ClientSecret = f.Random.Guid().ToString(),
                    Senha = f.Internet.Password(),
                    Descricao = f.Lorem.Sentence()
                });
        }
        
        public SistemaInfo.BBC.Domain.Models.Usuario.Usuario GerarUsuario()
        {
            var lUsuarioFaker = new Faker<SistemaInfo.BBC.Domain.Models.Usuario.Usuario>("pt_BR")
                .CustomInstantiator(f => new SistemaInfo.BBC.Domain.Models.Usuario.Usuario
                {
                    Id = f.Random.Int(1),
                    Nome = f.Person.FullName,
                    Email = f.Internet.Email(),
                    Cpf = f.Person.Cpf(),
                    Telefone = f.Phone.PhoneNumber(),
                    Celular = f.Phone.PhoneNumber(),
                    Login = "TesteUnit" + f.Person.LastName,
                    EmpresaId = f.Random.Int(1),
                    GrupoEmpresaId = f.Random.Int(1),
                }).RuleFor(e => e.Email, (f, e) => f.Internet.Email(e.Nome));
            
            return lUsuarioFaker;
        }
    }
}

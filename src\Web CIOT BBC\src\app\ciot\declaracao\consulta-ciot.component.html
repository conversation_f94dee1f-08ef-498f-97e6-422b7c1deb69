<div class="container main-container">
    <div>
        <div class="container main-container">
            <div class="panel panel-default">
                <div class="panel-body">
                    <fieldset class="scheduler-border">
                        <legend class="scheduler-border">Consulta de CIOT</legend>
                        <div class="alert alert-warning alert" style="width: 95%;" role="alert">
                            <strong>Atenção!</strong> Guarde a senha e o número do CIOT, eles serão necessários em futuras retificações, consultas ou cancelamentos.
                        </div>
                        <form novalidate [formGroup]="consultaCiotForm">
                            <div class="form-horizontal">
                                <div class="row">
                                    <div class="col-sm-12 col-md-6 col-lg-3">
                                        <div class="form-group " [ngClass]="{'has-error': displayMessage.ciot }">
                                            <label class="control-label" for="ciot">CIOT:</label>
                                            <input type="text" class="form-control" id="ciot" formControlName="ciot" disabled style="font-weight: bold;" />
                                            <span class="text-danger" *ngIf="displayMessage.ciot">
                                                <p [innerHTML]="displayMessage.ciot"></p>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="col-sm-12 col-md-6 col-lg-3">
                                        <div class="form-group " [ngClass]="{'has-error': displayMessage.senha }">
                                            <label class="control-label" for="senha">Senha</label>
                                            <input type="text" class="form-control" id="senha" formControlName="senha" OnlyNumber disabled style="font-weight: bold;" />
                                            <span class="text-danger" *ngIf="displayMessage.senha">
                                                <p [innerHTML]="displayMessage.senha"></p>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-sm-12 col-md-6 col-lg-3">
                                        <div class="form-group " [ngClass]="{'has-error': displayMessage.contratado }">
                                            <label class="control-label" for="contratado">Contratado</label>
                                            <input type="text" class="form-control" id="contratado" formControlName="contratado" disabled />
                                            <span class="text-danger" *ngIf="displayMessage.contratado">
                                                <p [innerHTML]="displayMessage.contratado"></p>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="col-sm-12 col-md-6 col-lg-3">
                                        <div class="form-group " [ngClass]="{'has-error': displayMessage.rntrc }">
                                            <label class="control-label" for="rntrc">RNTRC</label>
                                            <input type="text" RntrcMask class="form-control" id="rntrc" formControlName="rntrc" disabled />
                                            <span class="text-danger" *ngIf="displayMessage.rntrc">
                                                <p [innerHTML]="displayMessage.rntrc"></p>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-sm-12 col-md-6 col-lg-3">
                                        <div class="form-group " [ngClass]="{'has-error': displayMessage.dtInicioFrete }">
                                            <label class="control-label" for="dtInicioFrete">Início do frete</label>
                                            <input type="text" class="form-control" id="dtInicioFrete" formControlName="dtInicioFrete" disabled />
                                            <span class="text-danger" *ngIf="displayMessage.dtInicioFrete">
                                                <p [innerHTML]="displayMessage.dtInicioFrete"></p>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="col-sm-12 col-md-6 col-lg-3">
                                        <div class="form-group " [ngClass]="{'has-error': displayMessage.dtTerminoFrete }">
                                            <label class="control-label" for="dtTerminoFrete">Final do frete</label>
                                            <input type="text" class="form-control" id="dtTerminoFrete" formControlName="dtTerminoFrete" disabled />
                                            <span class="text-danger" *ngIf="displayMessage.dtTerminoFrete">
                                                <p [innerHTML]="displayMessage.dtTerminoFrete"></p>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-sm-12 col-md-6 col-lg-3">
                                        <div class="form-group " [ngClass]="{'has-error': displayMessage.valorFrete }">
                                            <label class="control-label" for="valorFrete">Valor do frete</label>
                                            <div class="input-group mb-2">
                                                <div class="input-group-prepend">
                                                    <div class="input-group-text">R$</div>
                                                </div>
                                                <input currencyMask [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }" maxlength="20" type="text" class="form-control" id="valorFrete" formControlName="valorFrete" disabled />
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-12 col-md-6 col-lg-3">
                                        <div class="form-group " [ngClass]="{'has-error': displayMessage.totalImposto }">
                                            <label class="control-label" for="totalImposto">Total impostos</label>
                                            <div class="input-group mb-2">
                                                <div class="input-group-prepend">
                                                    <div class="input-group-text">R$</div>
                                                </div>
                                                <input currencyMask [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }" maxlength="20" type="text" class="form-control" id="totalImposto" formControlName="totalImposto" disabled />
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-12 col-md-6 col-lg-3">
                                        <label class="control-label" for="totalPedagio">Total do pedágio</label>
                                        <div class="input-group mb-2">
                                            <div class="input-group-prepend">
                                                <div class="input-group-text">R$</div>
                                            </div>
                                            <input type="text" class="form-control" id="totalPedagio" formControlName="totalPedagio" disabled />
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-sm-12 col-md-6 col-lg-3">
                                        <div class="form-group " [ngClass]="{'has-error': displayMessage.tipoViagem }">
                                            <label class="control-label" for="tipoViagem">Tipo de viagem</label>
                                            <input type="text" class="form-control" id="tipoViagem" formControlName="tipoViagem" disabled />
                                            <span class="text-danger" *ngIf="displayMessage.tipoViagem">
                                                <p [innerHTML]="displayMessage.tipoViagem"></p>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="col-sm-12 col-md-6 col-lg-3">
                                        <div class="form-group ">
                                            <label class="control-label" for="situacaoDeclaracao">Situação</label>
                                            <input type="text" class="form-control" id="situacaoDeclaracao" formControlName="situacaoDeclaracao" disabled />
                                        </div>
                                    </div>
                                </div>

                                <!-- <div class="row">
                                    <div class="col-sm-12 col-md-6 col-lg-3">
                                        <div class="form-group " [ngClass]="{'has-error': displayMessage.aviso }">
                                            <label class="control-label" for="aviso">Aviso</label>
                                            <textarea class="form-control" id="aviso" formControlName="aviso" style="margin: 0px -281.422px 0px 0px; width: 541px; height: 116px;" disabled></textarea>
                                            <span class="text-danger" *ngIf="displayMessage.aviso">
                                                <p [innerHTML]="displayMessage.aviso"></p>
                                            </span>
                                        </div>
                                    </div>
                                </div> -->
                                <br>
                                <button class="btn btn-danger" id="voltar" (click)="voltar()">Concluir</button>
                                <button class="btn btn-primary" id="imprimir" (click)="imprimir()" [disabled]="!botaoImprimirHabilitado">Imprimir</button>
                            </div>
                        </form>
                    </fieldset>
                </div>
            </div>
        </div>
    </div>
</div>
using System;
using System.IO;
using System.Xml;
using Xunit;

namespace SistemaInfo.BBC.Tests.Logging
{
    public class NLogConfigurationTests
    {
        [Fact]
        public void NLogConfig_ShouldExistInWebProject()
        {
            // Arrange
            string configPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "..", "..", "..", "..", "Web", "NLog.config");
            
            // Act
            bool configExists = File.Exists(configPath);
            
            // Assert
            Assert.True(configExists, "O arquivo NLog.config deve existir no projeto Web");
        }
        
        [Fact]
        public void NLogConfig_ShouldExistInApiCiotProject()
        {
            // Arrange
            string configPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "..", "..", "..", "..", "ApiCiot", "NLog.config");
            
            // Act
            bool configExists = File.Exists(configPath);
            
            // Assert
            Assert.True(configExists, "O arquivo NLog.config deve existir no projeto ApiCiot");
        }
        
        [Fact]
        public void NLogConfig_ShouldHaveFileTarget()
        {
            // Arrange
            string configPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "..", "..", "..", "..", "Web", "NLog.config");
            
            // Act
            if (!File.Exists(configPath))
            {
                // Se o arquivo não existir, o teste é inconclusivo
                Assert.True(false, "O arquivo NLog.config não existe");
                return;
            }
            
            XmlDocument doc = new XmlDocument();
            doc.Load(configPath);
            
            // Assert
            XmlNodeList targets = doc.SelectNodes("//target[@xsi:type='File']", GetNamespaceManager(doc));
            Assert.NotEmpty(targets);
        }
        
        [Fact]
        public void NLogConfig_ShouldHaveApplicationEventsTarget()
        {
            // Arrange
            string configPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "..", "..", "..", "..", "Web", "NLog.config");
            
            // Act
            if (!File.Exists(configPath))
            {
                // Se o arquivo não existir, o teste é inconclusivo
                Assert.True(false, "O arquivo NLog.config não existe");
                return;
            }
            
            XmlDocument doc = new XmlDocument();
            doc.Load(configPath);
            
            // Assert
            XmlNodeList targets = doc.SelectNodes("//target[@name='ApplicationEvents']", GetNamespaceManager(doc));
            Assert.NotEmpty(targets);
        }
        
        private XmlNamespaceManager GetNamespaceManager(XmlDocument doc)
        {
            XmlNamespaceManager nsManager = new XmlNamespaceManager(doc.NameTable);
            nsManager.AddNamespace("xsi", "http://www.w3.org/2001/XMLSchema-instance");
            return nsManager;
        }
    }
}

using System;
using System.Threading.Tasks;
using NLog;
using SistemaInfo.BBC.Application.Helpers;
using SistemaInfo.BBC.Application.Interface.Pedagio;
using SistemaInfo.BBC.Application.Objects.Api.Pedagio;
using SistemaInfo.BBC.Domain.Contracts.Base;
using SistemaInfo.BBC.Domain.Contracts.Pedagio;
using SistemaInfo.BBC.Domain.Models.Empresa.Repository;
using SistemaInfo.BBC.Domain.Models.GrupoEmpresa.Repository;
using SistemaInfo.BBC.Domain.Models.Parametros.Repository;
using SistemaInfo.BBC.Infra.Bus.Interface.Pedagio;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.Services.Pedagio;

public class PedagioIntegracaoAppService : AppService,
    IPedagioIntegracaoAppService
{
    private readonly IEmpresaReadRepository _empresaReadRepository;
    private readonly IGrupoEmpresaReadRepository _grupoEmpresaReadRepository;
    private readonly IPedagioPublisher _pedagioPublisher;
    private readonly IParametrosReadRepository _parametrosReadRepository;

    public PedagioIntegracaoAppService(IAppEngine engine, IEmpresaReadRepository empresaReadRepository,
        IParametrosReadRepository parametrosReadRepository, IPedagioPublisher pedagioPublisher,
        IGrupoEmpresaReadRepository grupoEmpresaReadRepository) : base(engine)
    {
        _empresaReadRepository = empresaReadRepository;
        _parametrosReadRepository = parametrosReadRepository;
        _pedagioPublisher = pedagioPublisher;
        _grupoEmpresaReadRepository = grupoEmpresaReadRepository;
    }

    public async Task<IntegrarPagamentoPedagioResponse> IntegrarPagamentoPedagio(
        IntegrarPagamentoPedagioRequest request)
    {
        try
        {
            #region Valida dados básicos

            var validation = ValidaCamposRequest(request);
            if (validation is not null) return validation;

            #endregion

            #region Verifica limite de pagamento

            var verificaLimite = await VerificaLimitePagamento(request.Valor);
            if (!verificaLimite)
                return new IntegrarPagamentoPedagioResponse(false,
                    "Pagamento excede o valor máximo de pagamento de pedágio.");

            #endregion

            #region Carrega parâmetros (empresa)

            var lEmpresa = await _empresaReadRepository.GetByIdIncludeGrupoEmpersaAsync(User.EmpresaId);

            if (lEmpresa.PermitirPagamentoValePedagio == 0)
                return new IntegrarPagamentoPedagioResponse(false,
                    "Pagamento não inciado. Sua empresa não tem permissão para usar essa funcionalidade!");

            var lValorPorcentagemTarifa = lEmpresa.UtilizaTarifaEmpresaPagamentoPedagio == 1
                ? lEmpresa.PorcentagemTarifaServiceValePedagio
                : lEmpresa.GrupoEmpresa.PercentualTarifaValePedagio;

            var pagaTarifaAutomatica = lEmpresa.UtilizaTarifaEmpresaPagamentoPedagio == 1
                ? lEmpresa.CobrarTarifaBbcValePedagio == 1
                : lEmpresa.GrupoEmpresa.CobrarTarifaBbcValePedagio == 1;


            var empresaReprocessa = lEmpresa.HabilitaReprocessamentoValePedagio == 1;
            var grupoEmpresaReprocessa = (lEmpresa.GrupoEmpresa?.HabilitaReprocessamentoValePedagio ?? 0) == 1;

            var reprocessa = (grupoEmpresaReprocessa || empresaReprocessa);

            #endregion

            #region Calcula tarifa para mandar pro MS

            var valorTarifa = request.Valor * lValorPorcentagemTarifa / 100;

            #endregion

            #region Envia mensagem para o microserviço

            var lMessage = new IntegrarPedagioMessage
            {
                CnpjEmpresa = lEmpresa.Cnpj,
                PagamentoExternoId = request.CodigoRequisicao,
                CodigoValePedagio = request.CodigoValePedagio,
                FormaPagamento = 0,
                Descricao = request.Descricao,
                Valor = request.Valor,
                ValorTarifa = Math.Round(valorTarifa ?? 0, 2),
                ContaValePedagio = lEmpresa.ContaValePedagio
            };

            lMessage.AddParametro(TipoParametroMessage.PagamentoTarifaPedagio, pagaTarifaAutomatica.ToString());
            lMessage.AddParametro(TipoParametroMessage.EmpresaId, lEmpresa.Id.ToString());
            lMessage.AddParametro(TipoParametroMessage.ReprocessaPagamentoPedagio, reprocessa.ToString());

            var lResponse = await _pedagioPublisher.PublicarEventoIntegracaoDePedagio(lMessage);


            return new IntegrarPagamentoPedagioResponse
            {
                sucesso = lResponse.Sucesso,
                mensagem = lResponse.Mensagem,
                data = lResponse.Data != null ? Mapper.Map<IntegrarPagamentoPedagioResponseData>(lResponse.Data) : null
            };

            #endregion
        }
        catch (Exception e)
        {
            LogManager.GetCurrentClassLogger().Error(e);
            return new IntegrarPagamentoPedagioResponse(false, "Ocorreu um erro ao executar a operação: " + e.Message);
        }
    }

    public async Task<CancelarPagamentoPedagioResponse> CancelarPagamentoPedagio(
        CancelarPagamentoPedagioRequest request)
    {
        try
        {
            new LogHelper().LogOperationStart("CancelarPagamentoPedagio");

            #region Verifica dados req

            if (request.CodigoRequisicao < 0)
                return new CancelarPagamentoPedagioResponse(false,
                    "Pagamento não inciado. O campo 'CodigoRequisicao' possui um valor inválido");
            if (request.CodigoValePedagio < 0)
                return new CancelarPagamentoPedagioResponse(false,
                    "Pagamento não inciado. O campo 'CodigoValePedagio' possui um valor inválido");
            if (request.Descricao?.Length > 500)
                return new CancelarPagamentoPedagioResponse(false,
                    "Pagamento não inciado. O campo 'Descricao' possui um tamanho inválido");

            #endregion

            #region manda mensagem para MS

            var messageRequest = Mapper.Map<CancelarPedagioMessage>(request);
            messageRequest.PrazoMaximoCancelamento = GetPrazoMaximoCancelamentoPagamento().Result;
            var lRetorno = await _pedagioPublisher.PublicarEventoCancelamentoDePedagio(messageRequest);

            return new CancelarPagamentoPedagioResponse
            {
                sucesso = lRetorno.Sucesso,
                mensagem = lRetorno.Mensagem,
                data = lRetorno.Data == null ? null : Mapper.Map<CancelarPagamentoPedagioResponseData>(lRetorno.Data)
            };

            #endregion
        }
        catch (Exception ex)
        {
            new LogHelper().Error(ex, "Erro ao executar CancelarPagamentoPedagio");
            throw;
        }
        finally
        {
            new LogHelper().LogOperationEnd("CancelarPagamentoPedagio");
        }
    }

    public async Task<ComplementarPagamentoPedagioResponse> ComplementarPagamentoPedagio(
        ComplementarPagamentoPedagioRequest request)
    {
        try
        {
            new LogHelper().LogOperationStart("ComplementarPagamentoPedagio");

            #region Valida dados básicos

            var validation = ValidaCamposRequest(request);
            if (validation is not null) return validation;

            #endregion

            #region Carrega parâmetros (empresa)

            var lEmpresa = await _empresaReadRepository.GetByIdIncludeGrupoEmpersaAsync(User.EmpresaId);

            if (lEmpresa.PermitirPagamentoValePedagio == 0)
                return new ComplementarPagamentoPedagioResponse(false,
                    "Pagamento não inciado. Sua empresa não tem permissão para usar essa funcionalidade!");

            var lValorPorcentagemTarifa = lEmpresa.UtilizaTarifaEmpresaPagamentoPedagio == 1
                ? lEmpresa.PorcentagemTarifaServiceValePedagio
                : lEmpresa.GrupoEmpresa.PercentualTarifaValePedagio;

            var pagaTarifaAutomatica = lEmpresa.UtilizaTarifaEmpresaPagamentoPedagio == 1
                ? lEmpresa.CobrarTarifaBbcValePedagio == 1
                : lEmpresa.GrupoEmpresa.CobrarTarifaBbcValePedagio == 1;

            #endregion

            #region Calcula tarifa para mandar pro MS

            var valorTarifa = request.Valor * lValorPorcentagemTarifa / 100;

            #endregion

            #region Envia mensagem para o microserviço

            var lMessage = new ComplementarPagamentoPedagioMessage
            {
                CnpjEmpresa = lEmpresa.Cnpj,
                PagamentoExternoId = request.CodigoRequisicao,
                IdComplemento = request.IdComplemento,
                FormaPagamento = 0,
                Descricao = request.Descricao,
                Valor = request.Valor,
                ValorTarifa = Math.Round(valorTarifa ?? 0, 2),
                ContaValePedagio = lEmpresa.ContaValePedagio
            };

            lMessage.AddParametro(TipoParametroMessage.PagamentoTarifaPedagio, pagaTarifaAutomatica.ToString());
            lMessage.AddParametro(TipoParametroMessage.EmpresaId, lEmpresa.Id.ToString());

            var lResponse = await _pedagioPublisher.PublicarEventoComplementoDePedagio(lMessage);

            return new ComplementarPagamentoPedagioResponse
            {
                sucesso = lResponse.Sucesso,
                mensagem = lResponse.Mensagem,
                data = lResponse.Data != null
                    ? Mapper.Map<ComplementarPagamentoPedagioResponseData>(lResponse.Data)
                    : null
            };

            #endregion
        }
        catch (Exception ex)
        {
            new LogHelper().Error(ex, "Erro ao executar ComplementarPagamentoPedagio");
            throw;
        }
        finally
        {
            new LogHelper().LogOperationEnd("ComplementarPagamentoPedagio");
        }
    }

    public async Task<CancelarComplementoPagamentoPedagioResponse> CancelarComplementoPagamentoPedagio(CancelarComplementoPagamentoPedagioRequest request) 
    {
        try {
            #region Verifica dados req

            if (request.CodigoRequisicao < 0)
                return new CancelarComplementoPagamentoPedagioResponse(false,
                    "Pagamento não inciado. O campo 'CodigoRequisicao' possui um valor inválido");
            if (request.IdComplemento < 0)
                return new CancelarComplementoPagamentoPedagioResponse(false,
                    "Pagamento não inciado. O campo 'CodigoValePedagio' possui um valor inválido");
            if (request.Descricao?.Length > 400)
                return new CancelarComplementoPagamentoPedagioResponse(false,
                    "Pagamento não inciado. O campo 'Descricao' possui um tamanho inválido");

            #endregion

            #region manda mensagem para MS

        
            var prazoCancelamento = GetPrazoMaximoCancelamentoPagamento().Result;
            
            var lRetorno = await _pedagioPublisher.PublicarEventoCancelamentComplementoDePedagio(new CancelarComplementoPedagioMessage
            {
                CodigoRequisicao = request.CodigoRequisicao,
                IdComplemento = request.IdComplemento,
                Descricao = request.Descricao,
                EmpresaId = User.EmpresaId,
                PrazoMaximoCancelamento = prazoCancelamento
            });

            return new CancelarComplementoPagamentoPedagioResponse
            {
                sucesso = lRetorno.Sucesso,
                mensagem = lRetorno.Mensagem,
                data = lRetorno.Data == null
                    ? null
                    : Mapper.Map<CancelarComplementoPagamentoPedagioResponseData>(lRetorno.Data)
            };

            #endregion
        }
        catch (Exception ex)
        {
            new LogHelper().Error(ex, "Erro ao executar CancelarComplementoPagamentoPedagio");
            throw;
        }
        finally
        {
            new LogHelper().LogOperationEnd("CancelarComplementoPagamentoPedagio");
        }
    }
    
    private async Task<bool> VerificaLimitePagamento(decimal requestValor)
    {
        var lParametro = await _parametrosReadRepository.GetParametroLimitePagamentoPedagio();
        if (lParametro == null) return true;
        return requestValor <= lParametro.Valor.ToDecimalSafe();
    }
    
    private async Task<int?> GetPrazoMaximoCancelamentoPagamento()
    {
        var lParametro = await _parametrosReadRepository.GetPrazoMaximoCancelamentoPagamento();
        return lParametro?.Valor.ToIntSafe();
    }

    private IntegrarPagamentoPedagioResponse ValidaCamposRequest(IntegrarPagamentoPedagioRequest request)
    {
        if (request.Valor <= 0.0m)
            return new IntegrarPagamentoPedagioResponse(false,
                "Pagamento não inciado. O campo 'Valor' possui um valor inválido");
        if (request.CodigoRequisicao <= 0)
            return new IntegrarPagamentoPedagioResponse(false,
                "Pagamento não inciado. O campo 'CodigoRequisicao' possui um valor inválido");
        if (request.Descricao?.Length > 400)
            return new IntegrarPagamentoPedagioResponse(false,
                "Pagamento não inciado. O campo 'Descricao' possui um tamanho inválido");
        if (User.EmpresaId == 0)
            return new IntegrarPagamentoPedagioResponse(false,
                "Pagamento não inciado. Ocorreu um erro ao validar a empresa logada, tente novamente!");
        return null;
    }

    private ComplementarPagamentoPedagioResponse ValidaCamposRequest(ComplementarPagamentoPedagioRequest request)
    {
        if (request.Valor <= 0.0m)
            return new ComplementarPagamentoPedagioResponse(false,
                "Pagamento não inciado. O campo 'Valor' possui um valor inválido");
        if (request.CodigoRequisicao <= 0)
            return new ComplementarPagamentoPedagioResponse(false,
                "Pagamento não inciado. O campo 'CodigoRequisicao' possui um valor inválido");
        if (request.IdComplemento <= 0)
            return new ComplementarPagamentoPedagioResponse(false,
                "Pagamento não inciado. O campo 'IdComplemento' possui um valor inválido");
        if (request.Descricao?.Length > 400)
            return new ComplementarPagamentoPedagioResponse(false,
                "Pagamento não inciado. O campo 'Descricao' possui um tamanho inválido");
        if (User.EmpresaId == 0)
            return new ComplementarPagamentoPedagioResponse(false,
                "Pagamento não inciado. Ocorreu um erro ao validar a empresa logada, tente novamente!");
        return null;
    }
}
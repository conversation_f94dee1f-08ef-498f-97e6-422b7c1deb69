using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc.Versioning;
using System;

namespace SistemaInfo.BBC.ApiIntegracao.Versioning
{
    /// <summary>
    /// Custom API version reader que detecta versão baseada na URL sem exigir parâmetros
    /// </summary>
    public class CustomUrlApiVersionReader : IApiVersionReader
    {
        /// <summary>
        /// Lê a versão da API baseada na URL
        /// </summary>
        /// <param name="request">Request HTTP</param>
        /// <returns>Versão detectada ou null se não encontrada</returns>
        public string Read(HttpRequest request)
        {
            if (request?.Path.HasValue == true)
            {
                var path = request.Path.Value.ToLowerInvariant();
                if (path.Contains("/v2/"))
                {
                    return "2.0";
                }
            }
            return "1.0";
        }

        /// <summary>
        /// Adiciona parâmetros de versão (não usado nesta implementação)
        /// </summary>
        /// <param name="context">Contexto da ação</param>
        public void AddParameters(IApiVersionParameterDescriptionContext context)
        {
        }
    }
}

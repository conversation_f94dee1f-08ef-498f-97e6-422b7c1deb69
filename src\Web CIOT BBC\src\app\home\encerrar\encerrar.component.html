<div class="container main-container">
    <br>
    <form novalidate (ngSubmit)="encerrar()" [formGroup]="meetupForm">
        <div class="form-horizontal">
            <div class="alert alert-danger" *ngIf="errors.length > 0">
                <h3 id="msgRetorno">Opa! Alguma coisa não deu certo:</h3>
                <ul>
                    <li *ngFor="let error of errors">{{ error }}</li>
                </ul>
            </div>
            <div class="row">
                <div class="col-sm-12 col-md-6 col-lg-3">
                    <div class="form-group required" [ngClass]="{'has-error': displayMessage.ciotEncerrar }">
                        <label class="control-label" for="ciotEncerrar">CIOT</label>
                        <input class="form-control" id="ciotEncerrar" type="text" formControlName="ciotEncerrar" placeholder="Informe o CIOT" />
                        <span class="text-danger" *ngIf="displayMessage.ciotEncerrar">
              <p [innerHTML]="displayMessage.ciotEncerrar"></p>
            </span>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12 col-md-6 col-lg-3">
                    <div class="form-group required" [ngClass]="{'has-error': displayMessage.senhaEncerrar }">
                        <label class="control-label" for="senhaEncerrar">Senha</label>
                        <input class="form-control" id="senhaEncerrar" type="password" formControlName="senhaEncerrar" OnlyNumber placeholder="Informe a senha" />
                        <span class="text-danger" *ngIf="displayMessage.senhaEncerrar">
              <p [innerHTML]="displayMessage.senhaEncerrar"></p>
            </span>
                    </div>
                </div>
            </div>
            <!-- <div class="g-recaptcha" data-sitekey="6LdTj0cUAAAAAJMT7t5dVnEU4k6chdIc0m-y-PZF"></div> -->
            <br/>
<!--            <re-captcha (resolved)="resolved($event)" siteKey="6LcqvgEVAAAAAEtksgMX9T9-7zHU9rje34hsJ3_5"></re-captcha>-->
            <br/>
            <button class="btn btn-danger" id="encerrar" type="submit" [disabled]='!meetupForm.valid' style="background-color: #00622c; border-color: #00622c;">Encerrar</button>
        </div>
    </form>
</div>
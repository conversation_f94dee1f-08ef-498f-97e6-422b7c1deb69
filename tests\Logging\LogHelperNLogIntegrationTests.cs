using System;
using System.IO;
using NLog;
using NLog.Config;
using NLog.Targets;
using SistemaInfo.BBC.Infra.CrossCutting.Logging;
using Xunit;

namespace SistemaInfo.BBC.Tests.Logging
{
    public class LogHelperNLogIntegrationTests
    {
        [Fact]
        public void LogHelper_ShouldIntegrateWithNLog()
        {
            // Arrange
            // Configuração temporária do NLog para testes
            var config = new LoggingConfiguration();
            var memoryTarget = new MemoryTarget("memory");
            config.AddTarget(memoryTarget);
            config.AddRuleForAllLevels(memoryTarget);
            LogManager.Configuration = config;
            
            string testMessage = "Test message " + Guid.NewGuid().ToString();
            
            // Act
            LogHelper.Info(testMessage);
            
            // Assert
            // Verificamos se a mensagem foi registrada no target de memória
            Assert.Contains(memoryTarget.Logs, log => log.Contains(testMessage));
            
            // Limpar a configuração
            LogManager.Configuration = null;
        }
        
        [Fact]
        public void LogHelper_ShouldLogExceptions()
        {
            // Arrange
            // Configuração temporária do NLog para testes
            var config = new LoggingConfiguration();
            var memoryTarget = new MemoryTarget("memory");
            config.AddTarget(memoryTarget);
            config.AddRuleForAllLevels(memoryTarget);
            LogManager.Configuration = config;
            
            var testException = new Exception("Test exception " + Guid.NewGuid().ToString());
            
            // Act
            LogHelper.Error(testException);
            
            // Assert
            // Verificamos se a exceção foi registrada no target de memória
            Assert.Contains(memoryTarget.Logs, log => log.Contains(testException.Message));
            
            // Limpar a configuração
            LogManager.Configuration = null;
        }
        
        [Fact]
        public void LogHelper_ShouldLogOperationStartAndEnd()
        {
            // Arrange
            // Configuração temporária do NLog para testes
            var config = new LoggingConfiguration();
            var memoryTarget = new MemoryTarget("memory");
            config.AddTarget(memoryTarget);
            config.AddRuleForAllLevels(memoryTarget);
            LogManager.Configuration = config;
            
            string operationName = "TestOperation" + Guid.NewGuid().ToString();
            
            // Act
            LogHelper.LogOperationStart(operationName);
            LogHelper.LogOperationEnd(operationName);
            
            // Assert
            // Verificamos se as mensagens de início e fim foram registradas
            Assert.Contains(memoryTarget.Logs, log => log.Contains($"Início da operação: {operationName}"));
            Assert.Contains(memoryTarget.Logs, log => log.Contains($"Fim da operação: {operationName}"));
            
            // Limpar a configuração
            LogManager.Configuration = null;
        }
    }
}

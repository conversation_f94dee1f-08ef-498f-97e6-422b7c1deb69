﻿using AutoMapper;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc.Formatters;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using SistemaInfo.BBC.Infra.CrossCutting.IoC;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using SistemaInfo.Framework.DomainDrivenDesign.Web.Filters;
using SistemaInfo.Framework.DomainDrivenDesign.Web.Secutiry.UserSession;
using SistemaInfo.Framework.Web.Filters;
using Swashbuckle.AspNetCore.Swagger;
using System;
using System.IO;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.Extensions.FileProviders;
using Newtonsoft.Json.Converters;
using NLog;
using SistemaInfo.BBC.Infra.Bus;
using SistemaInfo.BBC.Infra.Data.Context;
using SistemaInfo.Framework.CQRS.Message;
using SistemaInfo.Framework.Utils.AppConfiguration;
using SistemaInfo.Framework.Web.Utils;

namespace SistemaInfo.BBC.ApiCloud
{
    /// <summary>
    ///
    /// </summary>
    public class Startup
    {
        /// <inheritdoc />
        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        /// <summary>
        ///
        /// </summary>
        public IConfiguration Configuration { get; }

        /// <summary>
        ///
        /// </summary>
        /// <param name="services"></param>
        public void ConfigureServices(IServiceCollection services)
        {
            services
                .AddMvc(opts =>
                {
                    opts.Filters.Add<ApiActionExceptionFilter>();
                    opts.Filters.Add<ApiFaultResultCodeFilter>();
                    opts.OutputFormatters.Add(new XmlDataContractSerializerOutputFormatter());
                    opts.InputFormatters.Add(new XmlDataContractSerializerInputFormatter());
                }).AddJsonOptions(opts => opts.SerializerSettings.Converters.Add(new StringEnumConverter()));
            
            services.AddAutoMapper();
            services.AddSwaggerGen(c =>
            {
                c.SwaggerDoc("v1", new Info {Title = "/BBC/ApiCloud", Version = "v1"});
                c.DescribeAllEnumsAsStrings();
                var basePath = AppContext.BaseDirectory;
                var lXmlPath = Path.Combine(basePath, "App_Data", System.Reflection.Assembly.GetExecutingAssembly().GetName().Name + ".xml");
                c.IncludeXmlComments(lXmlPath);
            });
            
            RegisterServices(services, Configuration);
            using (var intermediateServiceProvider = services.BuildServiceProvider())
            {
                OnServicesConfigured(intermediateServiceProvider);
            }
        }
        
        private static void OnServicesConfigured(ServiceProvider serviceProvider) {

            using (var db = serviceProvider.GetRequiredService<ConfigContext>()) {
                var lLog = LogManager.GetCurrentClassLogger();
                try
                {
                    db.Database.OpenConnection();
                    db.Database.CloseConnection();
                    lLog.Info($"Conexão com banco validado com sucesso!");
                    Console.WriteLine($"Conexão com banco validado com sucesso!");
                }
                catch (Exception e)
                {
                    lLog.Error(e, "Erro ao verificar conexão com o banco: ");
                    Console.WriteLine($"Erro ao verificar conexão com o banco: {e}");
                }
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="app"></param>
        /// <param name="env"></param>
        /// <param name="httpAccessor"></param>
        /// <param name="appConfiguration"></param>
        /// <param name="serviceProvider"></param>
        public void Configure(IApplicationBuilder app, IHostingEnvironment env, IHttpContextAccessor httpAccessor, AppConfiguration appConfiguration, IServiceProvider serviceProvider)
        {
            app.UseLogForApplicationEvents();
            app.UseSistemaInfoFramework(() => httpAccessor?.HttpContext?.RequestServices ?? serviceProvider);

            app.UsePathBase("/BBC/ApiCloud");
            
            app.UseCustomStatusCodePages();
            app.UseCustomDefaultFiles();
            app.UseCustomStaticFiles();
            
          
            app.UseStatusCodePages();
            app.UseCustomDefaultFiles();
            app.UseCustomStaticFiles();
          
            app.UseMiddleware<AuthenticationExceptionHandlingMiddleware>();
            app.UseMvc(routes =>
            {
                routes.MapRoute("DefaultApiCloud", "BBC/ApiCloud/{controller}/{action}/{id}");
            });
            
            SetSwagger(app);
            app.UseAuthentication();
        }

        private void SetSwagger(IApplicationBuilder app)
        {
            app.UseSwagger();

            var swaggerEndpoint = "/BBC/ApiCloud/swagger/v1/swagger.json";

            // appsettings 
            // "Swagger": {
            //     "SwaggerEndpoint": "/BBC/ApiCloud/swagger/v1/swagger.json"
            // }
            if (!string.IsNullOrWhiteSpace(Configuration["Swagger:SwaggerEndpoint"]))
                swaggerEndpoint = Configuration["Swagger:SwaggerEndpoint"];

            app.UseSwaggerUI(c => { c.SwaggerEndpoint(swaggerEndpoint, "/BBC/ApiCloud"); });
        }

        private static void RegisterServices(IServiceCollection serviceCollection, IConfiguration configuration)
        {
            serviceCollection.TryAddSingleton<IHttpContextAccessor, HttpContextAccessor>();
            serviceCollection.AddSingleton<IPublishMessageBusConfigurations>(provider => null);
            DependencyInjector.RegisterServices(serviceCollection);
            MessageBusDependencyInjector.RegisterServices(serviceCollection, configuration);
            
        }
    }
}
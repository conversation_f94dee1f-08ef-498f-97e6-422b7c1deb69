using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using AutoMapper.QueryableExtensions;
using DinkToPdf;
using DinkToPdf.Contracts;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using NLog;
using SistemaInfo.BBC.Application.Helpers;
using SistemaInfo.BBC.Application.Helpers.Emprestimo;
using SistemaInfo.BBC.Application.Interface.Emprestimo;
using SistemaInfo.BBC.Application.Objects.Api.Emprestimo;
using SistemaInfo.BBC.Application.Objects.Api.Portador;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.BBC.Domain.Models.Emprestimo.Commands;
using SistemaInfo.BBC.Domain.Models.Emprestimo.Repository;
using SistemaInfo.BBC.Domain.Models.Parametros.Repository;
using SistemaInfo.BBC.Domain.Models.Portador.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using SistemaInfo.Framework.Utils;
using IContaConductorReadRepository =
    SistemaInfo.BBC.Domain.Models.ContasConductor.Repository.IContaConductorReadRepository;

namespace SistemaInfo.BBC.Application.Services.Emprestimo
{
    public class EmprestimoAppService :
        AppService<Domain.Models.Emprestimo.Emprestimo, IEmprestimoReadRepository, IEmprestimoWriteRepository>,
        IEmprestimoAppService
    {
        private readonly IParametrosReadRepository _parametrosReadRepository;
        private readonly IContaConductorReadRepository _contaConductorReadRepository;
        private readonly IConverter _converter;

        public EmprestimoAppService(IAppEngine engine, IEmprestimoReadRepository readRepository,
            IEmprestimoWriteRepository writeRepository, IPortadorReadRepository portadorReadRepository,
            IParametrosReadRepository parametrosReadRepository, IConverter converter,
            IContaConductorReadRepository contaConductorReadRepository) : base(engine, readRepository, writeRepository)
        {
            _PortadorReadRepository = portadorReadRepository;
            _parametrosReadRepository = parametrosReadRepository;
            _contaConductorReadRepository = contaConductorReadRepository;
            _converter = converter;
        }

        public IPortadorReadRepository _PortadorReadRepository { get; }

        public ConsultarEmprestimosResponse ConsultarEmprestimos(int take, int page, string cpfCnpj)
        {
            try
            {
                new LogHelper().LogOperationStart("ConsultarEmprestimos");
                var lEmprestimos = Repository.Query.GetAll();

                if (!string.IsNullOrWhiteSpace(cpfCnpj))
                    lEmprestimos = lEmprestimos.Where(x => x.Portador.CpfCnpj == cpfCnpj);

                var retorno = lEmprestimos.Skip((page - 1) * take)
                    .Take(take)
                    .Include(a => a.Portador)
                    .ProjectTo<ConsultarEmprestimos>(Engine.Mapper.ConfigurationProvider).ToList();

                var lCount = lEmprestimos.Count();

                return new ConsultarEmprestimosResponse
                {
                    Items = retorno,
                    TotalItems = lCount
                };
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar ConsultarEmprestimos");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("ConsultarEmprestimos");
            }
        }

        public ConsultarEmprestimosResponse ConsultarGridEmprestimos(int take, int page, OrderFilters orderFilters,
            List<QueryFilters> filters)
        {
            var emprestimos = GetDadosGridRelatorioEmprestimo(orderFilters, filters);

            var retorno = emprestimos.Skip((page - 1) * take)
                .Take(take)
                .Include(a => a.Portador)
                .ProjectTo<ConsultarEmprestimos>(Engine.Mapper.ConfigurationProvider).ToList();

            return new ConsultarEmprestimosResponse
            {
                Items = retorno,
                TotalItems = emprestimos.Count()
            };
        }

        public List<ConsultarEmprestimos> DadosRelatorioGridPagamentos(OrderFilters orderFilters,
            List<QueryFilters> filters)
        {
            var dados = GetDadosGridRelatorioEmprestimo(orderFilters, filters);
            return dados.ProjectTo<ConsultarEmprestimos>(Engine.Mapper.ConfigurationProvider).ToList();
        }

        public async Task<RespPadrao> IntegrarEmprestimo(EmprestimoRequest lEmprestimoReq)
        {
            try
            {
                var lEmprestimo = Mapper.Map<EmprestimoSalvarCommand>(lEmprestimoReq);

                var portador = _PortadorReadRepository.Where(x => x.CpfCnpj == lEmprestimo.CpfCnpjPortador)
                    .FirstOrDefault();

                if (portador != null)
                    lEmprestimo.PortadorId = portador.Id;

                var lIdStateCadastrado = Repository.Query.FirstOrDefault(o => o.IdState == lEmprestimo.IdState);

                if (lIdStateCadastrado != null)
                {
                    //atualiza registro
                    lIdStateCadastrado.Status = lEmprestimo.Status;
                    lIdStateCadastrado.ValorPago = lEmprestimo.ValorPago;
                    lIdStateCadastrado.DataEmprestimo = lEmprestimo.DataEmprestimo;
                    lIdStateCadastrado.ValorAquisicao = lEmprestimo.ValorAquisicao;
                    lIdStateCadastrado.Conta = lEmprestimo.Conta;
                    lIdStateCadastrado.Agencia = lEmprestimo.Agencia;
                    lIdStateCadastrado.TaxaRetencao = lEmprestimo.TaxaRetencao;
                    lIdStateCadastrado.CpfCnpjPortador = lEmprestimo.CpfCnpjPortador;

                    Repository.Command.Update(lIdStateCadastrado);
                    Repository.Command.SaveChanges();

                    return new RespPadrao
                    {
                        sucesso = true,
                        mensagem = "Integração atualizada com sucesso!"
                    };
                }

                lEmprestimo.ValidarCadastro();

                await Engine.CommandBus.SendCommandAsync(lEmprestimo);

                return new RespPadrao
                {
                    sucesso = true,
                    mensagem = "Integração efetuada com sucesso!"
                };
            }
            catch (Exception e)
            {
                return new RespPadrao
                {
                    sucesso = false,
                    mensagem = e.Message
                };
            }
        }

        public async Task<RespPadrao> Cadastrar(EmprestimoCadastrarRequest emprestimoCadastrarRequest)
        {
            try
            {
                new LogHelper().LogOperationStart("Cadastrar");
                var emprestimo = Mapper.Map<EmprestimoSalvarCommand>(emprestimoCadastrarRequest);
                await Engine.CommandBus.SendCommandAsync(emprestimo);

                return new RespPadrao(true, string.Empty);
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar Cadastrar");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("Cadastrar");
            }
        }

        public EmprestimoConsultarParaEdicao ConsultarParaEdicao(int id)
        {
            try
            {
                new LogHelper().LogOperationStart("ConsultarParaEdicao");
                if (id < 0)
                {
                    throw new Exception("ID inválido!");
                }

                var emprestimo = Repository.Query.GetByIdIncludePortadorRetencoes(id);

                if (emprestimo == null)
                    throw new Exception($"Empréstimo {id} não encontrado.");

                var emprestimoEdicao =
                    Mapper.Map<EmprestimoConsultarParaEdicao>(emprestimo);

                emprestimoEdicao.Retencoes = Repository.Query.GetRetencoes(id)
                    .ProjectTo<EmprestimoRetencaoConsultarParaEdicao>(Engine.Mapper.ConfigurationProvider).ToList();

                if (!emprestimo.CpfCnpjPortador.IsNullOrWhiteSpace())
                {
                    emprestimoEdicao.CpfCnpjPortador = emprestimo.CpfCnpjPortador.ToCpfOrCnpj();
                }

                return emprestimoEdicao;
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar ConsultarParaEdicao");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("ConsultarParaEdicao");
            }
        }

        public async Task<RespPadrao> SinalizaClienteEmprestimo(SinalizarEmprestimoRequest lEmprestimoReq)
        {
            try
            {
                var lLink = _parametrosReadRepository.GetParametrosListAsync(
                    Domain.Models.Parametros.Parametros.ParametroGeralId,
                    Domain.Models.Parametros.Parametros.TipoDoParametro.CodigoLinkEmpresa,
                    Domain.Models.Parametros.Parametros.TipoDoValor.String).Result;

                var lSucesso = true;

                var contaConductor = await _contaConductorReadRepository.FirstOrDefaultAsync(x =>
                    x.CpfCnpj == lEmprestimoReq.CpfCnpj);

                if (contaConductor != null)
                {
                    foreach (var link in lLink)
                    {
                        var request = JsonConvert.SerializeObject(new
                        {
                            cpfCnpj = lEmprestimoReq.CpfCnpj.OnlyNumbers(),
                            idContaConductor = contaConductor.Id,
                            idBanco = contaConductor.IdBanco,
                            agencia = contaConductor.Agencia,
                            conta = contaConductor.Conta,
                            digitoVerificadorConta = contaConductor.DigitoVerificadorConta,
                            digitoVerificadorAgencia = contaConductor.DigitoVerificadorAgencia,
                            idConta = contaConductor.IdConta,
                            idCartao = contaConductor.IdCartao
                        });

                        var response = new HttpClient();
                        var content = new StringContent(request, Encoding.UTF8, "application/json");

                        var result = response.PostAsync(link.Valor, content).Result;
                        var lRetorno = result.StatusCode == HttpStatusCode.OK ? 200 : result.StatusCode.ToIntSafe();

                        if (lRetorno != 200) lSucesso = false;
                    }
                }
                else
                {
                    lSucesso = false;
                }

                if (lSucesso)
                    return new RespPadrao
                    {
                        sucesso = true,
                        mensagem = "Operação realizada com sucesso!"
                    };
                return new RespPadrao
                {
                    sucesso = false,
                    mensagem = "Falha em realizar o processo!"
                };
            }
            catch (Exception e)
            {
                var lLog = LogManager.GetCurrentClassLogger();
                lLog.Error(e, "Erro ao realizar o processo SinalizaClienteEmprestimo: ");
                return new RespPadrao
                {
                    sucesso = false,
                    mensagem = "Erro ao realizar o processo!"
                };
            }
        }

        public IDocument GerarRelatorioEmprestimo(int emprestimoId)
        {
            try
            {
                new LogHelper().LogOperationStart("GerarRelatorioEmprestimo");
                var emprestimo = Repository.Query.Include(o => o.Portador).Include(o => o.Retencoes)
                    .FirstOrDefault(o => o.Id == emprestimoId);

                var emprestimoParaRelatorio =
                    Mapper.Map<Domain.Models.Emprestimo.Emprestimo, EmprestimoConsultarParaRelatorio>(emprestimo);

                var html = emprestimo == null
                    ? HtmlEmprestimoReportHelper.GetHtmlImpressaoEmprestimoNulo(emprestimoId)
                    : HtmlEmprestimoReportHelper.GetHtmlImpressaoEmprestimo(emprestimoParaRelatorio);

                var htmlToPdfDocument = new HtmlToPdfDocument
                {
                    GlobalSettings =
                    {
                        ColorMode = ColorMode.Color,
                        Orientation = Orientation.Portrait,
                        PaperSize = PaperKind.A4Plus,
                        Margins =
                        {
                            Top = 3,
                            Bottom = 3,
                            Left = 2,
                            Right = 2
                        }
                    },
                    Objects =
                    {
                        new ObjectSettings
                        {
                            PagesCount = true,
                            HtmlContent = html,
                            WebSettings = { DefaultEncoding = "utf-8" },
                            HeaderSettings = null
                        }
                    }
                };

                return htmlToPdfDocument;
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar GerarRelatorioEmprestimo");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("GerarRelatorioEmprestimo");
            }
        }

        private IQueryable<Domain.Models.Emprestimo.Emprestimo> GetDadosGridRelatorioEmprestimo(
            OrderFilters orderFilters, List<QueryFilters> filters)
        {
            var lEmprestimos = Repository.Query.GetAll();

            lEmprestimos = lEmprestimos.AplicarFiltrosDinamicos(filters);

            lEmprestimos = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                ? lEmprestimos.OrderByDescending(o => o.Id)
                : lEmprestimos.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            return lEmprestimos;
        }
    }
}
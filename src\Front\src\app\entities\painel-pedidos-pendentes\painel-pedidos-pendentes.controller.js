(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('PainelPedidosPendentesController', PainelPedidosPendentesController);

        PainelPedidosPendentesController.inject = [
        'BaseService',
        '$rootScope',
        'toastr',
        '$scope',
        'PersistentDataService',
        '$timeout',
        '$state',
        '$stateParams',
        'PERFIL_ADMINISTRADOR',
        'oitozero.ngSweetAlert',
        '$uibModal',
        '$window'
    ];

    function PainelPedidosPendentesController(
        BaseService, 
        $rootScope, 
        toastr, 
        $scope, 
        PersistentDataService, 
        $timeout, 
        $state, 
        $stateParams, 
        PERFIL_ADMINISTRADOR,  
        SweetAlert,
        $uibModal,
        $window) {
        var vm = this;

        vm.status = 0;
        
        vm.reenviarPagamento = [];
        vm.reenviarPagamentoId = [];
        vm.retencaoRelatorio = [];

        vm.ativarTodasConta = false;
        vm.desabilitarBtnRelatorio = false;


        vm.enumStatus = [
            { id: 0, descricao: 'Pendente'}, 
            { id: 1, descricao: 'Integrado'}
        ]

        vm.headerItems = [{ name: 'Movimentações' }, { name: 'Painel pedidos pendentes' }];

        vm.load = function () {
        }

        vm.atualizaTela = function () {
            vm.gridOptions.dataSource.refresh();
            vm.AtualizaComboStatus();
        }

        vm.AtualizaComboStatus = function(){
            vm.enumStatus = [];
        }
        
        vm.dateOptions = {
            timePicker: false,
            timePicker24Hour: false,
            applyClass: 'btn-primary',
            locale: {
                applyLabel: "Aplicar",
                fromLabel: "De",
                format: "DD/MM/YYYY",
                toLabel: "Até",
                cancelLabel: 'Cancelar',
                customRangeLabel: 'Período',
                monthNames: ['Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho', 'Julho', 'Agosto', 'Setembro',
                    'Outubro', 'Novembro', 'Dezembro'],
                daysOfWeek: ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb']
            },
            ranges: {
                'Últimos 1 dias': [moment().add(-1, 'days'), moment()],
                'Últimos 7 dias': [moment().add(-7, 'days'), moment()]
            }
        };

        vm.date = {
            startDate: moment().add(-7, 'days'),
            endDate: moment()
        };

        vm.contas = [];
        vm.clicouNoCheckBoxConta = 0;
        vm.reenviando = false;

        $scope.$watch('vm.consultaEmpresa.selectedValue', function (newValue) {
            vm.empresaConsulta = vm.consultaEmpresa.selectedValue;
        });

        vm.reenviarPedido = function (entity) {
            if(entity.numeroPedidoSap){
               return toastr.error("Pedido com status integrado, não podem ser reenviados!");                
            }
            vm.reenviando = true;

            var msg = 'Deseja reenviar o pedido?';

            return Sistema.Msg.confirm(msg, function () {
                BaseService.post('ProtocoloAbastecimento', 'ReenviarPedidoPendente', entity.id).then(function (response) {
                    response.sucesso ? toastr.success('Pedido enviado com sucesso!') : toastr.error(response.message);
                    vm.reenviando = false;
                    vm.gridOptions.dataSource.refresh();
                    
                });
            }, function(){
                vm.reenviando = false;            
            })
        };

        vm.bloquearDesbloquearPedido = function (entity) {
            return toastr.error("Implementações futuras!"); 
        };

        vm.cancelarPedido = function (entity) {
            return toastr.error("Implementações futuras!");   
        };

        vm.gridOptions = {
            data: [],
            onRegisterApi: BaseService.dataGrid.defaultOnRegisterApi,
            dataSource: {
                autoBind: false,
                url: "ProtocoloAbastecimento/ConsultarGridProtocoloPedidosPendentes",
                params: function () {
                    return {
                        DtInicial: vm.date.startDate.toDate(),
                        DtFinal: vm.date.endDate.toDate(),
                        EmpresaId: vm.consultaEmpresa.selectedValue ? vm.consultaEmpresa.selectedValue : 0,
                        Status: vm.status
                    }
                },
            },
            columnDefs: [
            {
                name: 'Ações',
                width: '10%',
                cellTemplate: '<div ng-if="!row.groupHeader" class="ui-grid-cell-contents" title="TOOLTIP">\
                                    <div class="container-btn-action">\
                                        <button tooltip-placement="right" uib-tooltip="Exibir abastecimentos" type="button" ng-click="grid.appScope.vm.consultarProtocolo(row.entity.id)" ng-class="row.entity.status===\'Aberto\' ? \'btn btn-xs btn-info\' : \'btn btn-xs btn-info\'">\
                                            <i ng-class="\'fa fa-eye\'"></i>\
                                        </button>\
                                    </div>\
                                    <button tooltip-placement="right" ng-show="!row.entity.numeroPedidoSap && !grid.appScope.vm.usuAdm"\
                                        uib-tooltip="Reenviar pedido" type="button"\
                                        ng-click="grid.appScope.vm.reenviarPedido(row.entity)"\
                                        ng-disabled="row.entity.numeroPedidoSap || grid.appScope.vm.reenviando"\
                                        ng-class="\'btn btn-xs btn-danger\'">\
                                        <i ng-class="\'fa fa-paper-plane-o\'"></i>\
                                    </button>\
                                </div>'
            },
            {
                name: 'Codigoprotocolo',
                displayName: 'Código Protocolo',
                width: 100,
                field: 'id',
                serverField: 'id',
                primaryKey: true,
                type: 'number',
                enableFiltering: true
            },
            {
                name: 'CnpjEmpresa',
                displayName: 'CNPJ Empresa',
                width: 140,
                field: 'cnpjEmpresa',
                serverField: 'Empresa.Cnpj',
                type: 'text',
                enableFiltering: true
            },
            {
                name: 'Empresa',
                displayName: 'Empresa',
                width: 140,
                field: 'nomeEmpresa',
                serverField: 'nomeEmpresa',
                type: 'text',
                enableFiltering: false
            },
            {
                name: 'CnpjFornecedor',
                displayName: 'CNPJ Fornecedor',
                width: 140,
                field: 'cnpjPosto',
                serverField: 'Posto.Cnpj',
                type: 'text',
                enableFiltering: true
            },
            {
                name: 'Fornecedor',
                displayName: 'Fornecedor',
                width: 140,
                field: 'nomePosto',
                serverField: 'nomePosto',
                type: 'text',
                enableFiltering: false
            },
            {
                name: 'NotaFiscal',
                displayName: 'Nota fiscal',
                width: '*',
                minWidth: 150,
                field: 'notaFiscal',
                serverField: 'notaFiscal',
                type: 'text',
                enableFiltering: true
            },
            {
                name: 'ValorPedido',
                displayName: 'Valor pedido',
                width: '*',
                minWidth: 150,
                field: 'valorXml',
                serverField: 'valorXml',
                cellTemplate: '<div class="ui-grid-cell-contents">\
                                    <input type="text" ng-model="row.entity.valorXml" readonly\
                                            class="no-borders" style="background: none;" ui-money-mask="3" />\
                            </div>',
                enableFiltering: false
            },
            {
                name: 'Data geração',
                displayName: 'Data geração',
                width: 100,
                type: 'text',
                field: 'dataCadastro',
                serverField: 'dataCadastro',
                enableFiltering: false,
                enableSorting: false
            },
            {
                name: 'NumeroPedidoSap',
                displayName: 'Número pedido',
                width: 100,
                type: 'text',
                field: 'numeroPedidoSap',
                serverField: 'numeroPedidoSap',
                enableFiltering: false,
                enableSorting: false
            },
            {
                name: 'Status',
                displayName: 'Status',
                width: 170,
                field: 'status',
                type: 'text',
                serverField: 'status',
                enableFiltering: false
            },
            ]
        };

        vm.consultaEmpresa = {
            columnDefs: [{
                name: 'Cód.',
                field: 'id',
                width: 60,
                type: 'number',
                primaryKey: true
            }, {
                name: 'Nome Fantasia',
                field: 'nomeFantasia',
                width: '*'
            }, {
                name: 'Razão Social',
                field: 'razaoSocial',
                width: '*'
            }, {
                name: 'Email',
                field: 'email',
                width: 120
            }],
            desiredValue: 'id',
            desiredText: 'nomeFantasia',
            url: 'Empresa/ConsultarGridEmpresaCombo',
            paramsMethod: function () {
                return {}
            }
        };

        
        vm.baixarDocumento = function (documento, tipo) {
            var downloadArchive = document.createElement("a");
            
            if (tipo == 0) {
                downloadArchive.href = "data:application/pdf;base64," + documento.pdf;
                downloadArchive.download = "Nota_" + documento.cnpj + ".pdf";    
            }
            if (tipo == 1) {
                downloadArchive.href = "data:application/pdf;base64," + documento.xml;
                downloadArchive.download = "Nota_" + documento.cnpj + ".xml";    
            }

            downloadArchive.click();
        };

        vm.consultarProtocolo = function (id) { 
            $uibModal.open({
                animation: true,
                ariaLabelledBy: 'modal-title',
                ariaDescribedBy: 'modal-body',
                templateUrl: 'app/entities/painel-protocolo-abastecimento/modal-abastecimentos/modal-abastecimentos.html',
                controller: function ($uibModalInstance, $uibModalStack, $scope, BaseService, id) {
                    var vm = this;

                    vm.protocolo = id;

                    vm.headerItems = [{
                        name: 'Abastecimentos'
                    }];

                    vm.gridAbastecimentosOptions = {
                        data: [],
                        onRegisterApi: BaseService.dataGrid.getGridApiRegistration("gridAbastecimentosOptions"),
                        dataSource: {
                            autoBind: true,
                            url: "Abastecimento/ConsultarGridProtocoloAbastecimento",
                            params: function () {
                                return {       
                                    protocolo: vm.protocolo
                                }
                            },
                        },
                        columnDefs: [{
                            name: 'Codigo',
                            displayName: 'Código',
                            width: 80,
                            field: 'id',
                            serverField: 'id',
                            primaryKey: true,
                            type: 'number',
                            enableFiltering: true
                        },
                        {
                            name: 'Combustivel',
                            displayName: 'Combustível',
                            width: 140,
                            field: 'combustivel',
                            serverField: 'combustivel',
                            enableFiltering: false,
                            enableSorting: false
                        },
                        {
                            name: 'Valor abastecimento',
                            displayName: 'Valor abastecimento',
                            width: 140,
                            field: 'valorAbastecimento',
                            serverField: 'valorAbastecimento',
                            cellTemplate: '<div class="ui-grid-cell-contents">\
                                                <input type="text" ng-model="row.entity.valorAbastecimento" readonly\
                                                        class="no-borders" style="background: none;" ui-money-mask="3" />\
                                        </div>',
                            enableFiltering: false,
                            enableSorting: false
                        },
                        {
                            name: 'ItemNotaFiscal',
                            displayName: 'Item NF',
                            width: 140,
                            field: 'itemNotaFiscal',
                            serverField: 'itemNotaFiscal',
                            enableFiltering: false,
                            enableSorting: false
                        },
                        {
                            name: 'PedidoSap',
                            displayName: 'Pedido SAP',
                            width: 140,
                            field: 'pedidoSap',
                            serverField: 'pedidoSap',
                            enableFiltering: false,
                            enableSorting: false
                        }]
                    };
                
                    vm.fechar = function () {
                        $uibModalStack.dismissAll();
                    }
                    
                },
                controllerAs: 'vm',
                keyboard: true,
                size: 'lg',
                resolve: {
                    id: id
                }
            }).result.then(function () {
            });
        };

        // Controle de aba!!
        $scope.$on('$stateChangeStart', function () {
            PersistentDataService.store('PainelPedidosPendentesController', vm, "Painel pedidos pendentes", "PainelPedidosPendentesController", "painel-pedidos-pendentes.index");
        });

        var selfScope = PersistentDataService.get('PainelPedidosPendentesController');
       if (angular.isDefined(selfScope)) {
            angular.extend(vm, selfScope.data);

            $timeout(function () {
                vm.gridOptions.dataSource.refresh();
            }, 450);
        }
    }
})();
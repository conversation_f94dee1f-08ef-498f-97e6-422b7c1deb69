(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('TransacoesPagamentoHistoricoController', TransacoesPagamentoHistoricoController);

        TransacoesPagamentoHistoricoController.inject = ['BaseService', '$rootScope', 'toastr','$stateParams', '$scope', 'PersistentDataService', '$timeout', '$state', '$uibModal', 'PERFIL_ADMINISTRADOR', 'JsonUtilsService'];

    function TransacoesPagamentoHistoricoController(BaseService, $rootScope, toastr, $scope, $stateParams, PersistentDataService, $timeout, $state, $uibModal, PERFIL_ADMINISTRADOR, JsonUtilsService) {
        var vm = this;
        vm.headerItems = [{
            name: 'Movimentações'
        }, {
            name: 'Painel de pagamento viagem', 
            link: 'viagens.index',
        }, {
            name: '<PERSON>game<PERSON><PERSON>', 
            link: 'viagens.pagamentos-viagem({link:'+ $stateParams.viagem+'})'
        }, {
            name: $stateParams.link == 'editar' ? 'Detalhes pagamento Histórico' : 'Detalhes pagamento Histórico'
        }];

        vm.onClickVoltar = function (wizard) {
            var ativoIndex = wizard.getActivePosition();
            if (ativoIndex == 1)
                $state.go('viagens.pagamentos-viagem', {
                    link: $stateParams.viagem,
                });

            wizard.go(ativoIndex - 1);
        };

        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.perfil == PERFIL_ADMINISTRADOR;
        };
        
        vm.codPagamentoHistorico = $stateParams.pagamentoHistorico;
        vm.codPagamentoEvento = $stateParams.pagamentoEvento;
        vm.logsPagamentoHistoricoObj = {};

        init();
        function init() {
            BaseService.post('Viagem', 'ConsultarTransacoesPagamentoHistorico', {
                PagamentoHistoricoId: vm.codPagamentoHistorico,
                PagamentoEventoId: vm.codPagamentoEvento
            }).then(function (response) {
                if (!response.success) {
                    toastr.error(response.message);
                    $uibModalInstance.close();
                    return;
                } else {
                    vm.logsPagamentoHistoricoObj = response.data.logsPagamentoHistorico;

                     // Aplicar conversões de enum nos JSONs usando o serviço compartilhado
                    if (vm.logsPagamentoHistoricoObj.jsonEnvio) {
                        var conversions = {
                            'Tipo': 'tipoPagamentoEvento',
                            'FormaPagamento': 'formaPagamentoEvento',
                            'TipoConta': 'tipoConta',
                            'Status': 'status'
                        };
                        vm.logsPagamentoHistoricoObj.jsonEnvio = JsonUtilsService.convertEnumsInJson(vm.logsPagamentoHistoricoObj.jsonEnvio, conversions);
                    }

                    if (vm.logsPagamentoHistoricoObj.jsonRetorno) {
                        var retornoConversions = {
                            'Tipo': 'tipoPagamentoEvento',
                            'FormaPagamento': 'formaPagamentoEvento',
                            'TipoConta': 'tipoConta',
                            'StatusPagamento': 'status',
                            'Status': 'status',
                            'StatusViagem': 'statusViagem'
                        };
                        vm.logsPagamentoHistoricoObj.jsonRetorno = JsonUtilsService.convertEnumsInJson(vm.logsPagamentoHistoricoObj.jsonRetorno, retornoConversions);
                    }

                    if (vm.logsPagamentoHistoricoObj.jsonRetornoCancelamento) {
                        var retornoCancelamentConversions = {
                            'Evento': 'eventoCancelamento'
                        };
                        vm.logsPagamentoHistoricoObj.jsonRetornoCancelamento = JsonUtilsService.convertEnumsInJson(vm.logsPagamentoHistoricoObj.jsonRetornoCancelamento, retornoCancelamentConversions);
                    }
                    
                }
            });
        }

        vm.VerTransacao = function (idTransacao) {
            $uibModal.open({
                animation: true,
                ariaLabelledBy: 'modal-title',
                ariaDescribedBy: 'modal-body',
                templateUrl: 'app/entities/viagens/detalhe-transacao/modal-detalhe-transacao-pagamento.html',
                controller: 'ModalDetalhesTransacaoPagamentoController',
                controllerAs: 'vm',
                keyboard: true,
                size: 'lg',
                windowClass: 'custom-modal-width',
                resolve: {
                    idTransacao: function() {
                        return idTransacao;
                    }
                }
            });
        };

        // Usar o serviço compartilhado para funções JSON
        vm.jsonUtils = JsonUtilsService;

        vm.copiar = function (campo) {
            var jsonArray = [
                vm.logsPagamentoObj.jsonEnvio,
                vm.logsPagamentoObj.jsonRetorno,
                vm.logsPagamentoObj.jsonEnvioCancelamento,
                vm.logsPagamentoObj.jsonRetornoCancelamento
            ];

            var labels = [
                'JSON Envio',
                'JSON Retorno',
                'JSON Envio Cancelamento',
                'JSON Retorno Cancelamento'
            ];

            JsonUtilsService.copyJsonByIndex(campo, jsonArray, labels);
        }

        vm.gridOptions = {
            data: [],
            onRegisterApi: BaseService.dataGrid.defaultOnRegisterApi,
            dataSource: {
                url: "Viagem/ConsultarTransacoesPagamentoHistorico",
                params: function () {
                    return {
                        PagamentoHistoricoId: vm.codPagamentoHistorico,
                        PagamentoEventoId: vm.codPagamentoEvento
                    }
                }
            },
            columnDefs: [
                {
                    name: 'Ações',
                    width: '100',
                    cellTemplate: '<div class="ui-grid-cell-contents" align="center" title="TOOLTIP">\
                        <button tooltip-placement="right" uib-tooltip="Visualizar detalhes" type="button" \
                            ng-class="{ \'btn btn-xs btn-info\': true }" \
                            ng-click="grid.appScope.vm.VerTransacao(row.entity.transacaoId)">\
                            <i class="fa fa-eye"></i>\
                        </button>\
                    </div>'
                },
                {
                    name: 'Codigo',
                    displayName: 'Código',
                    width: '*',
                    minWidth: 120,
                    field: 'transacaoId',
                    type: 'number',
                    enableFiltering: true
                },
                {
                    name: 'Status',
                    displayName: 'Status',
                    width: '*',
                    minWidth: 120,
                    field: 'status',
                    serverField: 'Status',
                    enableGrouping: false,
                    enableFiltering: true,
                    enum: true,
                    enumTipo: 'EStatusPagamentoEvento'
                },
                {
                    name: 'FormaPagamento',
                    displayName: 'Forma Pagamento',
                    width: '*',
                    minWidth: 150,
                    field: 'formaPagamento',
                    serverField: 'FormaPagamento',
                    enableGrouping: false,
                    enableFiltering: true,
                    enum: true,
                    enumTipo: 'EFormaPagamentoEvento'
                },
                {
                    name: 'Valor',
                    displayName: 'Valor',
                    width: '*',
                    minWidth: 120,
                    field: 'valor',
                    type: 'number',
                    enableFiltering: true
                },
                {
                    name: 'CodigoContaOrigem',
                    displayName: 'Id Conta Origem',
                    width: '*',
                    minWidth: 200,
                    field: 'codigoContaOrigem',
                    type: 'number',
                    enableFiltering: true
                },
                {
                    name: 'CodigoContaDestino',
                    displayName: 'Id Conta Destino',
                    width: '*',
                    minWidth: 200,
                    field: 'codigoContaDestino',
                    type: 'number',
                    enableFiltering: true
                },
                {
                    name: 'ChavePix',
                    displayName: 'Chave Pix',
                    width: '*',
                    minWidth: 220,
                    field: 'chavePix',
                    type: 'text',
                    enableFiltering: false
                },
                {
                    name: 'CodigoBanco',
                    displayName: 'Código Banco',
                    width: '*',
                    minWidth: 120,
                    field: 'codigoBanco',
                    type: 'number',
                    enableFiltering: true
                },
                {
                    name: 'Agencia',
                    displayName: 'Agência',
                    width: '*',
                    minWidth: 150,
                    field: 'codigoBanco',
                    type: 'number',
                    enableFiltering: true
                },
                {
                    name: 'Conta',
                    displayName: 'Conta',
                    width: '*',
                    minWidth: 150,
                    field: 'conta',
                    type: 'text',
                    enableFiltering: true
                },
                {
                    name: 'TipoConta',
                    displayName: 'Tipo Conta',
                    width: '*',
                    minWidth: 150,
                    field: 'tipoConta',
                    type: 'text',
                    enableFiltering: true,
                    enum: true,
                    enumTipo: 'ETipoContaDock',
                    pipe: function (input) {
                        return tipoContaDock(input)
                    },
                    cellTemplate: '<div class="ui-grid-cell-contents" align="left">\
                                            <p ng-show="row.entity.tipoConta === 1"> Corrente </p>\
                                            <p ng-show="row.entity.tipoConta === 2"> Poupança </p>\
                                            <p ng-show="row.entity.tipoConta === 3"> Salário </p>\
                                       </div>'
                },
                {
                    name: 'DataBaixa',
                    displayName: 'Data Baixa',
                    minWidth: 150,
                    field: 'dataBaixa',
                    type: 'date',
                    enableFiltering: false
                },
                {
                    name: 'DataCadastro',
                    displayName: 'Data Cadastro',
                    minWidth: 150,
                    field: 'dataCadastro',
                    type: 'date',
                    enableFiltering: false
                },
                {
                    name: 'DataAlteracao',
                    displayName: 'Data Alteração',
                    minWidth: 150,
                    field: 'dataAlteracao',
                    type: 'date',
                    enableFiltering: false
                },
                {
                    name: 'Data Cancelamento',
                    displayName: 'Data Cancelamento',
                    minWidth: 150,
                    field: 'dataCancelamento',
                    type: 'date',
                    enableFiltering: false
                }
            ]
        };

        var selfScope = PersistentDataService.get('PagamentosViagemController');
        var filho = PersistentDataService.get('TransacoesPagamentoHistoricoController');
        if (angular.isDefined(filho)) {
            $timeout(function () {
                $state.go('viagens.pagamentos.transacoes.transacoes-pagamento-historico', {
                    link: filho.data.transacao.id > 0 ? 'editar' : 'novo'
                });
            }, 50);
        } else if (angular.isDefined(selfScope)) {
            angular.extend(vm, selfScope.data);

            $timeout(function () {
                vm.gridOptions.dataSource.refresh();
            }, 450);
        }
       

    }
})();
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using JetBrains.Annotations;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.BBC.Domain.Models.Portador.Exeptions;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Models;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Models.Validator;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Domain.Models.Portador
{
    public class Portador : Entity<Portador, int, NotImplementedEntityValidator<Portador>>
    {
        private static readonly Regex _regex = new Regex(@"^(?!\.)(""([^""\r\\]|\\[""\r\\])*""|"
                                                         + @"([-a-z0-9!#$%&'*+/=?^_`{|}~]|(?<!\.)\.)*)(?<!\.)"
                                                         + @"@[a-z0-9][\w\.-]*[a-z0-9]\.[a-z][a-z\.]*[a-z]$",
            RegexOptions.IgnoreCase);

        public string Nome { get; set; }
        public string CpfCnpj { get; set; }
        public string Email { get; set; }
        public string Endereco { get; set; }
        public int? EstadoId { get; set; }
        public int? CidadeId { get; set; }
        public string Cep { get; set; }
        public int? EnderecoNumero { get; set; }
        public string Telefone { get; set; }
        public int? UsuarioCadastroId { get; set; }
        public DateTime DataCadastro { get; set; }
        public string Celular { get; set; }
        public string Bairro { get; set; }
        public string Complemento { get; set; }
        public int Ativo { get; set; }
        public string RNTRC { get; set; }
        public string NumeroCNH { get; set; }
        public int? UsuarioBloqueioId { get; set; }
        public DateTime? DataBloqueio { get; set; }
        public int? UsuarioDesbloqueioId { get; set; }
        public DateTime? DataDesbloqueio { get; set; }
        public string RazaoSocial { get; set; }
        public string NomePai { get; set; }
        public string NomeMae { get; set; }
        public ESexo? Sexo { get; set; }
        public string NumeroIdentidade { get; set; }
        public string OrgaoEmissor { get; set; }
        public string UfEmissao { get; set; }
        public DateTime? EmissaoIdentidade { get; set; }
        public string InscricaoEstadual { get; set; }
        public DateTime? DataAberturaEmpresa { get; set; }
        public string FormaConstituicao { get; set; }
        public string Cnae { get; set; }
        public ETipoPessoa TipoPessoa { get; set; }
        public DateTime? DataNascimento { get; set; }
        public string NaturezaJuridica{ get; set; }
        public EAtividade? Atividade { get; set; }
        public string Placa{ get; set; }
        public int? CarretaId { get; set; }
        public int? Carreta2Id { get; set; }
        public int? Carreta3Id { get; set; }
        public int? ControlaAbastecimentoCentroCusto { get; set; }
        public int? EmpresaIdFrota { get; set; }
        
        //colunas adicionais para api mobile
        public string SenhaApi { get; set; }
        public int QuantidadeErroSenha { get; set; } = 0;
        public int? UsuarioDesbloqueioMobileId { get; set; }
        public DateTime DataDesbloqueioMobile { get; set; }
        public DateTime DataBloqueioMobile { get; set; }
        
        //Colunas adicionais para controle de acesso mobile
        public DateTime? DataCriacaoSenha { get; set; }
        public DateTime? DataUltimoAcesso { get; set; }
        public int? SenhaProvisoria { get; set; } = 0;
        
        //Colunas adicionais para tranportador
        public EVisibilidadePortador Visibilidade { get; set; } = EVisibilidadePortador.Portador;
        public int CiotTacAgregado { get; set; }
        
        
        
        public EStatusPortador Status { get; set; }
        
        public string MotivoCancelamento { get; set; }

        public int? UsuarioCancelamentoId { get; set; }
        
        public DateTime? DataCancelamento { get; set; }
        
        public string CNH { get; set; }
        
        public DateTime? DataEmissaoCNH { get; set; }
        
        public DateTime? DataVencimentoCNH { get; set; }
        
        #region Propriedades de Navegacao

        public virtual ICollection<PortadorRepresentanteLegal.PortadorRepresentanteLegal> PortadorRepresentanteLegal { get; set; }
        public virtual ICollection<PortadorCentroCusto.PortadorCentroCusto> PortadorCentroCusto { get; set; }
        public virtual Estado.Estado Estado { get; set; }
        public virtual Cidade.Cidade Cidade { get; set; }
        public virtual Usuario.Usuario UsuarioCadastro { get; set; }
        public virtual Usuario.Usuario UsuarioDesbloqueio { get; set; }
        public virtual Usuario.Usuario UsuarioDesbloqueioMobile { get; set; }
        public virtual Empresa.Empresa EmpresaFrota { get; set; }
        public virtual Usuario.Usuario UsuarioBloqueio { get; set; }
        public virtual Veiculo.Veiculo Carreta { get; set; }
        public virtual Veiculo.Veiculo Carreta2 { get; set; }
        public virtual Veiculo.Veiculo Carreta3 { get; set; }
        public virtual PercentualTransferencia.PercentualTransferencia PercentualTransferencia { get; set; }
        
        public virtual Usuario.Usuario UsuarioCancelamento { get; set; }
        
        #endregion

        public void ValidarCriacao()
        {
            if (string.IsNullOrEmpty(Nome))
                throw new PortadorInvalidException("Nome é obrigatório");

            if (Nome.Length > 200)
                throw new PortadorInvalidException("Nome permite apenas 200 caracteres");

            if (Email != null)
            {
                if (Email.Length > 200)
                    throw new PortadorInvalidException("E-mail permite apenas 200 caracteres");

                if (Email.Length > 0 && !_regex.IsMatch(Email))
                    throw new PortadorInvalidException("E-mail inválido");

            }

            if (string.IsNullOrEmpty(CpfCnpj))
                throw new PortadorInvalidException("CPF/CNPJ é obrigatório");

            if (CpfCnpj.OnlyNumbers().Length > 11)
            {
                if (!CpfCnpj.OnlyNumbers().ValidaCNPJ())
                    throw new PortadorInvalidException("CPF/CNPJ inválido");
            }
            else
            {
                if (!CpfCnpj.OnlyNumbers().ValidaCPF())
                    throw new PortadorInvalidException("CPF/CNPJ inválido");
            }

            if (string.IsNullOrEmpty(Endereco))
                throw new PortadorInvalidException("Endereço é obrigatório");

            if (Endereco.Length > 200)
                throw new PortadorInvalidException("Endereço permite apenas 200 caracteres");

            if (string.IsNullOrEmpty(Bairro))
                throw new PortadorInvalidException("Bairro é obrigatório");

            if (!string.IsNullOrEmpty(Complemento))
                if (Complemento.Length > 100)
                    throw new PortadorInvalidException("Complemento permite apenas 100 caracteres");

            if (!CidadeId.HasValue || CidadeId == 0)
                throw new PortadorInvalidException("Cidade é obrigatório");
        }

        public void ValidarCriacaoFrota()
        {
            if (string.IsNullOrEmpty(Nome))
                throw new PortadorInvalidException("Nome é obrigatório");

            if (Nome.Length > 200)
                throw new PortadorInvalidException("Nome permite apenas 200 caracteres");

            if (Email != null)
            {
                if (Email.Length > 200)
                    throw new PortadorInvalidException("E-mail permite apenas 200 caracteres");

                if (Email.Length > 0 && !_regex.IsMatch(Email))
                    throw new PortadorInvalidException("E-mail inválido");

            }

            if (string.IsNullOrEmpty(CpfCnpj))
                throw new PortadorInvalidException("CPF/CNPJ é obrigatório");

            if (CpfCnpj.OnlyNumbers().Length > 11)
            {
                if (!CpfCnpj.OnlyNumbers().ValidaCNPJ())
                    throw new PortadorInvalidException("CPF/CNPJ inválido");
            }
            else
            {
                if (!CpfCnpj.OnlyNumbers().ValidaCPF())
                    throw new PortadorInvalidException("CPF/CNPJ inválido");
            }
            
            if (string.IsNullOrEmpty(CNH))
                throw new PortadorInvalidException("CNH é obrigatório");
            
            if (DataEmissaoCNH == null)
                throw new PortadorInvalidException("Data emissão CNH é obrigatória");
            
            if (DataVencimentoCNH == null)
                throw new PortadorInvalidException("Data vencimento CNH é obrigatória");
            
            if (DataEmissaoCNH > DataVencimentoCNH)
                throw new PortadorInvalidException("A data de emissão da CNH não pode ser maior que a data de vencimento");
            
            Bairro = "";
        }
        
        public void ValidarIntegrar()
        {
            if (TipoPessoa.HasFlag(ETipoPessoa.Fisica))
            {
                if (string.IsNullOrEmpty(NomeMae))
                    throw new PortadorInvalidException("Nome mãe é obrigatório");

                if (string.IsNullOrEmpty(NomePai))
                    throw new PortadorInvalidException("Nome pai é obrigatório");

                if (!Sexo.HasValue)
                    throw new PortadorInvalidException("Sexo é obrigatório");

                if (string.IsNullOrEmpty(NumeroIdentidade))
                    throw new PortadorInvalidException("Número identidade é obrigatório");

                if (string.IsNullOrEmpty(OrgaoEmissor))
                    throw new PortadorInvalidException("Orgão emissor é obrigatório");
                
                if (string.IsNullOrEmpty(UfEmissao))
                    throw new PortadorInvalidException("UF emissão é obrigatório");
                
                if (!EmissaoIdentidade.HasValue)
                    throw new PortadorInvalidException("Emissão identidade é obrigatório");
            }

            if (TipoPessoa.HasFlag(ETipoPessoa.Juridica))
            {
                if (string.IsNullOrEmpty(RazaoSocial))
                    throw new PortadorInvalidException("Razão social é obrigatório");

                if (string.IsNullOrEmpty(InscricaoEstadual))
                    throw new PortadorInvalidException("Inscrição estadual é obrigatório");

                if (!DataAberturaEmpresa.HasValue)
                    throw new PortadorInvalidException("Data abertura empresa é obrigatório");

                if (string.IsNullOrEmpty(FormaConstituicao))
                    throw new PortadorInvalidException("Forma constituição é obrigatório");
            }
        }



        public bool SenhaCorreta(string senhaValidar)
        {
            if (senhaValidar.IsEmpty())
                return false;
            
            return senhaValidar.GetHashSha1() == SenhaApi;
        }
        
        
        public bool Cancelado()
        {
            return Status == EStatusPortador.Cancelado;
        }
        
        public bool Bloqueado()
        {
            return Ativo == 0 || Status == EStatusPortador.Bloqueado;
        }

        public bool TentativasExcedida(int numeroTentativas)
        {
            return QuantidadeErroSenha >= numeroTentativas;
        }

        public bool UsaSenhaProvisoria()
        {
            return SenhaProvisoria == 1 && DataCriacaoSenha != null;
        }

        public bool UltrapassouPeriodoSenhaProvisoria(int lPeriodoMaximo)
        {
            return DataCriacaoSenha != null && DataCriacaoSenha.Value.AddMinutes(lPeriodoMaximo) <
                DateTime.Now;
        }

        public bool Inativo(int lPeriodoMaximo)
        {
            return DataUltimoAcesso != null &&
                DataUltimoAcesso.Value.AddDays(lPeriodoMaximo) < DateTime.Now &&
                SenhaProvisoria == 0;
        }

        public string StatusName()
        {
            if (Status == EStatusPortador.Normal)
            {
                return Ativo == 1 ? "Ativo" : "Inativo";
            }
            return Status.DescriptionAttr();
        }
    }
}

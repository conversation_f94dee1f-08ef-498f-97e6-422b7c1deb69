﻿using System;
using SistemaInfo.BBC.Domain.External.CIOT.DTO;

namespace SistemaInfo.BBC.Domain.Contracts.Operacoes;

public class ConsultarTacAgregadoResponseMessage
{
    public ConsultarTacAgregadoResponseMessage()
    {
    }

    public ConsultarTacAgregadoResponseMessage(bool sucesso, string excecao)
    {
        Sucesso = sucesso;
        Erro = new Excecao()
        {
            Mensagem = excecao
        };
    }
    public bool Sucesso { get; set; }

    public Excecao Erro { get; set; }

    public string CIOT { get; set; }
        
    public DateTime DataDeclaracao { get; set; }
        
    public string SenhaAlteracao { get; set; }
        
    public string CodigoVerificador { get; set; }
    public string StatusCiot { get; set; }
    public string TipoCiot { get; set; }
}
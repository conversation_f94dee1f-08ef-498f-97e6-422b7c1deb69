using System.Collections.Generic;
using AutoMapper;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Domain.Models.Portador.Commands.Base;
using SistemaInfo.BBC.Domain.Models.PortadorCentroCusto.Commands;
using SistemaInfo.BBC.Domain.Models.UsuarioFrota.Base;

namespace SistemaInfo.BBC.Domain.Models.UsuarioFrota.Commands
{
    public class UsuarioFrotaSalvarCommand : BaseUsuarioFrotaCommand
    {
        public List<PortadorCentroCustoSalvarComRetornoCommand> PortadorCentroCusto { get; set; }
        
        public void ValidarCadastro()
        {
            var portador = Mapper.Map<BaseUsuarioFrotaCommand, Portador.Portador>(this);
            portador.ValidarCriacaoFrota();
        }
        
        public void ValidarIntegrar()
        {
            var portador = Mapper.Map<BaseUsuarioFrotaCommand, Portador.Portador>(this);
            portador.ValidarIntegrar();
        }
    }

    
    public class UsuarioFrotaSalvarComRetornoCommand : UsuarioFrotaSalvarCommand
    {
        
    }
    
   
}
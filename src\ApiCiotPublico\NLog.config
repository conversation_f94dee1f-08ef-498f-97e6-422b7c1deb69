<?xml version="1.0" encoding="utf-8" ?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      autoReload="true"
      internalLogLevel="Warn"
      internalLogFile="c:\temp\nlog\internal-nlog-ApiCiot.log">

  <extensions>
    <add assembly="NLog.Web.AspNetCore"/>
  </extensions>

  <targets async="false">
    <target
        name="file"
        xsi:type="File"
        layout="${longdate}|${callsite}|${level}: ${message}${onexception:${newline}${newline}Exception\: ${exception:format=ToString}${newline}${newline}Stack Trace\: ${exception:format=StackTrace}${newline}}"
        fileName="${basedir}/logs/ApiCiot.log"
        archiveFileName="${basedir}/logs/Api_{#}.log"
        archiveDateFormat="yyyy-MM-dd"
        archiveEvery="Day"
        archiveNumbering="Date"
        maxArchiveFiles="180"
        encoding="utf-8"
        concurrentWrites="true"
        concurrentWriteAttempts="1"
        concurrentWriteAttemptDelay="10"
        lineEnding="CRLF" />

    <target
        name="fatal"
        xsi:type="File"
        layout="############################################################# FATAL EXCEPTION ######################################################${newline}${longdate}|${callsite}|${level}: ${message}${onexception:${newline}${newline}Exception\: ${exception:format=ToString}${newline}${newline}Stack Trace\: ${exception:format=StackTrace}${newline}}************************************************************************************************************************************"
        fileName="${basedir}/logs/ApiCiot.log"
        archiveFileName="${basedir}/logs/Api_{#}.log"
        archiveDateFormat="yyyy-MM-dd"
        archiveEvery="Day"
        archiveNumbering="Date"
        maxArchiveFiles="180"
        encoding="utf-8"
        concurrentWrites="true"
        concurrentWriteAttempts="1"
        concurrentWriteAttemptDelay="10"
        lineEnding="CRLF" />

    <target
        name="fileLine"
        xsi:type="File"
        layout="${longdate} ------------------------------------------------------------------------------------------------------------"
        fileName="${basedir}/logs/ApiCiot.log"
        archiveFileName="${basedir}/logs/Api_{#}.log"
        archiveDateFormat="yyyy-MM-dd"
        archiveEvery="Day"
        archiveNumbering="Date"
        maxArchiveFiles="180"
        encoding="utf-8"
        concurrentWrites="true"
        concurrentWriteAttempts="1"
        concurrentWriteAttemptDelay="10"
        lineEnding="CRLF"/>

    <target
      name="buslog"
      xsi:type="File"
      layout="${longdate}|${message}${onexception:${newline}${newline}Exception\: ${exception:format=ToString}${newline}${newline}Stack Trace\: ${exception:format=StackTrace}${newline}}"
      fileName="${basedir}/logs/buslog_Api.log"
      archiveFileName="${basedir}/logs/buslog_Api_{#}.log"
      archiveDateFormat="yyyy-MM-dd"
      archiveEvery="Day"
      archiveNumbering="Date"
      maxArchiveFiles="2"
      encoding="utf-8"
      concurrentWrites="true"
      concurrentWriteAttempts="1"
      concurrentWriteAttemptDelay="10"
      lineEnding="CRLF" />

    <target
      name="ApplicationEvents"
      xsi:type="File"
      layout="${longdate}|${level}: ${message}${onexception:${newline}${newline}Exception\: ${exception:format=ToString}${newline}${newline}Stack Trace\: ${exception:format=StackTrace}${newline}}"
      fileName="${basedir}/logs/ApplicationEvents.log"
      encoding="utf-8"
      concurrentWrites="true"
      concurrentWriteAttempts="1"
      concurrentWriteAttemptDelay="10"
      lineEnding="CRLF" />
  </targets>
  <rules>
    <logger name="ApplicationEvents" writeTo="ApplicationEvents" final="true" />
    <logger name="buslog" minlevel="Debug" writeTo="buslog" final="true" />
    <logger name="FileLine" minlevel="Debug" writeTo="fileLine" final="true" />
    <logger name="*" level="Fatal" writeTo="fatal" />
    <logger name="*" levels="Trace,Debug,Info,Warn,Error" writeTo="file" />
  </rules>
</nlog>


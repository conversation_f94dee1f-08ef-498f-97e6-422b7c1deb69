using System;
using System.Threading.Tasks;
using JetBrains.Annotations;
using Microsoft.AspNetCore.Mvc;
using SistemaInfo.BBC.Application.Interface.Viagem;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.PagamentoEvento;
using SistemaInfo.BBC.Application.Objects.Web.Transacao;
using SistemaInfo.BBC.Application.Objects.Web.Viagem;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Web.Attributes;
using SistemaInfo.BBC.Web.Controllers.Base;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Web.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [Route("Viagem")]
    public class ViagemController : WebControllerBase<IViagemAppService>
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="engine"></param>
        /// <param name="appService"></param>
        public ViagemController(IAppEngine engine, IViagemAppService appService) : base(engine, appService)
        {
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("ConsultarGridViagem")]
        [Menu(new[] { EMenus.Viagens })]
        public async Task<JsonResult> ConsultarGridViagem([FromBody] ConsultarGridViagemRequest request)
        {
            var response = await AppService.ConsultarGridViagem(request);
            return ResponseBase.ResponderSucesso(response);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpGet("ConsultarViagensCiot")]
        [Menu(new[] { EMenus.PainelCiot })]
        public async Task<JsonResult> ConsultarGridViagem([NotNull] string ciot)
        {
            if (ciot == null) throw new ArgumentNullException(nameof(ciot));
            var response = await AppService.ConsultarViagensCiot(ciot);
            return ResponseBase.Responder(response);
        }
        
           
        /// <summary>
        /// 
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("ConsultarPagamentosViagem")]
        [Menu(new[] { EMenus.Viagens })]
        public async Task<JsonResult> ConsultarPagamentosViagem([FromBody] ConsultarGridPagamentosViagemRequest request)
        {
            var response = await AppService.ConsultarPagamentosViagem(request.ViagemId, request.Take, request.Page, request.Order, request.Filters);
            return ResponseBase.ResponderSucesso(response);
        }


        /// <summary>
        /// 
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("ConsultarPagamentosHistoricoViagem")]
        [Menu(new[] { EMenus.Viagens })]
        public JsonResult ConsultarPagamentosHistoricoViagem([FromBody] ConsultarGridPagamentosViagemRequest request) =>
            ResponseBase.Responder(AppService.ConsultarPagamentosHistoricoViagem(request.ViagemId, request.Take, request.Page, request.Order, request.Filters));
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("ConsultarTransacoesPagamento")]
        [Menu(new[] { EMenus.Viagens })]
        public async Task<JsonResult> ConsultarTransacoesPagamento([FromBody] ConsultarGridTransacoesPagamentoRequest request)
        {
            var response = await AppService.ConsultarTransacoesPagamento(request.PagamentoId, request.Take, request.Page, request.Order, request.Filters);
            return ResponseBase.ResponderSucesso(response);
        }
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("ConsultarTransacoesPagamentoHistorico")]
        [Menu(new[] { EMenus.Viagens })]
        public async Task<JsonResult> ConsultarTransacoesPagamentoHistorico([FromBody] ConsultarGridTransacoesPagamentoHistoricoRequest request)
        {
            var response = await AppService.ConsultarTransacoesPagamentoHistorico(request.PagamentoHistoricoId, request.PagamentoEventoId, request.Take, request.Page, request.Order, request.Filters);
            return ResponseBase.ResponderSucesso(response);
        }
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="idTransacao"></param>
        /// <returns></returns>
        [HttpGet("ConsultarTransacaoPorId")]
        [Menu(new[] { EMenus.Viagens })]
        public async Task<JsonResult> ConsultarTransacaoPorId(int idTransacao) =>
            ResponseBase.Responder(await AppService.ConsultarTransacaoPorId(idTransacao));
        
        /// <summary>
        ///
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("ConsultarGridViagemCombo")]
        [Menu(new[] { EMenus.PainelCiot })]
        public async Task<JsonResult> ConsultarGridViagemCombo([FromBody] BaseGridRequest request)
            => ResponseBase.Responder(await AppService.ConsultarGridViagemCombo(request.Take, request.Page, request.Order, request.Filters));

        /// <summary>
        /// Consulta grid de pagamentos antecipados
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("ConsultarGridPagamentosAntecipados")]
        [Menu(new[] { EMenus.Viagens, EMenus.CentralPendenciasAntecipados })]
        public async Task<JsonResult> ConsultarGridPagamentosAntecipados([FromBody] ConsultaGridPagamentosAntecipadosRequest request)
        {
            try
            {
                var response = await AppService.ConsultarGridPagamentosAntecipados(request);
                return ResponseBase.ResponderSucesso(response);
            }
            catch (Exception ex)
            {
                return ResponseBase.ResponderErro($"Erro ao consultar pagamentos antecipados: {ex.Message}");
            }
        }

        /// <summary>
        /// Consulta grid central de pendência de pagamentos antecipados
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("ConsultarGridCentralPendenciaPagamentosAntecipados")]
        [Menu(new[] { EMenus.Viagens, EMenus.CentralPendenciasAntecipados })]
        public async Task<JsonResult> ConsultarGridCentralPendenciaPagamentosAntecipados([FromBody] ConsultaGridCentralPendenciaPagamentosAntecipadosRequest request)
        {
            try
            {
                var response = await AppService.ConsultarGridCentralPendenciaPagamentosAntecipados(request);
                return ResponseBase.ResponderSucesso(response);
            }
            catch (Exception ex)
            {
                return ResponseBase.ResponderErro($"Erro ao consultar central de pendência de pagamentos antecipados: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="idViagem"></param>
        /// <returns></returns>
        [HttpGet("ConsultarValoresViagem")]
        [Menu(new[] { EMenus.Viagens })]
        public async Task<JsonResult> ConsultarValoresViagem(int idViagem) =>
            ResponseBase.Responder(await AppService.ConsultarValoresViagem(idViagem));
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="idViagem"></param>
        /// <returns></returns>
        [HttpGet("ConsultarVeiculosViagem")]
        [Menu(new[] { EMenus.Viagens })]
        public async Task<JsonResult> ConsultarVeiculosViagem(int idViagem) =>
            ResponseBase.Responder(await AppService.ConsultarVeiculosViagem(idViagem));



    }
}
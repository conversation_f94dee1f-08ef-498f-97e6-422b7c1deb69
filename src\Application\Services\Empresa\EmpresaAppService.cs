using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using AutoMapper.QueryableExtensions;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using NLog;
using Org.BouncyCastle.Crypto.Engines;
using SistemaInfo.BBC.Application.Email.Empresa;
using SistemaInfo.BBC.Application.External.Conductor.Interface;
using SistemaInfo.BBC.Application.Helpers;
using SistemaInfo.BBC.Application.Interface.Empresa;
using SistemaInfo.BBC.Application.Interface.Parametros;
using SistemaInfo.BBC.Application.Interface.Portador;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.Ciot;
using SistemaInfo.BBC.Application.Objects.Web.Documentos;
using SistemaInfo.BBC.Application.Objects.Web.Empresa;
using SistemaInfo.BBC.Application.Objects.Web.Portador;
using SistemaInfo.BBC.Domain.Components.Email;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Domain.External.CIOT.Interface;
using SistemaInfo.BBC.Domain.External.Conductor.DTO.Cartao;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.BBC.Domain.Models.Abastecimento.Repository;
using SistemaInfo.BBC.Domain.Models.Documento.Commands;
using SistemaInfo.BBC.Domain.Models.Documento.Repository;
using SistemaInfo.BBC.Domain.Models.Empresa.Commands;
using SistemaInfo.BBC.Domain.Models.Empresa.Repository;
using SistemaInfo.BBC.Domain.Models.Portador.Commands;
using SistemaInfo.BBC.Domain.Models.Portador.Repository;
using SistemaInfo.BBC.Domain.Models.Usuario.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.Services.Empresa
{
    public class EmpresaAppService :
        AppService<Domain.Models.Empresa.Empresa, IEmpresaReadRepository, IEmpresaWriteRepository>, IEmpresaAppService
    {
        private readonly ICartaoAppService _cartaoAppService;
        private readonly INotificationEmailExecutor _notificationEmailExecutor;
        private readonly IParametrosAppService _parametrosAppService;
        private readonly IPortadorAppService _portadorAppService;
        private readonly ICiotClienteIpRepository _ciotClienteIpRepository;
        private readonly IAbastecimentoReadRepository _abastecimentoReadRepository;
        private readonly IUsuarioReadRepository _usuarioReadRepository;
        private readonly IPortadorReadRepository _portadorReadRepository;
        private readonly IDocumentoReadRepository _documentoReadRepository;
        
        public EmpresaAppService(IAppEngine engine,
            IEmpresaReadRepository readRepository,
            IEmpresaWriteRepository writeRepository,
            IUsuarioReadRepository usuarioReadRepository,
            INotificationEmailExecutor notificationEmailExecutor,
            ICartaoAppService cartaoAppService,
            IPortadorReadRepository portadorReadRepository,
            IParametrosAppService parametrosAppService,
            IPortadorAppService portadorAppService,
            ICiotClienteIpRepository ciotClienteIpRepository, 
            IAbastecimentoReadRepository abastecimentoReadRepository, 
            IDocumentoReadRepository documentoReadRepository)
            : base(engine, readRepository, writeRepository)
        {
            _usuarioReadRepository = usuarioReadRepository;
            _notificationEmailExecutor = notificationEmailExecutor;
            _cartaoAppService = cartaoAppService;
            _portadorReadRepository = portadorReadRepository;
            _parametrosAppService = parametrosAppService;
            _portadorAppService = portadorAppService;
            _ciotClienteIpRepository = ciotClienteIpRepository;
            _abastecimentoReadRepository = abastecimentoReadRepository;
            _documentoReadRepository = documentoReadRepository;
        }


        public async Task<RespPadrao> CadastreSe(EmpresaRequest empresaRequest)
        {
            try
            {
                var empresaCommand = Mapper.Map<EmpresaSaveCommand>(empresaRequest);

                empresaCommand.StatusCadastro = StatusCadastro.PendentedeValidacao.ToInt();

                var portadorEmpCommand = Mapper.Map<PortadorSalvarCommand>(empresaRequest);

                var lRetornoValida = await ValidaCadastreSe(empresaRequest, portadorEmpCommand);
                
                if (!lRetornoValida.sucesso)
                    return new RespPadrao
                    {
                        sucesso = false,
                        mensagem = lRetornoValida.mensagem
                    };

                var retornoEmpresa = await Engine.CommandBus.SendCommandAsync<Domain.Models.Empresa.Empresa>(empresaCommand);
                
                var codPortadorPj = await Repository.Command.SavePortadorPJ(portadorEmpCommand);

                //salvar na tabela portadorEmpresa
                await _portadorAppService.SalvarPortadorEmpresa(new PortadorEmpresaRequest
                {
                    EmpresaId = retornoEmpresa.Id,
                    PortadorId = codPortadorPj
                });

                /*if (empresaRequest.RepLegalList == null || !empresaRequest.RepLegalList.Any())
                {
                    return new RespPadrao
                    {
                        sucesso = true,
                        mensagem = ""
                    };
                }

                foreach (var repLegal in empresaRequest.RepLegalList)
                {
                    var portadorCommand = Mapper.Map<PortadorSalvarCommand>(repLegal);

                    var codPortadorPf = await Repository.Command.SavePortadorPF(portadorCommand);
                    await Repository.Command.SavePortadorRepLegal(empresaCommand, repLegal.CpfCnpj);

                    //salvar na tabela portadorEmpresa
                    await _portadorAppService.SalvarPortadorEmpresa(new PortadorEmpresaRequest
                    {
                        EmpresaId = retornoEmpresa.Id,
                        PortadorId = codPortadorPf
                    });
                }*/

                return new RespPadrao(true);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return new RespPadrao(false, "Erro ao cadastrar a Empresa: " + e.Message);
            }
        }

        public async Task<RespPadrao> Cadastrar(EmpresaRequest request)
        {
            try
            {
                var mensagem = "Cadastro de empresa atualizado. ";

                #region Valida e salva a request de empresa

                var empresaCommand = Mapper.Map<EmpresaSaveCommand>(request);
                await Engine.CommandBus.SendCommandAsync<Domain.Models.Empresa.Empresa>(empresaCommand);

                #endregion

                #region Cadastro da pessoa jurídica na Conductor

                var retornoCadastroConductor = await CadastrarPJConductor(request);

                if (!retornoCadastroConductor.sucesso) mensagem += "Não foi possível salvar a pessoa na Conductor. ";
                

                if (request.DocumentosEmpresa is not null)
                {
                    var resultCadastro = await CadastrarDocumentosEmpresa(request.Id, request.DocumentosEmpresa);
                    if (!resultCadastro.sucesso) mensagem += "Não foi possível cadastrar os documentos da empresa";
                }
                
                #endregion
                
                #region Salva os parâmetros

                if (request.TipoEmissaoCiot != null)
                {
                    _parametrosAppService.SaveParametro(empresaCommand.Id,
                        Domain.Models.Parametros.Parametros.TipoDoParametro.CodigoTipoEmissaoCiot,
                        request.TipoEmissaoCiot.ToString(), null,
                        Domain.Models.Parametros.Parametros.TipoDoValor.Number);
                }

                #endregion

                #region Envio de IPs de Clientes é api do CIOT

                if (request.CiotClienteIps.Count <= 0)
                {
                    return new RespPadrao
                    {
                        sucesso = true,
                        mensagem = mensagem
                    };
                }

                var ciotClienteIpsAdaptado = request.CiotClienteIps
                    .Select(ip => Mapper.Map(ip, new CiotClienteIpRequestAdaptado()))
                    .ToList();

                var ciotClienteIpsApiRequest = new CiotClienteIpApiRequest
                {
                    ClienteIps = ciotClienteIpsAdaptado,
                    CpfCnpj = request.Cnpj,
                    NomeFantasia = request.NomeFantasia,
                    RazaoSocial = request.RazaoSocial
                };

                var serializedClienteIpsApiRequest = JsonConvert.SerializeObject(ciotClienteIpsApiRequest);

                var retornoEnvioIps = await _ciotClienteIpRepository.SalvarClienteIps(serializedClienteIpsApiRequest);

                string respostaIp = null;
                
                if(retornoEnvioIps == null)
                {
                    respostaIp += "Erro ao realizar a integração de IPs no CIOT: O serviço está indisponível no momento.";
                }
                
                if(retornoEnvioIps != null && retornoEnvioIps.Sucesso == false)
                {
                    respostaIp += $"Erro ao realizar a integração de IPs no CIOT: {retornoEnvioIps.Mensagem}";
                }

                #endregion

                return new RespPadrao
                {
                    data = respostaIp,
                    sucesso = true,
                    mensagem = mensagem
                };
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return new RespPadrao(false, "Ocorreu um erro ao salvar a Empresa: " + e.Message);
            }
        }

        private async Task<RespPadrao> CadastrarDocumentosEmpresa(int empresaId, List<DocumentosEmpresaRequest> requestDocumentosEmpresa)
        {
            try
            {
                foreach (var documentosEmpresaRequest in requestDocumentosEmpresa)
                {
                    var lSaveCommand = Mapper.Map<DocumentoSalvarCommand>(documentosEmpresaRequest);
                    lSaveCommand.EmpresaId = empresaId;
                    await Engine.CommandBus.SendCommandAsync(lSaveCommand);
                }
                return new RespPadrao(true);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return new RespPadrao(false, e.Message);
            }
        }
        
        public async Task<RespPadrao> ExcluirDocumentosEmpresa(int id)
        {
            try
            {
                if (id == 0) 
                    return new RespPadrao(false);
                await Engine.CommandBus.SendCommandAsync(new DocumentoExcluirCommand {Id = id});
                return new RespPadrao(true);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return new RespPadrao(false, e.Message);
            }
        }

        public async Task<RespPadrao> CadastrarComRetornoAsync(EmpresaRequest request)
        {
            try
            {
                #region Valida e salva a request de empresa

                var command = Mapper.Map<EmpresaSaveComRetornoCommand>(request);
                var empresa = await Engine.CommandBus.SendCommandAsync<Domain.Models.Empresa.Empresa>(command);

                #endregion

                #region Salva PortadorEmpresa e Portador Pessoa Jurídica

                var portadorCommand = Mapper.Map<PortadorSalvarCommand>(request);

                await Repository.Command.SavePortadorPJ(portadorCommand);
                
                if (request.RepLegaisList != null)
                {
                    var cnpj = request.Cnpj.OnlyNumbers();
                    var portador = await _portadorReadRepository.GetByCpfCnpjAsync(cnpj);
                    
                    Repository.Command.RemoveRepresentanteLegal(portador?.Id ?? 0);

                    foreach (var repLegal in request.RepLegaisList)
                    {
                        await Repository.Command.SavePortadorRepLegal(command, repLegal.CpfCnpj);
                    }
                }

                #endregion
                
                #region Salva Documentos Recebedor Autorizado
                
                if (request.DocumentosEmpresa is not null)
                {
                    await CadastrarDocumentosEmpresa(empresa.Id, request.DocumentosEmpresa);
                }
                
                #endregion

                #region Salva os parâmetros

                if (request.TipoEmissaoCiot != null)
                {
                    var parametro = new Domain.Models.Parametros.Parametros()
                    {
                        TipoParametros = Domain.Models.Parametros.Parametros.TipoDoParametro.CodigoTipoEmissaoCiot,
                        TipoValor = Domain.Models.Parametros.Parametros.TipoDoValor.Number,
                        Valor = request.TipoEmissaoCiot.ToString(),
                        InfoAdicional = "",
                        ReferenciaId = empresa.Id
                    };

                    await _parametrosAppService.Repository.Command.AddAsync(parametro);
                }

                await _parametrosAppService.Repository.Command.SaveChangesAsync();

                #endregion

                #region Envio de IPs de Clientes é api do CIOT

                if (request.CiotClienteIps.Count <= 0)
                {
                    return new RespPadrao(true, "Empresa cadastrada com sucesso.", empresa.Id);
                }

                var ciotClienteIpsAdaptado = request.CiotClienteIps
                    .Select(ip => Mapper.Map(ip, new CiotClienteIpRequestAdaptado()))
                    .ToList();

                var ciotClienteIpsApiRequest = new CiotClienteIpApiRequest
                {
                    ClienteIps = ciotClienteIpsAdaptado,
                    CpfCnpj = request.Cnpj,
                    NomeFantasia = request.NomeFantasia,
                    RazaoSocial = request.RazaoSocial
                };

                var serializedClienteIpsApiRequest = JsonConvert.SerializeObject(ciotClienteIpsApiRequest);

                await _ciotClienteIpRepository.SalvarClienteIps(serializedClienteIpsApiRequest);

                #endregion

                return new RespPadrao(true, "Empresa cadastrada com sucesso.", empresa.Id);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return new RespPadrao(false, "Ocorreu um erro ao cadastrar a Empresa: " + e.Message);
            }
        }

        public async Task<RespPadrao> AtivarInativar(EmpresaRequest empresaRequest)
        {
            try
            {
                var grupoEmpresa = await Repository.Query
                    .Where(x => x.Id == empresaRequest.Id)
                    .Include(x => x.GrupoEmpresa)
                    .Select(x => x.GrupoEmpresa)
                    .FirstOrDefaultAsync();

                if (grupoEmpresa != null && grupoEmpresa.Ativo == 0 && empresaRequest.Ativo == 0)
                    return new RespPadrao(false, "Não foi possível ativar a empresa, pois seu grupo está inativo.");

                var empresaCommand = Mapper.Map<EmpresaSaveStatusCommand>(empresaRequest);
                await Engine.CommandBus.SendCommandAsync(empresaCommand);
                return new RespPadrao(true, "Status da empresa alterado com sucesso.");
            }
            catch (Exception e)
            {
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = "Houve um erro ao alterar o status da empresa: " + e.Message
                };
            }
        }

        public GridAvaliacaoEmpresaResponse DadosGridAvaliacaoEmpresa(int take, int page, OrderFilters orderFilters,
            List<QueryFilters> filters)
        {
            
            #region Consulta personalizada filtros da grid

            foreach (var item in filters)
            {
                item.Valor = item.Campo switch
                {
                    "cnpj" => item.Valor.Replace(".", "").Replace("/", "").Replace("-", ""),
                    _ => item.Valor
                };
            }

            #endregion
            
            var empresas = Repository.Query.GetAll()
                .Include(r => r.GrupoEmpresa)
                .Where(o =>
                o.StatusCadastro == StatusCadastro.Bloqueado || o.StatusCadastro == StatusCadastro.PendentedeValidacao);

            empresas = empresas.AplicarFiltrosDinamicos(filters);
            empresas = string.IsNullOrEmpty(orderFilters?.Campo)
                ? empresas.OrderByDescending(o => o.RazaoSocial)
                : empresas.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            var response = empresas.Select(o => new GridAvaliacaoEmpresaDadosResponse
                {
                    Id = o.Id,
                    Cnpj = o.Cnpj.ToCNPJFormato(),
                    RazaoSocial = o.RazaoSocial,
                    Telefone = o.Telefone.ToTelefoneFormato(),
                    StatusCadastro = o.StatusCadastro.DescriptionAttr()
                })
                .Skip((page - 1) * take).Take(take);

            return new GridAvaliacaoEmpresaResponse {Registros = response.ToList(), TotalRegistros = empresas.Count()};
        }

        public GridEmpresaResponse DadosGridEmpresa(int take, int page, OrderFilters orderFilters,
            List<QueryFilters> filters, int? grupoEmpresaId = null)
        {

            var empresas = Repository.Query.GetAll();
            
            var empresasId = _usuarioReadRepository.GetEmpresasAcessoUsuario(Engine.User.Id).Result;
            if (!empresasId.IsEmpty())
            {
                empresas = empresas.Where(x => empresasId.Contains(x.Id.ToInt()));
            }
            
            #region Consulta personalizada filtros da grid

            foreach (var item in filters)
            {
                item.Valor = item.Campo switch
                {
                    "cnpj" => item.Valor.Replace(".", "").Replace("/", "").Replace("-", ""),
                    "grupoEmpresa.cnpj" => item.Valor.Replace(".", "").Replace("/", "").Replace("-", ""),
                    _ => item.Valor
                };
            }

            #endregion

            empresas = empresas.Include(r => r.GrupoEmpresa).AplicarFiltrosDinamicos(filters);
            empresas = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                ? empresas.OrderByDescending(o => o.Id)
                : empresas.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            var response = empresas.Select(o => new GridEmpresaRegistrosResponse
                {
                    Id = o.Id,
                    Cnpj = o.Cnpj.ToCNPJFormato(),
                    RazaoSocial = o.RazaoSocial.Trim(),
                    GrupoEmpresa = o.GrupoEmpresa == null ? "" : o.GrupoEmpresa.RazaoSocial.Trim(),
                    GrupoEmpresaCnpj = o.GrupoEmpresa == null ? "" : o.GrupoEmpresa.Cnpj.ToCNPJFormato().Trim(),
                    Telefone = o.Telefone.ToTelefoneFormatoNovo(),
                    Ativo = o.Ativo
                })
                .Skip((page - 1) * take).Take(take);
            var registros = response.ToList();
            return new GridEmpresaResponse {Registros = registros, TotalRegistros = empresas.Count()};
        }

        public EmpresaParaAvaliacaoResponse DadosEmpresaParaValidar(int id)
        {
            try
            {
                new LogHelper().LogOperationStart("DadosEmpresaParaValidar");
                var empresa = Repository.Query
                    .Include(a => a.Cidade)
                    .Include(o => o.Cidade.Estado)
                    .FirstOrDefault(a => a.Id == id);

                if (empresa == null)
                    return new EmpresaParaAvaliacaoResponse();

                var portadorCnpjEmpresa = _portadorReadRepository
                    .Where(x => x.CpfCnpj == empresa.Cnpj)
                    .Include(x => x.PortadorRepresentanteLegal)
                    .FirstOrDefault();

                var lPortadorRepLegais = new List<PortadorRepLegalResponse>();

                if (portadorCnpjEmpresa != null)
                {
                    foreach (var representantes in portadorCnpjEmpresa.PortadorRepresentanteLegal)
                    {
                        var portadorPf = _portadorReadRepository.GetById(representantes.PortadorRepresentanteId);

                        lPortadorRepLegais.Add(new PortadorRepLegalResponse
                        {
                            Id = portadorPf.Id,
                            Nome = portadorPf.Nome,
                            CpfCnpj = portadorPf.CpfCnpj.FormatarCpfCnpj()
                        });
                    }
                }

                var response = new EmpresaParaAvaliacaoResponse
                {
                    Id = empresa.Id, Bairro = empresa.Bairro, Celular = empresa.Celular?.ToTelefoneFormato(),
                    Cep = empresa.Cep?.ToCEPFormato(), Complemento = empresa.Complemento, Email = empresa.Email,
                    Endereco = empresa.Endereco, Telefone = empresa.Telefone?.ToTelefoneFormato(),
                    CidadeId = empresa.CidadeId, NomeFantasia = empresa.NomeFantasia, CidadeNome = empresa.Cidade?.Nome,
                    RazaoSocial = empresa.RazaoSocial, EnderecoNumero = empresa.EnderecoNumero?.ToString(),
                    EstadoId = empresa.Cidade?.EstadoId ?? 0, EstadoNome = empresa.Cidade?.Estado?.Nome,
                    Cnpj = empresa.Cnpj?.ToCNPJFormato(), StatusCadastro = (int)empresa.StatusCadastro,
                    DescricaoStatusCadastro = empresa.StatusCadastro.DescriptionAttr(), Ativo = empresa.Ativo,
                    DataBloqueio = empresa.DataBloqueio, UsuarioBloqueioId = empresa.UsuarioBloqueioId,
                    UsuarioDesbloqueioId = empresa.UsuarioDesbloqueioId, DataDesbloqueio = empresa.DataDesbloqueio,
                    RepLegalList = lPortadorRepLegais
                };

                return response;
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar DadosEmpresaParaValidar");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("DadosEmpresaParaValidar");
            }
        }

        public async Task<ConsultarPorIdEmpresaResponse> BuscarPorIdParaEdicao(int id)
        {
            try
            {
                new LogHelper().LogOperationStart("BuscarPorIdParaEdicao");

                if (id < 0) throw new Exception("Id invalido!");


                var lRetorno =
                    Mapper.Map<ConsultarPorIdEmpresaResponse>(
                        await Repository.Query.ConsultarPorIdIncluindoCidadeTipoEmpresaCfop(id));

                var lPortador = await _portadorReadRepository.GetByCpfCnpjAsync(lRetorno.Cnpj);

                if (lPortador != null)
                {
                    lRetorno.NomeCnpjPortador = lPortador.Nome + " / " + lPortador.CpfCnpj.FormatarCpfCnpj();
                    lRetorno.IdPortador = lPortador.Id;
                    lRetorno.RepLegaisList = await _portadorReadRepository
                        .GetRepLegais(lPortador.Id)
                        .ProjectTo<ConsultarPorIdEmpresaResponse.PortadorRepLegalEmpResponse>(Engine.Mapper
                            .ConfigurationProvider)
                        .ToListAsync();
                }

                var lParametro = await _parametrosAppService.GetParametrosAsync(lRetorno.Id,
                    Domain.Models.Parametros.Parametros.TipoDoParametro.CodigoTipoEmissaoCiot,
                    Domain.Models.Parametros.Parametros.TipoDoValor.Number);

                if (lParametro != null) lRetorno.TipoEmissaoCiot = lParametro.Valor.ToIntSafe();

                return lRetorno;
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar BuscarPorIdParaEdicao");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("BuscarPorIdParaEdicao");
            }
        }

        public ConsultarPorIdEmpresaResponse BuscarPorId(int id)
        {
            try
            {
                new LogHelper().LogOperationStart("BuscarPorId");
                var empresa = Repository.Query.Include(x => x.GrupoEmpresa).FirstOrDefault(x => x.Id == id);
                return Mapper.Map<ConsultarPorIdEmpresaResponse>(empresa);
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar BuscarPorId");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("BuscarPorId");
            }
        } 
        
        public void EnviarEmailValidacaoCadastro(string destinatario, StatusCadastro statusCadastro,
            string parecerExterno, string usuario, string senha)
        {
            EmailEmpresaValidacao.EnviarEmailValidacao(_notificationEmailExecutor, destinatario, statusCadastro,
                parecerExterno, usuario, senha);
        }

        public async Task<ConsultarEmpresaComboResponse> ConsultarGridEmpresaComboAbastecimentos(int take, int page, OrderFilters orderFilters,
            List<QueryFilters> filters)
        {
            
            var lDataInicial = filters.FirstOrDefault(f => StringUtils.EqualsIgnoreCase(f.Campo, "DataInicio"))?.Valor.ToDateTime();
            var lDataFinal = filters.FirstOrDefault(f => StringUtils.EqualsIgnoreCase(f.Campo, "DataFim"))?.Valor.ToDateTime().AddDays(1).AddSeconds(-1);
            
            var lEmpresasComAbastecimentoNoPeriodo = _abastecimentoReadRepository
                .Where(x => x.PostoId == User.AdministradoraId && 
                            x.Status == EStatusAbastecimento.Aprovado &&
                            x.EmpresaId != null &&
                            //(x.ProtocoloAbastecimentoId == null || x.ProtocoloAbastecimento.Status == EStatusProtocolo.Reprovado) &&
                            x.DataCadastro >= lDataInicial && x.DataCadastro <= lDataFinal)
                .Select(x => x.EmpresaId)
                .Distinct()
                .ToList();
            
            var lEmpresas = Repository.Query
                .Where(e => e.Ativo == 1 && e.StatusCadastro == StatusCadastro.Ativo && 
                            lEmpresasComAbastecimentoNoPeriodo.Contains(e.Id));

            var lCount = lEmpresas.Count();
            
            lEmpresas = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                ? lEmpresas.OrderByDescending(o => o.Id)
                : lEmpresas.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            var retorno = lEmpresas.Skip((page - 1) * take)
                .Take(take)
                .ProjectTo<ConsultarEmpresaCombo>().ToList();

            return new ConsultarEmpresaComboResponse
            {
                items = retorno,
                totalItems = lCount
            };
        }
        
        
        public async Task<ConsultarEmpresaComboResponse> ConsultarGridEmpresaCombo(int take, int page, OrderFilters orderFilters,
            List<QueryFilters> filters)
        {
            
            var usuario = await _usuarioReadRepository.GetByIdAsync(Engine.User.Id);
            
            var lEmpresa =
                Repository.Query.Include(e => e.GrupoEmpresa)
                    .Where(e => e.Ativo == 1 && e.StatusCadastro == StatusCadastro.Ativo);

            var listaEmpresasIds = _usuarioReadRepository.GetEmpresasAcessoUsuario(Engine.User.Id).Result;
            if (!listaEmpresasIds.IsEmpty())
                lEmpresa = lEmpresa.Where(o => listaEmpresasIds.Contains(o.Id));
            
           // if (usuario?.GrupoEmpresaId != null)
           //     lEmpresa = lEmpresa.Where(x => x.GrupoEmpresaId == usuario.GrupoEmpresaId);
            
            var lEmpresaList = lEmpresa.ProjectTo<ConsultarEmpresaCombo>();

            lEmpresaList = lEmpresaList.AplicarFiltrosDinamicos(filters);
            lEmpresaList = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                ? lEmpresaList.OrderByDescending(o => o.Id)
                : lEmpresaList.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            var retorno = lEmpresaList.Skip((page - 1) * take).Take(take);
                
            
            var lCount = lEmpresaList.Count();
            return new ConsultarEmpresaComboResponse
            {
                items = retorno.ToList(),
                totalItems = lCount
            };
        }
        
        public async Task<ConsultarEmpresaComboResponse>  ConsultarGridEmpresaComboClientSecret(int take, int page, OrderFilters orderFilters,
            List<QueryFilters> filters)
        {
         
            //   var usuario = await _usuarioReadRepository.GetByIdAsync(Engine.User.Id);
            //    var empresas = Repository.Query.Include(e => e.GrupoEmpresa)
            //            .Where(e => e.Ativo == 1);
            //    if (usuario.GrupoEmpresaId != null)
            //        empresas = empresas.Where(x => x.GrupoEmpresaId == usuario.GrupoEmpresaId);
            var empresasId = await _usuarioReadRepository.GetEmpresasAcessoUsuario(Engine.User.Id); 
            
            var empresas = Repository.Query.Include(e => e.GrupoEmpresa)
                .Where(e => e.Ativo == 1);
            if(!empresasId.IsEmpty())
                empresas = empresas.Where(o => empresasId.Contains(o.Id));
            
            empresas = empresas.AplicarFiltrosDinamicos(filters);
            
            empresas = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                ? empresas.OrderByDescending(o => o.Id)
                : empresas.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            var count = await empresas.CountAsync();
            
            var retorno = empresas.Skip((page - 1) * take).Take(take);

            var empresasResponse = retorno.ProjectTo<ConsultarEmpresaCombo>(Engine.Mapper.ConfigurationProvider);
            
            return new ConsultarEmpresaComboResponse
            {
                items = await empresasResponse.ToListAsync(),
                totalItems = count
            };
        }

        public ConsultarEmpresaComboResponse ConsultarGridEmpresaComboProtocoloAbastecimento(int take, int page, 
            OrderFilters orderFilters, List<QueryFilters> filters)
        {
            var lDataInicial = filters.FirstOrDefault(f => StringUtils.EqualsIgnoreCase(f.Campo, "DataInicio"))?.Valor.ToDateTime();
            var lDataFinal = filters.FirstOrDefault(f => StringUtils.EqualsIgnoreCase(f.Campo, "DataFim"))?.Valor.ToDateTime().AddDays(1).AddSeconds(-1);
            
            //Empresas com abastecimentos aprovados ainda sem protocolos gerados
            var lEmpresasComAbastecimentoNoPeriodo = _abastecimentoReadRepository
                .Where(x => x.PostoId == User.AdministradoraId && 
                            x.Status == EStatusAbastecimento.Aprovado &&
                            x.EmpresaId != null &&
                            (x.ProtocoloAbastecimentoId == null || x.ProtocoloAbastecimento.Status == EStatusProtocolo.Reprovado) &&
                            x.DataCadastro >= lDataInicial && x.DataCadastro <= lDataFinal)
                .Select(x => x.EmpresaId)
                .Distinct()
                .ToList();
            
            var lEmpresas = Repository.Query
                .Where(e => e.Ativo == 1 && e.StatusCadastro == StatusCadastro.Ativo && 
                            lEmpresasComAbastecimentoNoPeriodo.Contains(e.Id));

            var lCount = lEmpresas.Count();
            
            lEmpresas = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                ? lEmpresas.OrderByDescending(o => o.Id)
                : lEmpresas.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            var retorno = lEmpresas.Skip((page - 1) * take)
                .Take(take)
                .ProjectTo<ConsultarEmpresaCombo>().ToList();

            return new ConsultarEmpresaComboResponse
            {
                items = retorno,
                totalItems = lCount
            };
        }

        public List<ConsultarEmpresaCombo> ConsultarEmpresaCombo()
        {
            try
            {
                new LogHelper().LogOperationStart("ConsultarEmpresaCombo");
                return Repository.Query.GetAll().OrderBy(e => e.NomeFantasia).ProjectTo<ConsultarEmpresaCombo>()
                    .ToList();
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar ConsultarEmpresaCombo");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("ConsultarEmpresaCombo");
            }
        }

        public async Task<RespPadrao> ConsultaParametroEmpresaPermiteEncerramentoCiot()
        {
            try
            {
                new LogHelper().LogOperationStart("ConsultaParametroEmpresaPermiteEncerramentoCiot");
                var result = true;

                if (User.EmpresaId > 0)
                    result = await Repository.Query.Where(x => x.Id == User.EmpresaId)
                        .Select(z => z.PermitirEncerramentoPainelCiot).FirstOrDefaultAsync() == 1;

                return new RespPadrao
                {
                    sucesso = true,
                    mensagem = "Consulta realizada com sucesso!",
                    data = result
                };
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar ConsultaParametroEmpresaPermiteEncerramentoCiot");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("ConsultaParametroEmpresaPermiteEncerramentoCiot");
            }
        }

        public async Task<List<CiotClienteIpResponseAdaptado>> ConsultarClienteIps(string empresaCnpj)
        {
            var lClienteIpsResponse = await _ciotClienteIpRepository.ConsultarClienteIps(empresaCnpj);

            if (lClienteIpsResponse == null) return null;
            
            var lClienteIps = JsonConvert.DeserializeObject<List<CiotClienteIpResponse>>(lClienteIpsResponse.Retorno.ToStringSafe());
            return Mapper.Map(lClienteIps, new List<CiotClienteIpResponseAdaptado>());
        }

        public async Task<ConsultarGridDocumentosEmpresaResponse> ConsultarGridDocumentosEmpresa(ConsultarGridDocumentosEmpresaRequest request)
        {
            try
            {
                var empresaId = Engine.User.EmpresaId != 0 ? Engine.User.EmpresaId : request.EmpresaId;
                var documentos = Repository.Query.Where(x => x.Id == empresaId).SelectMany(x => x.Documentos);
                
                documentos = documentos.AplicarFiltrosDinamicos(request.Filters);
        
                var count = documentos.Count();
        
                documentos = string.IsNullOrWhiteSpace(request.Order?.Campo)
                    ? documentos.OrderByDescending(o => o.Id)
                    : documentos.OrderBy($"{request.Order?.Campo} {request.Order?.Operador.DescriptionAttr()}");

                return new ConsultarGridDocumentosEmpresaResponse()
                {
                    Items = await documentos.ProjectTo<ConsultarGridDocumentosEmpresaItem>().ToListAsync(),
                    TotalItems = count
                };
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return new ConsultarGridDocumentosEmpresaResponse();
            }
        }

        public string GetSexo(int? Sexo)
        {
            try
            {
                new LogHelper().LogOperationStart("GetSexo");
                switch (Sexo)
                {
                    case (int)ESexo.Masculino: return "M";

                    case (int)ESexo.Feminino: return "F";

                    case (int)ESexo.Outros: return "O";

                    case (int)ESexo.Indefinido: return "N";

                    default: return "N";
                }
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar GetSexo");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("GetSexo");
            }
        }

        public async Task<RespPadrao> CadastrarPJConductor(EmpresaRequest request)
        {
            try
            {
                new LogHelper().LogOperationStart("CadastrarPJConductor");
                var retornoPessoaJuridica = await _cartaoAppService
                    .ConsultarPessoaJuridica(request.Cnpj.OnlyNumbers());

                if (retornoPessoaJuridica.content == null)
                {
                    var portador = Repository.Query.GetPortador(request.Cnpj.OnlyNumbers());
                    //var lRepresentanteLegal = Repository.Query.GetRepresentanteLegal(lPortador.Id);
                    
                    //verifica se existe socio
                    var pessoa = Mapper.Map<ContaPessoaJuridicaReq>(portador);

                    //var lUfEstado = Repository.Query.GetUF(lPortador.EstadoId.Value);
                    //var lUfEmissaoSigla = Repository.Query.GetUF(lPortador.UfEmissao.ToInt());
                    //var lNomeCidade = Repository.Query.GetNomeCidade(lPortador.CidadeId.Value);
                    
                    /*
                    lPessoa.idOrigemComercial = 1;
                    lPessoa.idProduto = 1;
                    lPessoa.diaVencimento = 10;

                    lPessoa.telefones = new List<TelefonePjReq>();
                    lPessoa.enderecos = new List<EnderecoPjReq>();
                    lPessoa.socios = new List<SocioPjReq>();

                    lPessoa.telefones.Add(new TelefonePjReq
                    {
                        telefone = lPortador.Telefone.Substring(2),
                        ddd = "0" + lPortador.Telefone.Substring(1, 2),
                        idTipoTelefone = 1
                    });

                    lPessoa.telefones.Add(new TelefonePjReq
                    {
                        telefone = lPortador.Celular.Substring(2),
                        ddd = "0" + lPortador.Celular.Substring(1, 2),
                        idTipoTelefone = 1
                    });


                    lPessoa.enderecos.Add(new EnderecoPjReq
                    {
                        idTipoEndereco = 1,
                        cep = lPortador.Cep,
                        logradouro = lPortador.Endereco,
                        numero = lPortador.EnderecoNumero.ToIntSafe(),
                        complemento = lPortador.Complemento,
                        pontoReferencia = lPortador.Complemento,
                        bairro = lPortador.Bairro,
                        cidade = lNomeCidade,
                        uf = lUfEstado,
                        pais = "Brasil",
                        enderecoCorrespondencia = 1
                    });

                    var telefoneRepLegal = new List<TelefonePjReq>();

                    foreach (var repLegais in lRepresentanteLegal)
                    {
                        var lSocio = Repository.Query.GetPortadorPorId(repLegais.PortadorRepresentanteId);

                        lSocio.UfEmissao = Repository.Query.GetUF(lSocio.UfEmissao.ToInt());

                        telefoneRepLegal.Add(new TelefonePjReq
                        {
                            telefone = lPortador.Telefone.Substring(2),
                            ddd = "0" + lPortador.Telefone.Substring(1, 2),
                            idTipoTelefone = 1
                        });

                        telefoneRepLegal.Add(new TelefonePjReq
                        {
                            telefone = lPortador.Celular.Substring(2),
                            ddd = "0" + lPortador.Celular.Substring(1, 2),
                            idTipoTelefone = 1
                        });

                        lPessoa.socios.Add(new SocioPjReq
                        {
                            nome = lSocio.Nome,
                            cpf = lSocio.CpfCnpj,
                            dataNascimento = Convert.ToDateTime(lSocio.DataNascimento).ToString("yyyy-MM-dd"),
                            sexo = GetSexo((int) lSocio.Sexo),
                            numeroIdentidade = lSocio.NumeroIdentidade,
                            orgaoExpedidorIdentidade = lSocio.OrgaoEmissor,
                            unidadeFederativaIdentidade = lSocio.UfEmissao,
                            dataEmissaoIdentidade =
                                Convert.ToDateTime(lSocio.EmissaoIdentidade).ToString("yyyy-MM-dd"),
                            email = lSocio.Email,
                            telefones = telefoneRepLegal
                        });
                    }
*/

                    var retornoSavePessoaJuridica = await _cartaoAppService.CadastrarContaPessoaJuridica(pessoa);

                    if (retornoSavePessoaJuridica != null)
                    {
                        return new RespPadrao
                        {
                            sucesso = false,
                            mensagem = "Erro ao salvar a pessoa na Conductor!"
                        };
                    }
                }

                return new RespPadrao
                {
                    sucesso = true,
                    mensagem = ""
                };
            }
            catch (Exception e)
            {
                new LogHelper().Error(e, "Erro ao executar CadastrarPJConductor", request);
                return new RespPadrao
                {
                    sucesso = false,
                    mensagem = e.Message
                };
            }
            finally
            {
                new LogHelper().LogOperationEnd("CadastrarPJConductor");
            }
        }

        private async Task<RespPadrao> ValidaCadastreSe(EmpresaRequest empresaRequest, PortadorSalvarCommand portadorEmpCommand)
        {
            try
            {
                new LogHelper().LogOperationStart("ValidaCadastreSe");
                var cnpj = empresaRequest.Cnpj.OnlyNumbers();
                
                var cnpjCadastrado = await Repository.Query.FirstOrDefaultAsync(o => o.Cnpj == cnpj);

                if (cnpjCadastrado != null)
                    throw new Exception($"CNPJ {empresaRequest.Cnpj.FormatarCpfCnpj()} já cadastrado.");

                portadorEmpCommand.ValidarCadastro();
                
                if (Repository.Query.VerificaCpfCnpjPortador(portadorEmpCommand.CpfCnpj.OnlyNumbers()))
                    throw new Exception($"CNPJ {portadorEmpCommand.CpfCnpj.FormatarCpfCnpj()} já cadastrado.");

                /*if (empresaRequest.RepLegalList == null || !empresaRequest.RepLegalList.Any())
                {
                    return new RespPadrao
                    {
                        sucesso = true,
                        mensagem = ""
                    };
                }
                
                foreach (var repLegal in empresaRequest.RepLegalList)
                {
                    var portadorCommand = Mapper.Map<PortadorSalvarCommand>(repLegal);
                    portadorCommand.ValidarCadastro();
                    if (Repository.Query.VerificaCpfCnpjPortador(portadorCommand.CpfCnpj.OnlyNumbers()))
                        throw new Exception($"CPF {portadorCommand.CpfCnpj.FormatarCpfCnpj()} já cadastrado.");
                }*/

                return new RespPadrao
                {
                    sucesso = true,
                    mensagem = ""
                };
            }
            catch (Exception e)
            {
                return new RespPadrao
                {
                    sucesso = false,
                    mensagem = e.Message
                };
            }
            finally
            {
                new LogHelper().LogOperationEnd("ValidaCadastreSe");
            }
        }
    }
}

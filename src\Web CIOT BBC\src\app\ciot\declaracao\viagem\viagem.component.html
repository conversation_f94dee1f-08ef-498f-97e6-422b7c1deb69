<div class="container main-container">
    <form novalidate [formGroup]="viagemForm">
        <div class="form-horizontal">
            <div class="row">
                <div class="form-group required">
                    <label class="control-label" for="tipoViagem">Tipo de viagem: &nbsp; </label>
                    <input type="radio" value="1" name="tipoViagem" (change)="onTipoViagemChanged()"
                        [(ngModel)]="tipoViagem" [ngModelOptions]="{standalone: true}" checked> Padrão &nbsp;
                    <input type="radio" value="3" name="tipoViagem" (change)="onTipoViagemChanged()"
                        [(ngModel)]="tipoViagem" [ngModelOptions]="{standalone: true}"> TAC-agregado
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12 col-md-6 col-lg-3">
                    <div class="form-group required" [ngClass]="{'has-error': displayMessage.dtInicioFrete }">
                        <label class="control-label" for="dtInicioFrete">Início do frete</label>
                        <input type="text" class="form-control" #dp="bsDatepicker" bsDatepicker [bsValue]="dataInicio"
                            [bsConfig]="bsConfig" [(ngModel)]="dataInicio" [disabled]="'3' == tipoViagem"
                            [ngModelOptions]="{standalone: true}">
                        <small id="dtInicioFreteHelp" class="form-text text-muted">Ex: 01/01/2001</small>
                        <span class="text-danger" *ngIf="displayMessage.dtInicioFrete">
                            <p [innerHTML]="displayMessage.dtInicioFrete"></p>
                        </span>
                    </div>
                </div>
                <div class="col-sm-12 col-md-6 col-lg-3">
                    <div class="form-group required" [ngClass]="{'has-error': displayMessage.dtTerminoFrete }">
                        <label class="control-label" for="dtTerminoFrete">Final do frete</label>
                        <input type="text" class="form-control" #dp="bsDatepicker" bsDatepicker [bsValue]="dataFim"
                            [bsConfig]="bsConfig" [(ngModel)]="dataFim" [ngModelOptions]="{standalone: true}">
                        <small id="dtTerminoFreteHelp" class="form-text text-muted">Ex: 01/01/2001</small>
                        <span class="text-danger" *ngIf="displayMessage.dtTerminoFrete">
                            <p [innerHTML]="displayMessage.dtTerminoFrete"></p>
                        </span>
                    </div>
                </div>
            </div>

            <!--
            <div id="idCodigoIBGE" class="form-group required" [ngClass]="{'has-error': displayMessage.cidadeIBGE}">
                <label class="control-label" for="cidadeIBGE">Código IBGE - Cidade:</label>
                <div class="alert alert-danger" *ngIf="noResultCodigoIBGE">Cidade não encontrada</div>
                <input [(ngModel)]="codigoIBGECompleterText" [typeahead]="cidadeIBGEList.Retorno"
                    (typeaheadNoResults)="typeaheadNoResultsNatureza($event)"
                    (typeaheadOnSelect)="onChoosedCidadeByIBGE($event)" typeaheadOptionField="DESCRICAO"
                    class="form-control" [disabled]="'3' == tipoViagem" autocomplete="off"
                    [ngModelOptions]="{standalone: true}">
            </div>
            -->
            

            <div class="row">
                <div id="codigoIBGECidadeOrigem" class="col-sm-12 col-md-6 col-lg-3">
                    <div class="form-group required" [ngClass]="{'has-error': displayMessage.codigoIBGECidadeOrigem}">
                        <label class="control-label" for="codigoIBGECidadeOrigem">Cidade Origem</label>
                        <div class="alert alert-danger" *ngIf="noResultCidadeOrigem">Cidade não encontrada</div>
                        <input [(ngModel)]="cidadeOrigemCompleterText" [typeahead]="cidadeList"
                            (typeaheadNoResults)="typeaheadNoResultsCidadeOrigem($event)" typeaheadOptionField="nome"
                            [typeaheadItemTemplate]="customItemTemplate"
                            (typeaheadOnSelect)="onSelectCidadeOrigem($event)" class="form-control" autocomplete="off"
                            [disabled]="'3' == tipoViagem || !podeDigitar" [ngModelOptions]="{standalone: true}"
                            placeholder="Digite para buscar...">
                    </div>
                </div>
                <div id="codigoIBGECidade" class="col-sm-12 col-md-6 col-lg-3">
                    <div class="form-group required" [ngClass]="{'has-error': displayMessage.codigoIBGECidade}">
                        <label class="control-label" for="codigoIBGECidade">Cidade Destino</label>
                        <div class="alert alert-danger" *ngIf="noResult">Cidade não encontrada</div>
                        <input [(ngModel)]="cidadeCompleterText" [typeahead]="cidadeList"
                            (typeaheadNoResults)="typeaheadNoResults($event)" typeaheadOptionField="nome"
                            (typeaheadOnSelect)="onSelectCidade($event)" class="form-control" autocomplete="off"
                            [disabled]="'3' == tipoViagem || !podeDigitar" [ngModelOptions]="{standalone: true}"
                            placeholder="Digite para buscar...">
                    </div>
                </div>

                <div class="col-sm-12 col-md-3 col-lg-3">
                    <div class="form-group" [ngClass]="{'has-error': displayMessage.parcelaUnica}">
                        <label class="control-label" for="freteRetorno">Há frete retorno:</label>
                        <input id="freteRetorno" type="checkbox" formControlName="freteRetorno" />
                        <span class="text-danger" *ngIf="displayMessage.freteRetorno">
                            <p [innerHTML]="displayMessage.freteRetorno"></p>
                        </span>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-sm-12 col-md-6 col-lg-3" id="idCepRetorno">
                    <div class="form-group required" [ngClass]="{'has-error': displayMessage.cepRetorno }">
                        <label class="control-label" for="cepRetorno">Cep retorno</label>
                        <div class="input-group">
                            <input cepMask maxlength="9" class="form-control" id="cepRetorno" type="text"
                                formControlName="cepRetorno" placeholder="00000-000" />
                        </div>
                        <span class="text-danger" *ngIf="displayMessage.cepRetorno">
                            <p [innerHTML]="displayMessage.cepRetorno"></p>
                        </span>
                    </div>
                </div>

                <div id="idNaturezaCarga" class="form-group required"
                    [ngClass]="{'has-error': displayMessage.naturezaCarga}">
                    <label class="control-label" for="naturezaCarga">Natureza da carga:</label>
                    <div class="alert alert-danger" *ngIf="noResultNatureza">Natureza não encontrada</div>
                    <input [(ngModel)]="naturezaCompleterText" [typeahead]="naturezaCargaList"
                        (typeaheadNoResults)="typeaheadNoResultsNatureza($event)"
                        (typeaheadOnSelect)="onSelectNatureza($event)" typeaheadOptionField="descricao"
                        [typeaheadItemTemplate]="naturezaItemTemplate" class="form-control"
                        [disabled]="'3' == tipoViagem" autocomplete="off" [ngModelOptions]="{standalone: true}"
                        placeholder="Digite para buscar...">
                </div>

                <div class="col-sm-12 col-md-6 col-lg-3" id="idPesoCarga">
                    <div class="form-group required" [ngClass]="{'has-error': displayMessage.pesoCarga}">
                        <label class="control-label" for="pesoCarga">Peso da carga (KG):</label>
                        <input type="text" OnlyNumber class="form-control" id="pesoCarga" min="0" maxlength="5"
                            formControlName="pesoCarga" />
                        <span class="text-danger" *ngIf="displayMessage.pesoCarga">
                            <p [innerHTML]="displayMessage.pesoCarga"></p>
                        </span>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-sm-12 col-md-6 col-lg-3">
                    <div class="form-group required" [ngClass]="{'has-error': displayMessage.valorFrete }">
                        <label class="control-label" for="valorFrete">Valor do frete</label>
                        <input currencyMask [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }" maxlength="20"
                            size="10" class="form-control" step="0.01" min="0" id="valorFrete"
                            formControlName="valorFrete" placeholder="0.00" pattern="^\d+(?:\.\d{1,2})?$" />
                        <span class="text-danger" *ngIf="displayMessage.valorFrete">
                            <p [innerHTML]="displayMessage.valorFrete"></p>
                        </span>
                    </div>
                </div>
                <div class="col-sm-12 col-md-6 col-lg-3">
                    <div class="form-group " [ngClass]="{'has-error': displayMessage.valorDespesas }">
                        <label class="control-label" for="valorDespesas">Valor das despesas</label>
                        <input currencyMask [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }" maxlength="20"
                            size="10" class="form-control" step="0.01" min="0" id="valorDespesas"
                            formControlName="valorDespesas" placeholder="0.00" pattern="^\d+(?:\.\d{1,2})?$" />
                        <span class="text-danger" *ngIf="displayMessage.valorDespesas">
                            <p [innerHTML]="displayMessage.valorDespesas"></p>
                        </span>
                    </div>
                </div>
                <div class="col-sm-12 col-md-6 col-lg-3">
                    <div class="form-group " [ngClass]="{'has-error': displayMessage.totalImposto }">
                        <label class="control-label" for="totalImposto">Total impostos</label>
                        <input currencyMask [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }" maxlength="20"
                            size="10" class="form-control" step="0.01" min="0" id="totalImposto"
                            formControlName="totalImposto" placeholder="0.00" pattern="^\d+(?:\.\d{1,2})?$" />
                        <span class="text-danger" *ngIf="displayMessage.totalImposto">
                            <p [innerHTML]="displayMessage.totalImposto"></p>
                        </span>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12 col-md-6 col-lg-3">
                    <div class="form-group">
                        <div class="form-group" [ngClass]="{'has-error': displayMessage.valorCombustivel}">
                            <label class="control-label" for="valorCombustivel">Valor do combustível</label>
                            <input currencyMask [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
                                maxlength="20" size="10" class="form-control" step="0.01" min="0" id="valorCombustivel"
                                formControlName="valorCombustivel" placeholder="0.00" pattern="^\d+(?:\.\d{1,2})?$" />
                        </div>
                    </div>
                </div>
                <div class="col-sm-12 col-md-6 col-lg-3">
                    <div class="form-group">
                        <div class="form-group" [ngClass]="{'has-error': displayMessage.totalPedagio}">
                            <label class="control-label" for="totalPedagio">Total de pedágio</label>
                            <input currencyMask [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
                                maxlength="20" size="10" class="form-control" step="0.01" min="0" id="totalPedagio"
                                placeholder="0.00" formControlName="totalPedagio" pattern="^\d+(?:\.\d{1,2})?$" />
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12 col-md-6 col-lg-3">
                    <div class="form-group required" [ngClass]="{'has-error': displayMessage.quantidadeTarifas }">
                        <label class="control-label" for="quantidadeTarifas">Quantidade de tarifas:</label>
                        <input RntrcMask size="10" class="form-control" min="0" maxlength="3" id="quantidadeTarifas"
                            formControlName="quantidadeTarifas" />
                        <span class="text-danger" *ngIf="displayMessage.quantidadeTarifas">
                            <p [innerHTML]="displayMessage.quantidadeTarifas"></p>
                        </span>
                    </div>
                </div>
                <div class="col-sm-12 col-md-6 col-lg-3">
                    <div class="form-group" [ngClass]="{'has-error': displayMessage.valorTotalTarifas}">
                        <label class="control-label" for="valorTotalTarifas">Valor total das tarifas:</label>
                        <input currencyMask [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }" maxlength="20"
                            size="10" class="form-control" step="0.01" min="0" id="valorTotalTarifas"
                            formControlName="valorTotalTarifas" placeholder="0.00" pattern="^\d+(?:\.\d{1,2})?$" />
                        <span class="text-danger" *ngIf="displayMessage.valorTotalTarifas">
                            <p [innerHTML]="displayMessage.valorTotalTarifas"></p>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
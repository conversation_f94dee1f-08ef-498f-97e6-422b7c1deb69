using SistemaInfo.BBC.Domain.Enum;
using Xunit;

namespace BBC.Test.Tests.StatusVeiculoEnum.Fixture
{
    [CollectionDefinition(nameof(StatusVeiculoEnumCollection))]
    public class StatusVeiculoEnumCollection : ICollectionFixture<StatusVeiculoEnumFixture>
    {
        
    }

    public class StatusVeiculoEnumFixture : MockEngine
    {
        public StatusVeiculo ObterStatusVeiculoAtivo()
        {
            return StatusVeiculo.Ativo;
        }

        public StatusVeiculo ObterStatusVeiculoBloqueado()
        {
            return StatusVeiculo.Bloqueado;
        }

        public StatusVeiculo[] ObterTodosStatusVeiculo()
        {
            return new[]
            {
                StatusVeiculo.Bloqueado,
                StatusVeiculo.Ativo
            };
        }

        public string[] ObterDescricoesStatusVeiculo()
        {
            return new[]
            {
                "Bloqueado",
                "Ativo"
            };
        }

        public int[] ObterValoresStatusVeiculo()
        {
            return new[] { 0, 1 };
        }
    }
}

using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using AutoMapper.QueryableExtensions;
using Microsoft.EntityFrameworkCore;
using SistemaInfo.BBC.Application.Email.Abastecimento;
using SistemaInfo.BBC.Application.Helpers;
using SistemaInfo.BBC.Application.Interface.AutorizacaoAbastecimento;
using SistemaInfo.BBC.Application.Objects.Api.AutorizacaoAbastecimento;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.AutorizacaoAbastecimento;
using SistemaInfo.BBC.Application.Objects.Web.ManutencaoAbastecimento;
using SistemaInfo.BBC.Domain.Components.Email;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.BBC.Domain.Models.Abastecimento.Repository;
using SistemaInfo.BBC.Domain.Models.AutorizacaoAbastecimento.Commands;
using SistemaInfo.BBC.Domain.Models.AutorizacaoAbastecimento.Repository;
using SistemaInfo.BBC.Domain.Models.Empresa.Repository;
using SistemaInfo.BBC.Domain.Models.Usuario.Repository;
using SistemaInfo.BBC.Domain.Models.Veiculo.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.Services.AutorizacaoAbastecimento
{
    public class AutorizacaoAbastecimentoAppService : AppService<
            Domain.Models.AutorizacaoAbastecimento.AutorizacaoAbastecimento,
            IAutorizacaoAbastecimentoReadRepository, IAutorizacaoAbastecimentoWriteRepository>,
        IAutorizacaoAbastecimentoAppService
    {
        private readonly IEmpresaReadRepository _empresaReadRepository;
        private readonly IVeiculoReadRepository _veiculoReadRepository;
        private readonly IUsuarioReadRepository _usuarioReadRepository;
        private readonly INotificationEmailExecutor _emailExecutor;
        private readonly IAbastecimentoReadRepository _abastecimento;

        public AutorizacaoAbastecimentoAppService(
            IAppEngine engine,
            IEmpresaReadRepository empresaReadRepository,
            IUsuarioReadRepository usuarioReadRepository,
            IVeiculoReadRepository veiculoReadRepository,
            IAutorizacaoAbastecimentoReadRepository readRepository,
            IAutorizacaoAbastecimentoWriteRepository writeRepository, INotificationEmailExecutor emailExecutor,
            IAbastecimentoReadRepository abastecimento) : base(
            engine, readRepository, writeRepository)
        {
            _empresaReadRepository = empresaReadRepository;
            _usuarioReadRepository = usuarioReadRepository;
            _veiculoReadRepository = veiculoReadRepository;
            _emailExecutor = emailExecutor;
            _abastecimento = abastecimento;
        }

        public ConsultarGridAutorizacaoAbastecimentoResponse ConsultarGridAutorizacaoAbastecimento(int take, int page,
            OrderFilters orderFilters,
            List<QueryFilters> filters)
        {
            IQueryable<Domain.Models.AutorizacaoAbastecimento.AutorizacaoAbastecimento> lAutorizacaoAbastecimento;

            lAutorizacaoAbastecimento = Repository.Query.GetAll();

            lAutorizacaoAbastecimento = lAutorizacaoAbastecimento.AplicarFiltrosDinamicos(filters);
            lAutorizacaoAbastecimento = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                ? lAutorizacaoAbastecimento.OrderByDescending(o => o.Id)
                : lAutorizacaoAbastecimento.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            var lUser = _usuarioReadRepository.GetUsuarioFull(User.Id);

            if (lUser?.UsuarioCentroCusto != null && User.EmpresaId > 0)
            {
                var lCentroCustoUsuario = lUser.UsuarioCentroCusto.Select(x => x.CentroCustoId).ToList();
                lAutorizacaoAbastecimento = lAutorizacaoAbastecimento
                    .Where(a => lCentroCustoUsuario.Contains(a.Veiculo.CentroCustoId.Value));
            }

            if (lUser?.UsuarioFilial != null && User.EmpresaId > 0)
            {
                var lFilialUsuario = lUser.UsuarioFilial.Select(x => x.FilialId).ToList();
                lAutorizacaoAbastecimento = lAutorizacaoAbastecimento
                    .Where(a => lFilialUsuario.Contains(a.Veiculo.FilialId.Value));
            }

            var retorno = lAutorizacaoAbastecimento.Skip((page - 1) * take)
                .Take(take).ProjectTo<ConsultarGridAutorizacaoAbastecimento>(Engine.Mapper.ConfigurationProvider)
                .ToList();

            return new ConsultarGridAutorizacaoAbastecimentoResponse
            {
                items = retorno,
                totalItems = lAutorizacaoAbastecimento.Count()
            };
        }

        public AutorizacaoAbastecimentoResponse ConsultarPorId(int idAutorizacaoAbastecimento)
        {
            try
            {
                new LogHelper().LogOperationStart("ConsultarPorId");
                return Mapper.Map<AutorizacaoAbastecimentoResponse>(
                    Repository.Query.GetAutorizacaoAbastecimentoById(idAutorizacaoAbastecimento));
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar ConsultarPorId");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("ConsultarPorId");
            }
        }

        public async Task<RespPadrao> ValidacoesSave(List<AutorizacaoAbastecimentoRequest> lRequestAutorizacao)
        {
            try
            {
                var validacaoMotivo = "";
                var validacaoInsucesso = 0;
                var mensagem = "";

                foreach (var requestAutorizacao in lRequestAutorizacao)
                {
                    var lVeiculoAutorizacao = await _veiculoReadRepository
                        .Where(x => x.Id == requestAutorizacao.VeiculoId)
                        .Include(x => x.VeiculoCombustiveis)
                        .ThenInclude(x => x.Combustivel)
                        .Include(x => x.Filial)
                        .FirstOrDefaultAsync();

                    if (lVeiculoAutorizacao == null)
                    {
                        mensagem = "Veiculo não encontrado!";

                        validacaoInsucesso = validacaoInsucesso + 1;
                    }

                    // Validação segura para verificar existencia de autorização
                    var orcamentoExistente = await Repository.Query
                        .FirstOrDefaultAsync(x => x.VeiculoId == requestAutorizacao.VeiculoId
                                                  && x.CombustivelId == requestAutorizacao.CombustivelId
                                                  && x.DataCadastro.Month == DateTime.Now.Month
                                                  && x.Metodo == requestAutorizacao.Metodo
                                                  && x.Status == StatusAutorizacaoAbastecimento.Aberto);

                    if (orcamentoExistente != null)
                    {
                        mensagem = "Veículo placa " + orcamentoExistente.Veiculo.Placa +
                                   ", já possui autorização para combustivel " +
                                   orcamentoExistente.Combustivel.Nome +
                                   ", para o mês vigente!";

                        validacaoInsucesso = validacaoInsucesso + 1;
                    }

                    // validação de empresa
                    var lUsuarioLogado = await _usuarioReadRepository.Where(x => x.Id == User.Id)
                        .Include(x => x.GrupoUsuario).FirstOrDefaultAsync();

                    var lGrupoUsuario = lUsuarioLogado.GrupoUsuario.Descricao;

                    if (lGrupoUsuario != "Administrador")
                    {
                        if (requestAutorizacao.EmpresaId != null)
                        {
                            if (requestAutorizacao.EmpresaId != User.EmpresaId)
                            {
                                mensagem = "Veículo placa " + lVeiculoAutorizacao?.Placa +
                                           ", não pertence a empresa logada!";

                                validacaoInsucesso = validacaoInsucesso + 1;
                            }
                        }
                        else
                        {
                            if (lVeiculoAutorizacao?.Filial?.EmpresaId != User.EmpresaId)
                            {
                                mensagem = "Veículo placa " + lVeiculoAutorizacao?.Placa +
                                           ", não pertence a empresa logada!";

                                validacaoInsucesso = validacaoInsucesso + 1;
                            }
                        }
                    }

                    // validação de pacidade do tanque x litragem solicitada
                    var combutivelSelecionadoVeiculo = lVeiculoAutorizacao?.VeiculoCombustiveis
                        .FirstOrDefault(x => x.CombustivelId == requestAutorizacao.CombustivelId);

                    if (combutivelSelecionadoVeiculo?.Capacidade < requestAutorizacao.Litragem)
                    {
                        mensagem = "Veículo placa " + lVeiculoAutorizacao.Placa +
                                   " não possui capacidade de tanque para o combustivel " +
                                   combutivelSelecionadoVeiculo.Combustivel.Nome +
                                   ", capacidade máxima do tanque " + combutivelSelecionadoVeiculo?.Capacidade +
                                   " litros!";

                        validacaoInsucesso = validacaoInsucesso + 1;
                    }

                    validacaoMotivo = validacaoMotivo + mensagem + "<br>";
                }

                if (validacaoInsucesso > 0)
                {
                    return new RespPadrao
                    {
                        sucesso = false,
                        mensagem = validacaoMotivo
                    };
                }

                return new RespPadrao
                {
                    sucesso = true
                };
            }
            catch (Exception e)
            {
                return new RespPadrao
                {
                    sucesso = false,
                    mensagem = "Falha ao validar informações! " + e
                };
            }
        }


        public async Task<RespPadrao> Save(List<AutorizacaoAbastecimentoRequest> lAutorizacaoAbastecimentoReq,
            bool integracao)
        {
            try
            {
                var listaPlacasNaoGeradas = "";
                var autorizacaoInsucesso = 0;

                var validarSave = await ValidacoesSave(lAutorizacaoAbastecimentoReq);

                if (!validarSave.sucesso)
                {
                    return new RespPadrao
                    {
                        sucesso = false,
                        mensagem = validarSave.mensagem
                    };
                }

                foreach (var requestAutorizacao in lAutorizacaoAbastecimentoReq)
                {
                    var lVeiculoAutorizacao = await _veiculoReadRepository
                        .Where(x => x.Id == requestAutorizacao.VeiculoId)
                        .Include(x => x.VeiculoCombustiveis)
                        .ThenInclude(x => x.Combustivel)
                        .Include(x => x.Filial)
                        .FirstOrDefaultAsync();

                    requestAutorizacao.FilialId = lVeiculoAutorizacao?.FilialId;
                    requestAutorizacao.ModeloId = lVeiculoAutorizacao?.ModeloId;
                    requestAutorizacao.Metodo = requestAutorizacao.Metodo == 0
                        ? lVeiculoAutorizacao.TipoAbastecimento.GetHashCode()
                        : requestAutorizacao.Metodo;
                    requestAutorizacao.EmpresaId = lVeiculoAutorizacao.EmpresaId;

                    var commandList = Mapper.Map<AutorizacaoAbastecimentoSalvarComRetornoCommand>(requestAutorizacao);

                    var retorno =
                        await Engine.CommandBus
                            .SendCommandAsync<Domain.Models.AutorizacaoAbastecimento.AutorizacaoAbastecimento>(
                                commandList);

                    if (retorno.Id > 0 && integracao)
                    {
                        var abastecimento = _abastecimento
                            .Include(x => x.Posto)
                            .Include(x => x.Combustivel)
                            .Include(x => x.Veiculo)
                            .Include(x => x.Portador).FirstOrDefault(x => x.AutorizacaoAbastecimentoId == retorno.Id);

                        if (abastecimento != null && abastecimento.Portador.Email.HasValue())
                        {
                            var data = new ComprovanteAbastecimentoResponse()
                            {
                                NomePosto = abastecimento.Posto.RazaoSocial == null
                                    ? abastecimento.Posto.NomeFantasia
                                    : abastecimento.Posto.RazaoSocial,
                                CnpjPosto = abastecimento.Posto.Cnpj.FormatarCpfCnpj(),
                                CodAutorizacao = abastecimento.AutorizacaoAbastecimentoId.ToString(),
                                Funcionario = abastecimento.Portador.Nome,
                                Placa = abastecimento.Veiculo.Placa.ToPlacaFormato(),
                                CombustivelNome = abastecimento.Combustivel.Nome,
                                Litragem = abastecimento.Litragem.ToString(CultureInfo.InvariantCulture),
                                Valor = StringHelper.FormatMoney(abastecimento.ValorAbastecimento),
                                DataCadastro = abastecimento.DataCadastro
                            };

                            EmailComprovanteAbastecimento.EnviarEmailComprovanteAbastecimento(_emailExecutor,
                                abastecimento.Portador.Email, data);
                        }

                        return new RespPadrao
                        {
                            sucesso = true,
                            mensagem = "Autorização(oes) criada(s) com sucesso!",
                            data = retorno.Id
                        };
                    }

                    ;

                    if (retorno.Id == 0)
                    {
                        listaPlacasNaoGeradas = listaPlacasNaoGeradas + ", " + lVeiculoAutorizacao.Placa;
                        autorizacaoInsucesso = autorizacaoInsucesso + 1;
                    }
                }

                if (autorizacaoInsucesso > 0)
                {
                    return new RespPadrao
                    {
                        sucesso = true,
                        mensagem = "Autorização(oes) criada(s) com divergencia! placas" + listaPlacasNaoGeradas +
                                   " não geradas!"
                    };
                }

                return new RespPadrao
                {
                    sucesso = true,
                    mensagem = "Autorização(oes) criada(s) com sucesso! "
                };
            }
            catch (Exception e)
            {
                return new RespPadrao
                {
                    sucesso = false,
                    mensagem = "Falha ao gerar autorização! " + e
                };
            }
        }

        public async Task<RespPadraoApi> IntegrarAutorizacaoAbastecimento(
            AutorizarAbastecimentoIntegrarRequest autorizarAbastecimentoRequest)
        {
            try
            {
                new LogHelper().LogOperationStart("IntegrarAutorizacaoAbastecimento");
                var autorizacaoAbastecimento = new AutorizacaoAbastecimentoRequest();

                //Coleta informações dao veículo caso não localizado retorna msg de erro
                var veiculo = new Domain.Models.Veiculo.Veiculo();

                if (!autorizarAbastecimentoRequest.Placa.IsNullOrWhiteSpace())
                {
                    var placa = autorizarAbastecimentoRequest.Placa.Replace("-", "");
                    veiculo = _veiculoReadRepository.Where(x => x.Placa == placa.ToUpper())
                        .Include(x => x.VeiculoCombustiveis)
                        .ThenInclude(x => x.Combustivel).FirstOrDefault();
                }
                else
                {
                    return new RespPadraoApi()
                    {
                        sucesso = false,
                        mensagem = "Placa ou Frota não informada."
                    };
                }


                if (veiculo == null)
                {
                    return new RespPadraoApi()
                    {
                        sucesso = false,
                        mensagem = "Placa ou Frota não encontrada."
                    };
                }

                autorizacaoAbastecimento.VeiculoId = veiculo.Id;

                //Verifica se o combustivel esta vinculado ao veiculo informado
                var combustivelId = 0;
                foreach (var combustivel in veiculo.VeiculoCombustiveis)
                {
                    if (combustivel.Combustivel.Nome == autorizarAbastecimentoRequest.Combustivel)
                    {
                        combustivelId = combustivel.CombustivelId;
                    }
                }

                //Coleta informações do combustivel caso não localizado retorna msg de erro
                if (combustivelId == 0)
                {
                    return new RespPadraoApi()
                    {
                        sucesso = false,
                        mensagem = "Combustivel não encontrada ou não vinculado ao veículo informado."
                    };
                }

                autorizacaoAbastecimento.CombustivelId = combustivelId;

                autorizacaoAbastecimento.Metodo = (int)veiculo.TipoAbastecimento;
                autorizacaoAbastecimento.Litragem =
                    Convert.ToDecimal(autorizarAbastecimentoRequest.Litragem.Replace(".", ","));

                if (veiculo.TipoAbastecimento == null || veiculo.TipoAbastecimento == 0)
                {
                    return new RespPadraoApi()
                    {
                        sucesso = false,
                        mensagem = "Veículo informado, não possui tipo de abastecimento! verificar cadastro."
                    };
                }

                autorizacaoAbastecimento.Metodo = (int)veiculo.TipoAbastecimento;

                autorizacaoAbastecimento.IdentificadorOrigem = autorizarAbastecimentoRequest.IdentificadorOrigem;

                var lAutorizacaoSave = new List<AutorizacaoAbastecimentoRequest>();

                lAutorizacaoSave.Add(autorizacaoAbastecimento);

                var response = await Save(lAutorizacaoSave, true);

                //Retorno de sucesso da autorização
                if (response.data != null)
                {
                    return new RespPadraoApi()
                    {
                        sucesso = true,
                        mensagem = response.mensagem,
                        id = response.data.ToInt()
                    };
                }

                return new RespPadraoApi()
                {
                    sucesso = false,
                    mensagem = response.mensagem
                };
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar IntegrarAutorizacaoAbastecimento");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("IntegrarAutorizacaoAbastecimento");
            }
        }

        private RespPadraoApi ValidaCancelarAutorizacaoAbastecimento(
            Domain.Models.AutorizacaoAbastecimento.AutorizacaoAbastecimento autorizacaoAbastecimento)
        {
            if (autorizacaoAbastecimento == null)
            {
                return new RespPadraoApi()
                {
                    sucesso = false,
                    mensagem = "Autorização abastecimento não cadastrada!"
                };
            }

            if (autorizacaoAbastecimento.Status == StatusAutorizacaoAbastecimento.Cancelado)
            {
                return new RespPadraoApi()
                {
                    sucesso = true,
                    mensagem = "Autorização já abastecimento cancelada!"
                };
            }

            if (autorizacaoAbastecimento.LitragemUtilizada > 0 ||
                autorizacaoAbastecimento.Status == StatusAutorizacaoAbastecimento.Baixado)
            {
                return new RespPadraoApi()
                {
                    sucesso = false,
                    mensagem = "Não é possível cancelar um abastecimento já utilizado!"
                };
            }

            return new RespPadraoApi()
            {
                sucesso = true,
                mensagem = "Registro cancelado com sucesso!"
            };
        }

        public async Task<RespPadraoApi> CancelarAutorizacaoAbastecimento(
            CancelarAbastecimentoIntegrarRequest autorizarAbastecimentoRequest)
        {
            try
            {
                var autorizacaoAbastecimento =
                    await Repository.Query.GetByIdAsync(autorizarAbastecimentoRequest.AutorizacaoId);

                if (autorizacaoAbastecimento.Status == StatusAutorizacaoAbastecimento.Cancelado)
                {
                    return new RespPadraoApi()
                    {
                        id = autorizacaoAbastecimento.Id,
                        sucesso = true,
                        mensagem = "Autorização já cancelada!"
                    };
                }

                var resultValidacaoCancelamentoAutorizacaoAbastecimento =
                    ValidaCancelarAutorizacaoAbastecimento(autorizacaoAbastecimento);

                if (resultValidacaoCancelamentoAutorizacaoAbastecimento.sucesso == false)
                {
                    return resultValidacaoCancelamentoAutorizacaoAbastecimento;
                }

                var command =
                    Mapper.Map<AutorizacaoAbastecimentoCancelarComRetornoCommand>(autorizarAbastecimentoRequest);
                var retorno = await Engine.CommandBus
                    .SendCommandAsync<Domain.Models.AutorizacaoAbastecimento.AutorizacaoAbastecimento>(command);

                return new RespPadraoApi()
                {
                    id = retorno.Id,
                    sucesso = true,
                    mensagem = "Autorização abastecimento cancelado com sucesso!"
                };
            }
            catch (Exception e)
            {
                return new RespPadraoApi()
                {
                    sucesso = false,
                    mensagem = e.Message
                };
            }
        }
    }
}
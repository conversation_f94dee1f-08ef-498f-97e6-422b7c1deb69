﻿using System;
using System.Linq;

namespace SistemaInfo.BBC.Domain.Helper;


public class SenhaGenerator
{
    public static string Gerar()
    {
        var random = new Random();

        //  1 letra maiúscula
        char letraMaiuscula = (char)random.Next('A', 'Z' + 1);

        // 3 letras minúsculas
        string letrasMinusculas = new string(Enumerable.Range(0, 3)
            .Select(_ => (char)random.Next('a', 'z' + 1)).ToArray());

        // 1 número
        char numero = (char)random.Next('0', '9' + 1);

        // 1 caractere especial
        string especiais = "!@#$%&*?";
        char especial = especiais[random.Next(especiais.Length)];

        // Junta tudo
        string senhaBruta = letraMaiuscula + letrasMinusculas + numero + especial;

        // Embaralha a senha
        string senhaFinal = new string(senhaBruta.ToCharArray()
            .OrderBy(_ => random.Next()).ToArray());

        return senhaFinal;
    }
}

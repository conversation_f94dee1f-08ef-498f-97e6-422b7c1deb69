using System;
using System.ComponentModel;

namespace SistemaInfo.BBC.Domain.Enum
{
    public enum Tipo
    {        
        [Description("Adiantamento")]
        Adiantamento = 0,
        [Description("Saldo")]
        Saldo = 1,
        [Description("Complemento")]
        Complemento = 2,
        [Description("Avulso")]
        Avulso = 3,
        [Description("TarifaANTT")]
        TarifaANTT = 4,
        [Description("Cancelamento")]
        Cancelamento = 5,
        [Description("Tarifas")]
        Tarifas = 6
    }
    
    public enum Status
    {
        Bloqueado = 0,
        Aberto = 1,
        Baixado = 2,
        Cancelado = 3,
        Processamento = 4,
        Todos = 5
    }
    
    public enum FormaPagamento
    {
        Deposito = 0,
        Cartao = 1,
        Cheque = 2,
        Outros = 3,
        Pix = 4,
        Antecipacao = 5,
        RetencaoAntecipacao = 6
    }
    
    public enum FormaPagamentoEvento
    {
        [Description("Deposito")]
        Deposito = 1,
        [Description("Pix")]
        Pix = 4,
        [Description("Antecipacao")]
        Antecipacao = 5,
        [Description("RetencaoAntecipacao")]
        RetencaoAntecipacao = 6,
    }
    
    public enum StatusPagamentoTransacao
    {
        Baixado = 1,
        Pendente = 2,
        NaoExecutado = 3,
        Cancelado = 4
    }
    
    public enum FormaPagamentoTransacaoPedagio
    {
        P2P = 0
    }
    public enum TipoTransacao
    {
        Pedagio = 0,
        Tarifa = 1,
        CancelamentoPedagio = 2,
        ComplementoPedagio = 3
    }
}
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Net.Mail;
using System.Threading.Tasks;
using AutoMapper.QueryableExtensions;
using Microsoft.EntityFrameworkCore;
using NLog;
using SistemaInfo.BBC.Application.Email.Usuario;
using SistemaInfo.BBC.Application.External.Conductor.Interface;
using SistemaInfo.BBC.Application.Helpers;
using SistemaInfo.BBC.Application.Interface.UsuarioFrota;
using SistemaInfo.BBC.Application.Objects.Api.Portador;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.Pagamentos;
using SistemaInfo.BBC.Application.Objects.Web.Portador;
using SistemaInfo.BBC.Application.Objects.Web.PortadorCentroCusto;
using SistemaInfo.BBC.Application.Objects.Web.Transportador;
using SistemaInfo.BBC.Application.Objects.Web.UsuarioFrota;
using SistemaInfo.BBC.Domain.Components.Email;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.BBC.Domain.Models.Empresa.Repository;
using SistemaInfo.BBC.Domain.Models.Parametros.Repository;
using SistemaInfo.BBC.Domain.Models.Portador.Repository;
using SistemaInfo.BBC.Domain.Models.PortadorEmpresa;
using SistemaInfo.BBC.Domain.Models.PortadorEmpresa.Commands;
using SistemaInfo.BBC.Domain.Models.PortadorEmpresa.Repository;
using SistemaInfo.BBC.Domain.Models.Usuario.Repository;
using SistemaInfo.BBC.Domain.Models.UsuarioFrota.Commands;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.Services.UsuarioFrota
{
    public class UsuarioFrotaAppService :
        AppService<Domain.Models.Portador.Portador, IPortadorReadRepository, IPortadorWriteRepository>,
        IUsuarioFrotaAppService
    {
        private readonly ICartaoAppService _cartaoAppService;
        private readonly IEmpresaReadRepository _empresaReadRepository;
        private readonly IPortadorEmpresaReadRepository _portadorEmpresaReadRepository;
        private readonly IPortadorReadRepository _portadorReadRepository;
        private readonly IParametrosReadRepository _parametrosReadRepository;
        private readonly IUsuarioReadRepository _usuarioReadRepository;
        private INotificationEmailExecutor _notificationEmailExecutor;

        public UsuarioFrotaAppService(IAppEngine engine,
            IPortadorReadRepository readRepository,
            IPortadorWriteRepository writeRepository,
            ICartaoAppService cartaoAppService,
            IEmpresaReadRepository empresaReadRepository,
            IPortadorEmpresaReadRepository portadorEmpresaReadRepository,
            IPortadorReadRepository portadorReadRepository,
            INotificationEmailExecutor notificationEmailExecutor,
            IParametrosReadRepository parametrosReadRepository,
            IUsuarioReadRepository usuarioReadRepository)
            : base(engine, readRepository, writeRepository)
        {
            _cartaoAppService = cartaoAppService;
            _empresaReadRepository = empresaReadRepository;
            _portadorEmpresaReadRepository = portadorEmpresaReadRepository;
            _portadorReadRepository = portadorReadRepository;
            _notificationEmailExecutor = notificationEmailExecutor;
            _parametrosReadRepository = parametrosReadRepository;
            _usuarioReadRepository = usuarioReadRepository;
        }

        #region Operações portador

        private async Task<IQueryable<Domain.Models.Portador.Portador>> BuscarPortador()
        {
            var lPortador = Repository.Query
                .Include(x => x.UsuarioBloqueio)
                .Include(x => x.UsuarioDesbloqueio)
                .Include(x => x.UsuarioCancelamento)
                .Include(x => x.UsuarioDesbloqueioMobile)
                .Include(x => x.UsuarioCadastro)
                .Include(x => x.EmpresaFrota)
                .AsQueryable();
            var empresasHabilitadas = await _usuarioReadRepository.GetEmpresasAcessoUsuario(Engine.User.Id);
            
            if (!empresasHabilitadas.IsEmpty()) {
                lPortador = lPortador.Where(x =>
                        x.EmpresaIdFrota.HasValue && empresasHabilitadas.Contains(x.EmpresaIdFrota.Value));
            }
            
            if (Engine.User.IsNivelAdministradora ||
                Engine.User.IsNivelSuperUsuario) return lPortador;
            
            var lPortadorlist = lPortador.ToList();

            lPortadorlist.ForEach(portador =>
            {
                portador.Ativo = portador.EmpresaIdFrota == Engine.User.EmpresaId ? portador.Ativo : 0;
            });
            
            lPortador = lPortadorlist.AsQueryable();
            return lPortador;
        }
       
        private (List<Domain.Models.Portador.Portador> portadores, int total) AplicarFiltro(IQueryable<Domain.Models.Portador.Portador> aPortador, int take, int page, OrderFilters orderFilters,
            List<QueryFilters> filters)
        {
            aPortador = aPortador.Where(x => x.Atividade == EAtividade.Frota);

            var statusQuery = filters.Where(x => x.Campo == "status");

            // Verificar se existe filtro específico para status "normal"
            var statusNormalFilter = statusQuery.FirstOrDefault(x => x.Valor == "1" || x.Valor.ToLower() == "Ativo");
            var statusNormalInativoFilter = statusQuery.FirstOrDefault(x => x.Valor == "3" || x.Valor.ToLower() == "Inativo");
            
            if (statusNormalFilter != null)
            {
                // Para status "normal", filtrar por Status = Normal E Ativo = 1
                aPortador = aPortador.Where(x => x.Status == EStatusPortador.Normal && x.Ativo == 1);
                filters = filters.Where(x => x.Campo != "status").ToList();
            }

            if (statusNormalInativoFilter != null)
            {
                aPortador = aPortador.Where(x => x.Status == EStatusPortador.Normal && x.Ativo == 0);
                filters = filters.Where(x => x.Campo != "status").ToList();
            }

            aPortador = aPortador.AplicarFiltrosDinamicos(filters);

            if (statusQuery.IsEmpty() || statusNormalFilter != null)
            {
                // Se não há filtro de status ou já foi tratado o status normal, apenas excluir cancelados
                if (statusNormalFilter == null)
                {
                    aPortador = aPortador.Where(x => x.Status != EStatusPortador.Cancelado);
                }
            }
            
            aPortador = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                ? aPortador.OrderByDescending(o => o.Id)
                : aPortador.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            var countTotal = aPortador.Count();
            
            var lRetorno = aPortador.Skip((page - 1) * take)
                .Take(take)
                .ToList();
            
            return (lRetorno, countTotal);
        }
        
        // private (List<Domain.Models.Portador.Portador> portadores, int total) AplicarFiltro(IQueryable<Domain.Models.Portador.Portador> aPortador, int take, int page, OrderFilters orderFilters,
        //     List<QueryFilters> filters)
        // {
        //     aPortador = aPortador.Where(x => x.Atividade == EAtividade.Frota);
        //
        //     var query = filters.Where(x => x.Campo == "status");
        //     
        //     aPortador = aPortador.AplicarFiltrosDinamicos(filters);
        //     if (query.IsEmpty())
        //     {
        //         aPortador = aPortador.Where(x => x.Status != EStatusPortador.Cancelado);
        //     }
        //     
        //     aPortador = string.IsNullOrWhiteSpace(orderFilters?.Campo)
        //         ? aPortador.OrderByDescending(o => o.Id)
        //         : aPortador.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");
        //
        //     var countTotal = aPortador.Count();
        //     
        //     var lRetorno = aPortador.Skip((page - 1) * take)
        //         .Take(take)
        //         .ToList();
        //     
        //     return (lRetorno, countTotal);
        // }
        
        public ConsultarGridUsuarioFrotaResponse ConsultarGrid(int take, int page, OrderFilters orderFilters,
            List<QueryFilters> filters)
        {
            var lPortador = BuscarPortador().Result;
            var (lPortadorFilter, total) = AplicarFiltro(lPortador, take, page, orderFilters, filters);

            var retorno = Mapper.Map<List<ConsultarGridUsuarioFrota>>(lPortadorFilter);
            var totalTentativas = _parametrosReadRepository.GetTotalTentativaErroSenhaPortadorFrota().Result;
            foreach (var portador in retorno)
            {
                portador.QuantidadeErroSenhaVsParametro = $"{portador.QuantidadeErroSenha}/{totalTentativas}";
                portador.PertenceEmpresaFrota = portador.empresaIdFrota == Engine.User.EmpresaId ? 1 : 0;
            }
            
            return new ConsultarGridUsuarioFrotaResponse()
            {
                items = retorno,
                totalItems = total
            };
        }
        
        public async Task<ConsultarPorIdUsuarioFrotaResponse> ConsultarPorId(int id)
        {
            try
            {
                new LogHelper().LogOperationStart("ConsultarPorId");

                if (id < 0)
                {
                    throw new Exception("ID inválido!");
                }

                var lDados = Repository.Query.GetByIdIncludes(id);
                
                await ValidarPermissaoEmpresaAsync(lDados.EmpresaIdFrota);
                
                var lPortador = Mapper.Map<ConsultarPorIdUsuarioFrotaResponse>(lDados);

                lPortador.PortadorCentroCusto = Repository.Query.GetCentroCusto(id)
                    .ProjectTo<PortadorCentroCustoResp>().ToList();
                
                if (lDados.EmpresaIdFrota > 0)
                {
                    lPortador.EmpresaId = lDados.EmpresaIdFrota;
                    lPortador.EmpresaNome = lDados.EmpresaFrota.NomeFantasia;
                }
                else
                {
                    lPortador.EmpresaId = _portadorEmpresaReadRepository
                        .FirstOrDefault(x => x.PortadorId == lPortador.Id)?
                        .EmpresaId;

                    lPortador.EmpresaNome = _empresaReadRepository
                        .FirstOrDefault(x => x.Id == lPortador.EmpresaId)?
                        .NomeFantasia;
                }

                var lConta = _cartaoAppService.ConsultarContas(null, null, null, null, lPortador.CpfCnpj, true);

                if (lConta != null)
                {
                    if (lConta.Result.content != null)
                    {
                        if (!lConta.Result.content.FirstOrDefault().Sucesso)
                        {
                            throw new Exception("");
                        }
                        var lContaId = lConta.Result.content.FirstOrDefault().id;
                        var lDadosConta = _cartaoAppService.ConsultarContasPorId(lContaId).Result;
                    }
                }

                return lPortador;
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar ConsultarPorId");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("ConsultarPorId");
            }
        }

        public ConsultarPorIdUsuarioFrotaResponse BuscarPorId(int id)
        {
            throw new NotImplementedException();
        }

        public Task<IEnumerable<ConsultarPorIdUsuarioFrotaResponse>> BuscarTodos()
        {
            throw new NotImplementedException();
        }

        public async Task<RespPadrao> EnviarEmailAvisoDesvinculacaoPortador(UsuarioFrotaSalvarCommand portador,
            int empresaAntigaId)
        {
            if (portador == null || empresaAntigaId == 0)
            {
                return new RespPadrao
                {
                    sucesso = false,
                    mensagem = "Parâmetros portador ou empresaAntigaId inválidos."
                };
            }

            var lEmpresa = await _empresaReadRepository.GetByIdAsync(empresaAntigaId);

            var lGestoresDeCadastrosDaAntigaEmpresa = await _usuarioReadRepository
                .Where(u => u.EmpresaId == empresaAntigaId)
                .Include(u => u.GrupoUsuario)
                .ThenInclude(g => g.GrupoUsuarioMenu)
                .ThenInclude(m => m.Menu)
                .Where(u => u.GrupoUsuario.GrupoUsuarioMenu.FirstOrDefault(m => m.Menu.Descricao == "Portador") != null)
                .ToListAsync();

            foreach (var lGestor in lGestoresDeCadastrosDaAntigaEmpresa)
            {
                EmailUsuarioGestorNotificacaoDesvinculoPortador.EnviarEmail(_notificationEmailExecutor,
                    lGestor.Email, portador.CpfCnpj, portador.Nome, lGestor.Nome, lEmpresa.NomeFantasia);
            }

            return new RespPadrao
            {
                sucesso = true,
                mensagem = "Emails enviados com sucesso!"
            };
        }

        public async Task<RespPadrao> Save(UsuarioFrotaRequest request)
        {
            try
            {
                var lRetornoRecuperarSenha = false;

                //Se um usuario não administrador está tentando cadastrar pra outra empresa que não a dele
                if (Engine.User.EmpresaId != 0 && request.EmpresaId != Engine.User.EmpresaId)
                {
                    return new RespPadrao
                    {
                        sucesso = false,
                        mensagem = "Empresa selecionada diferente da empresa do usuário atual."
                    };
                }

                var lEmpresaId = Engine.User.EmpresaId != 0 ? Engine.User.EmpresaId : request.EmpresaId;

                var lPortadorJaExistente = await _portadorReadRepository.GetByCpfCnpjAsync(request.CpfCnpj);

                int? lEmpresaAntigaId = lPortadorJaExistente?.EmpresaIdFrota;

                List<PortadorEmpresa> lJaCadastradoNessaEmpresa;
                //Se for um cadastro novo
                if (request.Id == "Auto" || request.Id.IsNullOrWhiteSpace())
                {
                    //Consultas para cadastros existentes do CPF/CNPJ
                    var lPortadorEmpresa = _portadorEmpresaReadRepository
                        .Include(x => x.Portador);

                    var lCadastradoEmOutraEmpresa = lPortadorEmpresa
                        .Where(x => x.EmpresaId != lEmpresaId && x.Portador.CpfCnpj == request.CpfCnpj)
                        .ToList();

                    lJaCadastradoNessaEmpresa = lPortadorEmpresa
                        .Where(x => x.EmpresaId == lEmpresaId && x.Portador.CpfCnpj == request.CpfCnpj)
                        .ToList();

                    //Se não encontrou empresa
                    if (lEmpresaId == 0 || lEmpresaId == null)
                    {
                        return new RespPadrao
                        {
                            sucesso = false,
                            mensagem = "Empresa não encontrada."
                        };
                    }

                    //Se ta cadastrado em outra empresa
                    if (lCadastradoEmOutraEmpresa.Count > 0 && lJaCadastradoNessaEmpresa.Count == 0)
                    {
                        if (request.Id == "Auto")
                        {
                            lRetornoRecuperarSenha = RecuperarSenha(new PortadorRecuperarSenhaRequest()
                            {
                                CpfCnpj = request.CpfCnpj,
                                Mobile = false
                            }).Result.sucesso;
                        }
                    }

                    request.Id = null;
                    request.CpfCnpj.OnlyNumbers();
                    var lCpfCnpjCadastrado = await Repository.Query
                        .FirstOrDefaultAsync(o => o.CpfCnpj == request.CpfCnpj);

                    if (lCpfCnpjCadastrado != null && lCpfCnpjCadastrado.Id > 0)
                    {
                        request.Id = lCpfCnpjCadastrado.Id.ToString();
                    }
                }

                var lRequestIdSafe = request.Id.ToIntSafe();

                if (request.Id.ToInt() > 0)
                    request.SenhaApi = !request.SenhaApi.IsNullOrWhiteSpace()
                        ? request.SenhaApi.GetHashSha1()
                        : Repository.Query.GetById(request.Id.ToInt()).SenhaApi;
                
                var lEmpresaPortadorExistente = await _portadorEmpresaReadRepository
                    .GetByPortadorIdAndEmpresaId(lRequestIdSafe, request.EmpresaId ?? 0);
                var lPortador = Mapper.Map<UsuarioFrotaSalvarComRetornoCommand>(request);
                lPortador.ValidarCadastro();
                lPortador.EmpresaIdFrota = request.EmpresaId;

                var lRetorno = await Engine.CommandBus.SendCommandAsync<Domain.Models.Portador.Portador>(lPortador);
                if (lEmpresaAntigaId != null && lRetorno.EmpresaIdFrota != lEmpresaAntigaId)
                {
                    await EnviarEmailAvisoDesvinculacaoPortador(lPortador, (int)lEmpresaAntigaId);
                }

                if (lEmpresaPortadorExistente == null)
                {
                    await SalvarPortadorEmpresa(new PortadorEmpresaRequest
                    {
                        EmpresaId = lEmpresaId ?? 0,
                        PortadorId = lRetorno?.Id ?? 0
                    });
                }

                if (string.IsNullOrWhiteSpace(request.Id) && request.Atividade == EAtividade.Frota)
                {
                    lRetornoRecuperarSenha = RecuperarSenha(new PortadorRecuperarSenhaRequest()
                    {
                        CpfCnpj = request.CpfCnpj,
                        Mobile = false
                    }).Result.sucesso;
                }

                if (!lRetornoRecuperarSenha && string.IsNullOrWhiteSpace(request.Id) &&
                    request.Atividade == EAtividade.Frota)
                {
                    return new RespPadrao
                    {
                        sucesso = false,
                        mensagem = "Cadastro realizado, porém senha não enviada para o portador!"
                    };
                }
                
                return new RespPadrao
                {
                    sucesso = true,
                    mensagem = "Operação realizada com sucesso!"
                };
            }
            catch (Exception e)
            {
                return new RespPadrao
                {
                    sucesso = false,
                    mensagem = e.Message
                };
            }
        }
        
        public async Task<RespPadrao> AlterarStatus(UsuarioFrotaStatusRequest lPortadorStatus)
        {
            try
            {
                var lPortador = Mapper.Map<UsuarioFrotaAlterarStatusCommand>(lPortadorStatus);
                await Engine.CommandBus.SendCommandAsync(lPortador);
                return new RespPadrao
                {
                    sucesso = true,
                    mensagem = "Operação realizada com sucesso!"
                };
            }
            catch (Exception e)
            {
                return new RespPadrao
                {
                    sucesso = false,
                    mensagem = "Erro interno ao tentar alterar o status do portador: " + e.Message
                };
            }
        }

        public async Task<RespPadrao> Bloquear(UsuarioFrotaStatusRequest lPortadorStatus)
        {
            try
            {
                
                var lDados = Repository.Query.GetByIdIncludes(lPortadorStatus.Id);
                if (lDados == null)
                {
                    throw new Exception("Portador não encontrado");
                }
                
                await ValidarPermissaoEmpresaAsync(lDados.EmpresaIdFrota);
                
                var lPortador = Mapper.Map<UsuarioFrotaBloquearCommand>(lPortadorStatus);
                await Engine.CommandBus.SendCommandAsync(lPortador);
                return new RespPadrao
                {
                    sucesso = true,
                    mensagem = "Operação realizada com sucesso!"
                };
            }
            catch (Exception e)
            {
                return new RespPadrao
                {
                    sucesso = false,
                    mensagem = "Erro interno ao tentar alterar o status do portador: " + e.Message
                };
            }
        }

        public async Task<RespPadrao> Cancelar(UsuarioFrotaCancelarRequest lPortadorStatus)
        {
            try
            {
                var lDados = Repository.Query.GetByIdIncludes(lPortadorStatus.Id);
                if (lDados == null)
                {
                    throw new Exception("Portador não encontrado");
                }
                
                if (lDados.Status == EStatusPortador.Cancelado)
                {
                    throw new Exception("Portador já cancelado");
                }
                
                await ValidarPermissaoEmpresaAsync(lDados.EmpresaIdFrota);
                
                var lPortador = Mapper.Map<UsuarioFrotaCancelarCommand>(lPortadorStatus);
                await Engine.CommandBus.SendCommandAsync(lPortador);
                return new RespPadrao
                {
                    sucesso = true,
                    mensagem = "Operação realizada com sucesso!"
                };
            }
            catch (Exception e)
            {
                return new RespPadrao
                {
                    sucesso = false,
                    mensagem = "Não foi possível cancelar o usuário frota." + e.Message
                };
            }
        }
        

        public Domain.Models.Portador.Portador ConsultarPorCpfCnpj(string cpfCnpj)
        {
            return Repository.Query.Where(p => p.CpfCnpj == cpfCnpj).FirstOrDefault();
        }
        
        public bool Existe(int id)
        {
            try
            {
                new LogHelper().LogOperationStart("Existe");
                throw new NotImplementedException();
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar Existe");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("Existe");
            }
        }

        public async Task<RespPadrao> SalvarPortadorEmpresa(PortadorEmpresaRequest lPortadorEmpresa)
        {
            try
            {
                lPortadorEmpresa.EmpresaId =
                    Engine.User.EmpresaId == 0 ? lPortadorEmpresa.EmpresaId : Engine.User.EmpresaId;

                await Engine.CommandBus.SendCommandAsync(Mapper.Map<PortadorEmpresaSalvarCommand>(lPortadorEmpresa));

                return new RespPadrao
                {
                    sucesso = true,
                    mensagem = "Registro salvo com sucesso!"
                };
            }
            catch (Exception e)
            {
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = e.Message
                };
            }
        }
        
        public async Task<RespPadrao> RecuperarSenha(PortadorRecuperarSenhaRequest model)
        {
            var lLog = LogManager.GetCurrentClassLogger();

            #region Valida parâmetros de e-mail

            var lParametroEmail = await _parametrosReadRepository
                .GetParametrosAsync(-1, Domain.Models.Parametros.Parametros.TipoDoParametro.EmailUsuario,
                    Domain.Models.Parametros.Parametros.TipoDoValor.Criptografia);

            if (lParametroEmail?.ValorCriptografado == null)
                return new RespPadrao
                {
                    sucesso = false,
                    data = null,
                    mensagem = "Dados inválidos, não foi possível recuperar a senha!"
                };

            var lParametroEmailSenha = await _parametrosReadRepository
                .GetParametrosAsync(-1, Domain.Models.Parametros.Parametros.TipoDoParametro.EmailSenha,
                    Domain.Models.Parametros.Parametros.TipoDoValor.Criptografia);

            if (lParametroEmailSenha?.ValorCriptografado == null)
                return new RespPadrao
                {
                    sucesso = false,
                    data = null,
                    mensagem = "Dados inválidos, não foi possível recuperar a senha!"
                };

            #endregion

            #region Busca o portador e altera a senha

            var lPortador = await _portadorReadRepository.GetByCpfCnpjAsync(model.CpfCnpj);

            if (lPortador == null)
            {
                return new RespPadrao
                {
                    sucesso = false,
                    data = null,
                    mensagem = "Dados inválidos, não foi possível recuperar a senha!"
                };
            }

            if (!string.IsNullOrWhiteSpace(model.Email))
                if (lPortador.Email != model.Email)
                    return new RespPadrao(false, "Dados inválidos, não foi possível recuperar a senha!");
            
            var lSenhaAntiga = lPortador.SenhaApi;
            var (sucesso, msg, lSenhaNova) = await AlterarSenha(lPortador);
            if (!sucesso)
            {
                return new RespPadrao(false, msg);
            }
            
            #endregion

            #region Envio de email
            
            return await EnviarEmailNovaSenha(lPortador, lSenhaNova, lSenhaAntiga, lLog);
            
            #endregion
        }

        private async Task<RespPadrao> EnviarEmailNovaSenha(Domain.Models.Portador.Portador lPortador, String lSenhaNova, string lSenhaAntiga, Logger lLog)
        {
            
            var lCaminhoAplicacao = AppDomain.CurrentDomain.BaseDirectory;
            var lRemetente = "<EMAIL>";
            var lPortadorNovo = lPortador.DataUltimoAcesso is null;
            var lUrlAplicativo = await _parametrosReadRepository.GetByTipoDoParametroAsync(Domain.Models.Parametros
                .Parametros.TipoDoParametro.LinkAplicativoCadastroPortador);
            var styleUrlApp = string.IsNullOrWhiteSpace(lUrlAplicativo?.Valor) || !lPortadorNovo
                ? "style='display: none'"
                : $"href='{lUrlAplicativo.Valor}'";
            var lCpfCnpjMascarado = lPortador.CpfCnpj.MascaraCpfCnpjEmail();
            try
            {
                using (var lStreamReader =
                       new StreamReader(lCaminhoAplicacao + @"\Content\Email\Usuario\recuperar-senha-portador.html"))
                {
                    var lEmailHtml = await lStreamReader.ReadToEndAsync();

                    var lEmailbody = lPortadorNovo
                        ? $"&nbsp;&nbsp;&nbsp; Olá, {lPortador.Nome}, seja bem-vindo! " +
                          "Abaixo suas informações de acesso ao (app) BBC Frota: "
                        : $"&nbsp;&nbsp;&nbsp; Olá, {lPortador.Nome}! " +
                          "Sua senha foi redefinida automaticamente. " +
                          "Para sua segurança, altere-a novamente ao realizar o login no sistema.";
                    lEmailHtml = lEmailHtml
                        .Replace("{NOME_USUARIO}", lPortador.Nome)
                        .Replace("{NOVA_SENHA}", lSenhaNova.ToString())
                        .Replace("{STYLE-LINK}", styleUrlApp)
                        .Replace("{NOVO_USUARIO}", lCpfCnpjMascarado)
                        .Replace("{STYLE_USUARIO}", lPortadorNovo ? "" : "style='display: none'")
                        .Replace("{CORPO}", "corpoNovoUsuario")
                        .Replace("{EMAIL_TITLE}", lPortadorNovo ? "PRIMEIRO ACESSO" : "RECUPERAÇÃO DE SENHA")
                        .Replace("{EMAIL_BODY}", lEmailbody);

                    var lView = AlternateView.CreateAlternateViewFromString(lEmailHtml, null, "text/html");

                    var lResultEmail = await _notificationEmailExecutor.ExecuteAsync(new Domain.Components.Email.Email
                    {
                        To = new[] { new Domain.Components.Email.Email.EmailAddress { Address = lPortador.Email } },
                        Priority = MailPriority.High,
                        Subject = lPortadorNovo ? "PRIMEIRO ACESSO" : "RECUPERAÇÃO DE SENHA",
                        IsBodyHtml = true,
                        AlternateView = lView,
                        From = new Domain.Components.Email.Email.EmailAddress { Address = lRemetente }
                    });

                    if (!lResultEmail.Sucesso)
                    {
                        throw new Exception("Falha ao enviar e-mail.");
                    }
                }
            }
            catch (Exception e)
            {
                lLog.Error(e);

                if (!string.IsNullOrEmpty(lSenhaAntiga))
                    await Repository.Command.AlterarSenhaBase(lSenhaAntiga, lPortador, false);

                return new RespPadrao
                {
                    sucesso = false,
                    data = lPortador.Id,
                    mensagem = "Não foi possível alterar sua senha. E-mail não enviado."
                };
            }
            
            return new RespPadrao
            {
                sucesso = true,
                data = lPortador.Id,
                mensagem = "Senha alterada com sucesso! E-mail enviado."
            };
        }
        
        private async Task<(bool sucesso, string mensagem, string senha)> AlterarSenha(Domain.Models.Portador.Portador aPortador)
        {
            var lSenhaNova = SenhaGenerator.Gerar();
            try
            {
                await Repository.Command.AlterarSenhaBase(lSenhaNova, aPortador);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return (false, "Não foi possível alterar sua senha! " + e.Message, null);
            }
            return (true, "Senha alterada com sucesso!", lSenhaNova);
        }
        
        #endregion

        #region Transportador

        public ConsultarGridTransportadorResponse ConsultarGridTransportador(int take, int page,
            OrderFilters orderFilters,
            List<QueryFilters> filters)
        {
            var lTransportador = Repository.Query.Where(x => x.Visibilidade == EVisibilidadePortador.Transportador);

            //Portador x empresa
            if (Engine.User.EmpresaId > 0)
            {
                lTransportador = lTransportador.Where(c => c.EmpresaIdFrota == Engine.User.EmpresaId);
            }

            lTransportador = lTransportador.AplicarFiltrosDinamicos(filters);
            lTransportador = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                ? lTransportador.OrderByDescending(o => o.Id)
                : lTransportador.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            var retorno = lTransportador.Skip((page - 1) * take)
                .Take(take)
                .ProjectTo<ConsultarGridTransportador>(Engine.Mapper.ConfigurationProvider).ToList();

            return new ConsultarGridTransportadorResponse()
            {
                items = retorno,
                totalItems = lTransportador.Count()
            };
        }
        
        #endregion
        
        
        private async Task ValidarPermissaoEmpresaAsync(int? empresaIdFrota)
        {
            var empresas = await _usuarioReadRepository.GetEmpresasAcessoUsuario(Engine.User.Id);
            var isAdmin = empresas == null || !empresas.Any();

            if (!isAdmin)
            {
                if (empresaIdFrota == null || !empresas.Contains(empresaIdFrota.Value))
                {
                    throw new Exception("Você não possui permissão para realizar essa operação");
                }
            }
        }
    }
    
    
}
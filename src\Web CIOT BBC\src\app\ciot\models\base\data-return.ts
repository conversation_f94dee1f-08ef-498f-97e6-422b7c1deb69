import { Excecao } from "../common/excecao";

export interface DataReturn<T> { 
    excecao: Excecao;
    data: T;

    Retorno: DadosRetornoPadrao;
    Sucesso: boolean;
    Mensagem: string;
}

class DadosRetornoPadrao {
    CIOT: string;
    DataCancelamento: Date;
    ProtocoloCancelamento: string;
    Sucesso: boolean; 
    CodigoVerificador: string;
    AvisoTransportador: string;
    EmContingencia: boolean;
    ProtocoloErro: string;
    SenhaAlteracao: string;
    Excecao: {
        Tipo: number;
        Codigo: string;
        Mensagem: string;
    };
}
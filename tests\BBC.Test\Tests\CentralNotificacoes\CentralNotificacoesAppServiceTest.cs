using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using BBC.Test.Tests.CentralNotificacoes.Fixture;
using Moq;
using SistemaInfo.BBC.Application.Objects.Web.CentralNotificacoes;
using SistemaInfo.BBC.Application.Services.CentralNotificacoes;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Models.Notificacao;
using SistemaInfo.BBC.Domain.Models.Notificacao.Commands;
using SistemaInfo.BBC.Domain.Models.Notificacao.Repository;
using SistemaInfo.Framework.CQRS.Bus;
using Xunit;

namespace BBC.Test.Tests.CentralNotificacoes
{
    [Collection(nameof(CentralNotificacoesCollection))]
    public class CentralNotificacoesAppServiceTest
    {
        private readonly CentralNotificacoesFixture _fixture;
        private readonly CentralNotificacoesAppService _appService;
        private readonly Mock<INotificacaoReadRepository> _readRepository;

        public CentralNotificacoesAppServiceTest(CentralNotificacoesFixture fixture)
        {
            _fixture = fixture;
            _appService = fixture.Mocker.CreateInstance<CentralNotificacoesAppService>();
            _readRepository = fixture.Mocker.GetMock<INotificacaoReadRepository>();
        }
    
        private bool TemMesmaEstrutura(object obj1, object obj2)
        {
            var propriedades1 = obj1.GetType().GetProperties(BindingFlags.Public | BindingFlags.Instance);
            var propriedades2 = obj2.GetType().GetProperties(BindingFlags.Public | BindingFlags.Instance);

            if (propriedades1.Length != propriedades2.Length)
                return false;

            return propriedades1.All(p1 =>
                propriedades2.Any(p2 =>
                    p1.Name == p2.Name &&
                    p1.PropertyType == p2.PropertyType));
        }
        
        [Fact(DisplayName = "Consulta padrão da grid CentralNotificacoes")]
        [Trait("CentralNotificacoesAppService", "ConsultarGridCentralNotificacoes")]
        public void CentralNotificacoesDadosGridCentralNotificacoes_ConsultaPadrao_DeveRetornarGridCentralNotificacoesResponse()
        {
            //Arrange
            var lOrderFilters = new OrderFilters
            {
                Campo = "id",
                Operador = EOperadorOrder.Descending
            };

            //Action
            var lResponse = _appService
                .ConsultarGridCentralNotificacoes(1,DateTime.Now.ToShortDateString(),DateTime.Now.ToShortDateString(), new Random().Next(0, 1),1,new Random().Next(0, 1), lOrderFilters, new List<QueryFilters>());

            //Assert
            Assert.True(TemMesmaEstrutura(lResponse, new ConsultarGridCentralNotificacoesResponse()));
        }

        [Fact(DisplayName = "Consulta CentralNotificacoes por Id inválido")]
        [Trait("CentralNotificacoesAppService", "ConsultarPorId")]
        public void CentralNotificacoesConsultarPorId_IdInvalido_DeveRetornarNull()
        {
            // Arrange
            var centralNotificacoesId = -1; // ID negativo inválido
            _readRepository.Setup(c => c.FirstOrDefault(a => a.Id == centralNotificacoesId)).Returns(null as SistemaInfo.BBC.Domain.Models.Notificacao.Notificacao);

            // Action
            var response = _appService.ConsultarPorId(centralNotificacoesId);

            // Assert
            Assert.Null(response);
        }
        
        [Fact(DisplayName = "Save exceção")]
        [Trait(nameof(CentralNotificacoesAppService), nameof(CentralNotificacoesAppService.Save))]
        public async void Save_Excecao_RetornaFalha()
        {
            // Arrange
            var request = _fixture.GerarCentralNotificacoesRequest();
            var msgExcecao = "Mensagem da exceção.";
            var msgEsperada = "Ocorreu um erro ao salvar a notificação: " + msgExcecao;
            var sucessoEsperado = false;

            _fixture.Mocker.GetMock<ICommandBus>()
                .Setup(c => c.SendCommandAsync<Notificacao>(
                    It.Is<NotificacaoSalvarComRetornoCommand>(c => c.Id == request.Id)))
                .ThrowsAsync(new Exception(msgExcecao));
            //Act
            var resp = await _appService.Save(request);

            //Assert
            Assert.Equal(sucessoEsperado, resp.sucesso);
            Assert.Equal(msgEsperada, resp.mensagem);
            Assert.Null(resp.data);
            Assert.Null(resp.id);
        }
        
        [Fact(DisplayName = "Save sucesso")]
        [Trait(nameof(CentralNotificacoesAppService), nameof(CentralNotificacoesAppService.Save))]
        public async void Save_RetornaSucesso()
        {
            // Arrange
            var request = _fixture.GerarCentralNotificacoesRequest();
            var msgEsperada = "Notificação salva com sucesso.";
            var sucessoEsperado = true;

            //Act
            var resp = await _appService.Save(request);

            //Assert
            Assert.Equal(sucessoEsperado, resp.sucesso);
            Assert.Equal(msgEsperada, resp.mensagem);
            Assert.Null(resp.data);
            Assert.Null(resp.id);
        }
        
        [Fact(DisplayName = "AlteraStatus com exceção")]
        [Trait(nameof(CentralNotificacoesAppService), nameof(CentralNotificacoesAppService.Save))]
        public async void AlterarStatus_Excecao_RetornaFalha()
        {
            var msgExcecao = "Mensagem da exceção.";
            var msgEsperada = "Ocorreu um erro ao salvar a notificação: " + msgExcecao;
            var req = new CentralNotificacoesRequest();

            _fixture.Mocker.GetMock<ICommandBus>()
                .Setup(c => c.SendCommandAsync<Notificacao>(
                    It.IsAny<NotificacaoSalvarComRetornoCommand>()))
                .ThrowsAsync(new Exception(msgExcecao));

            var retorno = await _appService.Save(req);

            Assert.False(retorno.sucesso);
            Assert.Equal(msgEsperada, retorno.mensagem);
            Assert.Null(retorno.data);
        }
    }
}
using System;
using System.IO;
using Xunit;

namespace SistemaInfo.BBC.Application.Tests.Scripts
{
    public class LoggingScriptTests
    {
        [Fact]
        public void AddLoggingScript_ShouldExist()
        {
            // Arrange
            string scriptPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "..", "..", "..", "..", "add-logging-to-services.ps1");
            
            // Act
            bool scriptExists = File.Exists(scriptPath);
            
            // Assert
            Assert.True(scriptExists, "O script add-logging-to-services.ps1 deve existir");
        }
        
        [Fact]
        public void AddLoggingScript_ShouldContainExpectedPatterns()
        {
            // Arrange
            string scriptPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "..", "..", "..", "..", "add-logging-to-services.ps1");
            
            // Act
            string scriptContent = File.Exists(scriptPath) ? File.ReadAllText(scriptPath) : string.Empty;
            
            // Assert
            Assert.Contains("using SistemaInfo.BBC.Infra.CrossCutting.Logging;", scriptContent);
            Assert.Contains("LogHelper.LogOperationStart", scriptContent);
            Assert.Contains("LogHelper.Error", scriptContent);
            Assert.Contains("LogHelper.LogOperationEnd", scriptContent);
            Assert.Contains("try", scriptContent);
            Assert.Contains("catch", scriptContent);
            Assert.Contains("finally", scriptContent);
        }
        
        [Fact]
        public void AddLoggingScript_ShouldAddUsingStatement()
        {
            // Arrange
            string scriptPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "..", "..", "..", "..", "add-logging-to-services.ps1");
            
            // Act
            string scriptContent = File.Exists(scriptPath) ? File.ReadAllText(scriptPath) : string.Empty;
            
            // Assert
            Assert.Contains("Add using statement if not present", scriptContent);
            Assert.Contains("using SistemaInfo.BBC.Infra.CrossCutting.Logging;", scriptContent);
        }
        
        [Fact]
        public void AddLoggingScript_ShouldAddTryCatchFinally()
        {
            // Arrange
            string scriptPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "..", "..", "..", "..", "add-logging-to-services.ps1");
            
            // Act
            string scriptContent = File.Exists(scriptPath) ? File.ReadAllText(scriptPath) : string.Empty;
            
            // Assert
            Assert.Contains("Add try after the opening brace", scriptContent);
            Assert.Contains("Add catch and finally before the closing brace", scriptContent);
        }
    }
}

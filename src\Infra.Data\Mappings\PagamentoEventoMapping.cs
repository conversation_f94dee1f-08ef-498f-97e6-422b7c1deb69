using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Domain.Models.PagamentoEvento;
using SistemaInfo.Framework.EntityFramework.Configuration;

namespace SistemaInfo.BBC.Infra.Data.Mappings
{
    public class PagamentoEventoMapping : EntityTypeConfiguration<PagamentoEvento>
    {
        public override void Map(EntityTypeBuilder<PagamentoEvento> builder)
        {
            builder.ToTable("PagamentoEvento");
            builder.HasKey(b => b.Id);
            
            builder.Property(b => b.Id).IsRequired().HasColumnName("Id").ValueGeneratedOnAdd();
            builder.Property(b => b.EmpresaId).HasColumnName("EmpresaId").HasColumnType("int");
            builder.Property(b => b.<PERSON>).IsRequired().HasColumnName("Valor").HasColumnType("decimal");
            builder.Property(b => b.Status).HasColumnName("Status").HasColumnType("int");
            builder.Property(b => b.MotivoPendencia).HasColumnName("MotivoPendencia").HasColumnType("varchar(200)");
            builder.Property(b => b.Tipo).HasColumnName("Tipo").HasColumnType("int");
            builder.Property(b => b.CodigoTransacao).HasColumnName("CodigoTransacao").HasColumnType("varchar(100)");
            builder.Property(b => b.FormaPagamento).HasColumnName("FormaPagamento").HasColumnType("int");
            builder.Property(b => b.ContadorReenvio).HasColumnName("ContadorReenvio").HasColumnType("int");
            builder.Property(b => b.ViagemId).IsRequired().HasColumnName("ViagemId").HasColumnType("int");
            builder.Property(b => b.DataBaixa).HasColumnName("DataBaixa").HasColumnType("timestamp");
            builder.Property(b => b.PagamentoExternoId).HasColumnName("PagamentoExternoId").HasColumnType("int");
            builder.Property(b => b.ContadorVerificacaoStatusPix).HasColumnName("ContadorVerificacaoStatusPix").HasColumnType("int");
            builder.Property(b => b.DataTerceiraVerificacaoStatusPix).HasColumnName("DataTerceiraVerificacaoStatusPix").HasColumnType("timestamp");
            builder.Property(b => b.Ocorrencia).HasColumnName("Ocorrencia").HasColumnType("varchar(500)");
            
            builder.Property(b => b.DataCancelamento).HasColumnName("DataCancelamento").HasColumnType("timestamp");
            builder.Property(b => b.DataSolicitacaoCancelamento).HasColumnName("DataSolicitacaoCancelamento").HasColumnType("timestamp");
            builder.Property(b => b.ValorCancelamento).HasColumnName("ValorCancelamento").HasColumnType("decimal");
            builder.Property(b => b.UsuarioCacelamentoId).HasColumnName("UsuarioCacelamentoId").HasColumnType("int");
            
            builder.Property(b => b.UsuarioCadastroId).HasColumnName("UsuarioCadastroId").HasColumnType("int");
            builder.Property(b => b.DataCadastro).IsRequired().HasColumnName("DataCadastro").HasColumnType("timestamp");
            builder.Property(b => b.UsuarioAlteracaoId).HasColumnName("UsuarioAlteracaoId").HasColumnType("int");
            builder.Property(b => b.DataAlteracao).HasColumnName("DataAlteracao").HasColumnType("timestamp");

            builder.Property(b => b.ValorTransferenciaMotorista).HasColumnName("ValorTransferenciaMotorista").HasColumnType("decimal");

            builder.HasOne(b => b.UsuarioCadastro).WithMany().HasForeignKey(b => b.UsuarioCadastroId);
            builder.HasOne(b => b.UsuarioAlteracao).WithMany().HasForeignKey(b => b.UsuarioAlteracaoId);
            builder.HasOne(b => b.Viagem).WithMany().HasForeignKey(b => b.ViagemId);
            builder.HasOne(b => b.Empresa).WithMany().HasForeignKey(b => b.EmpresaId);
            
            builder.Property(b => b.ValorTarifaBbc).HasColumnName("ValorTarifaBbc").HasColumnType("decimal");
            builder.Property(b => b.ValorTarifaPix).HasColumnName("ValorTarifaPix").HasColumnType("decimal");
            builder.Property(b => b.TarifaBbc).HasColumnName("TarifaBbc").HasColumnType("decimal");
            builder.Property(b => b.TarifaPix).HasColumnName("TarifaPix").HasColumnType("decimal");
            builder.Property(b => b.CobrancaTarifa).HasColumnName("CobrancaTarifa").HasColumnType("int");
            builder.Property(b => b.ChavePix).HasColumnName("ChavePix").HasColumnType("varchar(150)");
            builder.Property(b => b.Agencia).HasColumnName("Agencia").HasColumnType("varchar(150)");
            builder.Property(b => b.Conta).HasColumnName("Conta").HasColumnType("varchar(150)");
            builder.Property(b => b.CodigoBanco).HasColumnName("CodigoBanco").HasColumnType("varchar(150)");
            builder.Property(b => b.TipoConta).HasColumnName("TipoConta").HasColumnType("int");
            builder.Property(b => b.RecebedorAutorizado).HasColumnName("RecebedorAutorizado").HasColumnType("varchar(150)");
            builder.Property(b => b.Descricao).HasColumnName("Descricao").HasColumnType("varchar(200)");
            builder.Property(b => b.WebhookUrl).HasColumnName("WebhookUrl").HasColumnType("varchar(400)");
            
            builder.Property(b => b.JsonEnvio).HasColumnName("JsonEnvio").HasColumnType("json");
            builder.Property(b => b.JsonRetorno).HasColumnName("JsonRetorno").HasColumnType("json");
            builder.Property(b => b.JsonEnvioCancelamento).HasColumnName("JsonEnvioCancelamento").HasColumnType("json");
            builder.Property(b => b.JsonRetornoCancelamento).HasColumnName("JsonRetornoCancelamento").HasColumnType("json");
            builder.Property(b => b.DataRetorno).HasColumnName("DataRetorno").HasColumnType("timestamp");
            builder.Property(b => b.DataCadastroCancelamento).HasColumnName("DataCadastroCancelamento").HasColumnType("timestamp");
            builder.Property(b => b.DataRetornoCancelamento).HasColumnName("DataRetornoCancelamento").HasColumnType("timestamp");
            
            // Campo adicionando devido a viagem V2 e para CWI
            
            builder.Property(b => b.DataPrevisaoPagamento).HasColumnName("DataPrevisaoPagamento").HasColumnType("timestamp");

            builder.Property(b => b.StatusAntecipacaoParcelaProprietario).HasColumnName("StatusAntecipacaoParcelaProprietario")
                .HasColumnType("int")
                .HasDefaultValue(StatusAntecipacaoParcelaProprietario.NaoDisponivel);
            
            
            builder.Property(b => b.AntecipacaoMotivo).HasColumnName("AntecipacaoMotivo").HasColumnType("varchar(400)");
            builder.Property(b => b.DataAlteracaoAntecipacao).HasColumnName("DataAlteracaoAntecipacao").HasColumnType("timestamp");
            builder.Property(b => b.DataCadastroAntecipacao).HasColumnName("DataCadastroAntecipacao").HasColumnType("timestamp");

            builder.HasOne(c => c.Viagem)
                .WithMany(c => c.PagamentoEvento)
                .HasForeignKey(c => c.ViagemId);
        }
    }
}
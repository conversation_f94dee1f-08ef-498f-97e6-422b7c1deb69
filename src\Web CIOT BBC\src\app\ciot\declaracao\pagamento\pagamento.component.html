<div class="container main-container">
    <form novalidate [formGroup]="pagamentoForm">
        <div class="form-horizontal">
            <div class="row">
                <div class="col-sm-12 col-md-6 col-lg-3">
                    <div class="form-group required" [ngClass]="{'has-error': displayMessage.formaPagamento }">
                        <label class="control-label" for="formaPagamento">Forma de pagamento:</label>
                        <select class="form-control" formControlName="formaPagamento">
                            <option [ngValue]="1">Conta cartão</option>
                            <option [ngValue]="2">Transferência</option>
                            <option [ngValue]="3">Dep<PERSON>ito</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="row" id="idCpfCnpjPagamento">
                <div class="col-sm-12 col-md-6 col-lg-3">
                    <div class="form-group required" [ngClass]="{'has-error': displayMessage.cpfCnpjConta }">
                        <label class="control-label" for="cpfCnpjConta">CPF/CNPJ da conta</label>
                        <input [mask]="maskCpfCnpj" type="text" class="form-control" id="cpfCnpjConta"
                            formControlName="cpfCnpjConta" placeholder="Informe o CPF/CNPJ" />
                        <span class="text-danger" *ngIf="displayMessage.cpfCnpjConta">
                            <p [innerHTML]="displayMessage.cpfCnpjConta"></p>
                        </span>
                    </div>
                </div>
            </div>

            <div class="row" id="idBancos">
                <div class="col-sm-12 col-md-6 col-lg-10">
                    <div class="form-group required" [ngClass]="{'has-error': displayMessage.banco}">
                        <label class="control-label" for="banco">Banco:</label>
                        <app-autocomplete-bancos (choosedBanco)='onChoosedBanco($event)'></app-autocomplete-bancos>
                    </div>
                </div>
            </div>
            <div class="row" id="idAgenciaConta">
                <div class="col-sm-12 col-md-6 col-lg-3">
                    <div class="form-group required" [ngClass]="{'has-error': displayMessage.agencia}">
                        <label class="control-label" for="agencia">Agência:</label>
                        <input class="form-control" id="agencia" type="text" formControlName="agencia" maxlength="6" />
                        <span class="text-danger" *ngIf="displayMessage.agencia">
                            <p [innerHTML]="displayMessage.agencia"></p>
                        </span>
                    </div>
                </div>
                <div class="col-sm-12 col-md-6 col-lg-3">
                    <div class="form-group required" [ngClass]="{'has-error': displayMessage.conta}">
                        <label class="control-label" for="conta">Conta:</label>
                        <input class="form-control" id="conta" type="text" formControlName="conta" maxlength="6" />
                        <span class="text-danger" *ngIf="displayMessage.conta">
                            <p [innerHTML]="displayMessage.conta"></p>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

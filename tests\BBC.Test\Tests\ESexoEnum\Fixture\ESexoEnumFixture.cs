using SistemaInfo.BBC.Domain.Enum;
using Xunit;

namespace BBC.Test.Tests.ESexoEnum.Fixture
{
    [CollectionDefinition(nameof(ESexoEnumCollection))]
    public class ESexoEnumCollection : ICollectionFixture<ESexoEnumFixture>
    {

    }

    public class ESexoEnumFixture : MockEngine
    {
        public ESexo ObterSexoMasculino()
        {
            return ESexo.Masculino;
        }

        public ESexo ObterSexoFeminino()
        {
            return ESexo.Feminino;
        }

        public ESexo ObterSexoOutros()
        {
            return ESexo.Outros;
        }

        public ESexo ObterSexoIndefinido()
        {
            return ESexo.Indefinido;
        }

        public ESexo[] ObterTodosOsSexos()
        {
            return new[]
            {
                ESexo.Masculino,
                ESexo.Feminino,
                ESexo.Outros,
                ESexo.Indefinido
            };
        }

        public string[] ObterDescricoesSexos()
        {
            return new[]
            {
                "<PERSON><PERSON><PERSON><PERSON>",
                "<PERSON><PERSON><PERSON>",
                "<PERSON>ros",
                "Indefinido"
            };
        }

        public int[] ObterValoresSexos()
        {
            return new[] { 1, 2, 3, 4 };
        }
    }
}

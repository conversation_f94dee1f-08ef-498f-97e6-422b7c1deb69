using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper.QueryableExtensions;
using Microsoft.EntityFrameworkCore;
using SistemaInfo.BBC.Application.Helpers;
using SistemaInfo.BBC.Application.Interface.Menu;
using SistemaInfo.BBC.Application.Objects.Api.Menu;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.Menu;
using SistemaInfo.BBC.Domain.Models.GrupoUsuarioMenu.Repository;
using SistemaInfo.BBC.Domain.Models.Menu.Commands;
using SistemaInfo.BBC.Domain.Models.Menu.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;

namespace SistemaInfo.BBC.Application.Services.Menu
{
    public class MenuAppService : AppService<Domain.Models.Menu.Menu, IMenuReadRepository, IMenuWriteRepository>,
        IMenuAppService
    {
        private readonly IGrupoUsuarioMenuReadRepository _grupoUsuarioMenuReadRepository;

        public MenuAppService(IAppEngine engine, IMenuReadRepository readRepository,
            IMenuWriteRepository writeRepository, IGrupoUsuarioMenuReadRepository grupoUsuarioMenuReadRepository) :
            base(engine, readRepository, writeRepository)
        {
            _grupoUsuarioMenuReadRepository = grupoUsuarioMenuReadRepository;
        }

        public List<GrupoUsuarioMenuGridResponse> GetMenusDisponiveisPorModulo(int idModulo, int? idGrupoUsuario,
            int? idEmpresa, int? idPosto)
        {
            var lMenus = Repository.Query
                .Where(m => m.ModuloMenu.Any(a => a.ModuloId == idModulo && m.IsMenuPai == 0))
                .Include(x => x.GrupoUsuarioMenu);

            
            if (User.EmpresaId > 0)
                lMenus = lMenus.Where(m => m.IsMostraApenasAdmin != 1)
                    .Include(x => x.GrupoUsuarioMenu);
            

            var lMenusRetorno = lMenus.ProjectTo<GrupoUsuarioMenuGridResponse>(Engine.Mapper.ConfigurationProvider)
                .ToList();

            foreach (var menu in lMenusRetorno)
            {
                var grupoUsuarioMenu = _grupoUsuarioMenuReadRepository.Where(m =>
                    m.MenuId == menu.idMenu &&
                    m.GrupoUsuarioId == idGrupoUsuario &&
                    m.Menu.IsMenuPai == 0);

                if (grupoUsuarioMenu.Any())
                    menu.ativo = true;
            }

            return lMenusRetorno;
        }

        public List<ConsultaGridMenu> ConsultaMenuApi()
        {
            try
            {
                new LogHelper().LogOperationStart("ConsultaMenuApi");
                return Mapper.Map<List<ConsultaGridMenu>>(Repository.Query.GetAll().ToList());
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar ConsultaMenuApi");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("ConsultaMenuApi");
            }
        }

        public async Task<RespPadraoApi> IntegrarMenuApi(MenuIntegrarApiRequest request)
        {
            try
            {
                new LogHelper().LogOperationStart("IntegrarMenuApi");
                var menuBd =
                    Repository.Query.FirstOrDefault(x => x.Descricao == request.NomeMenu || x.Link == request.Link);

                if (menuBd != null && request.NovoMenu)
                {
                    if (menuBd.Descricao == request.NomeMenu && menuBd.Link == request.Link)
                    {
                        return new RespPadraoApi()
                        {
                            sucesso = false,
                            mensagem = "Descrição e link já cadastrados!"
                        };
                    }

                    if (menuBd.Descricao == request.NomeMenu)
                    {
                        return new RespPadraoApi()
                        {
                            sucesso = false,
                            mensagem = "Descrição já cadastrada!"
                        };
                    }

                    if (menuBd.Link == request.Link)
                    {
                        return new RespPadraoApi()
                        {
                            sucesso = false,
                            mensagem = "Link já cadastrado!"
                        };
                    }
                }

                if (!request.NovoMenu)
                {
                    menuBd.Descricao = request.NomeMenu;
                    menuBd.Link = request.Link;

                    var menuUpd = Mapper.Map<Domain.Models.Menu.Menu>(menuBd);

                    Repository.Command.Update(menuUpd);
                    await Repository.Command.SaveChangesAsync();

                    if (menuUpd.Id > 0)
                    {
                        return new RespPadraoApi()
                        {
                            id = menuUpd.Id,
                            sucesso = true,
                            mensagem = "Registro salvo com sucesso!"
                        };
                    }
                }

                var command = Mapper.Map<MenuSalvarComRetornoCommand>(request);
                var retorno = await Engine.CommandBus.SendCommandAsync<Domain.Models.Menu.Menu>(command);

                if (retorno.Id > 0)
                {
                    return new RespPadraoApi()
                    {
                        id = retorno.Id,
                        sucesso = true,
                        mensagem = "Registro salvo com sucesso!"
                    };
                }

                return new RespPadraoApi()
                {
                    sucesso = false,
                    mensagem = "Erro ao integrar!"
                };
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar IntegrarMenuApi");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("IntegrarMenuApi");
            }
        }
    }
}
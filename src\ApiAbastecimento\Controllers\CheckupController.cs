using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using NLog;
using SistemaInfo.BBC.ApiAbastecimento.Controllers.Base;
using SistemaInfo.BBC.Application.Interface.AutorizacaoAbastecimento;
using SistemaInfo.BBC.Application.Interface.PagamentoAbastecimento;
using SistemaInfo.BBC.Application.Interface.Posto;
using SistemaInfo.BBC.Infra.Data.Context;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;

namespace SistemaInfo.BBC.ApiAbastecimento.Controllers
{
    /// <summary>
    /// Controller responsável por verificar a saúde da API de Abastecimento
    /// </summary>
    [Route("Checkup")]
    public class CheckupController : ApiControllerBase
    {
        private readonly IAutorizacaoAbastecimentoAppService _autorizacaoAppService;
        private readonly IPagamentoAbastecimentoAppService _pagamentoAppService;
        private readonly IPostoAppService _postoAppService;
        private readonly ConfigContext _configContext;
        private static readonly Logger Logger = LogManager.GetCurrentClassLogger();

        /// <summary>
        /// Construtor com injeção de dependências
        /// </summary>
        /// <param name="engine"></param>
        /// <param name="autorizacaoAppService"></param>
        /// <param name="pagamentoAppService"></param>
        /// <param name="postoAppService"></param>
        /// <param name="configContext"></param>
        public CheckupController(IAppEngine engine,
            IAutorizacaoAbastecimentoAppService autorizacaoAppService,
            IPagamentoAbastecimentoAppService pagamentoAppService,
            IPostoAppService postoAppService,
            ConfigContext configContext) : base(engine)
        {
            _autorizacaoAppService = autorizacaoAppService;
            _pagamentoAppService = pagamentoAppService;
            _postoAppService = postoAppService;
            _configContext = configContext;
        }

        /// <summary>
        /// Método de checkup que verifica a saúde dos serviços críticos da API de Abastecimento
        /// Retorna 200 (OK) se todos os serviços estão funcionando ou 500 (Erro Interno) se algum falhar
        /// </summary>
        /// <returns>Status HTTP 200 ou 500</returns>
        [AllowAnonymous]
        [HttpGet]
        public async Task<IActionResult> Get()
        {
            try
            {
                Logger.Info("Iniciando checkup da API de Abastecimento");

                // Teste 1: Verificar se o serviço de autorização de abastecimento está respondendo
                await TestAutorizacaoAbastecimentoService();
                Logger.Info("Checkup - Serviço de autorização de abastecimento: OK");

                // Teste 2: Verificar se o serviço de pagamento de abastecimento está respondendo
                await TestPagamentoAbastecimentoService();
                Logger.Info("Checkup - Serviço de pagamento de abastecimento: OK");

                // Teste 3: Verificar se o serviço de posto está respondendo
                await TestPostoService();
                Logger.Info("Checkup - Serviço de posto: OK");

                // Teste 4: Verificar conectividade com banco de dados
                await TestDatabaseConnection();
                Logger.Info("Checkup - Conexão com banco de dados: OK");

                Logger.Info("Checkup da API de Abastecimento concluído com sucesso");
                return Ok(new { status = "OK", message = "Todos os serviços estão funcionando corretamente" });
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Erro durante o checkup da API de Abastecimento");
                return StatusCode(500, new { status = "ERROR", message = "Erro interno nos serviços" });
            }
        }

        /// <summary>
        /// Testa o serviço de autorização de abastecimento
        /// </summary>
        private async Task TestAutorizacaoAbastecimentoService()
        {
            try
            {
                // Tenta acessar o serviço de autorização
                // Verifica se o serviço está instanciado e acessível
                if (_autorizacaoAppService == null)
                {
                    throw new Exception("Serviço de autorização de abastecimento não está disponível");
                }
                
                Logger.Debug("Teste do serviço de autorização de abastecimento executado com sucesso");
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Falha no teste do serviço de autorização de abastecimento");
                throw new Exception("Serviço de autorização de abastecimento não está respondendo", ex);
            }
        }

        /// <summary>
        /// Testa o serviço de pagamento de abastecimento
        /// </summary>
        private async Task TestPagamentoAbastecimentoService()
        {
            try
            {
                // Tenta acessar o serviço de pagamento de abastecimento
                // Verifica se o serviço está instanciado e acessível
                if (_pagamentoAppService == null)
                {
                    throw new Exception("Serviço de pagamento de abastecimento não está disponível");
                }
                
                Logger.Debug("Teste do serviço de pagamento de abastecimento executado com sucesso");
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Falha no teste do serviço de pagamento de abastecimento");
                throw new Exception("Serviço de pagamento de abastecimento não está respondendo", ex);
            }
        }

        /// <summary>
        /// Testa o serviço de posto
        /// </summary>
        private async Task TestPostoService()
        {
            try
            {
                // Tenta acessar o serviço de posto
                // Verifica se o serviço está instanciado e acessível
                if (_postoAppService == null)
                {
                    throw new Exception("Serviço de posto não está disponível");
                }
                
                Logger.Debug("Teste do serviço de posto executado com sucesso");
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Falha no teste do serviço de posto");
                throw new Exception("Serviço de posto não está respondendo", ex);
            }
        }

        /// <summary>
        /// Testa a conectividade com o banco de dados
        /// </summary>
        private async Task TestDatabaseConnection()
        {
            try
            {
                // Testa a conexão com o banco executando uma query simples
                await _configContext.Database.ExecuteSqlCommandAsync("SELECT 1");

                Logger.Debug("Teste de conectividade com banco de dados executado com sucesso");
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Falha no teste de conectividade com banco de dados");
                throw new Exception("Banco de dados não está acessível", ex);
            }
        }
    }
}

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Domain.Models.Portador;
using SistemaInfo.Framework.EntityFramework.Configuration;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Infra.Data.Mappings
{
    public class PortadorMapping : EntityTypeConfiguration<Portador>
    {
        public override void Map(EntityTypeBuilder<Portador> builder)
        {
            builder.ToTable("Portador");
            builder.HasKey(b => b.Id);
            
            builder.Property(b => b.Id).IsRequired().HasColumnName("Id").ValueGeneratedOnAdd();
            builder.Property(b => b.Nome).IsRequired().HasColumnName("Nome").HasColumnType("varchar(200)");
            builder.Property(b => b.CpfCnpj).IsRequired().HasColumnName("CpfCnpj").HasColumnType("varchar(14)");
            builder.Property(b => b.Email).HasColumnName("Email").HasColumnType("varchar(200)");
            builder.Property(b => b.Endereco).HasColumnName("Endereco").HasColumnType("varchar(200)");
            builder.Property(b => b.EstadoId).HasColumnName("EstadoId").HasColumnType("int");
            builder.Property(b => b.CidadeId).HasColumnName("CidadeId").HasColumnType("int");
            builder.Property(b => b.Cep).HasColumnName("Cep").HasColumnType("varchar(15)");
            builder.Property(b => b.EnderecoNumero).HasColumnName("EnderecoNumero").HasColumnType("int");
            builder.Property(b => b.Telefone).HasColumnName("Telefone").HasColumnType("varchar(15)");
            builder.Property(b => b.UsuarioCadastroId).HasColumnName("UsuarioCadastroId").HasColumnType("int");
            builder.Property(b => b.DataCadastro).IsRequired().HasColumnName("DataCadastro").HasColumnType("timestamp");
            builder.Property(b => b.Celular).HasColumnName("Celular").HasColumnType("varchar(15)");
            builder.Property(b => b.Bairro).IsRequired().HasColumnName("Bairro").HasColumnType("varchar(100)");
            builder.Property(b => b.Complemento).HasColumnName("Complemento").HasColumnType("varchar(200)");
            builder.Property(b => b.Ativo).IsRequired().HasColumnName("Ativo").HasColumnType("int");
            builder.Property(b => b.RNTRC).HasColumnName("RNTRC").HasColumnType("varchar(15)");
            builder.Property(b => b.UsuarioBloqueioId).HasColumnName("UsuarioBloqueioId").HasColumnType("int");
            builder.Property(b => b.DataBloqueio).HasColumnName("DataBloqueio").HasColumnType("timestamp");
            builder.Property(b => b.UsuarioDesbloqueioId).HasColumnName("UsuarioDesbloqueioId").HasColumnType("int");
            builder.Property(b => b.DataDesbloqueio).HasColumnName("DataDesbloqueio").HasColumnType("timestamp");
            builder.Property(b => b.NumeroCNH).HasColumnName("NumeroCNH").HasColumnType("varchar(11)");
            builder.Property(b => b.RazaoSocial).HasColumnName("RazaoSocial").HasColumnType("varchar(200)");
            builder.Property(b => b.NomeMae).HasColumnName("NomeMae").HasColumnType("varchar(200)");
            builder.Property(b => b.NomePai).HasColumnName("NomePai").HasColumnType("varchar(200)");
            builder.Property(b => b.Sexo).HasColumnName("Sexo").HasColumnType("int");
            builder.Property(b => b.NumeroIdentidade).HasColumnName("NumeroIdentidade").HasColumnType("varchar(20)");
            builder.Property(b => b.OrgaoEmissor).HasColumnName("OrgaoEmissor").HasColumnType("varchar(20)");
            builder.Property(b => b.UfEmissao).HasColumnName("UfEmissao").HasColumnType("varchar(20)");
            builder.Property(b => b.EmissaoIdentidade).HasColumnName("EmissaoIdentidade").HasColumnType("date");
            builder.Property(b => b.InscricaoEstadual).HasColumnName("InscricaoEstadual").HasColumnType("varchar(20)");
            builder.Property(b => b.DataAberturaEmpresa).HasColumnName("DataAberturaEmpresa").HasColumnType("date");
            builder.Property(b => b.FormaConstituicao).HasColumnName("FormaConstituicao").HasColumnType("varchar(200)");
            builder.Property(b => b.Cnae).HasColumnName("Cnae").HasColumnType("varchar(10)");
            builder.Property(b => b.TipoPessoa).HasColumnName("TipoPessoa").HasColumnType("int");
            builder.Property(b => b.DataNascimento).HasColumnName("DataNascimento").HasColumnType("date");
            builder.Property(b => b.NaturezaJuridica).HasColumnName("NaturezaJuridica").HasColumnType("varchar(4)");
            builder.Property(b => b.Atividade).HasColumnName("Atividade").HasColumnType("int");
            builder.Property(b => b.Placa).HasColumnName("Placa").HasColumnType("varchar(7)");
            builder.Property(b => b.CarretaId).HasColumnName("CarretaId").HasColumnType("int");
            builder.Property(b => b.Carreta2Id).HasColumnName("Carreta2Id").HasColumnType("int");
            builder.Property(b => b.Carreta3Id).HasColumnName("Carreta3Id").HasColumnType("int");
            builder.Property(b => b.ControlaAbastecimentoCentroCusto).HasColumnName("ControlaAbastecimentoCentroCusto").HasColumnType("int");
            
            //campos adicionais api mobile
            builder.Property(b => b.SenhaApi).HasColumnName("SenhaApi").HasColumnType("varchar(200)");
            builder.Property(b => b.UsuarioDesbloqueioMobileId).HasColumnName("UsuarioDesbloqueioMobileId").HasColumnType("int");
            builder.Property(b => b.QuantidadeErroSenha).HasColumnName("QuantidadeErroSenha").HasColumnType("int");
            builder.Property(b => b.DataDesbloqueioMobile).HasColumnName("DataDesbloqueioMobile").HasColumnType("timestamp");
            builder.Property(b => b.DataBloqueioMobile).HasColumnName("DataBloqueioMobile").HasColumnType("timestamp");
            
            //campos adicionais apimobile login
            builder.Property(b => b.DataUltimoAcesso).HasColumnName("DataUltimoAcesso").HasColumnType("timestamp");
            builder.Property(b => b.DataCriacaoSenha).HasColumnName("DataCriacaoSenha").HasColumnType("timestamp");
            builder.Property(b => b.SenhaProvisoria).HasColumnName("SenhaProvisoria").HasColumnType("int");
            
            //campos adicionais para tranportador
            builder.Property(b => b.Visibilidade).HasColumnName("Visibilidade").HasColumnType("int");
            builder.Property(b => b.CiotTacAgregado).HasColumnName("CiotTacAgregado").HasColumnType("int");
            
            builder.Property(b => b.EmpresaIdFrota).HasColumnName("EmpresaIdFrota").HasColumnType("int");
            
            //campos adicionais para usuario frota
            builder.Property(b => b.Status).HasColumnName("Status").HasColumnType("int").HasDefaultValue(EStatusPortador.Normal);;
            builder.Property(b => b.MotivoCancelamento).HasColumnName("MotivoCancelamento").HasColumnType("varchar(500)");
            builder.Property(b => b.UsuarioCancelamentoId).HasColumnName("UsuarioCancelamentoId").HasColumnType("int");
            builder.Property(b => b.DataCancelamento).HasColumnName("DataCancelamento").HasColumnType("timestamp");
            builder.Property(b => b.DataEmissaoCNH).HasColumnName("DataEmissaoCNH").HasColumnType("date");
            builder.Property(b => b.DataVencimentoCNH).HasColumnName("DataVencimentoCNH").HasColumnType("date");
            builder.Property(b => b.CNH).HasColumnName("CNH").HasColumnType("varchar(200)");
            
            //campos adicionais Viagem v2
            builder.Property(b => b.TipoTransportador).HasColumnName("TipoTransportador").HasColumnType("int");
            builder.Property(b => b.EquiparadoTac).HasColumnName("EquiparadoTac").HasColumnType("int");
            builder.Property(b => b.RntrcAtivo).HasColumnName("RntrcAtivo").HasColumnType("int");
            builder.Property(b => b.DataConsultaSituacao).HasColumnName("DataConsultaSituacao").HasColumnType("timestamp");
            
            //campos FK 
            builder.HasOne(b => b.UsuarioDesbloqueioMobile).WithMany().HasForeignKey(b => b.UsuarioDesbloqueioMobileId);
            builder.HasOne(b => b.EmpresaFrota).WithMany().HasForeignKey(b => b.EmpresaIdFrota);
            builder.HasOne(b => b.Estado).WithMany().HasForeignKey(b => b.EstadoId);
            builder.HasOne(b => b.Cidade).WithMany().HasForeignKey(b => b.CidadeId);
            builder.HasOne(b => b.UsuarioCadastro).WithMany().HasForeignKey(b => b.UsuarioCadastroId);
            builder.HasOne(b => b.UsuarioDesbloqueio).WithMany().HasForeignKey(b => b.UsuarioDesbloqueioId);
            builder.HasOne(b => b.UsuarioBloqueio).WithMany().HasForeignKey(b => b.UsuarioBloqueioId);
            builder.HasOne(b => b.Carreta).WithMany().HasForeignKey(b => b.CarretaId);
            builder.HasOne(b => b.Carreta2).WithMany().HasForeignKey(b => b.Carreta2Id);
            builder.HasOne(b => b.Carreta3).WithMany().HasForeignKey(b => b.Carreta3Id);
            
            builder.HasOne(b => b.UsuarioCancelamento).WithMany().HasForeignKey(b => b.UsuarioCancelamentoId);
        }
    }
}
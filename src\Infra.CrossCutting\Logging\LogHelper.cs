using System;
using Newtonsoft.Json;
using NLog;

namespace SistemaInfo.BBC.Infra.CrossCutting.Logging
{
    /// <summary>
    /// Classe utilitária para logging
    /// </summary>
    public static class LogHelper
    {
        /// <summary>
        /// Registra o início de uma operação
        /// </summary>
        public static void LogOperationStart(string operationName, string details = null)
        {
            var logger = LogManager.GetCurrentClassLogger();
            var message = $"--> Início da operação: {operationName} <--";
            if (!string.IsNullOrEmpty(details))
                message += $" Detalhes: {details}";
                
            logger.Info(message);
        }
        
        /// <summary>
        /// Registra o fim de uma operação
        /// </summary>
        public static void LogOperationEnd(string operationName, string details = null)
        {
            var logger = LogManager.GetCurrentClassLogger();
            var message = $"--> Fim da operação: {operationName} <--";
            if (!string.IsNullOrEmpty(details))
                message += $" Detalhes: {details}";
                
            logger.Info(message);
        }
        
        /// <summary>
        /// Registra uma mensagem de informação
        /// </summary>
        public static void Info(string message)
        {
            var logger = LogManager.GetCurrentClassLogger();
            logger.Info(message);
        }
        
        /// <summary>
        /// Registra uma mensagem de informação com um objeto para serializar
        /// </summary>
        public static void Info<T>(string message, T obj)
        {
            var logger = LogManager.GetCurrentClassLogger();
            logger.Info($"{message} {JsonConvert.SerializeObject(obj)}");
        }
        
        /// <summary>
        /// Registra uma mensagem de erro
        /// </summary>
        public static void Error(string message)
        {
            var logger = LogManager.GetCurrentClassLogger();
            logger.Error(message);
        }
        
        /// <summary>
        /// Registra uma mensagem de erro com exceção
        /// </summary>
        public static void Error(Exception ex, string message = null)
        {
            var logger = LogManager.GetCurrentClassLogger();
            if (string.IsNullOrEmpty(message))
                logger.Error(ex);
            else
                logger.Error(ex, message);
        }
        
        /// <summary>
        /// Registra uma mensagem de erro com um objeto para serializar
        /// </summary>
        public static void Error<T>(string message, T obj)
        {
            var logger = LogManager.GetCurrentClassLogger();
            logger.Error($"{message} {JsonConvert.SerializeObject(obj)}");
        }
    }
}

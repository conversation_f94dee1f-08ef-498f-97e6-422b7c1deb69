﻿using System.Collections.Generic;
using SistemaInfo.BBC.Application.Objects.Mobile.Viagem.Response;

public class PagamentosFake
{
    public static List<PagamentoMobileResponse> GetPagamentosFake()
    {
        return
        [
            new PagamentoMobileResponse
            {
                Id = 8762,
                ViagemId = 1234,
                RazaoSocialEmpresa = "JSL S/A",
                PagamentoExternoId = 2025012827,
                Status = "",
                FormaPagamento = "",
                RecebedorAutorizado = "13585507000104",
                Tipo = "",
                ValorParcela = 11.41M,
                ValorTransferenciaMotorista = 4.56M,
                //DataCadastro = "28/04/2025 14:27:11",
                DataAlteracao = "28/04/2025 14:59:58",
                DataBaixa = "28/04/2025 14:59:56",
                DataCancelamento = null,
                Transacoes =
                [
                    new TransacaoMobileResponse()
                    {
                        DataCadastro = "28/04/2025 14:27:11",
                        DataAlteracao = "28/04/2025 14:59:58",
                        DocumentoOrigem = "52548435012266",
                        DocumentoDestino = "13585507000104",
                        Valor = 11.41M,
                        Status = "Fechado"
                    }
                ]
            }
        ];
    }
}
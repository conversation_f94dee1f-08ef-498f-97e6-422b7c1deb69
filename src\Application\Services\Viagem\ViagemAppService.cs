using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Linq.Dynamic;
using System.Linq.Expressions;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using AutoMapper.QueryableExtensions;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using NLog;
using SistemaInfo.BBC.Application.Helpers;
using SistemaInfo.BBC.Application.Interface.DeclaracaoCiot;
using SistemaInfo.BBC.Application.Interface.GrupoEmpresa;
using SistemaInfo.BBC.Application.Interface.Mensagem;
using SistemaInfo.BBC.Application.Interface.PagamentoEvento;
using SistemaInfo.BBC.Application.Interface.Parametros;
using SistemaInfo.BBC.Application.Interface.Pix;
using SistemaInfo.BBC.Application.Interface.Transacao;
using SistemaInfo.BBC.Application.Interface.Veiculo;
using SistemaInfo.BBC.Application.Interface.Viagem;
using SistemaInfo.BBC.Application.Objects.Api.Pix;
using SistemaInfo.BBC.Application.Objects.Api.Pix.Transferencia;
using SistemaInfo.BBC.Application.Objects.Api.Transacao;
using SistemaInfo.BBC.Application.Objects.Api.Viagem;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.Mensagem;
using SistemaInfo.BBC.Application.Objects.Web.Portador;
using SistemaInfo.BBC.Application.Objects.Web.Transacao;
using SistemaInfo.BBC.Application.Objects.Web.Viagem;
using SistemaInfo.BBC.Application.Utils;
using SistemaInfo.BBC.Domain.CloudTranslationService;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Domain.External.Conductor.DTO.Cartao;
using SistemaInfo.BBC.Domain.External.Conductor.DTO.Transferencia;
using SistemaInfo.BBC.Domain.External.Conductor.Interface;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.BBC.Domain.Models.Cidade.Repository;
using SistemaInfo.BBC.Domain.Models.Empresa.Repository;
using SistemaInfo.BBC.Domain.Models.Notificacao;
using SistemaInfo.BBC.Domain.Models.Notificacao.Commands;
using SistemaInfo.BBC.Domain.Models.PagamentoEvento.Commands;
using SistemaInfo.BBC.Domain.Models.PagamentoEvento.Repository;
using SistemaInfo.BBC.Domain.Models.PagamentoEventoHistorico.Repository;
using SistemaInfo.BBC.Domain.Models.Pagamentos.Repository;
using SistemaInfo.BBC.Domain.Models.Parametros.Repository;
using SistemaInfo.BBC.Domain.Models.PercentualTransferencia.Repository;
using SistemaInfo.BBC.Domain.Models.PercentualTransferenciaPortador.Repository;
using SistemaInfo.BBC.Domain.Models.Pix.Responses.Transferencia;
using SistemaInfo.BBC.Domain.Models.Portador.Commands;
using SistemaInfo.BBC.Domain.Models.Portador.Repository;
using SistemaInfo.BBC.Domain.Models.Transacao.Repository;
using SistemaInfo.BBC.Domain.Models.Usuario.Repository;
using SistemaInfo.BBC.Domain.Models.Viagem.Commands;
using SistemaInfo.BBC.Domain.Models.Viagem.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using SistemaInfo.Framework.Utils;


namespace SistemaInfo.BBC.Application.Services.Viagem
{
    public class ViagemAppService :
        AppService<Domain.Models.Viagem.Viagem, IViagemReadRepository, IViagemWriteRepository>,
        IViagemAppService
    {
        private readonly ICartaoRepository _cartaoRepository;
        private readonly IEmpresaReadRepository _empresaReadRepository;
        private readonly IPortadorReadRepository _portadorReadRepository;
        private readonly ICidadeReadRepository _cidadeReadRepository;
        private readonly IPagamentoEventoReadRepository _pagamentoEventoReadRepository;
        private readonly IPagamentoEventoWriteRepository _pagamentoEventoWriteRepository;
        private readonly IPagamentoEventoHistoricoReadRepository _pagamentoEventoHistoricoReadRepository;
        private readonly ITransferenciaRepository _transferenciaRepository;
        private readonly IParametrosAppService _parametrosAppService;
        private readonly IUsuarioRepository _usuarioExternalRepository;
        private readonly IUsuarioReadRepository _usuarioReadRepository;
        private readonly IPercentualTransferenciaReadRepository _percentualTransferenciaReadRepository;
        private readonly IPercentualTransferenciaPortadorReadRepository _percentualTransferenciaPortadorReadRepository;
        private readonly IDeclaracaoCiotAppService _declaracaoCiotAppService;
        private readonly IVeiculoAppService _veiculoAppService;
        private readonly IPixAppService _pixAppService;
        private readonly ITransacaoRegisterAppService _transacaoRegisterAppService;
        private readonly IMensagemAppService _mensagemAppService;
        private readonly ITransacaoReadRepository _transacaoReadRepository;
        private readonly IParametrosReadRepository _parametrosReadRepository;
        private readonly IGrupoEmpresaAppService _grupoEmpresaAppService;
        private readonly ICloudTranslationService _cloudTranslationService;
        private IPagamentoEventoAppSerivce _pagamentoEventoAppSerivce;
        private readonly IPagamentosReadRepository _pagamentosReadRepository;
        private readonly IPagamentosWriteRepository _pagamentosWriteRepository;

        public ViagemAppService(IAppEngine engine,
            IViagemReadRepository readRepository,
            IViagemWriteRepository writeRepository,
            IEmpresaReadRepository empresaReadRepository,
            ICartaoRepository cartaoRepository,
            IPortadorReadRepository portadorReadRepository,
            ICidadeReadRepository cidadeReadRepository,
            IPagamentoEventoReadRepository pagamentoEventoReadRepository,
            IPagamentoEventoHistoricoReadRepository pagamentoEventoHistoricoReadRepository,
            ITransferenciaRepository transferenciaRepository,
            IParametrosAppService parametrosAppService,
            IUsuarioRepository usuarioExternalRepository,
            IPercentualTransferenciaReadRepository percentualTransferenciaReadRepository,
            IPercentualTransferenciaPortadorReadRepository percentualTransferenciaPortadorReadRepository,
            IDeclaracaoCiotAppService declaracaoCiotAppService,
            IVeiculoAppService veiculoAppService,
            IPixAppService pixAppService,
            IPagamentoEventoWriteRepository pagamentoEventoWriteRepository,
            IParametrosReadRepository parametrosReadRepository,
            ITransacaoRegisterAppService transacaoRegisterAppService,
            IGrupoEmpresaAppService grupoEmpresaAppService,
            ITransacaoReadRepository transacaoReadRepository, IUsuarioReadRepository usuarioReadRepository,
            ICloudTranslationService cloudTranslationService, IPagamentoEventoAppSerivce pagamentoEventoAppSerivce,
            IMensagemAppService mensagemAppService,
            IPagamentosReadRepository pagamentosReadRepository,
            IPagamentosWriteRepository pagamentosWriteRepository)
            : base(engine, readRepository, writeRepository)
        {
            _empresaReadRepository = empresaReadRepository;
            _cartaoRepository = cartaoRepository;
            _portadorReadRepository = portadorReadRepository;
            _cidadeReadRepository = cidadeReadRepository;
            _pagamentoEventoReadRepository = pagamentoEventoReadRepository;
            _pagamentoEventoHistoricoReadRepository = pagamentoEventoHistoricoReadRepository;
            _transferenciaRepository = transferenciaRepository;
            _parametrosAppService = parametrosAppService;
            _usuarioExternalRepository = usuarioExternalRepository;
            _percentualTransferenciaReadRepository = percentualTransferenciaReadRepository;
            _percentualTransferenciaPortadorReadRepository = percentualTransferenciaPortadorReadRepository;
            _declaracaoCiotAppService = declaracaoCiotAppService;
            _veiculoAppService = veiculoAppService;
            _pixAppService = pixAppService;
            _pagamentoEventoWriteRepository = pagamentoEventoWriteRepository;
            _parametrosReadRepository = parametrosReadRepository;
            _transacaoRegisterAppService = transacaoRegisterAppService;
            _grupoEmpresaAppService = grupoEmpresaAppService;
            _transacaoReadRepository = transacaoReadRepository;
            _cloudTranslationService = cloudTranslationService;
            _pagamentoEventoAppSerivce = pagamentoEventoAppSerivce;
            _usuarioReadRepository = usuarioReadRepository;
            _mensagemAppService = mensagemAppService;
            _pagamentosReadRepository = pagamentosReadRepository;
            _pagamentosWriteRepository = pagamentosWriteRepository;
        }


        public async Task<ViagemIntegrarResponse> IntegrarPagamentoViagemV1(
            PagamentoViagemIntegrarRequest viagemIntegrarRequest,
            string token = "", bool servicoReenvio = false, int pagamentoEventoId = 0)
        {
            var empresa = await _empresaReadRepository.GetByIdAsync(Engine.User.EmpresaId);
            if (empresa == null)
            {
                return new ViagemIntegrarResponse(false, servicoReenvio
                    ? "Empresa do usuário não localizada."
                    : "Empresa do evento de pagamento não localizada.");
            }

            if (!empresa.UsaV1())
            {
                return new ViagemIntegrarResponse(false, "Empresa sem permissão para realizar a integração");
            }
            
            return await IntegrarPagamentoViagem(viagemIntegrarRequest, token, servicoReenvio, pagamentoEventoId);
        }


        public async Task<ViagemIntegrarResponse> IntegrarPagamentoViagem(
            PagamentoViagemIntegrarRequest viagemIntegrarRequest,
            string token = "", bool servicoReenvio = false, int pagamentoEventoId = 0)
        {
            #region Coleta de informações e valaidação de campos

            Domain.Models.PagamentoEvento.PagamentoEvento pagamentoEventoExistenteReenvio = null;

            if (pagamentoEventoId != 0)
                pagamentoEventoExistenteReenvio =
                    await _pagamentoEventoReadRepository.ConsultarPorIdIncludeViagemEmpresa(pagamentoEventoId);

            
            var portadorContratado =
                await _portadorReadRepository.GetByCpfCnpjAsync(viagemIntegrarRequest.CpfCnpjContratado);
            var portadorMotorista = await _portadorReadRepository.GetByCpfCnpjAsync(viagemIntegrarRequest.CpfMotorista);

            var empresaId = Engine.User.EmpresaId;
            var empresa = pagamentoEventoExistenteReenvio != null
                ? pagamentoEventoExistenteReenvio.Empresa
                : await _empresaReadRepository.GetByIdAsync(empresaId);

            if (empresa == null)
            {
                return new ViagemIntegrarResponse(false, servicoReenvio
                    ? "Empresa do usuário não localizada."
                    : "Empresa do evento de pagamento não localizada.");
            }

            if (!servicoReenvio && empresa.RecebedorAutorizado != true &&
                !string.IsNullOrWhiteSpace(viagemIntegrarRequest.RecebedorAutorizado))
            {
                return new ViagemIntegrarResponse(false,
                    "Empresa não está parametrizada para transferência ao recebedor autorizado.");
            }

            empresaId = empresa.Id;

            if (!viagemIntegrarRequest.FilialId.IsNullOrWhiteSpace())
            {
                if (viagemIntegrarRequest.FilialId.Length > 15)
                    return new ViagemIntegrarResponse(false, "O campo 'FilialId' não pode ter mais que 15 caracteres.");
                viagemIntegrarRequest.FilialId = Regex.Replace(viagemIntegrarRequest.FilialId, "[^a-zA-Z0-9]", "");
            }

            if (portadorContratado == null)
            {
                if (!string.IsNullOrWhiteSpace(viagemIntegrarRequest.CpfCnpjContratado))
                {
                    var newPortadorProprietario =
                        await CadastrarPortador(viagemIntegrarRequest.CpfCnpjContratado, false);

                    if (newPortadorProprietario.Id > 0)
                    {
                        portadorContratado = newPortadorProprietario;
                        if (viagemIntegrarRequest.CpfCnpjContratado == viagemIntegrarRequest.CpfMotorista)
                        {
                            portadorMotorista = newPortadorProprietario;
                        }
                    }
                    else
                    {
                        return new ViagemIntegrarResponse(false,
                            "Portador contratado não localizado ou não cadastrado na Conductor.");
                    }
                }
                else
                {
                    return new ViagemIntegrarResponse(false,
                        "Campo CpfCnpjContratado é obrigatório.");
                }
            }

            if (portadorMotorista == null)
            {
                if (!string.IsNullOrWhiteSpace(viagemIntegrarRequest.CpfMotorista))
                {
                    var newPortadorMotorista = await CadastrarPortador(viagemIntegrarRequest.CpfMotorista, false);

                    if (newPortadorMotorista.Id > 0)
                    {
                        portadorMotorista = newPortadorMotorista;
                    }
                    else
                    {
                        return new ViagemIntegrarResponse(false,
                            "Portador motorista não localizado ou não cadastrado na Conductor.");
                    }
                }
                else
                {
                    return new ViagemIntegrarResponse(false, "Campo CpfMotorista é obrigatório.");
                }
            }

            if (!viagemIntegrarRequest.IbgeOrigem.HasValue && !servicoReenvio)
                return new ViagemIntegrarResponse(false, "Campo IbgeOrigem é obrigatório.");

            var cidadeOrigemId = await _cidadeReadRepository
                .GetIdByIbgeAsync(viagemIntegrarRequest.IbgeOrigem.ToIntSafe());

            if (cidadeOrigemId == 0 && !servicoReenvio)
                return new ViagemIntegrarResponse(false, "Campo IbgeOrigem não localizado na base ou inválido.");

            if (!viagemIntegrarRequest.IbgeDestino.HasValue && !servicoReenvio)
                return new ViagemIntegrarResponse(false, "Campo IbgeDestino é obrigatório.");

            var cidadeDestinoId = await _cidadeReadRepository
                .GetIdByIbgeAsync(viagemIntegrarRequest.IbgeDestino.ToIntSafe());

            if (cidadeDestinoId == 0 && !servicoReenvio)
                return new ViagemIntegrarResponse(false, "Campo IbgeDestino não localizado na base ou inválido.");

            if (!viagemIntegrarRequest.PagamentoExternoId.HasValue)
                return new ViagemIntegrarResponse(false, "Campo PagamentoExternoId é obrigatório.");

            if (!viagemIntegrarRequest.ViagemExternoId.HasValue)
                return new ViagemIntegrarResponse(false, "Campo ViagemExternoId é obrigatório.");

            if (!viagemIntegrarRequest.Tipo.HasValue)
                return new ViagemIntegrarResponse(false, "Campo Tipo é obrigatório.");

            if (!viagemIntegrarRequest.FormaPagamento.HasValue)
                return new ViagemIntegrarResponse(false, "Campo FormaPagamento é obrigatório.");

            #region Validacao do hash de segurança

            if (!servicoReenvio)
            {
                if (string.IsNullOrWhiteSpace(viagemIntegrarRequest.HashValidacao))
                    return new ViagemIntegrarResponse(false,
                        "Inconsistência na validação do processo de pagamento de frete.");

                if (string.IsNullOrWhiteSpace(token))
                    return new ViagemIntegrarResponse(false,
                        "Inconsistência na validação do processo de pagamento de frete.");

                string hashValidacao;

                using (var md5 = MD5.Create())
                {
                    var hash = token + viagemIntegrarRequest.CpfCnpjContratado + viagemIntegrarRequest.Valor;
                    var bytes = md5.ComputeHash(Encoding.UTF8.GetBytes(hash));
                    var sb = new StringBuilder();
                    foreach (var b in bytes) sb.Append(b.ToString("x2"));
                    hashValidacao = sb.ToString();
                }

                if (!viagemIntegrarRequest.HashValidacao.Equals(hashValidacao))
                    return new ViagemIntegrarResponse(false,
                        $"Inconsistência na validação do processo de pagamento de frete.");
            }

            #endregion

            #endregion

            #region Avaliar Emisão do CIOT

            bool vinculaCiot = false;
            if (!viagemIntegrarRequest.Ciot.IsNullOrWhiteSpace())
            {
                if (viagemIntegrarRequest.CodigoNaturezaCarga.IsNullOrWhiteSpace())
                    return new ViagemIntegrarResponse(false,
                        "O código natureza da carga deve ser informado ao informar o número do CIOT.");
                if (viagemIntegrarRequest.VerificadorCiot.IsNullOrWhiteSpace())
                    return new ViagemIntegrarResponse(false,
                        "O verificador do CIOT deve ser informado ao informar o número do CIOT.");
                if (viagemIntegrarRequest.PesoCarga == null && viagemIntegrarRequest.TipoViagem == 1)
                    return new ViagemIntegrarResponse(false,
                        "O peso da carga deve ser informado ao informar o número do CIOT.");
                vinculaCiot = true;
            }

            #endregion

            #region Pagamento

            if (viagemIntegrarRequest.FormaPagamento == FormaPagamentoEvento.Pix)
            {
                if (servicoReenvio)
                {
                    if (pagamentoEventoExistenteReenvio == null)
                        return new ViagemIntegrarResponse(false, "Pagamento não encontrado para reenvio.");


                    if (pagamentoEventoExistenteReenvio.ChavePix.IsNullOrWhiteSpace() &&
                        (pagamentoEventoExistenteReenvio.Conta.IsNullOrWhiteSpace() ||
                         pagamentoEventoExistenteReenvio.Agencia.IsNullOrWhiteSpace()))
                    {
                        return new ViagemIntegrarResponse(false,
                            "Campos ChavePix ou Agencia e Conta obrigatórios caso a forma de pagamento seja Pix.");
                    }

                    if (pagamentoEventoExistenteReenvio.ChavePix.IsNullOrWhiteSpace() &&
                        !pagamentoEventoExistenteReenvio.CodigoBanco.IsNullOrWhiteSpace() &&
                        pagamentoEventoExistenteReenvio.TipoConta == null)
                    {
                        return new ViagemIntegrarResponse(false,
                            "Para pagamentos de tipo Pix para outros bancos é necessário informar o Código do Banco e o Tipo de Conta (Poupanca ou Corrente).");
                    }
                }
                else
                {
                    if (viagemIntegrarRequest.ChavePix.IsNullOrWhiteSpace() &&
                        (viagemIntegrarRequest.Conta.IsNullOrWhiteSpace() ||
                         viagemIntegrarRequest.Agencia.IsNullOrWhiteSpace()))
                    {
                        return new ViagemIntegrarResponse(false,
                            "Campos ChavePix ou Agencia e Conta obrigatórios caso a forma de pagamento seja Pix.");
                    }

                    if (viagemIntegrarRequest.ChavePix.IsNullOrWhiteSpace() &&
                        !viagemIntegrarRequest.CodigoBanco.IsNullOrWhiteSpace() &&
                        viagemIntegrarRequest.TipoConta == null)
                    {
                        return new ViagemIntegrarResponse(false,
                            "Para pagamentos de tipo Pix para outros bancos é necessário informar o Código do Banco e o Tipo de Conta (Poupanca ou Corrente).");
                    }
                }
            }

            var pagamentoEventoIntegrar = Mapper.Map<PagamentoEventoSalvarComRetornoCommand>(viagemIntegrarRequest);

            //Verificar Pagamento Evento Existente

            var pagamentoEventoExistente = pagamentoEventoExistenteReenvio ?? _pagamentoEventoReadRepository
                .Where(x => x.PagamentoExternoId == viagemIntegrarRequest.PagamentoExternoId &&
                            x.Status != StatusPagamento.NaoExecutado &&
                            x.EmpresaId == empresaId)
                .Include(x => x.Viagem)
                .Include(d => d.Viagem.PortadorProprietario)
                .Include(d => d.Viagem.PortadorMotorista)
                .LastOrDefault();

            //verifica situação do pagamento caso existente pendente
            if (pagamentoEventoExistente != null)
            {
                if (pagamentoEventoExistente.Viagem.ViagemExternoId != viagemIntegrarRequest.ViagemExternoId)
                {
                    return new ViagemIntegrarResponse(false,
                        $"PagamentoExternoId já existente para a viagem de cód {pagamentoEventoExistente.Viagem.Id}.");
                }

                if (pagamentoEventoExistente.Status is StatusPagamento.Aberto && !servicoReenvio)
                {
                    var lParametroTempoReenvioPagamentoStatusAberto = ((await _parametrosReadRepository
                        .GetByTipoDoParametroAsync(Domain.Models.Parametros.Parametros
                            .TipoDoParametro.TempoReenvioPagamentoStatusAberto))?.Valor ?? "5").ToInt();

                    var minutosDecorridos =
                        (int)(DateTime.Now - pagamentoEventoExistente.DataAlteracao.Value).TotalMinutes;
                    var minutosRestantes = Math.Max(0, lParametroTempoReenvioPagamentoStatusAberto - minutosDecorridos);

                    if (pagamentoEventoExistente.DataAlteracao >=
                        DateTime.Now.AddMinutes(lParametroTempoReenvioPagamentoStatusAberto * -1))
                        return RetornaResponseNaoExecutado(pagamentoEventoExistente,
                            "Evento com o status em aberto identificado. Processo em execução, caso o evento permaneça em aberto tente reenviar o evento novamente em " +
                            $"{minutosRestantes}" + " minuto(s)");
                }

                if ((pagamentoEventoExistente.Status is StatusPagamento.Processando or StatusPagamento.Erro) &&
                    !servicoReenvio)
                {
                    return new ViagemIntegrarResponse
                    {
                        Sucesso = true,
                        ViagemId = pagamentoEventoExistente.Viagem.Id,
                        StatusViagem = pagamentoEventoExistente.Viagem.Status?.GetHashCode(),
                        Pagamento = new PagamentoViagemResponse()
                        {
                            PagamentoEventoId = pagamentoEventoExistente.Id,
                            StatusPagamento = pagamentoEventoExistente.Status.GetHashCode(),
                            ValorParcela = pagamentoEventoExistente.Valor,
                            ValorMotorista = pagamentoEventoExistente.ValorTransferenciaMotorista,
                            CódTransacao = pagamentoEventoExistente.CodigoTransacao,
                            FormaPagamento = pagamentoEventoExistente.FormaPagamento?.GetHashCode(),
                            Mensagem =
                                "Evento de pagamento possui status '" + pagamentoEventoExistente.Status.ToString() +
                                "'." + (pagamentoEventoExistente.Status == StatusPagamento.Processando
                                    ? " Aguarde o processamento para o envio de uma nova requisição."
                                    : ""),
                            Transacoes = pagamentoEventoExistente.Transacao?
                                .Where(t => t.Tipo != Tipo.Tarifas)
                                .Select(t => Mapper.Map<PagamentoViagemTransacaoResponse>(t))
                                .ToList() ?? new List<PagamentoViagemTransacaoResponse>()
                        }
                    };
                }

                if (pagamentoEventoExistente.Status is StatusPagamento.Fechado or StatusPagamento.Cancelado)
                {
                    return new ViagemIntegrarResponse
                    {
                        Sucesso = true,
                        ViagemId = pagamentoEventoExistente.Viagem.Id,
                        StatusViagem = pagamentoEventoExistente.Viagem.Status?.GetHashCode(),
                        Pagamento = new PagamentoViagemResponse()
                        {
                            PagamentoEventoId = pagamentoEventoExistente.Id,
                            StatusPagamento = pagamentoEventoExistente.Status.GetHashCode(),
                            ValorParcela = pagamentoEventoExistente.Valor,
                            ValorMotorista = pagamentoEventoExistente.ValorTransferenciaMotorista,
                            CódTransacao = pagamentoEventoExistente.CodigoTransacao,
                            FormaPagamento = pagamentoEventoExistente.FormaPagamento?.GetHashCode(),
                            Mensagem = "Evento de pagamento possui status '" +
                                       pagamentoEventoExistente.Status.ToString() + "'.",
                            Transacoes = pagamentoEventoExistente.Transacao?
                                .Where(t => t.Tipo != Tipo.Tarifas)
                                .Select(t => Mapper.Map<PagamentoViagemTransacaoResponse>(t))
                                .ToList() ?? new List<PagamentoViagemTransacaoResponse>()
                        }
                    };
                }

                if (pagamentoEventoExistente.Tipo == Tipo.Cancelamento)
                {
                    return new ViagemIntegrarResponse
                    {
                        Sucesso = true,
                        ViagemId = pagamentoEventoExistente.Viagem.Id,
                        StatusViagem = pagamentoEventoExistente.Viagem.Status?.GetHashCode(),
                        Pagamento = new PagamentoViagemResponse()
                        {
                            PagamentoEventoId = pagamentoEventoExistente.Id,
                            StatusPagamento = pagamentoEventoExistente.Status.GetHashCode(),
                            ValorParcela = pagamentoEventoExistente.Valor,
                            ValorMotorista = pagamentoEventoExistente.ValorTransferenciaMotorista,
                            CódTransacao = pagamentoEventoExistente.CodigoTransacao,
                            FormaPagamento = pagamentoEventoExistente.FormaPagamento?.GetHashCode(),
                            Mensagem = "Evento de pagamento possui status 'Cancelado' ou está pendente de cancelamento.",
                            Transacoes = pagamentoEventoExistente.Transacao?
                                .Where(t => t.Tipo != Tipo.Tarifas)
                                .Select(t => Mapper.Map<PagamentoViagemTransacaoResponse>(t))
                                .ToList() ?? new List<PagamentoViagemTransacaoResponse>()
                        },
                    };
                }


                if (pagamentoEventoExistente.Status == StatusPagamento.Pendente &&
                    pagamentoEventoExistente.FormaPagamento == FormaPagamentoEvento.Pix)
                {
                    return new ViagemIntegrarResponse
                    {
                        Sucesso = true,
                        ViagemId = pagamentoEventoExistente.Viagem.Id,
                        StatusViagem = pagamentoEventoExistente.Viagem.Status?.GetHashCode(),
                        Pagamento = new PagamentoViagemResponse()
                        {
                            PagamentoEventoId = pagamentoEventoExistente.Id,
                            StatusPagamento = pagamentoEventoExistente.Status.GetHashCode(),
                            ValorParcela = pagamentoEventoExistente.Valor,
                            ValorMotorista = pagamentoEventoExistente.ValorTransferenciaMotorista,
                            CódTransacao = pagamentoEventoExistente.CodigoTransacao,
                            FormaPagamento = pagamentoEventoExistente.FormaPagamento?.GetHashCode(),
                            Mensagem =
                                "Já existe um pagamento Pix pendente para a parcela (PagamentoExternoId) informada.",
                            Transacoes = pagamentoEventoExistente.Transacao?
                                .Where(t => t.Tipo != Tipo.Tarifas)
                                .Select(t => Mapper.Map<PagamentoViagemTransacaoResponse>(t))
                                .ToList() ?? new List<PagamentoViagemTransacaoResponse>()
                        }
                    };
                }
            }

            #region Criação da viagem

            Domain.Models.Viagem.Viagem viagem;
            var viagemNew = false;

            //Casso exista o pagamento, pegar da pagamento
            if (pagamentoEventoExistente != null)
            {
                if (pagamentoEventoExistente.Viagem != null)
                {
                    viagem = pagamentoEventoExistente.Viagem;
                }
                else
                {
                    //Tratar erro caso o pagamento evento não possua viagem
                    return new ViagemIntegrarResponse(
                        "Erro ao criar viagem: PagamentoExternoId já existente e não vinculado a nenhuma viagem.");
                }
            }
            else
            {
                //Viagem já existente
                viagem = servicoReenvio
                    ? pagamentoEventoExistenteReenvio.Viagem
                    : Repository.Query
                        .Where(x => x.ViagemExternoId == viagemIntegrarRequest.ViagemExternoId &&
                                    x.EmpresaId == empresaId)
                        .Include(x => x.PagamentoEvento)
                        .Include(d => d.PortadorProprietario)
                        .Include(d => d.PortadorMotorista)
                        .FirstOrDefault();

                //Se não existente criar viagem
                if (viagem == null)
                {
                    try
                    {
                        viagemNew = true;
                        var viagemIntegrar = Mapper.Map<ViagemSalvarComRetornoCommand>(viagemIntegrarRequest);

                        //Atribuições de valor
                        viagemIntegrar.PortadorMotoristaId = portadorMotorista.Id;
                        viagemIntegrar.PortadorProprietarioId = portadorContratado.Id;
                        viagemIntegrar.Status = StatusViagem.Pendente;
                        viagemIntegrar.CidadeOrigemId = cidadeOrigemId;
                        viagemIntegrar.CidadeDestinoId = cidadeDestinoId;

                        viagem = await Engine.CommandBus.SendCommandAsync<Domain.Models.Viagem.Viagem>(viagemIntegrar);
                    }
                    catch (Exception e)
                    {
                        return new ViagemIntegrarResponse($"Erro ao criar viagem: {e.Message}");
                    }
                }
                else
                {
                    if (viagem.PortadorProprietario.CpfCnpj != viagemIntegrarRequest.CpfCnpjContratado)
                    {
                        return new ViagemIntegrarResponse(false, viagemId: viagem.Id,
                            statusViagem: viagem.Status?.GetHashCode(),
                            "Proprietário (portador contratado) informado não pertence a esta viagem.",
                            pagamento: null);
                    }

                    if (viagem.PortadorMotorista.CpfCnpj != viagemIntegrarRequest.CpfMotorista)
                    {
                        return new ViagemIntegrarResponse(false, viagemId: viagem.Id,
                            statusViagem: viagem.Status?.GetHashCode(),
                            "Motorista informado não pertence a esta viagem!", pagamento: null);
                    }

                    //Validação para reenviar saldo não executado;
                    bool validarViagemBaixada =
                        viagemIntegrarRequest.Tipo == Tipo.Saldo && pagamentoEventoExistente is null;


                    if (!validarViagemBaixada && viagem.Status == StatusViagem.Baixado &&
                        viagemIntegrarRequest.Tipo != Tipo.Complemento &&
                        viagemIntegrarRequest.Tipo != Tipo.Avulso
                       )
                    {
                        return new ViagemIntegrarResponse(true, viagemId: viagem.Id,
                            statusViagem: viagem.Status?.GetHashCode(), "Viagem já possui status baixado!",
                            pagamento: new PagamentoViagemResponse()
                            {
                                PagamentoEventoId = viagem.PagamentoEvento?.LastOrDefault()?.Id,
                                StatusPagamento = viagem.PagamentoEvento?.LastOrDefault()?.Status?.GetHashCode(),
                                ValorParcela = viagem.PagamentoEvento?.LastOrDefault()?.Valor,
                                ValorMotorista = viagem.PagamentoEvento?.LastOrDefault()
                                    ?.ValorTransferenciaMotorista,
                                CódTransacao = viagem.PagamentoEvento?.LastOrDefault()?.CodigoTransacao,
                                FormaPagamento = viagem.PagamentoEvento?.LastOrDefault()?.FormaPagamento
                                    ?.GetHashCode(),
                                Mensagem = viagem.PagamentoEvento?.LastOrDefault()?.MotivoPendencia,
                                Transacoes = viagem.PagamentoEvento?.LastOrDefault()?.Transacao?
                                    .Where(t => t.Tipo != Tipo.Tarifas)
                                    .Select(t => Mapper.Map<PagamentoViagemTransacaoResponse>(t))
                                    .ToList() ?? new List<PagamentoViagemTransacaoResponse>()
                            });
                    }


                    if (viagemIntegrarRequest.Tipo != Tipo.Complemento &&
                        viagemIntegrarRequest.Tipo != Tipo.Avulso &&
                        viagem.PagamentoEvento.Any(x =>
                            x.Tipo == Tipo.Saldo && x.Status != StatusPagamento.NaoExecutado))
                    {
                        return new ViagemIntegrarResponse(false, viagemId: viagem.Id,
                            statusViagem: viagem.Status?.GetHashCode(),
                            "Não é possível integrar um novo evento de pagamento pois já existe um pagamento de tipo 'Saldo' cadastrado.",
                            pagamento: null);
                    }

                    if (vinculaCiot)
                    {
                        if (viagemIntegrarRequest.Ciot != viagem.Ciot)
                        {
                            return new ViagemIntegrarResponse(false,
                                $"Já existe um ciot vinculado a esta viagem. Ciot: {viagem.Ciot}");
                        }

                        viagem.PesoCarga = viagemIntegrarRequest.PesoCarga;
                        viagem.CodigoNaturezaCarga = viagemIntegrarRequest.CodigoNaturezaCarga;
                    }
                }
            }

            #endregion

            Domain.Models.PagamentoEvento.PagamentoEvento pagamentoEvento;

            if (pagamentoEventoExistente != null)
            {
                pagamentoEvento = pagamentoEventoExistente;
            }
            else
            {
                try
                {
                    var viagemV2 = viagem.VersaoIntegracaoViagem == EVersaoIntegracao.V2;
                    
                    var requestPagamentoV2 = new PagamentoV2Request
                    {
                        PagamentoExternoId = viagemIntegrarRequest.PagamentoExternoId,
                        DataPrevisaoPagamento = viagemIntegrarRequest.DataPrevisaoPagamento,
                        Tipo = viagemIntegrarRequest.Tipo ?? Tipo.Adiantamento,
                        TipoConta = viagemIntegrarRequest.TipoConta ?? ETipoContaDock.Corrente,
                        FormaPagamento = viagemIntegrarRequest.FormaPagamento ?? FormaPagamentoEvento.Deposito,
                        Valor = viagemIntegrarRequest.Valor,
                        HashValidacao = viagemIntegrarRequest.HashValidacao,
                        ChavePix = viagemIntegrarRequest.ChavePix,
                        Agencia = viagemIntegrarRequest.Agencia,
                        Conta = viagemIntegrarRequest.Conta,
                        RecebedorAutorizado = viagemIntegrarRequest.RecebedorAutorizado,
                        WebhookUrl = viagemIntegrarRequest.WebhookUrl,
                    };
                    
                    pagamentoEventoIntegrar.ViagemId = viagem.Id;
                    pagamentoEventoIntegrar.EmpresaId = empresaId;
                    pagamentoEventoIntegrar.JsonEnvio = JsonConvert.SerializeObject(!viagemV2 ? viagemIntegrarRequest : requestPagamentoV2, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
                    pagamentoEvento =
                        await Engine.CommandBus.SendCommandAsync<Domain.Models.PagamentoEvento.PagamentoEvento>(pagamentoEventoIntegrar);
                }
                catch (Exception e)
                {
                    return new ViagemIntegrarResponse(viagemId: viagem.Id,
                        $"Erro ao gerar evento de pagamento: {e.Message}");
                }
            }

            RespostaViagemPagamento retornoGerarPagamentoEvento;

            var statusInicial = pagamentoEvento.Status;
            //Verifica forma pagamento
            if (viagemIntegrarRequest.FormaPagamento == FormaPagamentoEvento.Pix)
            {
                var chavePix = viagemIntegrarRequest.ChavePix ?? pagamentoEvento.ChavePix;
                var agencia = viagemIntegrarRequest.Agencia ?? pagamentoEvento.Agencia;
                var conta = viagemIntegrarRequest.Conta ?? pagamentoEvento.Conta;
                var codigoBanco = viagemIntegrarRequest.CodigoBanco ?? pagamentoEvento.CodigoBanco;
                var tipoConta = viagemIntegrarRequest.TipoConta == null
                    ? pagamentoEvento.TipoConta
                    : viagemIntegrarRequest.TipoConta.GetHashCode();
                var recebedorAutorizadoCpfCnpj =
                    viagemIntegrarRequest.RecebedorAutorizado ?? pagamentoEvento.RecebedorAutorizado;

                retornoGerarPagamentoEvento = await GerarPagamentoEventoViagemPix(pagamentoEvento, chavePix, agencia,
                    conta, codigoBanco, tipoConta,
                    empresa.RecebedorAutorizado == true || pagamentoEvento?.RecebedorAutorizado != null,
                    recebedorAutorizadoCpfCnpj);
            }
            else
            {
                retornoGerarPagamentoEvento = await GerarPagamentoEventoViagemP2P(pagamentoEvento);
            }

            if (empresa.GrupoEmpresaId != null)
                empresa.GrupoEmpresa = _grupoEmpresaAppService.Repository.Query.GetById(empresa.GrupoEmpresaId);


            var empresaReprocessa = empresa.StatusReprocessamentoPagamentoFrete == 1;
            var grupoEmpresaReprocessa = (empresa.GrupoEmpresa?.StatusReprocessamentoPagamentoFrete ?? 0) == 1;
            if (!retornoGerarPagamentoEvento.Sucesso)
            {
                if (!(grupoEmpresaReprocessa || empresaReprocessa))
                {
                    if ((retornoGerarPagamentoEvento.StatusPagamento != StatusPagamento.Pendente) ||
                        (retornoGerarPagamentoEvento.StatusPagamento == StatusPagamento.Pendente &&
                         viagemIntegrarRequest.FormaPagamento != FormaPagamentoEvento.Pix))
                    {
                        retornoGerarPagamentoEvento.StatusPagamento = StatusPagamento.NaoExecutado;
                    }
                }

                await RegistrarNotificacaoPagamento(retornoGerarPagamentoEvento.Mensagem, pagamentoEvento.Id);
            }


            pagamentoEvento = await RegistrarPendenciaPagamento(pagamentoEvento.Id,
                retornoGerarPagamentoEvento.Mensagem,
                retornoGerarPagamentoEvento.StatusPagamento, pagamentoEvento.Tipo, null, null);

            if (retornoGerarPagamentoEvento.Sucesso && pagamentoEvento.Tipo == Tipo.Saldo)
            {
                viagem.Status = StatusViagem.Baixado;
                viagem.DataBaixa = DateTime.Now;
            }

            #endregion

            var response = new ViagemIntegrarResponse
            {
                Sucesso = true,
                ViagemId = viagem.Id,
                ViagemExternoId = viagem.ViagemExternoId,
                StatusViagem = viagem.Status?.GetHashCode(),
                Mensagem = viagem.Status == StatusViagem.Baixado ? "Viagem baixada com sucesso!"
                    : viagemNew ? "Viagem criada com sucesso!" : "Pagamento integrado com sucesso à viagem!",
                Pagamento = new PagamentoViagemResponse()
                {
                    PagamentoEventoId = pagamentoEvento.Id,
                    PagamentoExternoId = pagamentoEvento.PagamentoExternoId,
                    StatusPagamento = retornoGerarPagamentoEvento.StatusPagamento.GetHashCode(),
                    ValorParcela = pagamentoEvento.Valor,
                    ValorMotorista = pagamentoEvento.ValorTransferenciaMotorista,
                    CódTransacao = pagamentoEvento.CodigoTransacao,
                    FormaPagamento = pagamentoEvento.FormaPagamento?.GetHashCode(),
                    Mensagem = retornoGerarPagamentoEvento.Mensagem,
                    Transacoes = pagamentoEvento.Transacao?
                        .Where(t => t.Tipo != Tipo.Tarifas)
                        .Select(t => Mapper.Map<PagamentoViagemTransacaoResponse>(t))
                        .ToList() ?? new List<PagamentoViagemTransacaoResponse>()
                }
            };
            pagamentoEvento.JsonRetorno = JsonConvert.SerializeObject(response);
            pagamentoEvento.DataRetorno = DateTime.Now;

            await _pagamentoEventoAppSerivce.EnviaWebHookAtualizacaoStatus(pagamentoEvento);

            await Repository.Command.SaveChangesAsync();
            return response;
        }

        public ViagemIntegrarResponse RetornaResponseNaoExecutado(
            Domain.Models.PagamentoEvento.PagamentoEvento pagamentoEventoExistente, string mensagem)
        {
            return new ViagemIntegrarResponse
            {
                Sucesso = true,
                ViagemId = pagamentoEventoExistente.Viagem.Id,
                StatusViagem = pagamentoEventoExistente.Viagem.Status?.GetHashCode(),
                Pagamento = new PagamentoViagemResponse()
                {
                    PagamentoEventoId = pagamentoEventoExistente.Id,
                    StatusPagamento = pagamentoEventoExistente.Status.GetHashCode(),
                    ValorParcela = pagamentoEventoExistente.Valor,
                    ValorMotorista = pagamentoEventoExistente.ValorTransferenciaMotorista,
                    CódTransacao = pagamentoEventoExistente.CodigoTransacao,
                    FormaPagamento = pagamentoEventoExistente.FormaPagamento?.GetHashCode(),
                    Mensagem = mensagem
                }
            };
        }

        public async Task<CancelamentoEventoViagemResponse> CancelarEventoViagemV1(
            CancelamentoEventoViagemRequest cancelarEventoViagemRequest, int empresaId)
        {
   
            var empresa = await _empresaReadRepository.GetByIdAsync(empresaId);

            if (empresa == null)
            {
                return new CancelamentoEventoViagemResponse(false, "Empresa não localizada.");
            }
                
            if (!empresa.UsaV1())
            {
                return new CancelamentoEventoViagemResponse(false, "Empresa sem permissão para realizar a integração");
            }
            
            return await CancelarEventoViagem(cancelarEventoViagemRequest, empresaId);
        }

        public async Task<CancelamentoEventoViagemResponse> CancelarEventoViagem(
            CancelamentoEventoViagemRequest cancelarEventoViagemRequest, int empresaId)
        {
            try
            {
                #region Validações e coleta de dados

                if (!cancelarEventoViagemRequest.Valor.HasValue)
                    return new CancelamentoEventoViagemResponse(false, "Valor é um campo obrigatório!");

                if (!cancelarEventoViagemRequest.ViagemExternoId.HasValue)
                    return new CancelamentoEventoViagemResponse(false, "ViagemExternoId é um campo obrigatório!");

                if (empresaId == 0)
                    return new CancelamentoEventoViagemResponse(false, "IdEmpresa é um campo obrigatório!");

                if (!cancelarEventoViagemRequest.PagamentoExternoId.HasValue)
                    return new CancelamentoEventoViagemResponse(false, "PagamentoExternoId é um campo obrigatório!");

                var pagamentoEvento = _pagamentoEventoReadRepository
                    .Include(x => x.Transacao)
                    .Include(x => x.Viagem)
                    .ThenInclude(v => v.PortadorProprietario)
                    .Include(x => x.Empresa)
                    .Include(x => x.Notificacao)
                    .LastOrDefault(x => x.PagamentoExternoId == cancelarEventoViagemRequest.PagamentoExternoId
                                        && x.Viagem.ViagemExternoId == cancelarEventoViagemRequest.ViagemExternoId
                                        && x.EmpresaId == empresaId);

                if (pagamentoEvento == null)
                    return new CancelamentoEventoViagemResponse(false, "Pagamento evento não localizado!");

                pagamentoEvento.JsonEnvioCancelamento = JsonConvert.SerializeObject(cancelarEventoViagemRequest);
                pagamentoEvento.DataCadastroCancelamento = DateTime.Now;
                if (pagamentoEvento.Status == StatusPagamento.Cancelado
                    && pagamentoEvento.DataCancelamento.HasValue && pagamentoEvento.Tipo == Tipo.Cancelamento)
                    return new CancelamentoEventoViagemResponse(false, "Pagamento já cancelado!");

                if (pagamentoEvento.Status == StatusPagamento.Processando)
                    return new CancelamentoEventoViagemResponse(false, "O pagamento ainda está processando, aguarde!");

                if (pagamentoEvento.Valor != cancelarEventoViagemRequest.Valor)
                    return new CancelamentoEventoViagemResponse(false,
                        "Valor informado não está coerente com o valor do pagamento evento!");

                if (pagamentoEvento.DataCadastro.Date != cancelarEventoViagemRequest.DataRequisicao.Date)
                    return new CancelamentoEventoViagemResponse(false,
                        "Data informada não está coerente com a data de cadastro do pagamento evento!");

                var lParametroMaxDiasCancelamento = await _parametrosAppService.GetParametrosAsync(-1,
                    Domain.Models.Parametros.Parametros.TipoDoParametro.PrazoMaximaParaCancelamentoPagamentoFrete,
                    Domain.Models.Parametros.Parametros.TipoDoValor.Number);

                var periodoCancelamento =
                    pagamentoEvento.DataBaixa?.AddDays(lParametroMaxDiasCancelamento.Valor.ToInt());

                if (periodoCancelamento.HasValue)
                    if (DateTime.Now > periodoCancelamento)
                    {
                        await RegistrarNotificacaoPagamento("Período máximo para cancelamento excedido.",
                            pagamentoEvento.Id);
                        return new CancelamentoEventoViagemResponse(false,
                            "Período máximo para cancelamento excedido.");
                    }

                #endregion

                #region Cancelamento do pagamento

                CancelamentoEventoViagemResponse retornoCancelamento = null;

                try
                {
                    if (pagamentoEvento.FormaPagamento == FormaPagamentoEvento.Pix)
                    {
                        retornoCancelamento = await CancelarPagamentosPix(pagamentoEvento);
                    }
                    else
                    {
                        retornoCancelamento = await CancelarPagamentosP2P(pagamentoEvento);
                    }
                }
                catch (Exception)
                {
                    pagamentoEvento.JsonRetornoCancelamento = JsonConvert.SerializeObject(retornoCancelamento);
                    await Repository.Command.SaveChangesAsync();

                    return retornoCancelamento;
                }


                pagamentoEvento.JsonRetornoCancelamento = JsonConvert.SerializeObject(retornoCancelamento);
                pagamentoEvento.DataRetornoCancelamento = DateTime.Now;

                await Repository.Command.SaveChangesAsync();

                return retornoCancelamento;

                #endregion
            }
            catch (Exception e)
            {
                return new CancelamentoEventoViagemResponse(false, e.Message);
            }
        }

        public async Task<string> RetornarMensagemDockIntegracaoViagem(string mensagemTraduzida,
            TransferenciaEntreContaResp retornoTransferencia)
        {
            var mensagemRetornada = await _mensagemAppService.RegistraMensagem(new MensagemRequest
            {
                CodigoAplicacao = ECodigoAplicacao.Dock,
                TextoMensagemOriginal = retornoTransferencia.message,
                TextoMensagemPadrao = mensagemTraduzida,
                Ativo = 1,
                CodigoMensagem = null,
                DataInicioMensagem = null,
                DataFimMensagem = null,
                DescricaoMensagem = null,
                ImagemMensagem = null
            });

            var mensagemRetornoDockPagamento = "";

            if (mensagemRetornada.novaMensagem)
                return mensagemRetornada.data.TextoMensagemPadrao ?? mensagemTraduzida;

            if (!mensagemRetornada.novaMensagem)
            {
                if (mensagemRetornada.data.Ativo == 1 && mensagemRetornada.data.MensagemTratada == 1)
                {
                    mensagemRetornoDockPagamento = mensagemRetornada.data.TextoMensagem ?? mensagemTraduzida;
                }
                else if (mensagemRetornada.data.Ativo == 1 && mensagemRetornada.data.MensagemTratada == 0)
                {
                    mensagemRetornoDockPagamento = mensagemRetornada.data.TextoMensagemPadrao ?? mensagemTraduzida;
                }
                else
                {
                    mensagemRetornoDockPagamento = mensagemTraduzida;
                }
            }

            return mensagemRetornoDockPagamento;
        }

        private async Task<CancelamentoEventoViagemResponse> CancelarPagamentosPix(
            Domain.Models.PagamentoEvento.PagamentoEvento pagamentoEvento)
        {
            var lParametroReenvioPagamentoEvento = await _parametrosReadRepository
                .GetParametrosAsync(-1,
                    Domain.Models.Parametros.Parametros.TipoDoParametro.CodigoReenvioPagamentoEvento,
                    Domain.Models.Parametros.Parametros.TipoDoValor.Number);

            if (pagamentoEvento.Status is StatusPagamento.Pendente or StatusPagamento.Aberto
                or StatusPagamento.Processando or StatusPagamento.Fechado)
                return new CancelamentoEventoViagemResponse(false,
                    "Não é possível cancelar um pagamento via Pix com status '" + pagamentoEvento.Status + "'");

            if (pagamentoEvento.Status is StatusPagamento.Erro && pagamentoEvento.ContadorReenvio <=
                (lParametroReenvioPagamentoEvento?.Valor ?? "5").ToIntSafe())
                return new CancelamentoEventoViagemResponse(false, "Pagamento com erro ainda está sendo processado.");

            if (pagamentoEvento.Status is StatusPagamento.Erro or StatusPagamento.NaoExecutado)
            {
                pagamentoEvento.Status = StatusPagamento.Cancelado;
                pagamentoEvento.DataCancelamento = DateTime.Now;
                pagamentoEvento.Descricao = "Pagamento evento cancelado.";
                pagamentoEvento.Transacao.ForEach(x =>
                {
                    x.Status = StatusPagamento.Cancelado;
                    x.Descricao = "Transação cancelada!";
                    x.DataCancelamento = DateTime.Now;
                });

                _pagamentoEventoWriteRepository.Update(pagamentoEvento);
            }

            return new CancelamentoEventoViagemResponse
            {
                Sucesso = true,
                Mensagem = "Pagamento evento cancelado com sucesso.",
                PagamentoEvento = new CancelamentoEventoViagemPagamentoResponse()
                {
                    Id = pagamentoEvento.Id,
                    Tipo = Tipo.Cancelamento.GetDescription(),
                    Status = StatusPagamento.Cancelado.ToString()
                },
                Transacoes = pagamentoEvento.Transacao
                    .Where(x => x.Tipo != Tipo.Tarifas)
                    .OrderByDescending(x => x.DataCadastro)
                    .Select(x => new CancelamentoEventoViagemTransacaoResponse()
                    {
                        Id = x.Id,
                        Valor = x.Valor,
                        Status = x.Status.GetDescription(),
                        Sucesso = false,
                        ContaOrigem = x.Origem,
                        ContaDestino = x.Destino,
                        StatusEnum = x.Status,
                        Mensagem = "Transação cancelada!"
                    }).ToList()
            };
        }

        private async Task<CancelamentoEventoViagemResponse> CancelarPagamentosP2P(
            Domain.Models.PagamentoEvento.PagamentoEvento pagamentoEvento)
        {
            #region Cancelamento do pagamento

            var lLog = LogManager.GetCurrentClassLogger();

            var tipoPagamentoOrigem = pagamentoEvento.Tipo;
            if (pagamentoEvento.Tipo != Tipo.Cancelamento)
            {
                await RegistrarPendenciaPagamento(pagamentoEvento.Id, "Solicitado cancelamento do evento",
                    StatusPagamento.Pendente, Tipo.Cancelamento, null, DateTime.Now, true, true);
            }

            //Pagamento ordenado por ordem de cadastro do motorista/proprieario/empresa 
            var lPagamentosExtorno = pagamentoEvento.Transacao
                .OrderByDescending(x => x.DataCadastro)
                .Where(x => x.Tipo != Tipo.Tarifas && x.Status != StatusPagamento.Cancelado).ToList();

            var mensagemNotificacao = $"Estorno do pagamento realizado com sucesso.";
            foreach (var transacao in lPagamentosExtorno)
            {
                try
                {
                    var lPagamentosRequest = new ConsultarPagamentosContaRequest()
                    {
                        ContaDestino = transacao.Destino.ToString(),
                        ContaOrigem = transacao.Origem.ToString(),
                        Valor = Math.Round(transacao.Valor, 2, MidpointRounding.AwayFromZero).ToStringSafe()
                            .Replace(',', '.'),
                        DataPagamento = pagamentoEvento.DataCadastro.Date.ToString("u").Replace("Z", "")
                    };
                    //Verificar se já foi feito 
                    var retornoConsulta = await _cartaoRepository.ConsultarPagamentosConta(lPagamentosRequest);

                    var lRetornoExtratoPagamento = JsonConvert.DeserializeObject<List<Transferencia>>(
                        retornoConsulta,
                        new JsonSerializerSettings { DateTimeZoneHandling = DateTimeZoneHandling.Local });

                    #region Validações da transacao

                    //Se n existir n faz o extorno
                    if (!lRetornoExtratoPagamento.Any(x =>
                            x.description.Contains(
                                $"{pagamentoEvento.Viagem.ViagemExternoId}/{tipoPagamentoOrigem.GetSigla()}")))
                        return new CancelamentoEventoViagemResponse(false,
                            "Ocorreu um erro ao cancelar pagamento. Não foi possível encontrar o extrato do pagamento de origem.");

                    var retornoConsultaSaldo = await VerificarSaldoContaOrigem(transacao.Destino.Value,
                        Math.Round(transacao.Valor, 2, MidpointRounding.AwayFromZero));

                    if (!retornoConsultaSaldo.sucesso)
                        return new CancelamentoEventoViagemResponse(false,
                            $"Estorno da transação cód {transacao.Id} com conta destino {transacao.Origem} " +
                            $"e conta origem {transacao.Destino} no valor de {transacao.Valor.FormatMonetario()} " +
                            $"com pagamento evento cód {transacao.IdPagamentoEvento} não iniciada: {retornoConsultaSaldo.mensagem}");

                    #endregion

                    var transferencia = new Transferencia()
                    {
                        amount = Math.Round(transacao.Valor, 2, MidpointRounding.AwayFromZero),
                        destinationAccount = transacao.Origem,
                        originalAccount = transacao.Destino.Value,
                        description = JsonConvert.SerializeObject(new
                        {
                            description = "Número de Referência: " +
                                          $"{pagamentoEvento.Viagem.ViagemExternoId}/{pagamentoEvento.Tipo.GetSigla()}",
                            protocol = $"{pagamentoEvento.Viagem.ViagemExternoId}/{pagamentoEvento.Tipo.GetSigla()}"
                        })
                    };

                    lLog.Info("Json de cancelamento: " + JsonConvert.SerializeObject(transferencia));
                    transacao.JsonEnvioDockCancelamento = JsonConvert.SerializeObject(transferencia);

                    var retornoTransferencia =
                        await _transferenciaRepository.RealizaTransferenciaEntreContas(transferencia);
                    transacao.JsonRespostaDockCancelamento = retornoTransferencia.RetornoJson;
                    transacao.ResponseCodeDockCancelamento = retornoTransferencia.Code.ToInt();
                    if (retornoTransferencia.Sucesso)
                    {
                        transacao.Status = StatusPagamento.Cancelado;
                        transacao.DataCancelamento = DateTime.Now;
                    }
                    else
                    {
                        mensagemNotificacao =
                            $"Estorno da transação cód {transacao.Id} com conta destino {transacao.Origem} e conta origem {transacao.
                                Destino} no valor de {transacao.Valor.FormatMonetario()} com pagamento evento cód {transacao.
                                IdPagamentoEvento} não realizada: {retornoTransferencia.message}";
                        await RegistrarNotificacaoPagamento(mensagemNotificacao, pagamentoEvento.Id);
                        break;
                    }

                    await Engine.CommandBus.SendCommandAsync(transacao);
                }

                catch (Exception e)
                {
                    return new CancelamentoEventoViagemResponse(false, e.Message);
                }
            }

            #endregion

            if (lPagamentosExtorno.All(x => x.Status == StatusPagamento.Cancelado))
            {
                pagamentoEvento.Status = StatusPagamento.Cancelado;
                pagamentoEvento.DataCancelamento = DateTime.Now;
                await Engine.CommandBus
                    .SendCommandAsync<Domain.Models.PagamentoEvento.PagamentoEvento>(pagamentoEvento);
            }

            return new CancelamentoEventoViagemResponse()
            {
                Sucesso = true,
                Mensagem = mensagemNotificacao,
                PagamentoEvento = new CancelamentoEventoViagemPagamentoResponse()
                {
                    Id = pagamentoEvento.Id,
                    Tipo = Tipo.Cancelamento.ToString(),
                    Status = pagamentoEvento.Status.ToString()
                },
                Transacoes = lPagamentosExtorno.Select(x => new CancelamentoEventoViagemTransacaoResponse()
                {
                    Sucesso = x.Status == StatusPagamento.Cancelado,
                    Mensagem = x.Status == StatusPagamento.Cancelado
                        ? "Sucesso ao cancelar a transação do pagamento!"
                        : "Erro ao processar estorno da transação.",
                    Valor = x.Valor,
                    ContaOrigem = x.Origem,
                    ContaDestino = x.Destino,
                    Agencia = x.Agencia,
                    Conta = x.Conta,
                    CodigoBanco = x.CodigoBanco,
                    Status = x.Status.ToString(),
                    StatusEnum = x.Status,
                    Id = x.Id
                }).ToList()
            };
        }

        private async Task<CancelamentoEventoViagemResponse> CancelarPagamentosP2PPorDuplicidade(
            Domain.Models.PagamentoEvento.PagamentoEvento pagamentoEvento)
        {
            #region Cancelamento do pagamento

            var lLog = LogManager.GetCurrentClassLogger();

            if (pagamentoEvento.Tipo != Tipo.Cancelamento)
            {
                await RegistrarPendenciaPagamento(pagamentoEvento.Id, "Cancelado pelo sistema",
                    StatusPagamento.Pendente, pagamentoEvento.Tipo, null, DateTime.Now, true, true);
            }

            //Pagamento ordenado por ordem de cadastro do motorista/proprieario/empresa 
            var lPagamentosExtorno = pagamentoEvento.Transacao
                .OrderByDescending(x => x.DataCadastro)
                .Where(x => x.Status == StatusPagamento.Fechado).ToList();

            var mensagemNotificacao = $"Estorno do pagamento realizado com sucesso.";
            foreach (var transacao in lPagamentosExtorno)
            {
                try
                {
                    #region Validações da transacao

                    var retornoConsultaSaldo = await VerificarSaldoContaOrigem(transacao.Destino.Value,
                        Math.Round(transacao.Valor, 2, MidpointRounding.AwayFromZero));

                    if (!retornoConsultaSaldo.sucesso)
                        return new CancelamentoEventoViagemResponse(false,
                            $"Estorno da transação cód {transacao.Id} com conta destino {transacao.Origem} " +
                            $"e conta origem {transacao.Destino} no valor de {transacao.Valor.FormatMonetario()} " +
                            $"com pagamento evento cód {transacao.IdPagamentoEvento} não iniciada: {retornoConsultaSaldo.mensagem}");

                    #endregion

                    var transferencia = new Transferencia()
                    {
                        amount = Math.Round(transacao.Valor, 2, MidpointRounding.AwayFromZero),
                        destinationAccount = transacao.Origem,
                        originalAccount = transacao.Destino.Value,
                        description = JsonConvert.SerializeObject(new
                        {
                            description = "Número de Referência: " +
                                          $"{pagamentoEvento.Viagem.ViagemExternoId}/{pagamentoEvento.Tipo.GetSigla()}",
                            protocol = $"{pagamentoEvento.Viagem.ViagemExternoId}/{pagamentoEvento.Tipo.GetSigla()}"
                        })
                    };

                    lLog.Info("Json de cancelamento: " + JsonConvert.SerializeObject(transferencia));
                    transacao.JsonEnvioDockCancelamento = JsonConvert.SerializeObject(transferencia);

                    var retornoTransferencia =
                        await _transferenciaRepository.RealizaTransferenciaEntreContas(transferencia);
                    transacao.JsonRespostaDockCancelamento = retornoTransferencia.RetornoJson;
                    transacao.ResponseCodeDockCancelamento = retornoTransferencia.Code.ToInt();
                    if (retornoTransferencia.Sucesso)
                    {
                        transacao.Status = StatusPagamento.Cancelado;
                        transacao.DataCancelamento = DateTime.Now;
                    }
                    else
                    {
                        mensagemNotificacao =
                            $"Estorno da transação cód {transacao.Id} com conta destino {transacao.Origem} e conta origem {transacao.
                                Destino} no valor de {transacao.Valor.FormatMonetario()} com pagamento evento cód {transacao.
                                IdPagamentoEvento} não realizada: {retornoTransferencia.message}";
                        await RegistrarNotificacaoPagamento(mensagemNotificacao, pagamentoEvento.Id);
                        break;
                    }

                    await Engine.CommandBus.SendCommandAsync(transacao);
                }

                catch (Exception e)
                {
                    return new CancelamentoEventoViagemResponse(false, e.Message);
                }
            }

            #endregion

            if (lPagamentosExtorno.All(x => x.Status == StatusPagamento.Cancelado))
            {
                pagamentoEvento.Status = StatusPagamento.NaoExecutado;
                pagamentoEvento.DataCancelamento = DateTime.Now;
                await Engine.CommandBus
                    .SendCommandAsync<Domain.Models.PagamentoEvento.PagamentoEvento>(pagamentoEvento);
            }

            return new CancelamentoEventoViagemResponse()
            {
                Sucesso = true,
                Mensagem = mensagemNotificacao,
                PagamentoEvento = new CancelamentoEventoViagemPagamentoResponse()
                {
                    Id = pagamentoEvento.Id,
                    Status = pagamentoEvento.Status.ToString()
                },
                Transacoes = lPagamentosExtorno.Select(x => new CancelamentoEventoViagemTransacaoResponse()
                {
                    Sucesso = x.Status == StatusPagamento.Cancelado,
                    Mensagem = x.Status == StatusPagamento.Cancelado
                        ? "Sucesso ao cancelar a transação do pagamento!"
                        : "Erro ao processar estorno da transação.",
                    Valor = x.Valor,
                    ContaOrigem = x.Origem,
                    ContaDestino = x.Destino,
                    Agencia = x.Agencia,
                    Conta = x.Conta,
                    CodigoBanco = x.CodigoBanco,
                    Status = x.Status.ToString(),
                    StatusEnum = x.Status,
                    Id = x.Id
                }).ToList()
            };
        }

        public async Task<RespostaViagemPagamento> GerarPagamentoEventoViagemP2P(
            Domain.Models.PagamentoEvento.PagamentoEvento eventoPagamento)
        {
            var log = LogManager.GetCurrentClassLogger();

            try
            {
                #region Coleta de Contas BBC

                var empresa = await _empresaReadRepository.GetByIdAsync(eventoPagamento.EmpresaId.ToInt());
                var idContaMotoristaDestino = 0;

                //CONTA empresa

                #region Coleta conta Empresa

                int idContaOrigem;
                if (empresa.ContaFrete == null)
                {
                    var contasOrigem = await _cartaoRepository.ConsultarContas(null, null, empresa.Cnpj);

                    if (contasOrigem == null)
                    {
                        return new RespostaViagemPagamento()
                        {
                            Sucesso = false,
                            Mensagem = "Nenhuma conta da empresa foi encontrada.",
                            TipoOperacao = TipoOperacao.Erro,
                            StatusPagamento = StatusPagamento.NaoExecutado
                        };
                    }

                    var contaOrigemAtiva = contasOrigem.content?
                        .FirstOrDefault(x => ViagemUtils.StatusConta().Contains(x.idStatusConta));

                    if (contaOrigemAtiva == null)
                    {
                        return new RespostaViagemPagamento
                        {
                            Sucesso = false,
                            Mensagem = "Nenhuma conta ativa da empresa foi encontrada.",
                            TipoOperacao = TipoOperacao.Erro,
                            StatusPagamento = StatusPagamento.NaoExecutado
                        };
                    }

                    idContaOrigem = contaOrigemAtiva.id;
                }
                else
                {
                    idContaOrigem = empresa.ContaFrete ?? 0;
                }

                log.Info("Conta origem da empresa: " + idContaOrigem);

                #region Validacoes conta origem

                //Verificador saldo
                var retornoConsultaSaldo =
                    await VerificarSaldoContaOrigem(idContaOrigem, eventoPagamento.Valor.ToDecimal());

                if (!retornoConsultaSaldo.sucesso)
                {
                    return new RespostaViagemPagamento
                    {
                        Sucesso = false,
                        Mensagem = retornoConsultaSaldo.mensagem,
                        TipoOperacao = TipoOperacao.Notificacao,
                        StatusPagamento = StatusPagamento.Erro
                    };
                }

                #endregion

                #endregion

                //CONTA proprietario

                #region Coleta conta Proprietario

                if (string.IsNullOrWhiteSpace(eventoPagamento.Viagem.PortadorProprietario.CpfCnpj))
                {
                    return new RespostaViagemPagamento()
                    {
                        Sucesso = false,
                        Mensagem = "Proprietário (portador contratado) sem cpf/cnpj definido.",
                        TipoOperacao = TipoOperacao.Notificacao,
                        StatusPagamento = StatusPagamento.Erro
                    };
                }

                var contaProprietarioDestino = _cartaoRepository
                    .ConsultarContas(null, null, eventoPagamento.Viagem.PortadorProprietario.CpfCnpj)?.Result;

                if (contaProprietarioDestino == null)
                {
                    return new RespostaViagemPagamento()
                    {
                        Sucesso = false,
                        Mensagem = "Nenhuma conta do proprietário (portador contratado) foi encontrada.",
                        TipoOperacao = TipoOperacao.Notificacao,
                        StatusPagamento = StatusPagamento.Erro
                    };
                }

                var idContaProprietarioDestino = contaProprietarioDestino.content
                    ?.FirstOrDefault(x => ViagemUtils.StatusConta().Contains(x.idStatusConta))?.id ?? 0;

                if (idContaProprietarioDestino == 0)
                {
                    log.Info(
                        $"Nenhuma conta ativa do proprietário (portador contratado) foi encontrada. Pagamento evento Id {eventoPagamento.Id}");

                    return new RespostaViagemPagamento()
                    {
                        Sucesso = false,
                        Mensagem = "Nenhuma conta ativa do proprietário (portador contratado) foi encontrada.",
                        TipoOperacao = TipoOperacao.Notificacao,
                        StatusPagamento = StatusPagamento.Erro
                    };
                }

                log.Info("Conta destino do proprietário (portador contratado): " + idContaProprietarioDestino);

                if (eventoPagamento.Viagem.PortadorProprietario.CpfCnpj.Length > 11)
                {
                    var individuoPortadorContratado = await _cartaoRepository
                        .ConusltaContaPessoaJuridica(eventoPagamento.Viagem.PortadorProprietario.CpfCnpj);

                    var bloqueioSpds = individuoPortadorContratado.results?.FirstOrDefault()?.statusSPD;
                    if (bloqueioSpds != null)
                    {
                        foreach (var lBloqueioSpd in bloqueioSpds)
                        {
                            if (lBloqueioSpd.statusId == 1 && lBloqueioSpd.createDate < DateTime.Now.AddDays(-15))
                            {
                                return new RespostaViagemPagamento()
                                {
                                    Sucesso = false,
                                    Mensagem = "Conta de destino do proprietário (portador contratado) bloqueada.",
                                    TipoOperacao = TipoOperacao.Notificacao,
                                    StatusPagamento = StatusPagamento.Erro
                                };
                            }

                            if (lBloqueioSpd.statusId != 4 && lBloqueioSpd.statusId != 10 &&
                                lBloqueioSpd.statusId != 11 && lBloqueioSpd.statusId != 12 &&
                                lBloqueioSpd.statusId != 18 && lBloqueioSpd.statusId != 19 &&
                                lBloqueioSpd.statusId != 20 && lBloqueioSpd.statusId != 1)
                            {
                                return new RespostaViagemPagamento()
                                {
                                    Sucesso = false,
                                    Mensagem = "Conta de destino do proprietário (portador contratado) bloqueada.",
                                    TipoOperacao = TipoOperacao.Notificacao,
                                    StatusPagamento = StatusPagamento.Erro
                                };
                            }
                        }
                    }
                }
                else
                {
                    var idPessoaPortadorContratado = contaProprietarioDestino.content
                        ?.FirstOrDefault(x => ViagemUtils.StatusConta().Contains(x.idStatusConta))?.idPessoa
                        .ToDecimalSafe() ?? 0;

                    var validacaoStatusSpdContaProprietario =
                        await _cartaoRepository.ConsultarPessoa(idPessoaPortadorContratado);

                    var bloqueioSpds = validacaoStatusSpdContaProprietario?.statusSPD;
                    if (bloqueioSpds != null)
                    {
                        foreach (var lBloqueioSpd in bloqueioSpds)
                        {
                            if (lBloqueioSpd.statusId != 4 && lBloqueioSpd.statusId != 10 &&
                                lBloqueioSpd.statusId != 11 && lBloqueioSpd.statusId != 12 &&
                                lBloqueioSpd.statusId != 18 && lBloqueioSpd.statusId != 19 &&
                                lBloqueioSpd.statusId != 20)
                            {
                                return new RespostaViagemPagamento()
                                {
                                    Sucesso = false,
                                    Mensagem = "Conta de destino do proprietário (portador contratado) bloqueada.",
                                    TipoOperacao = TipoOperacao.Notificacao,
                                    StatusPagamento = StatusPagamento.Erro
                                };
                            }
                        }
                    }
                }

                #region Procura configuração de transferência automática
                
                var valorTransferenciaMotoristaAutomatica = 0m;
                
                //calcula o valor de transferência automática a ser transferido com base no tipo de pagamento
                if (eventoPagamento.ValorTransferenciaMotorista == null)
                {
                    valorTransferenciaMotoristaAutomatica = await ColetarValorTransferenciaAutomatica(eventoPagamento);
                    if (valorTransferenciaMotoristaAutomatica > 0)
                    {
                        eventoPagamento.ValorTransferenciaMotorista = valorTransferenciaMotoristaAutomatica;
                        await Repository.Command.SaveChangesAsync();
                    }
                }

                #endregion

                #endregion

                //CONTA motorista

                #region Coleta conta Motorista

                if (valorTransferenciaMotoristaAutomatica > 0)
                {
                    if (string.IsNullOrWhiteSpace(eventoPagamento.Viagem.PortadorMotorista.CpfCnpj))
                    {
                        return new RespostaViagemPagamento()
                        {
                            Sucesso = false,
                            Mensagem = "Conta do motorista de destino não foi definida.",
                            TipoOperacao = TipoOperacao.Notificacao,
                            StatusPagamento = StatusPagamento.Erro
                        };
                    }

                    var contaMotoristaDestino = _cartaoRepository
                        .ConsultarContas(null, null, eventoPagamento.Viagem.PortadorMotorista.CpfCnpj)?.Result;

                    if (contaMotoristaDestino == null)
                    {
                        return new RespostaViagemPagamento()
                        {
                            Sucesso = false,
                            Mensagem = "Conta do motorista de destino não foi definida.",
                            TipoOperacao = TipoOperacao.Notificacao,
                            StatusPagamento = StatusPagamento.Erro
                        };
                    }

                    idContaMotoristaDestino = contaMotoristaDestino.content
                        ?.FirstOrDefault(x => ViagemUtils.StatusConta().Contains(x.idStatusConta))?.id ?? 0;

                    log.Info(
                        $"Conta destino do motorista: {idContaMotoristaDestino}. Id pagamento evento: {eventoPagamento.Id}.");

                    if (eventoPagamento.Viagem.PortadorProprietario.CpfCnpj.Length > 11)
                    {
                        var individuoMotorista = await _cartaoRepository
                            .ConusltaContaPessoaJuridica(eventoPagamento.Viagem.PortadorMotorista.CpfCnpj);
                        var bloqueioSpds = individuoMotorista.results?.First()?.statusSPD;
                        if (bloqueioSpds != null)
                        {
                            foreach (var bloqueioSpd in bloqueioSpds)
                            {
                                if (bloqueioSpd.statusId == 1 &&
                                    bloqueioSpd.createDate < DateTime.Now.AddDays(-15))
                                {
                                    return new RespostaViagemPagamento()
                                    {
                                        Sucesso = false,
                                        Mensagem = "Conta do motorista de destino bloqueada.",
                                        TipoOperacao = TipoOperacao.Notificacao,
                                        StatusPagamento = StatusPagamento.Erro
                                    };
                                }

                                if (bloqueioSpd.statusId != 4 && bloqueioSpd.statusId != 10 &&
                                    bloqueioSpd.statusId != 11 && bloqueioSpd.statusId != 12 &&
                                    bloqueioSpd.statusId != 18 && bloqueioSpd.statusId != 19 &&
                                    bloqueioSpd.statusId != 20 && bloqueioSpd.statusId != 1)
                                {
                                    return new RespostaViagemPagamento()
                                    {
                                        Sucesso = false,
                                        Mensagem = "Conta do motorista de destino bloqueada.",
                                        TipoOperacao = TipoOperacao.Notificacao,
                                        StatusPagamento = StatusPagamento.Erro
                                    };
                                }
                            }
                        }
                    }
                    else
                    {
                        var idPessoaMotorista = contaMotoristaDestino.content
                            ?.FirstOrDefault(x => ViagemUtils.StatusConta().Contains(x.idStatusConta))?.idPessoa
                            .ToDecimalSafe() ?? 0;
                        var validacaoStatusSpdMotorista = await _cartaoRepository.ConsultarPessoa(idPessoaMotorista);
                        var bloqueiosSpd = validacaoStatusSpdMotorista?.statusSPD;
                        if (bloqueiosSpd != null)
                        {
                            foreach (var bloqueioSpd in bloqueiosSpd)
                            {
                                if (bloqueioSpd.statusId != 4 && bloqueioSpd.statusId != 10 &&
                                    bloqueioSpd.statusId != 11 && bloqueioSpd.statusId != 12 &&
                                    bloqueioSpd.statusId != 18 && bloqueioSpd.statusId != 19 &&
                                    bloqueioSpd.statusId != 20)
                                {
                                    return new RespostaViagemPagamento()
                                    {
                                        Sucesso = false,
                                        Mensagem = "Conta do motorista de destino não foi definida!",
                                        TipoOperacao = TipoOperacao.Notificacao,
                                        StatusPagamento = StatusPagamento.Erro
                                    };
                                }
                            }
                        }
                    }
                }

                #endregion

                #region Localizar conta AliasBank

                //do proprietario
                var retornoContaAliasBank = await LocalizarContaAliasBank(idContaProprietarioDestino);

                if (!retornoContaAliasBank.sucesso)
                {
                    log.Info("Erro ao validar conta AliasBank do proprietário!");

                    return new RespostaViagemPagamento()
                    {
                        Sucesso = false,
                        Mensagem =
                            $"Erro ao validar conta do proprietário (portador contratado). {retornoContaAliasBank.mensagem}",
                        TipoOperacao = TipoOperacao.Pendencia,
                        StatusPagamento = StatusPagamento.Erro
                    };
                }

                //do motorista
                if (idContaMotoristaDestino != 0 && valorTransferenciaMotoristaAutomatica > 0)
                {
                    var lRetornoContaAliasMotorista = await LocalizarContaAliasBank(idContaMotoristaDestino);

                    if (lRetornoContaAliasMotorista.sucesso == false)
                    {
                        log.Info("Erro ao validar conta AliasBank do motorista!");

                        return new RespostaViagemPagamento()
                        {
                            Sucesso = false,
                            Mensagem = $"Erro ao validar conta do motorista. {lRetornoContaAliasMotorista.mensagem}",
                            TipoOperacao = TipoOperacao.Pendencia,
                            StatusPagamento = StatusPagamento.Erro
                        };
                    }
                }

                #endregion

                #endregion;

                log.Info($"Inicio IntegrarPagamentoViagem Empresa: {Engine.User.EmpresaId}, " +
                         $"Conta origem: {idContaOrigem}, " +
                         $"Conta proprietario destino: {idContaProprietarioDestino}, " +
                         $"Conta motorista destino: {idContaProprietarioDestino}, " +
                         $"Valor do pagamento: {eventoPagamento.Valor}" +
                         $"Valor transferencia automatica p motorista: {valorTransferenciaMotoristaAutomatica}."
                );

                log.Info("Valida Pagamento Duplicado Dock");

                var lRespostaViagemPagamento = new RespostaViagemPagamento();

                var lEmpresaPropDuplicado =
                    await ValidarPagamentoDuplicado(eventoPagamento, idContaOrigem, idContaProprietarioDestino,
                        "proprietario");

                if (lEmpresaPropDuplicado.Sucesso && valorTransferenciaMotoristaAutomatica == 0)
                {
                    return lEmpresaPropDuplicado;
                }

                eventoPagamento.Valor = Math.Round(eventoPagamento.Valor, 2);

                if (!lEmpresaPropDuplicado.Sucesso)
                {
                    lRespostaViagemPagamento = await TransferenciaPagamentoEventoP2PDock(eventoPagamento, idContaOrigem,
                        idContaProprietarioDestino, eventoPagamento.Tipo);

                    if (!lRespostaViagemPagamento.Sucesso)
                    {
                        return new RespostaViagemPagamento()
                        {
                            Sucesso = false,
                            Mensagem = lRespostaViagemPagamento.Mensagem,
                            TipoOperacao = TipoOperacao.Erro,
                            StatusPagamento = StatusPagamento.Pendente
                        };
                    }
                }

                //Fez a transferencia pro proprietario e viu q tem q fz pro motorista
                if (valorTransferenciaMotoristaAutomatica > 0)
                {
                    var lPropMotoraDuplicado = await ValidarPagamentoDuplicado(eventoPagamento,
                        idContaProprietarioDestino,
                        idContaMotoristaDestino, "motorista");

                    if (lPropMotoraDuplicado.Sucesso)
                    {
                        lRespostaViagemPagamento.Mensagem += " " + lPropMotoraDuplicado.Mensagem;
                        lRespostaViagemPagamento.Sucesso = true;
                        lRespostaViagemPagamento.TipoOperacao = TipoOperacao.Notificacao;
                        lRespostaViagemPagamento.StatusPagamento = StatusPagamento.Fechado;

                        return lRespostaViagemPagamento;
                    }

                    var lTransferenciaProprietarioMotorista = await TransferenciaPagamentoEventoP2PDock(eventoPagamento,
                        idContaProprietarioDestino, idContaMotoristaDestino, Tipo.Adiantamento, true);

                    lRespostaViagemPagamento.Mensagem += " " + lTransferenciaProprietarioMotorista.Mensagem;
                    lRespostaViagemPagamento.Sucesso =
                        lRespostaViagemPagamento.Sucesso && lTransferenciaProprietarioMotorista.Sucesso;
                    lRespostaViagemPagamento.TipoOperacao = TipoOperacao.Notificacao;
                    lRespostaViagemPagamento.StatusPagamento = lRespostaViagemPagamento.Sucesso
                        ? StatusPagamento.Fechado
                        : StatusPagamento.Erro;
                }

                var empresaLogada = _empresaReadRepository.FirstOrDefaultAsync(x => x.Cnpj == empresa.Cnpj).Result;
                await CalculaTarifasTransacao(eventoPagamento.Id, empresaLogada, idContaOrigem);

                return lRespostaViagemPagamento;
            }
            catch (Exception e)
            {
                log.Error("IntegrarPagamento Erro pagamento P2P: " + e.Message);
                Console.WriteLine(e);
                throw;
            }
        }

        public async Task<RespostaViagemPagamento> GerarPagamentoEventoViagemPix(
            Domain.Models.PagamentoEvento.PagamentoEvento eventoPagamento, string chavePix, string agencia,
            string conta, string codigoBanco, int? tipoConta, bool? recebedorAutorizado = false,
            string recebedorAutorizadoCpfCnpj = null)
        {
            try
            {
                var lLog = LogManager.GetCurrentClassLogger();

                #region Validacoes Request

                //Se mandar o codigobanco igual ao parametrizado da BBC entao trata como agencia e conta interno
                var parametroBankNumberDock = await _parametrosReadRepository
                    .GetByTipoDoParametroAsync(Domain.Models.Parametros.Parametros.TipoDoParametro.BankNumberDock);

                var bankNumberDock = parametroBankNumberDock?.Valor;

                if (string.IsNullOrWhiteSpace(bankNumberDock)) bankNumberDock = "655";

                var pagarPorChavePix = !chavePix.IsNullOrWhiteSpace();
                var pagarPorAgenciaContaBanco = !agencia.IsNullOrWhiteSpace() && !conta.IsNullOrWhiteSpace();
                var pagarPorAgenciaContaBancoExterno = pagarPorAgenciaContaBanco && !codigoBanco.IsNullOrWhiteSpace()
                    && tipoConta != null && !codigoBanco.Equals(bankNumberDock);

                //Pelo menos um desses dois tem que ser verdadeiro
                if (!pagarPorChavePix && !pagarPorAgenciaContaBanco)
                {
                    return new RespostaViagemPagamento()
                    {
                        Sucesso = false,
                        Mensagem =
                            "Para pagamentos de tipo Pix é necessário informar uma chave pix ou agência e conta.",
                        TipoOperacao = TipoOperacao.Erro,
                        StatusPagamento = StatusPagamento.NaoExecutado
                    };
                }

                //Se for pagar pra banco externo vê se foi informado o código e o tipo de conta
                if (!pagarPorChavePix && !codigoBanco.IsNullOrWhiteSpace() && !codigoBanco.Equals(bankNumberDock) &&
                    tipoConta == null)
                {
                    return new RespostaViagemPagamento()
                    {
                        Sucesso = false,
                        Mensagem =
                            "Para pagamentos de tipo Pix para outros bancos é necessário informar o Código do Banco e o Tipo de Conta (Poupanca ou Corrente).",
                        TipoOperacao = TipoOperacao.Erro,
                        StatusPagamento = StatusPagamento.NaoExecutado
                    };
                }

                #endregion

                #region Validacoes conta Empresa

                var empresaLogada = await _empresaReadRepository.GetByIdAsync(eventoPagamento.EmpresaId.ToInt());

                int idContaEmpresa;
                if (empresaLogada.ContaFrete == null)
                {
                    var contasOrigem = await _cartaoRepository.ConsultarContas(null, null, empresaLogada.Cnpj);

                    if (contasOrigem == null)
                    {
                        return new RespostaViagemPagamento()
                        {
                            Sucesso = false,
                            Mensagem = "Nenhuma conta da empresa foi encontrada.",
                            TipoOperacao = TipoOperacao.Erro,
                            StatusPagamento = StatusPagamento.NaoExecutado
                        };
                    }

                    var contaOrigemAtiva = contasOrigem.content?
                        .FirstOrDefault(x => ViagemUtils.StatusConta().Contains(x.idStatusConta));

                    if (contaOrigemAtiva == null)
                    {
                        return new RespostaViagemPagamento
                        {
                            Sucesso = false,
                            Mensagem = "Nenhuma conta ativa da empresa foi encontrada.",
                            TipoOperacao = TipoOperacao.Erro,
                            StatusPagamento = StatusPagamento.NaoExecutado
                        };
                    }

                    idContaEmpresa = contaOrigemAtiva.id;
                }
                else
                {
                    idContaEmpresa = empresaLogada.ContaFrete ?? 0;
                }

                var retornoSaldoContaEmpresa =
                    await VerificarSaldoContaOrigem(idContaEmpresa, eventoPagamento.Valor.ToDecimal());

                if (!retornoSaldoContaEmpresa.sucesso)
                {
                    return new RespostaViagemPagamento
                    {
                        Sucesso = false,
                        Mensagem = retornoSaldoContaEmpresa.mensagem,
                        TipoOperacao = TipoOperacao.Erro,
                        StatusPagamento = StatusPagamento.Erro
                    };
                }

                #endregion

                #region Validacoes cpf/cnpj de destino

                var cpfCnpjDestino =
                    recebedorAutorizado == true && !string.IsNullOrWhiteSpace(recebedorAutorizadoCpfCnpj)
                        ? recebedorAutorizadoCpfCnpj.OnlyNumbers()
                        : eventoPagamento.Viagem.PortadorProprietario.CpfCnpj;

                if (string.IsNullOrWhiteSpace(cpfCnpjDestino))
                {
                    return new RespostaViagemPagamento()
                    {
                        Sucesso = false,
                        Mensagem = $"Cpf/Cnpj da conta destino não informado.",
                        TipoOperacao = TipoOperacao.Erro,
                        StatusPagamento = StatusPagamento.NaoExecutado
                    };
                }

                #endregion

                #region Validacoes formato de pagamento (chave, agencia, conta, banco externo)

                RespostaViagemPagamento retornoValidacaoPix;

                var transferenciaPixRequest = new CriarTransferenciaPixRequest();
                transferenciaPixRequest.Descricao =
                    "branchNumber " + $"{eventoPagamento.Viagem.FilialId}" + " nationalRegistration " +
                    $"{(eventoPagamento.RecebedorAutorizado.IsNullOrWhiteSpace() ?
                        eventoPagamento.Viagem.PortadorProprietario.CpfCnpj : eventoPagamento.RecebedorAutorizado)}" +
                    " description Número de Referência " +
                    $"{eventoPagamento.Viagem.ViagemExternoId}{eventoPagamento.Tipo.GetSigla()}" +
                    " protocol " + $"{eventoPagamento.Viagem.ViagemExternoId}{eventoPagamento.Tipo.GetSigla()}";
                transferenciaPixRequest.ValorFinalPagamento = decimal.ToDouble(eventoPagamento.Valor);
                transferenciaPixRequest.IdAccount = idContaEmpresa;

                if (pagarPorChavePix)
                {
                    retornoValidacaoPix = await ValidarPixComChavePix(eventoPagamento, empresaLogada,
                        transferenciaPixRequest, idContaEmpresa, cpfCnpjDestino, chavePix);
                }
                else if (!pagarPorAgenciaContaBancoExterno)
                {
                    retornoValidacaoPix = await ValidarPixComAgenciaConta(eventoPagamento, empresaLogada,
                        transferenciaPixRequest, idContaEmpresa, cpfCnpjDestino, agencia, conta,
                        recebedorAutorizado);
                }
                else
                {
                    retornoValidacaoPix = await ValidarPixPorAgenciaContaBancoExterno(eventoPagamento, empresaLogada,
                        transferenciaPixRequest, idContaEmpresa, cpfCnpjDestino, agencia, conta,
                        codigoBanco, (int)tipoConta, recebedorAutorizado);
                }

                if (!retornoValidacaoPix.Sucesso) return retornoValidacaoPix;

                #endregion

                #region Alimenta transacao

                if (eventoPagamento.Tipo == null)
                {
                    return new RespostaViagemPagamento()
                    {
                        Sucesso = false,
                        Mensagem = "Evento de pagamento sem Tipo informado.",
                        TipoOperacao = TipoOperacao.Erro,
                        StatusPagamento = StatusPagamento.NaoExecutado
                    };
                }

                var transacaoExistente = await GetTransacaoExistentePix(eventoPagamento, idContaEmpresa,
                    FormaPagamentoEvento.Pix, (Tipo)eventoPagamento.Tipo);

                if (transacaoExistente != null)
                {
                    if (transacaoExistente.Status != StatusPagamento.Fechado &&
                        transacaoExistente.Status != StatusPagamento.Erro &&
                        transacaoExistente.Status != StatusPagamento.NaoExecutado)
                        return new RespostaViagemPagamento
                        {
                            Sucesso = false,
                            Mensagem = "Uma transação Pix inválida para reprocessamento foi encontrada.",
                            TipoOperacao = TipoOperacao.Erro,
                            StatusPagamento = StatusPagamento.NaoExecutado
                        };

                    if (transacaoExistente.Status == StatusPagamento.Fechado)
                        return new RespostaViagemPagamento()
                        {
                            Sucesso = true,
                            TipoOperacao = TipoOperacao.Notificacao,
                            Mensagem = "Transferencia Pix já executada com sucesso!",
                            StatusPagamento = StatusPagamento.Fechado
                        };
                }

                var transacaoSalvarRequest = new TransacaoRequest()
                {
                    IdPagamentoEvento = eventoPagamento.Id,
                    Origem = idContaEmpresa,
                    FormaPagamento = FormaPagamentoEvento.Pix,
                    Tipo = eventoPagamento.Tipo,
                    Valor = eventoPagamento.Valor,
                    Descricao = "Transacao Pix iniciada",
                    Status = StatusPagamento.Processando,
                    IdEndToEnd = transferenciaPixRequest.IdEndToEnd,
                    Agencia = agencia,
                    Conta = conta,
                    CodigoBanco = codigoBanco,
                    TipoConta = tipoConta,
                    Description = transferenciaPixRequest.Descricao,
                    JsonEnvioDock = JsonConvert.SerializeObject(transferenciaPixRequest)
                };

                lLog.Info("Json de GerarPagamentoEventoViagemPix: " +
                          JsonConvert.SerializeObject(transacaoSalvarRequest));

                if (transacaoExistente != null) transacaoSalvarRequest.Id = transacaoExistente.Id;

                #endregion

                #region Salva transacao

                var transacaoResponse = await _transacaoRegisterAppService.RegistrarTransacao(transacaoSalvarRequest);

                if (!transacaoResponse.sucesso)
                    return new RespostaViagemPagamento()
                    {
                        Sucesso = false,
                        Mensagem = "Não foi possível criar o registro de transação Pix do evento de pagamento.",
                        TipoOperacao = TipoOperacao.Erro,
                        StatusPagamento = StatusPagamento.Erro
                    };

                #endregion

                #region Executa a transferencia Pix

                var pixEmpresaProprietarioDockResponse = await _pixAppService
                    .CriarTransferenciaPix(transferenciaPixRequest, idContaEmpresa);

                var pixInfo = (CriarTransferenciaPixBaaSResponse)pixEmpresaProprietarioDockResponse.data;

                #endregion

                if (!pixEmpresaProprietarioDockResponse.sucesso || pixInfo == null)
                    return await FinalizarAlterandoTransacao(transacaoResponse.id.ToIntSafe(),
                        "Erro de serviço ao realizar transação Pix. " + pixEmpresaProprietarioDockResponse.mensagem,
                        StatusPagamento.Erro, false, TipoOperacao.Erro,
                        JsonConvert.SerializeObject(pixEmpresaProprietarioDockResponse),
                        pixEmpresaProprietarioDockResponse.StatusCodeDock);

                var transacao = await _transacaoReadRepository.GetByIdAsync(transacaoResponse.id.ToIntSafe());
                if (transacao != null)
                {
                    transacao.IdEndToEnd = pixInfo.IdEndToEnd;
                }

                eventoPagamento.CodigoTransacao = pixInfo.TransactionCode;

                await Repository.Command.SaveChangesAsync();

                //Nota: HML do Pix da Dock sempre retornar status pendente
                if (pixInfo.TransactionStatus == "NOT_EXECUTED")
                    return await FinalizarAlterandoTransacao(transacaoResponse.id.ToIntSafe(),
                        "Não foi possível executar a transação Pix.",
                        StatusPagamento.NaoExecutado, false, TipoOperacao.Erro,
                        JsonConvert.SerializeObject(pixEmpresaProprietarioDockResponse),
                        pixEmpresaProprietarioDockResponse.StatusCodeDock);

                if (pixInfo.TransactionStatus == "EXECUTED")
                    return await FinalizarAlterandoTransacao(transacaoResponse.id.ToIntSafe(),
                        "Transferencia Pix executada com sucesso!", StatusPagamento.Fechado, true,
                        TipoOperacao.Notificacao, JsonConvert.SerializeObject(pixEmpresaProprietarioDockResponse),
                        pixEmpresaProprietarioDockResponse.StatusCodeDock,
                        pixInfo.TransactionDate != null
                            ? pixInfo.TransactionDate.ToDateTimeSafe().AddHours(-3)
                            : DateTime.Now);

                //Status Pendente

                //Reconsulta do status caso nesse meio tempo ele tenha sido marcado como executado

                var lParametrosSegundo =
                    (await _parametrosReadRepository.GetParametrosAsync(-1, Domain.Models.Parametros.Parametros
                        .TipoDoParametro.TempoEsperaSegundosPix, Domain.Models.Parametros.Parametros
                        .TipoDoValor.Number)).Valor.ToIntSafe();

                var dataIncio = DateTime.Now.AddSeconds(lParametrosSegundo);
                while (true)
                {
                    var reconsultaResponse =
                        await _pixAppService.ConsultarTransferencia(eventoPagamento.CodigoTransacao);
                    if (!reconsultaResponse.sucesso)
                        return await FinalizarAlterandoTransacao(transacaoResponse.id.ToIntSafe(),
                            "Transação Pix criada com status pendente.", StatusPagamento.Pendente, true,
                            TipoOperacao.Pendencia, JsonConvert.SerializeObject(pixEmpresaProprietarioDockResponse),
                            pixEmpresaProprietarioDockResponse.StatusCodeDock);

                    var pixInfoReconsulta = (ConsultarTransferenciaPixBaaSResponse)reconsultaResponse.data;

                    if (pixInfoReconsulta.TransactionStatus == "NOT_EXECUTED")
                    {
                        return await FinalizarAlterandoTransacao(transacaoResponse.id.ToIntSafe(),
                            $"Não foi possível executar a transação Pix. {pixInfoReconsulta.ErroDescricao}",
                            StatusPagamento.NaoExecutado, false, TipoOperacao.Erro,
                            JsonConvert.SerializeObject(reconsultaResponse), reconsultaResponse.StatusCodeDock);
                    }

                    if (pixInfoReconsulta.TransactionStatus == "EXECUTED")
                    {
                        return await FinalizarAlterandoTransacao(transacaoResponse.id.ToIntSafe(),
                            "Transferencia Pix executada com sucesso!", StatusPagamento.Fechado, true,
                            TipoOperacao.Notificacao, JsonConvert.SerializeObject(reconsultaResponse),
                            reconsultaResponse.StatusCodeDock,
                            pixInfo.TransactionDate != null
                                ? pixInfo.TransactionDate.ToDateTimeSafe().AddHours(-3)
                                : DateTime.Now);
                    }

                    if (DateTime.Now > dataIncio) break;
                }

                return await FinalizarAlterandoTransacao(transacaoResponse.id.ToIntSafe(),
                    "Transação Pix criada com status pendente.", StatusPagamento.Pendente, true,
                    TipoOperacao.Pendencia);
            }
            catch (Exception e)
            {
                return new RespostaViagemPagamento()
                {
                    Sucesso = false,
                    Mensagem =
                        $"Erro ao efetuar Pix para o {(recebedorAutorizado == true ? "motorista (recebedor autorizado)" : "proprietário (portador contratado)")}: {e.Message}",
                    TipoOperacao = TipoOperacao.Erro,
                    StatusPagamento = StatusPagamento.Erro
                };
            }
        }

        private async Task<RespostaViagemPagamento> ValidarPixPorAgenciaContaBancoExterno(
            Domain.Models.PagamentoEvento.PagamentoEvento eventoPagamento, Domain.Models.Empresa.Empresa empresaLogada,
            CriarTransferenciaPixRequest pixRequest, int idContaOrigemEmpresa, string cpfCnpjDestino, string agencia,
            string conta, string codigoBanco, int tipoContaDock, bool? recebedorAutorizado = false)
        {
            var bancoContaRequest = await _cartaoRepository
                .ConsultarBancoDock(codigoBanco);

            if (string.IsNullOrWhiteSpace(bancoContaRequest?.ispb))
            {
                return new RespostaViagemPagamento()
                {
                    Sucesso = false,
                    Mensagem = "Banco da conta do proprietário não encontrado.",
                    TipoOperacao = TipoOperacao.Erro,
                    StatusPagamento = StatusPagamento.NaoExecutado
                };
            }

            pixRequest.TipoTransferenciaPix = 0;
            pixRequest.InformacoesBeneficiario = new InformacoesBeneficiarioPixRequest()
            {
                BankAccountNumber = conta,
                BankBranchNumber = agencia,
                Ispb = bancoContaRequest.ispb,
                BankAccountType = ((ETipoContaDock)tipoContaDock).GetDescription(),
                BeneficiaryType = cpfCnpjDestino.Length > 11 ? "J" : "F",
                PayeeName = recebedorAutorizado == true
                    ? eventoPagamento.Viagem.PortadorMotorista.Nome
                    : eventoPagamento.Viagem.PortadorProprietario.Nome,
                NationalRegistration = cpfCnpjDestino
            };

            var retornoTarifas = await CalculaTarifasTransacao(eventoPagamento.Id, empresaLogada, idContaOrigemEmpresa);

            if (!retornoTarifas.Sucesso)
            {
                return new RespostaViagemPagamento()
                {
                    Sucesso = false,
                    Mensagem = $"Erro durante a transferência das tarifas. {retornoTarifas.Mensagem}",
                    TipoOperacao = TipoOperacao.Erro,
                    StatusPagamento = StatusPagamento.NaoExecutado
                };
            }

            return new RespostaViagemPagamento()
            {
                Sucesso = true
            };
        }

        private async Task<RespostaViagemPagamento> ValidarPixComChavePix(
            Domain.Models.PagamentoEvento.PagamentoEvento eventoPagamento,
            Domain.Models.Empresa.Empresa empresaLogada, CriarTransferenciaPixRequest pixRequest,
            int idContaOrigemEmpresa, string cpfCnpjDestino, string chavePix)
        {
            var lResultadoValidarChavePix = await _pixAppService
                .ValidarChave(idContaOrigemEmpresa, chavePix);

            if (lResultadoValidarChavePix.data == null)
            {
                return new RespostaViagemPagamento()
                {
                    Sucesso = false,
                    Mensagem = "Erro de serviço ao validar a chave Pix. Tente novamente mais tarde.",
                    TipoOperacao = TipoOperacao.Erro,
                    StatusPagamento = StatusPagamento.Erro
                };
            }

            var lRetornoTarifas = await CalculaTarifasTransacao(eventoPagamento.Id, empresaLogada,
                idContaOrigemEmpresa);

            if (!lRetornoTarifas.Sucesso)
            {
                return new RespostaViagemPagamento()
                {
                    Sucesso = false,
                    Mensagem = $"Erro durante a transferência das tarifas. {lRetornoTarifas.Mensagem}",
                    TipoOperacao = TipoOperacao.Erro,
                    StatusPagamento = StatusPagamento.Erro
                };
            }

            if (!lResultadoValidarChavePix.sucesso)
            {
                return new RespostaViagemPagamento()
                {
                    Sucesso = false,
                    Mensagem = lResultadoValidarChavePix.mensagem,
                    TipoOperacao = TipoOperacao.Erro,
                    StatusPagamento = StatusPagamento.NaoExecutado
                };
            }

            var lDataValidarChavePix = (ValidarChavePixBaaSResponse)lResultadoValidarChavePix.data;

            if (!lDataValidarChavePix.NationalRegistrationMask.Equals(MascaraCpfCnpjDestino(cpfCnpjDestino,
                    lDataValidarChavePix.NationalRegistrationMask)))
            {
                return new RespostaViagemPagamento()
                {
                    Sucesso = false,
                    Mensagem = $"Chave Pix não pertence à conta de destino informada.",
                    TipoOperacao = TipoOperacao.Erro,
                    StatusPagamento = StatusPagamento.NaoExecutado
                };
            }

            pixRequest.TipoTransferenciaPix = 1;
            pixRequest.InformacoesBeneficiario = Mapper.Map<InformacoesBeneficiarioPixRequest>(lDataValidarChavePix);
            pixRequest.ValorFinalPagamento = decimal.ToDouble(eventoPagamento.Valor);
            pixRequest.IdEndToEnd = lDataValidarChavePix.IdEndToEnd;

            return new RespostaViagemPagamento()
            {
                Sucesso = true
            };
        }

        private static string MascaraCpfCnpjDestino(string cpfCnpj, string nationalRegistrationMask)
        {
            return nationalRegistrationMask.Contains('*')
                ? Regex.Replace(cpfCnpj, @"^\d{3}(\d{3})(\d{3})\d{2}$", "***.$1.$2-**")
                : cpfCnpj;
        }

        private async Task<RespostaViagemPagamento> ValidarPixComAgenciaConta(
            Domain.Models.PagamentoEvento.PagamentoEvento eventoPagamento,
            Domain.Models.Empresa.Empresa empresaLogada, CriarTransferenciaPixRequest pixRequest,
            int idContaOrigemEmpresa, string cpfCnpjDestino, string agencia, string conta, bool? recebedorAutorizado)
        {
            var retornoAliasBankContaDestinoRequest = await _cartaoRepository.ConsultarContaAliasBankPorNumero(conta);
            if (retornoAliasBankContaDestinoRequest?.items == null ||
                !retornoAliasBankContaDestinoRequest.items.Any())
            {
                return new RespostaViagemPagamento()
                {
                    Sucesso = false,
                    Mensagem = $"Conta {conta} não encontrada.",
                    TipoOperacao = TipoOperacao.Erro,
                    StatusPagamento = StatusPagamento.NaoExecutado
                };
            }

            var aliasBankContaDestinoRequest = retornoAliasBankContaDestinoRequest.items.First();
            if (!aliasBankContaDestinoRequest.bankBranchNumber.Equals(agencia))
            {
                return new RespostaViagemPagamento()
                {
                    Sucesso = false,
                    Mensagem = $"Agência da conta {conta} difere da que foi informada ({agencia}).",
                    TipoOperacao = TipoOperacao.Erro,
                    StatusPagamento = StatusPagamento.NaoExecutado
                };
            }

            var contaPorIdAccount = await _cartaoRepository
                .ConsultarContasPorId(aliasBankContaDestinoRequest.idAccount.ToIntSafe());

            if (contaPorIdAccount?.NumeroReceitaFederal == null)
            {
                return new RespostaViagemPagamento()
                {
                    Sucesso = false,
                    Mensagem = $"Conta informada não foi encontrada.",
                    TipoOperacao = TipoOperacao.Erro,
                    StatusPagamento = StatusPagamento.NaoExecutado
                };
            }

            if (!contaPorIdAccount.NumeroReceitaFederal.EqualsIgnoreCase(cpfCnpjDestino))
            {
                return new RespostaViagemPagamento()
                {
                    Sucesso = false,
                    Mensagem = $"Conta informada não pertence ao recebedor informado.",
                    TipoOperacao = TipoOperacao.Erro,
                    StatusPagamento = StatusPagamento.NaoExecutado
                };
            }

            var bancoContaRequest = await _cartaoRepository
                .ConsultarBancoDock(aliasBankContaDestinoRequest.bankNumber);

            if (string.IsNullOrWhiteSpace(bancoContaRequest?.ispb))
            {
                return new RespostaViagemPagamento()
                {
                    Sucesso = false,
                    Mensagem = "Banco da conta de destino não encontrado.",
                    TipoOperacao = TipoOperacao.Erro,
                    StatusPagamento = StatusPagamento.NaoExecutado
                };
            }

            pixRequest.TipoTransferenciaPix = 0;
            pixRequest.InformacoesBeneficiario = new InformacoesBeneficiarioPixRequest()
            {
                BankAccountNumber = aliasBankContaDestinoRequest.bankAccountNumber,
                Ispb = bancoContaRequest.ispb,
                BankAccountType = aliasBankContaDestinoRequest.bankAccountType,
                BeneficiaryType = cpfCnpjDestino.Length > 11 ? "J" : "F",
                PayeeName = recebedorAutorizado == true
                    ? eventoPagamento.Viagem.NomeMotorista
                    : eventoPagamento.Viagem.PortadorProprietario.Nome,
                NationalRegistration = cpfCnpjDestino
            };

            var retornoTarifas = await CalculaTarifasTransacao(eventoPagamento.Id, empresaLogada, idContaOrigemEmpresa);

            if (!retornoTarifas.Sucesso)
            {
                return new RespostaViagemPagamento()
                {
                    Sucesso = false,
                    Mensagem = $"Erro durante a transferência das tarifas. {retornoTarifas.Mensagem}",
                    TipoOperacao = TipoOperacao.Erro,
                    StatusPagamento = StatusPagamento.NaoExecutado
                };
            }

            return new RespostaViagemPagamento()
            {
                Sucesso = true
            };
        }

        private async Task<RespostaViagemPagamento> FinalizarAlterandoTransacao(int idTransacao, string descricao,
            StatusPagamento status,
            bool sucesso, TipoOperacao tipoOperacao, string jsonRetorno = null, int? statusCode = null,
            DateTime? dataBaixa = null)
        {
            var alterarTransacaoRequest = new TransacaoAlterarStatusRequest()
            {
                Id = idTransacao,
                Descricao = descricao,
                Status = status,
                JsonRespostaDock = jsonRetorno,
                DataBaixa = dataBaixa != null ? dataBaixa.ToDateTimeSafe().AddHours(-3) : null,
                ResponseCodeDock = statusCode ?? 0,
                DataRetornoDock = jsonRetorno != null ? DateTime.Now : null
            };

            await _transacaoRegisterAppService.AtualizarTransacao(alterarTransacaoRequest);

            return new RespostaViagemPagamento()
            {
                Sucesso = sucesso,
                Mensagem = descricao,
                TipoOperacao = tipoOperacao,
                StatusPagamento = status
            };
        }

        public async Task<RespostaViagemPagamento> CalculaTarifasTransacao(int eventoPagamentoId,
            Domain.Models.Empresa.Empresa empresa, int idContaOrigem)
        {
            try
            {
                decimal valorTarifaBbc = 0;
                decimal valorTarifaPix = 0;

                var lTarifaPaga = _transacaoRegisterAppService.GetTransacoesByIdPagamentoEvento(eventoPagamentoId)
                    .Where(x => x.Tipo == Tipo.Tarifas && x.Status == StatusPagamento.Fechado).AsNoTracking();
                if (lTarifaPaga.Any())
                {
                    return new RespostaViagemPagamento()
                    {
                        Sucesso = true,
                        Mensagem = "Transferência da tarifa já realizada com sucesso.",
                        TipoOperacao = TipoOperacao.Notificacao
                    };
                }

                var eventoPagamento = _pagamentoEventoReadRepository
                                                                                        .Include(x => x.Transacao)
                                                                                        .Include(x => x.Viagem.PortadorProprietario)
                                                                                        .FirstOrDefault(x => x.Id == eventoPagamentoId);

                var lTransacaoMes = _pagamentoEventoReadRepository.Where(x =>
                    x.FormaPagamento == FormaPagamentoEvento.Pix && x.DataCadastro.Month == DateTime.Now.Month &&
                    x.EmpresaId == empresa.Id && x.DataCadastro.Year == DateTime.Now.Year).ToList();

                if (eventoPagamento == null)
                    throw new Exception("Não foi possível identificar o evento de pagamento.");

                var contaDestinoTarifa = _parametrosAppService.GetParametrosAsync(-1,
                    Domain.Models.Parametros.Parametros.TipoDoParametro.CodigoContaTransferenciaTarifaValorRetencao,
                    Domain.Models.Parametros.Parametros.TipoDoValor.Number).Result.Valor.ToInt();


                if (empresa.UtilizaTarifaEmpresa == 1 || empresa.GrupoEmpresaId == null)
                {
                    if (empresa.ValorTarifaBbc > 0 && empresa.ValorTarifaBbc != null)
                    {
                        valorTarifaBbc = eventoPagamento.Valor * empresa.ValorTarifaBbc.ToDecimalSafe() / 100;
                    }

                    if (lTransacaoMes.Count() > empresa.QtdMensalSemTaxaPix
                        && eventoPagamento.FormaPagamento == FormaPagamentoEvento.Pix)
                    {
                        valorTarifaPix = empresa.ValorTarifaPix.ToDecimalSafe();
                    }
                }
                else
                {
                    var lGrupoEmpresa = _grupoEmpresaAppService.ConsultarPorId(empresa.GrupoEmpresaId);

                    if (lGrupoEmpresa.percentualTarifaBbc > 0)
                    {
                        valorTarifaBbc = eventoPagamento.Valor * lGrupoEmpresa.percentualTarifaBbc.ToDecimalSafe() /
                                         100;
                    }

                    if (lTransacaoMes.Count > lGrupoEmpresa.qtdMensalSemTaxaPix
                        && eventoPagamento.FormaPagamento == FormaPagamentoEvento.Pix)
                    {
                        valorTarifaPix = lGrupoEmpresa.valorTarifaPix.ToDecimalSafe();
                    }
                }

                eventoPagamento.TarifaBbc = empresa.ValorTarifaBbc;
                eventoPagamento.ValorTarifaBbc = valorTarifaBbc;
                eventoPagamento.TarifaPix = empresa.ValorTarifaPix;
                eventoPagamento.ValorTarifaPix = valorTarifaPix;
                eventoPagamento.CobrancaTarifa = empresa.CobrancaTarifa;

                _pagamentoEventoWriteRepository.Update(eventoPagamento);
                _pagamentoEventoWriteRepository.SaveChanges();

                if (empresa.CobrancaTarifa == 1)
                {
                    var valorTarifasASerPagas =
                        Math.Round(valorTarifaBbc + valorTarifaPix, 2, MidpointRounding.AwayFromZero);

                    var transferenciaEmpresaParaBbc = await TransferenciaTarifaP2PDock(valorTarifasASerPagas,
                        eventoPagamento, idContaOrigem, contaDestinoTarifa, Tipo.Tarifas);

                    if (transferenciaEmpresaParaBbc.Sucesso)
                    {
                        return new RespostaViagemPagamento()
                        {
                            Sucesso = true,
                            Mensagem = transferenciaEmpresaParaBbc.Mensagem,
                            TipoOperacao = TipoOperacao.Pendencia
                        };
                    }

                    return new RespostaViagemPagamento()
                    {
                        Sucesso = false,
                        Mensagem = transferenciaEmpresaParaBbc.Mensagem,
                        TipoOperacao = TipoOperacao.Erro
                    };
                }

                return new RespostaViagemPagamento()
                {
                    Sucesso = true,
                    Mensagem = "Transferência da tarifa realizado com sucesso",
                    TipoOperacao = TipoOperacao.Notificacao
                };
            }
            catch (Exception e)
            {
                return new RespostaViagemPagamento()
                {
                    Sucesso = false,
                    Mensagem = e.Message,
                    TipoOperacao = TipoOperacao.Erro
                };
            }
        }

        private async Task<Domain.Models.Transacao.Transacao> GetTransacaoExistenteP2P(
            Domain.Models.PagamentoEvento.PagamentoEvento eventoPagamento, int contaDestinoId, int contaOrigemId,
            FormaPagamentoEvento formaPagamento, Tipo? tipo)
        {
            return await _transacaoReadRepository.GetByEventoFormaOrigemDestinoTipo(eventoPagamento.Id,
                formaPagamento, contaOrigemId, contaDestinoId, tipo);
        }
        
        private async Task<Domain.Models.Transacao.Transacao> GetTransacaoExistenteP2PRetencao(
            Domain.Models.PagamentoEvento.PagamentoEvento eventoPagamento, int contaOrigemId,
            FormaPagamentoEvento formaPagamento, Tipo? tipo)
        {
            return await _transacaoReadRepository.GetByEventoFormaOrigemTipo(eventoPagamento.Id,
                formaPagamento, contaOrigemId, tipo);
        }

        private async Task<Domain.Models.Transacao.Transacao> GetTransacaoExistentePix(
            Domain.Models.PagamentoEvento.PagamentoEvento eventoPagamento, int contaOrigemId,
            FormaPagamentoEvento formaPagamento, Tipo tipo)
        {
            return await _transacaoReadRepository.GetByEventoFormaOrigemTipo(eventoPagamento.Id,
                formaPagamento, contaOrigemId, tipo);
        }

        //Calcula o valor de transferência automática a ser pago com base no tipo de pagamento
        public async Task<decimal> ColetarValorTransferenciaAutomatica(
            Domain.Models.PagamentoEvento.PagamentoEvento eventoPagamento)
        {
            return await ColetarValorTransferenciaAutomatica(
                eventoPagamento.Viagem.PortadorProprietario.CpfCnpj,
                eventoPagamento.Tipo ?? Tipo.Adiantamento,
                eventoPagamento.Valor);
        }
        
        private async Task<decimal> ColetarValorTransferenciaAutomatica(
            string cnpjProprietario, Tipo tipo, decimal valor )
        {
            var lPercentual = await _percentualTransferenciaReadRepository
                .GetByProprietarioCpfCnpj(cnpjProprietario);

            if (lPercentual == null)
            {
                return 0;
            }

            decimal lValorTransferenciaAutomatica = 0;

            if (lPercentual.ParaTodosMotoristas != 0)
            {
                switch (tipo)
                {
                    case Tipo.Saldo:
                        lValorTransferenciaAutomatica =
                            Math.Round(valor * ((lPercentual.Saldo ?? 0) / 100), 2);
                        break;
                    case Tipo.Adiantamento:
                        lValorTransferenciaAutomatica =
                            Math.Round(valor * ((lPercentual.Adiantamento ?? 0) / 100), 2);
                        break;
                }
            }
            else
            {
                var lPercentualMotorista = await _percentualTransferenciaPortadorReadRepository
                    .GetAtivoByMotoristaCpfCnpjAndPercentualId(cnpjProprietario,
                        lPercentual.Id);

                if (lPercentualMotorista == null)
                {
                    return 0;
                }

                switch (tipo)
                {
                    case Tipo.Saldo:
                        lValorTransferenciaAutomatica =
                            Math.Round(valor * (lPercentualMotorista.Saldo / 100), 2);
                        break;
                    case Tipo.Adiantamento:
                        lValorTransferenciaAutomatica =
                            Math.Round(valor * (lPercentualMotorista.Adiantamento / 100), 2);
                        break;
                }
            }

            return lValorTransferenciaAutomatica;
        }
        

        //Checa duplicidade de pagamento
        public async Task<RespostaViagemPagamento> ValidarPagamentoDuplicado(
            Domain.Models.PagamentoEvento.PagamentoEvento eventoPagamento,
            int contaOrigemId, int contaDestinoId, string operacao)
        {
            var lPagamentoEvento =
                _pagamentoEventoReadRepository
                    .Where(x => x.PagamentoExternoId.ToInt() == eventoPagamento.PagamentoExternoId &&
                                x.ViagemId == eventoPagamento.ViagemId).ToList();

            var idsPagamentoEvento = lPagamentoEvento.Where(p => p.Id != eventoPagamento.Id).Select(x => x.Id).ToList();

            var lTransacao = _transacaoReadRepository
                .Where(x => idsPagamentoEvento.Contains(x.IdPagamentoEvento) && x.Status == StatusPagamento.Fechado)
                .ToList();

            foreach (var transacao in lTransacao)
            {
                //hotfix gerada para que se uma transação não ocorra corretamente, cancele as transações anteriores e gere novamente as mesmas em um novo evento.
                if (transacao.Status == StatusPagamento.Fechado)
                {
                    var eventoCancelamento = lPagamentoEvento.Where(x => x.Id == transacao.IdPagamentoEvento)
                        .FirstOrDefault();
                    await CancelarPagamentosP2PPorDuplicidade(eventoCancelamento);
                }

                if (transacao.Status == StatusPagamento.Fechado)
                {
                    return new RespostaViagemPagamento()
                    {
                        Sucesso = true,
                        Mensagem =
                            "Pagamento para o " + operacao + " do evento " + eventoPagamento.Id + " já efetuado!",
                        TipoOperacao = TipoOperacao.Notificacao,
                        StatusPagamento = StatusPagamento.Fechado
                    };
                }
            }

            return new RespostaViagemPagamento
            {
                Sucesso = false
            };
        }

        private async Task<RespostaViagemPagamento> TransferenciaPagamentoEventoP2PDock(
            Domain.Models.PagamentoEvento.PagamentoEvento eventoPagamento,
            int contaOrigemId, int contaDestinoId, Tipo? tipo, bool proprietarioParaMotorista = false)
        {
            var lTransacaoExistente = await GetTransacaoExistenteP2P(eventoPagamento, contaDestinoId, contaOrigemId,
                FormaPagamentoEvento.Deposito, tipo);

            var log = LogManager.GetCurrentClassLogger();

            var lRetornoTransferencia = new TransferenciaEntreContaResp
            {
                Sucesso = false
            };

            var lAlteraTransacao = new TransacaoAlterarStatusRequest();
            string lMensagem;

            try
            {
                var lValorTransacao = proprietarioParaMotorista
                    ? eventoPagamento.ValorTransferenciaMotorista.ToStringSafe()
                    : eventoPagamento.Valor.ToStringSafe();

                if (eventoPagamento.Id > 0 && contaOrigemId > 0 && contaDestinoId > 0 &&
                    !lValorTransacao.IsNullOrWhiteSpace())
                {
                    var lTransferencia = new Transferencia
                    {
                        amount = Decimal.Parse(lValorTransacao, NumberStyles.Any, new CultureInfo("pt-BR")),
                        destinationAccount = contaDestinoId.ToInt(),
                        originalAccount = contaOrigemId.ToInt(),
                        description = JsonConvert.SerializeObject(new
                        {
                            branchNumber = eventoPagamento.Viagem.FilialId,
                            nationalRegistration = eventoPagamento.Viagem.PortadorProprietario.CpfCnpj,
                            description = "Número de Referência: " +
                                          $"{eventoPagamento.Viagem.ViagemExternoId}/{eventoPagamento.Tipo.GetSigla()}",
                            protocol = $"{eventoPagamento.Viagem.ViagemExternoId}/{eventoPagamento.Tipo.GetSigla()}"
                        })
                    };

                    var lTransacaoReq = new TransacaoRequest()
                    {
                        IdPagamentoEvento = eventoPagamento.Id,
                        Valor = lTransferencia.amount,
                        Destino = lTransferencia.destinationAccount,
                        Origem = lTransferencia.originalAccount,
                        Descricao = "Transferencia P2P Sendo Criada",
                        Tipo = tipo,
                        FormaPagamento = FormaPagamentoEvento.Deposito,
                        Status = StatusPagamento.Processando,
                        DataCadastro = DateTime.Now,
                        Description = lTransferencia.description,
                        JsonEnvioDock = JsonConvert.SerializeObject(lTransferencia)
                    };

                    if (lTransacaoExistente != null)
                        lTransacaoReq.Id = lTransacaoExistente.Id;

                    var lTransacaoId = await _transacaoRegisterAppService.RegistrarTransacao(lTransacaoReq);

                    log.Info("Json de TransferenciaPagamentoEventoP2P: " + JsonConvert.SerializeObject(lTransferencia));

                    lAlteraTransacao.Id = lTransacaoId.id.ToIntSafe();
                    lRetornoTransferencia =
                        await _transferenciaRepository.RealizaTransferenciaEntreContas(lTransferencia);

                    var statusCode = await QualificarPagamentoFreteP2P(lRetornoTransferencia,
                        $"{eventoPagamento.Viagem.ViagemExternoId}/{eventoPagamento.Tipo.GetSigla()}");

                    lAlteraTransacao.Qualificado = statusCode == 204 ? 1 : 0;
                    lAlteraTransacao.JsonRespostaDock = lRetornoTransferencia.RetornoJson;
                    lAlteraTransacao.ResponseCodeDock = lRetornoTransferencia.Code.ToInt();
                    lAlteraTransacao.DataRetornoDock = DateTime.Now;
                }
            }
            catch (Exception e)
            {
                lMensagem = proprietarioParaMotorista
                    ? "Falha ao transferir entre proprietário e motorista." + e.Message
                    : "Pagamento para proprietário não efetuado. " + e.Message;

                log.Info(lMensagem);

                lAlteraTransacao.Descricao = lMensagem;
                lAlteraTransacao.Status = StatusPagamento.Erro;
                await _transacaoRegisterAppService.AtualizarTransacao(lAlteraTransacao);

                return new RespostaViagemPagamento()
                {
                    Sucesso = false,
                    Mensagem = lMensagem,
                    TipoOperacao = TipoOperacao.Erro
                };
            }

            if (lRetornoTransferencia.Sucesso)
            {
                var lEventoTipo = eventoPagamento.Tipo != null
                    ? eventoPagamento.Tipo.Value.ToStringSafe()
                    : "Sem tipo definido";

                lMensagem = proprietarioParaMotorista
                    ? $"Transferência ao motorista no valor R$ {eventoPagamento.ValorTransferenciaMotorista} realizada."
                    : $"Pagamento Id {eventoPagamento.Id}, {lEventoTipo}, de valor: R$ {eventoPagamento.Valor} realizado.";

                lAlteraTransacao.Descricao = "Transferencia P2P criada com sucesso";
                lAlteraTransacao.Status = StatusPagamento.Fechado;
                lAlteraTransacao.DataBaixa = DateTime.Now;

                await _transacaoRegisterAppService.AtualizarTransacao(lAlteraTransacao);
                return new RespostaViagemPagamento()
                {
                    Sucesso = true,
                    Mensagem = lMensagem,
                    TipoOperacao = TipoOperacao.Notificacao
                };
            }

            lAlteraTransacao.Descricao = "Pagamento não efetuado. Motivo: " + lRetornoTransferencia.message;
            lAlteraTransacao.Status = StatusPagamento.Erro;
            await _transacaoRegisterAppService.AtualizarTransacao(lAlteraTransacao);
            log.Info("Pagamento não efetuado. Motivo: " + lRetornoTransferencia.message);

            #region Mensagem de retorno da integração da viagem

            var mensagemDockTraduzida = await _cloudTranslationService.Traduzir(lRetornoTransferencia.message);

            var mensagemRetorno =
                await RetornarMensagemDockIntegracaoViagem(mensagemDockTraduzida, lRetornoTransferencia);

            #endregion

            lMensagem = proprietarioParaMotorista
                ? "Transferência entre proprietário e motorista não efetuada. Erro DOCK: " + mensagemRetorno
                : "Pagamento para o proprietário não efetuado. Erro DOCK: " + mensagemRetorno;

            return new RespostaViagemPagamento()
            {
                Sucesso = false,
                Mensagem = lMensagem,
                TipoOperacao = TipoOperacao.Notificacao
            };
        }

        private async Task<RespostaViagemPagamento> TransferenciaPagamentoEventoP2PComRetencaoDock(
            Domain.Models.PagamentoEvento.PagamentoEvento eventoPagamento,
            int contaOrigemId, int contaDestinoId, Tipo? tipo, string documentoContaRetencao, bool proprietarioParaMotorista = false)
        {
            var log = LogManager.GetCurrentClassLogger();
            
            var lTransacaoExistente = await GetTransacaoExistenteP2PRetencao(eventoPagamento, contaOrigemId,
                (FormaPagamentoEvento)6, tipo); // RetencaoAntecipacao = 6
            if (lTransacaoExistente != null && lTransacaoExistente.Realizada())
            {
                log.Info("Transação de retenção já realizada. Id:" + lTransacaoExistente.Id);

                return new RespostaViagemPagamento()
                {
                    Sucesso = true,
                    Mensagem = lTransacaoExistente.Descricao
                };
            }
            
            var lRetornoTransferencia = new TransferenciaEntreContaResp
            {
                Sucesso = false
            };

            var lAlteraTransacao = new TransacaoAlterarStatusRequest();
            string lMensagem;

            try
            {
                var lValorTransacao = proprietarioParaMotorista
                    ? eventoPagamento.ValorTransferenciaMotorista.ToStringSafe()
                    : eventoPagamento.Valor.ToStringSafe();

                if (eventoPagamento.Id > 0 && contaOrigemId > 0 && contaDestinoId >= 0 &&
                    !lValorTransacao.IsNullOrWhiteSpace())
                {
                    var lTransferencia = new Transferencia
                    {
                        amount = Decimal.Parse(lValorTransacao, NumberStyles.Any, new CultureInfo("pt-BR")),
                        destinationAccount = contaDestinoId.ToInt(),
                        originalAccount = contaOrigemId.ToInt(),
                        description = JsonConvert.SerializeObject(new
                        {
                            branchNumber = eventoPagamento.Viagem.FilialId,
                            nationalRegistration = documentoContaRetencao,
                            description = "Número de Referência: " +
                                          $"{eventoPagamento.Viagem.ViagemExternoId}/{eventoPagamento.Tipo.GetSigla()}",
                            protocol = $"{eventoPagamento.Viagem.ViagemExternoId}/{eventoPagamento.Tipo.GetSigla()}"
                        })
                    };

                    var lTransacaoReq = new TransacaoRequest()
                    {
                        IdPagamentoEvento = eventoPagamento.Id,
                        Valor = lTransferencia.amount,
                        Destino = lTransferencia.destinationAccount,
                        Origem = lTransferencia.originalAccount,
                        Descricao = "Transferencia P2P Retenção Sendo Criada",
                        Tipo = tipo,
                        FormaPagamento = (FormaPagamentoEvento)6, // RetencaoAntecipacao = 6
                        Status = StatusPagamento.Processando,
                        DataCadastro = DateTime.Now,
                        Description = lTransferencia.description,
                        JsonEnvioDock = JsonConvert.SerializeObject(lTransferencia)
                    };

                    if (lTransacaoExistente != null)
                        lTransacaoReq.Id = lTransacaoExistente.Id;

                    var lTransacaoId = await _transacaoRegisterAppService.RegistrarTransacao(lTransacaoReq);

                    log.Info("Json de TransferenciaPagamentoEventoP2PComRetencao: " + JsonConvert.SerializeObject(lTransferencia));

                    lAlteraTransacao.Id = lTransacaoId.id.ToIntSafe();
                    lRetornoTransferencia = await _transferenciaRepository.RealizaTransferenciaEntreContas(lTransferencia);

                    var statusCode = await QualificarPagamentoFreteP2P(lRetornoTransferencia,
                        $"{eventoPagamento.Viagem.ViagemExternoId}/{eventoPagamento.Tipo.GetSigla()}");

                    lAlteraTransacao.Qualificado = statusCode == 204 ? 1 : 0;
                    lAlteraTransacao.JsonRespostaDock = lRetornoTransferencia.RetornoJson;
                    lAlteraTransacao.ResponseCodeDock = lRetornoTransferencia.Code.ToInt();
                    lAlteraTransacao.DataRetornoDock = DateTime.Now;

                }
            }
            catch (Exception e)
            {
                log.Error(e, "Erro ao processar transferência P2P com retenção");
                lAlteraTransacao.Descricao = "Erro ao processar transferência P2P com retenção: " + e.Message;
                lAlteraTransacao.Status = StatusPagamento.Erro;
                await _transacaoRegisterAppService.AtualizarTransacao(lAlteraTransacao);

                return new RespostaViagemPagamento()
                {
                    Sucesso = false,
                    Mensagem = "Erro ao processar transferência P2P com retenção: " + e.Message,
                    TipoOperacao = TipoOperacao.Erro
                };
            }

            if (lRetornoTransferencia.Sucesso)
            {
                lMensagem = proprietarioParaMotorista
                    ? "Transferência entre proprietário e motorista (retenção) efetuada com sucesso."
                    : "Pagamento para conta de retenção efetuado com sucesso.";

                lAlteraTransacao.Descricao = "Transferencia P2P retenção criada com sucesso";
                lAlteraTransacao.Status = StatusPagamento.Fechado;
                lAlteraTransacao.DataBaixa = DateTime.Now;

                await _transacaoRegisterAppService.AtualizarTransacao(lAlteraTransacao);
                return new RespostaViagemPagamento()
                {
                    Sucesso = true,
                    Mensagem = lMensagem,
                    TipoOperacao = TipoOperacao.Notificacao
                };
            }

            lAlteraTransacao.Descricao = "Pagamento retenção não efetuado. Motivo: " + lRetornoTransferencia.message;
            lAlteraTransacao.Status = StatusPagamento.Erro;
            await _transacaoRegisterAppService.AtualizarTransacao(lAlteraTransacao);
            log.Info("Pagamento retenção não efetuado. Motivo: " + lRetornoTransferencia.message);

            #region Mensagem de retorno da integração da viagem

            var mensagemDockTraduzida = await _cloudTranslationService.Traduzir(lRetornoTransferencia.message);

            var mensagemRetorno = await RetornarMensagemDockIntegracaoViagem(mensagemDockTraduzida, lRetornoTransferencia);

            #endregion

            lMensagem = proprietarioParaMotorista
                ? "Transferência entre proprietário e motorista (retenção) não efetuada. Erro DOCK: " + mensagemRetorno
                : "Pagamento para conta de retenção não efetuado. Erro DOCK: " + mensagemRetorno;

            return new RespostaViagemPagamento()
            {
                Sucesso = false,
                Mensagem = lMensagem,
                TipoOperacao = TipoOperacao.Notificacao
            };
        }

        private async Task<RespostaViagemPagamento> TransferenciaTarifaP2PDock(decimal valorTarifa,
            Domain.Models.PagamentoEvento.PagamentoEvento eventoPagamento, int contaOrigemId, int contaDestinoId,
            Tipo? tipo)
        {
            var log = LogManager.GetCurrentClassLogger();
            var lAlteraTransacao = new TransacaoAlterarStatusRequest();

            try
            {
                var lTransferencia = new Transferencia
                {
                    amount = valorTarifa,
                    destinationAccount = contaDestinoId.ToInt(),
                    originalAccount = contaOrigemId.ToInt(),
                    description = JsonConvert.SerializeObject(new
                    {
                        branchNumber = eventoPagamento.Viagem.FilialId,
                        nationalRegistration = eventoPagamento.RecebedorAutorizado.IsNullOrWhiteSpace()
                            ? eventoPagamento.Viagem.PortadorProprietario.CpfCnpj
                            : eventoPagamento.RecebedorAutorizado,
                        description = "Número de Referência: " +
                                      $"{eventoPagamento.Viagem.ViagemExternoId}/{eventoPagamento.Tipo.GetSigla()}",
                        protocol = $"{eventoPagamento.Viagem.ViagemExternoId}/{eventoPagamento.Tipo.GetSigla()}"
                    })
                };

                var lTransacaoReq = new TransacaoRequest()
                {
                    IdPagamentoEvento = eventoPagamento.Id,
                    Valor = lTransferencia.amount,
                    Destino = lTransferencia.destinationAccount,
                    Origem = lTransferencia.originalAccount,
                    Descricao = "Transferência de Tarifas P2P em processamento",
                    Tipo = tipo,
                    FormaPagamento = FormaPagamentoEvento.Deposito,
                    Status = StatusPagamento.Processando,
                    DataCadastro = DateTime.Now,
                    Description = lTransferencia.description,
                    JsonEnvioDock = JsonConvert.SerializeObject(lTransferencia),
                    DataRetornoDock = DateTime.Now
                };

                var lTransacaoExistente = await GetTransacaoExistenteP2P(eventoPagamento, contaDestinoId, contaOrigemId,
                    FormaPagamentoEvento.Deposito, Tipo.Tarifas);

                if (lTransacaoExistente != null)
                    lTransacaoReq.Id = lTransacaoExistente.Id;

                lAlteraTransacao.Id =
                    (await _transacaoRegisterAppService.RegistrarTransacao(lTransacaoReq)).id.ToIntSafe();

                var lRetornoTransferencia =
                    await _transferenciaRepository.RealizaTransferenciaEntreContas(lTransferencia);

                log.Info("Json de TransferenciaTarifaP2P: " + JsonConvert.SerializeObject(lTransferencia));

                var statusCode = await QualificarPagamentoFreteP2P(lRetornoTransferencia,
                    $"{eventoPagamento.Viagem.ViagemExternoId}/{eventoPagamento.Tipo.GetSigla()}");

                lAlteraTransacao.Qualificado = statusCode == 204 ? 1 : 0;
                lAlteraTransacao.JsonRespostaDock = lRetornoTransferencia.RetornoJson;
                lAlteraTransacao.ResponseCodeDock = lRetornoTransferencia.Code.ToInt();
                lAlteraTransacao.DataRetornoDock = DateTime.Now;
                if (lRetornoTransferencia.Sucesso)
                {
                    lAlteraTransacao.Descricao = "Transferência de Tarifas P2P efetuada com sucesso";
                    lAlteraTransacao.Status = StatusPagamento.Fechado;
                    lAlteraTransacao.DataBaixa = DateTime.Now;
                }
                else
                {
                    lAlteraTransacao.Descricao = "Transferência de Tarifas P2P não efetuada. Motivo: " +
                                                 lRetornoTransferencia.message;
                    lAlteraTransacao.Status = StatusPagamento.Erro;
                }

                await _transacaoRegisterAppService.AtualizarTransacao(lAlteraTransacao);

                return new RespostaViagemPagamento()
                {
                    Sucesso = lRetornoTransferencia.Sucesso,
                    Mensagem = lRetornoTransferencia.Sucesso
                        ? $"Tarifa debitada com sucesso. {lAlteraTransacao.Descricao}"
                        : $"Erro ao debitar tarifa. {lAlteraTransacao.Descricao}",
                    TipoOperacao = lAlteraTransacao.Status == StatusPagamento.Erro
                        ? TipoOperacao.Erro
                        : TipoOperacao.Notificacao
                };
            }
            catch (Exception e)
            {
                log.Info($"Erro ao debitar tarifa do pagamento evento cód: {eventoPagamento.Id} => {e.Message}");

                lAlteraTransacao.Status = StatusPagamento.Erro;
                await _transacaoRegisterAppService.AtualizarTransacao(lAlteraTransacao);

                return new RespostaViagemPagamento()
                {
                    Sucesso = false,
                    Mensagem = $"Erro ao debitar tarifa do pagamento evento cód: {eventoPagamento.Id} => {e.Message}",
                    TipoOperacao = TipoOperacao.Erro
                };
            }
        }

        //Metodo para salvar notificações referentes ao pagamento evento
        public async Task<Notificacao> RegistrarNotificacaoPagamento(string notificacaoPagamento, int idPagamentoEvento)
        {
            try
            {
                new LogHelper().LogOperationStart("RegistrarNotificacaoPagamento");
                var commandNotificacao = new NotificacaoSalvarComRetornoCommand()
                {
                    Descricao = notificacaoPagamento,
                    PagamentoEventoId = idPagamentoEvento
                };

                var retornoNotificacao = await Engine.CommandBus
                    .SendCommandAsync<Notificacao>(commandNotificacao);

                return retornoNotificacao;
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar RegistrarNotificacaoPagamento");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("RegistrarNotificacaoPagamento");
            }
        }

        //Metodo para atualizar situacao do Pagamento Evento
        public async Task<Domain.Models.PagamentoEvento.PagamentoEvento> RegistrarPendenciaPagamento(
            int pagamentoEventoId, string pendenciaPagamento,
            StatusPagamento status, Tipo? tipoEvento, decimal? valorCancelamentoReq, DateTime? dataSolicitacao,
            bool contador = false, bool cancelamento = false, bool reenvioAutomatico = false)
        {
            var commandPendenciaPagamento = new PagamentoEventoSalvarComRetornoCommand()
            {
                Id = pagamentoEventoId,
                MotivoPendencia = pendenciaPagamento
            };

            var newPagamentoEvento = _pagamentoEventoReadRepository
                .Include(x => x.Transacao)
                .FirstOrDefault(x => x.Id == pagamentoEventoId);

            var contReenvio = 0;

            if (newPagamentoEvento?.ContadorReenvio != null)
                contReenvio = newPagamentoEvento.ContadorReenvio ?? 0;

            if (cancelamento)
            {
                commandPendenciaPagamento.Tipo = tipoEvento;
                commandPendenciaPagamento.ValorCancelamento = valorCancelamentoReq;
                commandPendenciaPagamento.DataSolicitacaoCancelamento = dataSolicitacao;

                if (status == StatusPagamento.Cancelado)
                {
                    commandPendenciaPagamento.Status = StatusPagamento.Cancelado;
                    commandPendenciaPagamento.DataCancelamento = DateTime.Now;
                }
                else
                {
                    var numeroTentativas = (await _parametrosAppService.GetParametrosAsync(-1,
                        Domain.Models.Parametros.Parametros.TipoDoParametro
                            .LimiteMaximoRetentativaCancelamentoPagamentoFrete,
                        Domain.Models.Parametros.Parametros.TipoDoValor.Number)).Valor.ToInt();

                    if (contReenvio == numeroTentativas)
                    {
                        commandPendenciaPagamento.MotivoPendencia =
                            "Número de tentativas atingido! Entrar em contato com contratado.";
                    }

                    commandPendenciaPagamento.ContadorReenvio = contReenvio + 1;
                }
            }

            if (contador && (status == StatusPagamento.Erro || status == StatusPagamento.Processando))
            {
                status = reenvioAutomatico ? StatusPagamento.Processando : StatusPagamento.Pendente;
                commandPendenciaPagamento.ContadorReenvio = contReenvio + 1;
            }
            else
                commandPendenciaPagamento.ContadorReenvio = contReenvio;

            if (status == StatusPagamento.Fechado)
                commandPendenciaPagamento.ContadorReenvio = 0;

            commandPendenciaPagamento.Status = status;

            var retornoPendenciaPagamento = await Engine.CommandBus
                .SendCommandAsync<Domain.Models.PagamentoEvento.PagamentoEvento>(commandPendenciaPagamento);

            //Incluir as transações para o retorno
            retornoPendenciaPagamento.Transacao = newPagamentoEvento?.Transacao;

            return retornoPendenciaPagamento;
        }

        //Localiza ID conta BBC
        private async Task<RespPadrao> LocalizarContaAliasBank(int contaDestinoId)
        {
            return await ViagemUtils.LocalizarContaAliasBank(contaDestinoId, _cartaoRepository);
        }

        //Verificador de saldo de conta origem
        public async Task<RespPadrao> VerificarSaldoContaOrigem(int idContaOrigem, decimal valorTransacao)
        {
            try
            {
                var lSaldoResponse = await _cartaoRepository.ConsultarSaldo(idContaOrigem.ToString());

                if (lSaldoResponse == null)
                {
                    return new RespPadrao
                    {
                        sucesso = false,
                        mensagem = "Erro ao avaliar saldo de conta " + idContaOrigem
                    };
                }
                
                if (lSaldoResponse.saldoDisponivelGlobal > 0)
                {
                    if (lSaldoResponse.saldoDisponivelGlobal >= valorTransacao)
                    {
                        return new RespPadrao
                        {
                            sucesso = true,
                            mensagem = "Conta ID: " + idContaOrigem + " Saldo disponivel para pagamento"
                        };
                    }

                    return new RespPadrao
                    {
                        sucesso = false,
                        mensagem = "Conta ID: " + idContaOrigem + " Insuficiência de fundos"
                    };
                }

                return new RespPadrao
                {
                    sucesso = false,
                    mensagem = "Erro ao avaliar saldo de conta " + idContaOrigem
                };
            }
            catch (Exception e)
            {
                return new RespPadrao
                {
                    sucesso = false,
                    mensagem = e.Message
                };
            }
        }

        private async Task<Domain.Models.Portador.Portador> CadastrarPortador(string cpfcnpj, bool validarDock = true)
        {
            try
            {
                new LogHelper().LogOperationStart("CadastrarPortador");
                var lLog = LogManager.GetCurrentClassLogger();

                var lPortadorId = (await _portadorReadRepository.GetIdByCpfCnpj(cpfcnpj)).ToIntSafe();

                if (lPortadorId != 0)
                    throw new InvalidOperationException("Não é possível cadastrar um Portador que já existe.");

                lLog.Info($"Portador não cadastrado para CPF/CNPJ: {cpfcnpj}.");

                var nomePortador = "Cadastro Automatico";

                if (validarDock)
                {
                    var individuoPortador = await _usuarioExternalRepository.ConsultaPortadorConductorAsync(cpfcnpj, 1);

                    if (individuoPortador == null) return new Domain.Models.Portador.Portador();

                    nomePortador = individuoPortador.content != null
                        ? individuoPortador.content.Select(x => x.nome).FirstOrDefault()?.ToString()
                        : individuoPortador.items.Select(x => x.name).FirstOrDefault()?.ToString();
                }

                var portador = new PortadorRequest();
                var cidade = await _cidadeReadRepository.FirstOrDefaultAsync();
                portador.CpfCnpj = cpfcnpj;
                portador.Bairro = "Bairro Padrão";
                portador.Nome = nomePortador;
                portador.EmpresaId = User.EmpresaId;
                portador.Visibilidade = EVisibilidadePortador.Transportador;
                portador.CidadeId = cidade.Id;
                portador.EstadoId = cidade.EstadoId;
                portador.TipoPessoa = cpfcnpj.Length > 11 ? 2 : 1;

                var command = Mapper.Map<PortadorSalvarComRetornoCommand>(portador);
                var retorno = Engine.CommandBus.SendCommand<Domain.Models.Portador.Portador>(command);

                lLog.Info($"Portador Cadastrado Automaticamente para CPF/CNPJ {cpfcnpj} com Id {retorno.Id}.");

                return retorno;
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar CadastrarPortador");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("CadastrarPortador");
            }
        }

        public List<int> StatusConta()
        {
            try
            {
                new LogHelper().LogOperationStart("StatusConta");
                return new List<int>()
                {
                    0,
                    200,
                    10,
                    109
                };
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar StatusConta");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("StatusConta");
            }
        }

        #region Tarefas

        public async Task ServiceCancelarPagamentoEventos()
        {
            var lLog = LogManager.GetCurrentClassLogger();
            try
            {
                var lLimiteTentativasCancelamento = await _parametrosReadRepository.GetParametrosAsync(-1,
                    Domain.Models.Parametros.Parametros.TipoDoParametro
                        .LimiteMaximoRetentativaCancelamentoPagamentoFrete,
                    Domain.Models.Parametros.Parametros.TipoDoValor.Number);

                if (lLimiteTentativasCancelamento?.Valor == null)
                {
                    lLog.Error(
                        "BAT_PGTO_02 ERRO: Parâmetro LimiteMaximoRetentativaCancelamentoPagamentoFrete inexistente.");
                    return;
                }

                var lNumeroTentativas = lLimiteTentativasCancelamento.Valor.ToIntSafe();

                var lPagamentoEventos = await _pagamentoEventoReadRepository
                    .Include(x => x.Viagem)
                    .Where(x => x.Tipo == Tipo.Cancelamento
                                && x.DataSolicitacaoCancelamento.HasValue
                                && (x.ContadorReenvio <= lNumeroTentativas || x.ContadorReenvio == null)
                                && x.DataCancelamento.Is(null)
                                && x.Status != StatusPagamento.Cancelado)
                    .ToListAsync();

                foreach (var lPagamentoEvento in lPagamentoEventos)
                {
                    if (lPagamentoEvento.Viagem == null) continue;

                    var lCancelamentoRequest = new CancelamentoEventoViagemRequest()
                    {
                        PagamentoExternoId = lPagamentoEvento.PagamentoExternoId.ToIntSafe(),
                        Valor = lPagamentoEvento.ValorCancelamento.ToDecimalSafe(),
                        DataRequisicao = lPagamentoEvento.DataCadastro.Date,
                        ViagemExternoId = lPagamentoEvento.Viagem.ViagemExternoId.ToIntSafe(),
                        Motivo = lPagamentoEvento.MotivoPendencia
                    };

                    var lCancelarResp = await CancelarEventoViagem(lCancelamentoRequest,
                        lPagamentoEvento.EmpresaId ?? 0);

                    if (!lCancelarResp.Sucesso) lLog.Error("BAT_PGTO_02 ERRO: " + lCancelarResp.Mensagem);
                }
            }
            catch (Exception e)
            {
                lLog.Error(e, "BAT_PGTO_02 ERRO");
            }
        }

        #endregion

        #region Consulta CartoesIP

        public async Task<List<ConsultarPagamentoDiaResponse>> ConsultarPagamentosDia(DateTime dtInicio, DateTime dtFim,
            int empresaId, bool incluiTarifa = false)
        {
            var idEmpresa = (await _empresaReadRepository.GetByIdAsync(User.EmpresaId))?.Id ?? empresaId;

            var pagamentosEvento = _pagamentoEventoReadRepository.AsNoTracking().Where(p =>
                    p.Empresa.Id == empresaId &&
                    p.DataBaixa >= dtInicio && p.DataBaixa <= dtFim &&
                    p.Status != StatusPagamento.Cancelado &&
                    (incluiTarifa == false ? p.Tipo != Tipo.Tarifas : true)).Select(x => new PagamentosDia
                {
                    Data = x.DataBaixa.ToDateTime(),
                    Valor = x.Valor
                }).GroupBy(x => x.Data.Date)
                .Select(t => new ConsultarPagamentoDiaResponse()
                {
                    Dia = t.Key,
                    Valor = t.Sum(x => x.Valor)
                }).ToList();

            var pagamentos = _pagamentosReadRepository.AsNoTracking().Where(p =>
                    p.Empresa.Id == empresaId &&
                    p.DataBaixa >= dtInicio && p.DataBaixa <= dtFim &&
                    p.Status != Status.Cancelado &&
                    (incluiTarifa == false ? p.Tipo != Tipo.Tarifas : true)).Select(x => new PagamentosDia
                {
                    Data = x.DataBaixa.ToDateTime(),
                    Valor = x.Valor
                }).GroupBy(x => x.Data.Date)
                .Select(t => new ConsultarPagamentoDiaResponse()
                {
                    Dia = t.Key,
                    Valor = t.Sum(x => x.Valor)
                });

            // Unindo os pagamentos das duas tabelas e agrupando por dia
            var pagamentosAgrupados = pagamentosEvento
                .Union(pagamentos.ToList())
                .GroupBy(x => x.Dia.Date)
                .Select(t => new ConsultarPagamentoDiaResponse()
                {
                    Dia = t.Key,
                    Valor = t.Sum(x => x.Valor)
                });

            return pagamentosAgrupados.ToList();
        }

        private TotalizadorItem GetTotalizadorItemFromQuery(
            IQueryable<Domain.Models.PagamentoEvento.PagamentoEvento> source,
            Expression<Func<Domain.Models.PagamentoEvento.PagamentoEvento, bool>> predicate, string name, string format,
            DateTime primeiroDiaSemana)
        {
            var totalizadorItem = new TotalizadorItem(name, format);

            var query = source.Where(predicate);
            foreach (var transacao in query.Where(x =>
                             x.DataBaixa > primeiroDiaSemana)
                         .GroupBy(x => x.DataBaixa.ToDateTime().Date))
            {
                totalizadorItem.SetValue(transacao.Key.ToDateTime().DayOfWeek, transacao.Sum(x => x.Valor));
            }

            totalizadorItem.TotalMes = query.Sum(x => x.Valor);
            return totalizadorItem;
        }

        // private TotalizadorItem GetTotalizadorItemFromQuery(IQueryable<Domain.Models.PagamentoEvento.PagamentoEvento> source,Expression<Func<Domain.Models.PagamentoEvento.PagamentoEvento, bool>> predicate, string name, string format)
        // {
        //     var totalizadorItem = new TotalizadorItem(name, format);
        //
        //     var query = source.Where(predicate);
        //     foreach (var transacao in query.Where(x =>
        //                      x.DataBaixa > DateTime.Today.StartOfWeek(DayOfWeek.Monday))
        //                  .GroupBy(x => x.DataBaixa.ToDateTime().Date))
        //     {
        //         totalizadorItem.SetValue(transacao.Key.ToDateTime().DayOfWeek, transacao.Sum(x => x.Valor));
        //     }
        //
        //     totalizadorItem.TotalMes = query.Sum(x => x.Valor);
        //     return totalizadorItem;
        // }
        //
        private TotalizadorItem GetTotalizadorItemFromQuery(IQueryable<Domain.Models.Pagamentos.Pagamentos> source,
            Expression<Func<Domain.Models.Pagamentos.Pagamentos, bool>> predicate, string name, string format,
            DateTime primeiroDiaSemana)
        {
            var totalizadorItem = new TotalizadorItem(name, format);

            var query = source.Where(predicate);
            foreach (var transacao in query.Where(x =>
                             x.DataBaixa > primeiroDiaSemana)
                         .GroupBy(x => x.DataBaixa.ToDateTime().Date))
            {
                totalizadorItem.SetValue(transacao.Key.ToDateTime().DayOfWeek, transacao.Sum(x => x.Valor));
            }

            totalizadorItem.TotalMes = query.Sum(x => x.Valor);
            return totalizadorItem;
        }

        // private TotalizadorItem GetTotalizadorItemFromQuery(IQueryable<Domain.Models.Pagamentos.Pagamentos> source,Expression<Func<Domain.Models.Pagamentos.Pagamentos, bool>> predicate, string name, string format)
        // {
        //     var totalizadorItem = new TotalizadorItem(name, format);
        //
        //     var query = source.Where(predicate);
        //     foreach (var transacao in query.Where(x =>
        //                      x.DataBaixa > DateTime.Today.StartOfWeek(DayOfWeek.Monday))
        //                  .GroupBy(x => x.DataBaixa.ToDateTime().Date))
        //     {
        //         totalizadorItem.SetValue(transacao.Key.ToDateTime().DayOfWeek, transacao.Sum(x => x.Valor));
        //     }
        //
        //     totalizadorItem.TotalMes = query.Sum(x => x.Valor);
        //     return totalizadorItem;
        // }
        //
        private TotalizadorItem GetTotalizadorItemFromQuery(IQueryable<Domain.Models.Transacao.Transacao> source,
            Expression<Func<Domain.Models.Transacao.Transacao, bool>> predicate, string name, string format)
        {
            var totalizadorItem = new TotalizadorItem(name, format);

            var query = source.Where(predicate);
            foreach (var transacao in query.Where(x =>
                             x.DataBaixa > DateTime.Today.StartOfWeek(DayOfWeek.Monday))
                         .GroupBy(x => x.DataBaixa.ToDateTime().Date))
            {
                totalizadorItem.SetValue(transacao.Key.ToDateTime().DayOfWeek, transacao.Sum(x => x.Valor));
            }

            totalizadorItem.TotalMes = query.Sum(x => x.Valor);
            return totalizadorItem;
        }

        public async Task<List<TotalizadorItem>> ConsultarPagamentosPorDiaGrid(int empresaId, DateTime database)
        {
            var idEmpresa = (await _empresaReadRepository.GetByIdAsync(User.EmpresaId))?.Id ?? empresaId;

            var lRet = new List<TotalizadorItem>();

            var (primeirDia, ultimoDia) = database.ObterInicioEFimDaSemana();
            var inicioMes = database.StartOfMonth();
            var hojeFimDoDia = ultimoDia;

            var pagamentoEvento = _pagamentoEventoReadRepository.AsNoTracking().Where(p =>
                p.Empresa.Id == idEmpresa && p.Status != StatusPagamento.Cancelado &&
                p.DataBaixa >= inicioMes && p.DataBaixa <= hojeFimDoDia);

            var pagamento = _pagamentosReadRepository.AsNoTracking().Where(p =>
                p.Empresa.Id == idEmpresa && p.Status != Status.Cancelado &&
                p.DataBaixa >= inicioMes && p.DataBaixa <= hojeFimDoDia);

            #region Valores terceiros/agregados - Adiantamento

            var totAdtoPE = GetTotalizadorItemFromQuery(pagamentoEvento,
                x => x.Tipo == Tipo.Adiantamento && x.DataBaixa != null && x.Status == StatusPagamento.Fechado,
                "Valores terceiros/agregados - Adiantamentos", "C2", primeirDia);

            var totAdtoP = GetTotalizadorItemFromQuery(pagamento,
                x => x.Tipo == Tipo.Adiantamento && x.DataBaixa != null && x.Status == Status.Baixado,
                "Valores terceiros/agregados - Adiantamentos", "C2", primeirDia);

            totAdtoPE.SomarCom(totAdtoP);

            lRet.Add(totAdtoPE);

            #endregion

            #region Valores terceiros / agregados - Saldos

            var totSaldoPE = GetTotalizadorItemFromQuery(pagamentoEvento,
                t => t.Tipo == Tipo.Saldo && t.DataBaixa != null, "Valores terceiros/agregados - Saldos", "C2",
                primeirDia);

            var totSaldoP = GetTotalizadorItemFromQuery(pagamento,
                t => t.Tipo == Tipo.Saldo && t.DataBaixa != null, "Valores terceiros/agregados - Saldos", "C2",
                primeirDia);

            totSaldoPE.SomarCom(totSaldoP);

            lRet.Add(totSaldoPE);

            #endregion

            #region Valores terceiros/agregados - Complementos

            var compplementoPE = GetTotalizadorItemFromQuery(pagamentoEvento,
                t => t.Tipo == Tipo.Complemento && t.DataBaixa != null, "Valores terceiros/agregados - Complementos",
                "C2", primeirDia);

            var compplementoP = GetTotalizadorItemFromQuery(pagamento,
                t => t.Tipo == Tipo.Complemento && t.DataBaixa != null, "Valores terceiros/agregados - Complementos",
                "C2", primeirDia);

            compplementoPE.SomarCom(compplementoP);

            lRet.Add(compplementoPE);

            #endregion

            #region Valores terceiros/agregados - Avulsos

            var totalAvulsoPE = GetTotalizadorItemFromQuery(pagamentoEvento,
                t => t.Tipo == Tipo.Avulso && t.DataBaixa != null, "Valores terceiros/agregados - Avulsos", "C2",
                primeirDia);

            var totalAvulsoP = GetTotalizadorItemFromQuery(pagamento,
                t => t.Tipo == Tipo.Avulso && t.DataBaixa != null, "Valores terceiros/agregados - Avulsos", "C2",
                primeirDia);

            totalAvulsoPE.SomarCom(totalAvulsoP);

            lRet.Add(totalAvulsoPE);

            #endregion

            #region Quantidade de pgtos contas BBC

            #region Quantidade de pgtos contas BBC PE

            var quantidadeP2PPE = new TotalizadorItem("Quantidade de pgtos. contas BBC", null);

            foreach (var transacao in pagamentoEvento.Where(x =>
                             x.DataBaixa > primeirDia && x.FormaPagamento == FormaPagamentoEvento.Deposito)
                         .GroupBy(x => x.DataBaixa.ToDateTime().Date))
            {
                quantidadeP2PPE.SetValue(transacao.Key.ToDateTime().DayOfWeek, transacao.Count().ToIntSafe());
            }

            quantidadeP2PPE.TotalMes = pagamentoEvento.Select(x => x.ViagemId).Distinct().Count();

            #endregion

            #region Quantidade de pgtos contas BBC P

            var quantidadeP2PP = new TotalizadorItem("Quantidade de pgtos. contas BBC", null);

            foreach (var transacao in pagamento.Where(x =>
                             x.DataBaixa > primeirDia && x.FormaPagamento == FormaPagamento.Deposito)
                         .GroupBy(x => x.DataBaixa.ToDateTime().Date))
            {
                quantidadeP2PP.SetValue(transacao.Key.ToDateTime().DayOfWeek, transacao.Count().ToIntSafe());
            }

            quantidadeP2PP.TotalMes = pagamentoEvento.Select(x => x.ViagemId).Distinct().Count();

            #endregion

            quantidadeP2PPE.SomarCom(quantidadeP2PP);
            lRet.Add(quantidadeP2PPE);

            #endregion

            #region Quantidade de pagtos Pix

            #region Quantidade de pagtos Pix PE

            var quantidadePixPEvento = new TotalizadorItem("Quantidade de pgtos. contas Pix", null);

            foreach (var transacao in pagamentoEvento.Where(x =>
                             x.DataBaixa > primeirDia && x.FormaPagamento == FormaPagamentoEvento.Pix)
                         .GroupBy(x => x.DataBaixa.ToDateTime().Date))
            {
                quantidadePixPEvento.SetValue(transacao.Key.ToDateTime().DayOfWeek, transacao.Count().ToIntSafe());
            }

            quantidadePixPEvento.TotalMes = pagamentoEvento.Select(x => x.ViagemId).Distinct().Count();

            #endregion

            #region Quantidade de pagtos Pix P

            var quantidadePixPagamento = new TotalizadorItem("Quantidade de pgtos. contas Pix", null);

            foreach (var transacao in pagamento.Where(x =>
                             x.DataBaixa > primeirDia && x.FormaPagamento == FormaPagamento.Pix)
                         .GroupBy(x => x.DataBaixa.ToDateTime().Date))
            {
                quantidadePixPagamento.SetValue(transacao.Key.ToDateTime().DayOfWeek, transacao.Count().ToIntSafe());
            }

            quantidadePixPagamento.TotalMes = pagamentoEvento.Select(x => x.ViagemId).Distinct().Count();

            #endregion

            quantidadePixPEvento.SomarCom(quantidadePixPagamento);

            lRet.Add(quantidadePixPEvento);

            #endregion

            return lRet;
        }

        #endregion

        #region Web

        public async Task<ConsultarGridViagemResponse> ConsultarGridViagem(ConsultarGridViagemRequest request)
        {
            try
            {
                var viagens = Repository.Query.GetAll();

                var lGrupoEmpresaId =
                    (await _usuarioReadRepository.AsNoTracking().FirstOrDefaultAsync(x => x.Id == User.Id))
                    .GrupoEmpresaId;

                if (User.EmpresaId > 0) viagens = viagens.AsNoTracking().Where(v => v.EmpresaId == User.EmpresaId);
                else if (lGrupoEmpresaId != null)
                    viagens = viagens.Where(v => v.Empresa.GrupoEmpresaId == lGrupoEmpresaId);

                viagens = viagens.Where(v =>
                    v.DataCadastro >= request.dataInicial.ToDateTime() &&
                    v.DataCadastro <= request.dataFinal.ToDateTime().AddDays(1).AddSeconds(-1));

                if (request.EmpresaId.HasValue && request.EmpresaId != 0)
                {
                    if (User.EmpresaId == request.EmpresaId || User.EmpresaId == 0)
                        viagens = viagens.Where(e => e.EmpresaId == request.EmpresaId);

                    else
                        throw new InvalidOperationException(
                            "Usuário sem permissões para visualizar pagamentos desta empresa.");
                }

                if (request.CodViagemInterno != null)
                {
                    viagens = viagens.Where(v => v.Id == request.CodViagemInterno);
                }

                var count = viagens.Count();

                viagens = viagens.AplicarFiltrosDinamicos(request.Filters);

                viagens = string.IsNullOrWhiteSpace(request.Order?.Campo)
                    ? viagens.OrderByDescending(o => o.Id)
                    : viagens.OrderBy($"{request.Order?.Campo} {request.Order?.Operador.DescriptionAttr()}");

                var lRetorno = await viagens.Skip((request.Page - 1) * request.Take).Take(request.Take)
                    .ProjectTo<ViagemConsultarGridItem>().ToListAsync();

                return new ConsultarGridViagemResponse
                {
                    TotalItems = count,
                    Items = lRetorno
                };
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e.Message);
                throw;
            }
        }

        public async Task<ConsultaGridPagamentoEventoViagemResponse> ConsultarPagamentosViagem(int viagemId,
            int requestTake, int requestPage, OrderFilters requestOrder, List<QueryFilters> requestFilters)
        {
            try
            {
                var pagamentos = Repository.Query.AsNoTracking().Where(v => v.Id == viagemId)
                    .SelectMany(x => x.PagamentoEvento);

                var count = pagamentos.Count();

                pagamentos = pagamentos.AplicarFiltrosDinamicos(requestFilters);

                pagamentos = string.IsNullOrWhiteSpace(requestOrder?.Campo)
                    ? pagamentos.OrderBy("Id descending")
                    : pagamentos.OrderBy($"{requestOrder.Campo} {requestOrder.Operador.DescriptionAttr()}");

                var lRetorno = await pagamentos.Skip((requestPage - 1) * requestTake).Take(requestTake)
                    .ProjectTo<ConsultaGridPagamentoEventoViagemItem>().ToListAsync();

                return new ConsultaGridPagamentoEventoViagemResponse
                {
                    TotalItems = count,
                    Items = lRetorno
                };
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                throw;
            }
        }


        public RespPadrao ConsultarPagamentosHistoricoViagem(int viagemId, int requestTake, int requestPage,
            OrderFilters requestOrder, List<QueryFilters> requestFilters)
        {
            try
            {
                var pagamentosHistorico = _pagamentoEventoHistoricoReadRepository.AsNoTracking()
                    .Where(v => v.ViagemId == viagemId);

                pagamentosHistorico = pagamentosHistorico.Skip((requestPage - 1) * requestTake).Take(requestTake);
                pagamentosHistorico = pagamentosHistorico.AplicarFiltrosDinamicos(requestFilters);

                pagamentosHistorico = string.IsNullOrWhiteSpace(requestOrder?.Campo)
                    ? pagamentosHistorico.OrderBy("Id descending")
                    : pagamentosHistorico.OrderBy($"{requestOrder.Campo} {requestOrder.Operador.DescriptionAttr()}");

                return new RespPadrao(true, "Sucesso.", new
                {
                    totalItems = pagamentosHistorico,
                    Items = pagamentosHistorico.ProjectTo<ConsultaGridPagamentoEventoHistoricoViagemItem>(Engine.Mapper
                        .ConfigurationProvider)
                });
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return new RespPadrao(false, e.Message);
            }
        }


        public async Task<ConsultarGridTransacaoPagamentoResponse> ConsultarTransacoesPagamento(int pagamentoId,
            int requestTake, int requestPage, OrderFilters requestOrder, List<QueryFilters> requestFilters)
        {
            try
            {
                var transacoes = _transacaoReadRepository.GetTransacoesByIdPagamentoEvento(pagamentoId);

                var count = transacoes.Count();

                transacoes = transacoes.AplicarFiltrosDinamicos(requestFilters);

                transacoes = string.IsNullOrWhiteSpace(requestOrder?.Campo)
                    ? transacoes.OrderByDescending(o => o.Id)
                    : transacoes.OrderBy($"{requestOrder?.Campo} {requestOrder?.Operador.DescriptionAttr()}");

                var lRetorno = await transacoes.Skip((requestPage - 1) * requestTake).Take(requestTake)
                    .ProjectTo<TransacaoPagamentoConsultarGridItem>().ToListAsync();

                var logsPagamento =
                    Mapper.Map<LogsPagamento>(await _pagamentoEventoReadRepository.GetByIdAsync(pagamentoId));

                return new ConsultarGridTransacaoPagamentoResponse
                {
                    TotalItems = count,
                    Items = lRetorno,
                    LogsPagamento = logsPagamento
                };
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e.Message);
                throw;
            }
        }

        public async Task<ConsultarGridTransacaoPagamentoHistoricoResponse> ConsultarTransacoesPagamentoHistorico(
            int pagamentoHistoricoId, int pagamentoEventoId, int requestTake, int requestPage,
            OrderFilters requestOrder, List<QueryFilters> requestFilters)
        {
            try
            {
                var transacoes = _transacaoReadRepository.GetTransacoesByIdPagamentoEvento(pagamentoEventoId);

                var count = transacoes.Count();

                transacoes = transacoes.AplicarFiltrosDinamicos(requestFilters);
                transacoes = string.IsNullOrWhiteSpace(requestOrder?.Campo)
                    ? transacoes.OrderByDescending(o => o.Id)
                    : transacoes.OrderBy($"{requestOrder?.Campo} {requestOrder?.Operador.DescriptionAttr()}");

                var lRetorno = await transacoes.Skip((requestPage - 1) * requestTake).Take(requestTake)
                    .ProjectTo<TransacaoPagamentoConsultarGridItem>().ToListAsync();

                var logsPagamentoHistorico =
                    Mapper.Map<LogsPagamentoHistorico>(
                        await _pagamentoEventoHistoricoReadRepository.GetByIdAsync(pagamentoHistoricoId));

                return new ConsultarGridTransacaoPagamentoHistoricoResponse
                {
                    TotalItems = count,
                    Items = lRetorno,
                    LogsPagamentoHistorico = logsPagamentoHistorico
                };
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e.Message);
                throw;
            }
        }

        public async Task<RespPadrao> ConsultarTransacaoPorId(int idTransacao)
        {
            var lLog = LogManager.GetCurrentClassLogger();
            try
            {
                if (idTransacao <= 0)
                    throw new Exception("Id inválido.");

                var transacao = await _transacaoReadRepository.GetByIdAsync(idTransacao);

                if (transacao == null)
                    throw new Exception("Transação não encontrada.");

                return new RespPadrao(true, "Sucesso!", Mapper.Map<ConsultarTransacaoResponse>(transacao));
            }
            catch (Exception e)
            {
                lLog.Error(e);
                return new RespPadrao(false, e.Message);
            }
        }

        public async Task<RespPadrao> ConsultarGridViagemCombo(int requestTake, int requestPage,
            OrderFilters requestOrder, List<QueryFilters> requestFilters)
        {
            var lLog = LogManager.GetCurrentClassLogger();
            try
            {
                var lViagem = Repository.Query
                    .Where(x => x.Status != StatusViagem.Cancelado && x.Status != StatusViagem.Bloqueado &&
                                x.Ciot == null);

                var lUser = await _usuarioReadRepository.GetByIdAsync(Engine.User.Id);

                if (lUser.EmpresaId != null)
                    lViagem = lViagem.Where(x => x.EmpresaId == lUser.EmpresaId);

                if (lUser.EmpresaId == null && lUser.GrupoEmpresaId != null)
                {
                    var lEmpresasGrupo = await _empresaReadRepository
                        .GetEmpresasIdByGrupoEmpresaId(lUser.GrupoEmpresaId.ToInt());
                    lViagem = lViagem.Where(x =>
                        x.Empresa.GrupoEmpresaId == lUser.GrupoEmpresaId ||
                        x.EmpresaId != 0 && lEmpresasGrupo.Contains(x.EmpresaId.ToInt()));
                }

                var lViagemQuery = lViagem.ProjectTo<ConsultarViagemCiotComboResponse>();

                lViagemQuery = lViagemQuery.AplicarFiltrosDinamicos(requestFilters);

                lViagemQuery = string.IsNullOrWhiteSpace(requestOrder?.Campo)
                    ? lViagemQuery.OrderByDescending(c => c.Id)
                    : lViagemQuery.OrderBy($"{requestOrder.Campo} {requestOrder.Operador.DescriptionAttr()}");

                var totalItems = await lViagemQuery.CountAsync();

                lViagemQuery = lViagemQuery.Skip((requestPage - 1) * requestTake).Take(requestTake);

                var lViagemList = await lViagemQuery.ToListAsync();


                return new RespPadrao()
                {
                    sucesso = true,
                    mensagem = "sucesso",
                    data = new
                    {
                        items = lViagemList,
                        totalItems
                    }
                };
            }
            catch (Exception e)
            {
                lLog.Error(e);
                return new RespPadrao(false, e.Message);
            }
        }

        public async Task<RespPadrao> ConsultarViagensCiot(string ciot)
        {
            try
            {
                var viagens = Repository.Query.Where(x => x.Ciot == ciot).ProjectTo<ConsultarViagensCiotGridResponse>();
                return new RespPadrao(true, "sucesso", await viagens.ToListAsync());
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return new RespPadrao(false, e.Message);
            }
        }

        #endregion

        public async Task<int> QualificarPagamentoFreteP2P(TransferenciaEntreContaResp objectJson, string receipt)
        {
            return await ViagemUtils.QualificarPagamentoFreteP2P(objectJson, receipt, _parametrosReadRepository);
        }

        #region Métodos Auxiliares V2

        private async Task<int> BuscarContaRetencaoAr()
        {
            var parametro = await _parametrosReadRepository.FirstOrDefaultAsync(x =>
                x.TipoParametros == Domain.Models.Parametros.Parametros.TipoDoParametro.ContaRetencaoAr);
            return parametro?.Valor.ToInt()??0;
        }

        private async Task<RespostaViagemPagamento> GerarPagamentoEventoViagemP2PComRetencao(
            Domain.Models.PagamentoEvento.PagamentoEvento pagamentoEvento, int contaRetencaoId)
        {
            var lLog = LogManager.GetCurrentClassLogger();

            try
            {
                var contaRetencaoInfo = await _cartaoRepository.ConsultarContasPorId(contaRetencaoId);
                if (contaRetencaoInfo.Code != null)
                {
                    lLog.Error($"Conta de retenção {contaRetencaoId} não encontrada no Conductor. Code: {contaRetencaoInfo.Code}. Mensagem: {contaRetencaoInfo.message}");
                }
                
                lLog.Info($"PagamentoEvento ID: {pagamentoEvento.Id} - Usando conta de retenção P2P. CPF/CNPJ original: {pagamentoEvento.Viagem.PortadorProprietario.CpfCnpj}, CPF/CNPJ retenção: {contaRetencaoInfo.NumeroReceitaFederal}, Conta: {contaRetencaoId}");
                var empresa = await _empresaReadRepository.GetByIdAsync(pagamentoEvento.EmpresaId.ToInt());
                    int idContaOrigem;
                    if (empresa.ContaFrete == null)
                    {
                        var contasOrigem = await _cartaoRepository.ConsultarContas(null, null, empresa.Cnpj);
                        if (contasOrigem?.content == null)
                        {
                            return new RespostaViagemPagamento()
                            {
                                Sucesso = false,
                                Mensagem = "Nenhuma conta da empresa foi encontrada.",
                                TipoOperacao = TipoOperacao.Erro,
                                StatusPagamento = StatusPagamento.NaoExecutado
                            };
                        }
                        var contaOrigemAtiva = contasOrigem.content?.FirstOrDefault(x => StatusConta().Contains(x.idStatusConta));
                        if (contaOrigemAtiva == null)
                        {
                            return new RespostaViagemPagamento
                            {
                                Sucesso = false,
                                Mensagem = "Nenhuma conta ativa da empresa foi encontrada.",
                                TipoOperacao = TipoOperacao.Erro,
                                StatusPagamento = StatusPagamento.NaoExecutado
                            };
                        }
                        idContaOrigem = contaOrigemAtiva.id;
                    }
                    else
                    {
                        idContaOrigem = empresa.ContaFrete ?? 0;
                    }

                    // PRIMEIRO: Cobrar tarifas ANTES de fazer o P2P para retenção
                    var empresaLogada = await _empresaReadRepository.FirstOrDefaultAsync(x => x.Cnpj == empresa.Cnpj);
                    var resultadoTarifas = await CalculaTarifasTransacao(pagamentoEvento.Id, empresaLogada, idContaOrigem);

                    if (!resultadoTarifas.Sucesso)
                    {
                        return new RespostaViagemPagamento
                        {
                            Sucesso = false,
                            Mensagem = $"Erro ao cobrar tarifas antes da retenção: {resultadoTarifas.Mensagem}",
                            TipoOperacao = TipoOperacao.Erro,
                            StatusPagamento = StatusPagamento.Erro
                        };
                    }

                    // SEGUNDO: Fazer o P2P para conta de retenção
                    var resultado = await TransferenciaPagamentoEventoP2PComRetencaoDock(
                        pagamentoEvento, idContaOrigem, contaRetencaoId, pagamentoEvento.Tipo, contaRetencaoInfo.NumeroReceitaFederal);

                    return resultado;
                }
            catch (Exception ex)
            {
                lLog.Error(ex, $"Erro ao processar pagamento P2P com conta de retenção {contaRetencaoId}");
                return new RespostaViagemPagamento
                {
                    Sucesso = false,
                    Mensagem = $"Erro ao processar pagamento com conta de retenção: {ex.Message}",
                    TipoOperacao = TipoOperacao.Erro,
                    StatusPagamento = StatusPagamento.Erro
                };
            }
        }

        #endregion

        #region Viagem V2

        public async Task<ViagemIntegrarResponse> PagamentoViagemAbrir(PagamentoViagemIntegrarRequest viagemIntegrarRequest, string token = "",
            bool servicoReenvio = false, int pagamentoEventoId = 0)
        {
            try
            {
                var lLog = LogManager.GetCurrentClassLogger();

                #region Validações iniciais

                if (viagemIntegrarRequest.ViagemExternoId is null or <= 0)
                {
                    return new ViagemIntegrarResponse(false, "ViagemExternoId é obrigatório e deve ser maior que zero.");
                }

                if (viagemIntegrarRequest.Valor <= 0)
                {
                    return new ViagemIntegrarResponse(false, "Valor deve ser maior que zero.");
                }

                if (!viagemIntegrarRequest.DataPrevisaoPagamento.HasValue)
                {
                    return new ViagemIntegrarResponse(false, "DataPrevisaoPagamento é obrigatória.");
                }

                #endregion

                #region Buscar viagem

                var empresaId = Engine.User.EmpresaId;
                var viagem = await Repository.Query
                    .FirstOrDefaultAsync(v => v.ViagemExternoId == viagemIntegrarRequest.ViagemExternoId && v.EmpresaId == empresaId);

                if (viagem == null)
                {
                    return new ViagemIntegrarResponse(false, $"Viagem com ViagemExternoId {viagemIntegrarRequest.ViagemExternoId} não encontrada.");
                }

                #endregion

                #region Verificar PagamentoEvento existente
                if (viagemIntegrarRequest.PagamentoExternoId.HasValue)
                {
                    var pagamentoEventoExistente = await _pagamentoEventoReadRepository
                        .Where(x => x.PagamentoExternoId == viagemIntegrarRequest.PagamentoExternoId &&
                                   x.EmpresaId == empresaId)
                        .FirstOrDefaultAsync();

                    if (pagamentoEventoExistente != null)
                    {
                        var statusDescricao = pagamentoEventoExistente.Status == StatusPagamento.Fechado ? "Processado" :
                                             pagamentoEventoExistente.Status == StatusPagamento.Processando ? "Processando" :
                                             pagamentoEventoExistente.Status == StatusPagamento.Cancelado ? "Cancelado" :
                                             pagamentoEventoExistente.Status == StatusPagamento.Aberto ? "Aberto" :
                                             pagamentoEventoExistente.Status.ToString();

                        lLog.Warn($"Já existe um PagamentoEvento (ID: {pagamentoEventoExistente.Id}) para PagamentoExternoId: {viagemIntegrarRequest.PagamentoExternoId} com status '{statusDescricao}'.");
                        return new ViagemIntegrarResponse(false, $"Já existe um pagamento para este PagamentoExternoId com status: {statusDescricao}");
                    }
                }

                #endregion

                #region Criar PagamentoEvento

                decimal? valorMotoristaAutomatico = null;
                if (FormaPagamentoEvento.Pix != viagemIntegrarRequest.FormaPagamento)
                {
                    valorMotoristaAutomatico = await ColetarValorTransferenciaAutomatica(viagemIntegrarRequest.CpfCnpjContratado,
                        viagemIntegrarRequest.Tipo ?? Tipo.Adiantamento,
                        viagemIntegrarRequest.Valor);
                }
                
                var pagamentoEventoCommand = new PagamentoEventoSalvarComRetornoCommand
                {
                    ViagemId = viagem.Id,
                    EmpresaId = empresaId,
                    Valor = viagemIntegrarRequest.Valor,
                    Status = StatusPagamento.Aberto,
                    Tipo = viagemIntegrarRequest.Tipo,
                    FormaPagamento = (FormaPagamento?)viagemIntegrarRequest.FormaPagamento,
                    PagamentoExternoId = viagemIntegrarRequest.PagamentoExternoId,
                    ChavePix = viagemIntegrarRequest.ChavePix,
                    Agencia = viagemIntegrarRequest.Agencia,
                    Conta = viagemIntegrarRequest.Conta,
                    CodigoBanco = viagemIntegrarRequest.CodigoBanco,
                    TipoConta = (int?)viagemIntegrarRequest.TipoConta,
                    RecebedorAutorizado = viagemIntegrarRequest.RecebedorAutorizado,
                    WebhookUrl = viagemIntegrarRequest.WebhookUrl,
                    UsuarioCadastroId = Engine.User.Id,
                    DataCadastro = DateTime.Now,
                    ValorTransferenciaMotorista = valorMotoristaAutomatico,
                    DataPrevisaoPagamento = viagemIntegrarRequest.DataPrevisaoPagamento.Value,
                    StatusCliente = viagemIntegrarRequest.StatusCliente
                };

                var requestPagamentoV2 = new PagamentoV2Request
                {
                    PagamentoExternoId = viagemIntegrarRequest.PagamentoExternoId,
                    DataPrevisaoPagamento = viagemIntegrarRequest.DataPrevisaoPagamento,
                    Tipo = viagemIntegrarRequest.Tipo ?? Tipo.Adiantamento,
                    TipoConta = viagemIntegrarRequest.TipoConta ?? ETipoContaDock.Corrente,
                    FormaPagamento = viagemIntegrarRequest.FormaPagamento ?? FormaPagamentoEvento.Deposito,
                    Valor = viagemIntegrarRequest.Valor,
                    HashValidacao = viagemIntegrarRequest.HashValidacao,
                    Status = (int)StatusPagamento.Aberto,
                    ChavePix = viagemIntegrarRequest.ChavePix,
                    Agencia = viagemIntegrarRequest.Agencia,
                    Conta = viagemIntegrarRequest.Conta,
                    RecebedorAutorizado = viagemIntegrarRequest.RecebedorAutorizado,
                    WebhookUrl = viagemIntegrarRequest.WebhookUrl,
                };
                
                var pagamentoEvento = await Engine.CommandBus.SendCommandAsync<Domain.Models.PagamentoEvento.PagamentoEvento>(pagamentoEventoCommand);
                pagamentoEvento.StatusAntecipacaoParcelaProprietario = StatusAntecipacaoParcelaProprietario.Disponivel;
                pagamentoEvento.JsonEnvio = JsonConvert.SerializeObject(requestPagamentoV2, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
                
                await Engine.CommandBus.SendCommandAsync<Domain.Models.PagamentoEvento.PagamentoEvento>(pagamentoEvento);

                #endregion
                
                var valorTransferenciaMotoristaAutomatica = await ColetarValorTransferenciaAutomatica(pagamentoEvento);
                if (valorTransferenciaMotoristaAutomatica > 0)
                {
                    pagamentoEvento.ValorTransferenciaMotorista = valorTransferenciaMotoristaAutomatica;
                    await Repository.Command.SaveChangesAsync();
                }

                lLog.Info($"PagamentoEvento criado com sucesso - ID: {pagamentoEvento.Id}, ViagemId: {viagem.Id}, Status: Aberto, StatusAntecipacaoParcelaProprietario: Disponivel");
                
                return new ViagemIntegrarResponse(
                    sucesso: true,
                    viagemId: viagem.Id,
                    statusViagem: viagem.Status.ToInt(),
                    mensagem: "Pagamento aberto com sucesso!",
                    pagamento: new PagamentoViagemResponse()
                    {
                        PagamentoEventoId = pagamentoEvento.Id,
                        StatusPagamento = pagamentoEvento.Status.GetHashCode(),
                        ValorParcela = pagamentoEvento.Valor,
                        ValorMotorista = pagamentoEvento.ValorTransferenciaMotorista,
                        CódTransacao = pagamentoEvento.CodigoTransacao,
                        FormaPagamento = pagamentoEvento.FormaPagamento?.GetHashCode(),
                        Mensagem = "Evento de pagamento possui status '"+ pagamentoEvento.Status.ToString()+"'.",
                        Transacoes = pagamentoEvento.Transacao?
                            .Where(t => t.Tipo != Tipo.Tarifas)
                            .Select(t => Mapper.Map<PagamentoViagemTransacaoResponse>(t))
                            .ToList() ?? new List<PagamentoViagemTransacaoResponse>()
                    }
                );
            }
            catch (Exception ex)
            {
                LogManager.GetCurrentClassLogger().Error(ex, $"Erro ao abrir pagamento para viagem {viagemIntegrarRequest.ViagemExternoId}");
                return new ViagemIntegrarResponse(false, $"Erro ao abrir pagamento: {ex.Message}");
            }
        }

        public async Task<ViagemIntegrarResponse> PagamentoViagemCancelar(PagamentoViagemIntegrarRequest viagemIntegrarRequest, string token = "",
            bool servicoReenvio = false, int pagamentoEventoId = 0)
        {
            try
            {
                var lLog = LogManager.GetCurrentClassLogger();
                var empresaId = Engine.User.EmpresaId;

                #region Validações iniciais

                if (viagemIntegrarRequest.ViagemExternoId is null or <= 0)
                {
                    return new ViagemIntegrarResponse(false, "ViagemExternoId é obrigatório e deve ser maior que zero.");
                }

                if (viagemIntegrarRequest.PagamentoExternoId is null or <= 0)
                {
                    return new ViagemIntegrarResponse(false, "PagamentoExternoId é obrigatório e deve ser maior que zero.");
                }

                if (viagemIntegrarRequest.Valor <= 0)
                {
                    return new ViagemIntegrarResponse(false, "Valor deve ser maior que zero.");
                }

                #endregion

                #region Buscar PagamentoEvento

                Domain.Models.PagamentoEvento.PagamentoEvento pagamentoEvento;
                
                if (pagamentoEventoId > 0)
                {
                    pagamentoEvento = await _pagamentoEventoReadRepository.GetByIdIncludeTransacoesEViagemAsync(pagamentoEventoId);
                }
                else
                {
                    pagamentoEvento = await _pagamentoEventoReadRepository
                        .Where(x => x.PagamentoExternoId == viagemIntegrarRequest.PagamentoExternoId &&
                                   x.Viagem.ViagemExternoId == viagemIntegrarRequest.ViagemExternoId &&
                                   x.EmpresaId == empresaId)
                        .Include(x => x.Viagem)
                        .Include(x => x.Transacao)
                        .Include(x => x.Empresa)
                        .FirstOrDefaultAsync();
                }

                if (pagamentoEvento == null)
                {
                    return new ViagemIntegrarResponse(false, "Pagamento evento não localizado!");
                }

                #endregion

                #region Criar CancelamentoEventoViagemRequest

                var cancelarEventoViagemRequest = new CancelamentoEventoViagemRequest
                {
                    PagamentoExternoId = viagemIntegrarRequest.PagamentoExternoId,
                    ViagemExternoId = viagemIntegrarRequest.ViagemExternoId,
                    Valor = viagemIntegrarRequest.Valor,
                    DataRequisicao = DateTime.Now,
                    Evento = Domain.Enum.EventoCancelamento.Cancelamento,
                    Motivo = "Cancelamento via PagamentoViagemCancelar"
                };

                #endregion

                #region Executar cancelamento

                lLog.Info($"Iniciando cancelamento do PagamentoEvento ID: {pagamentoEvento.Id}, PagamentoExternoId: {viagemIntegrarRequest.PagamentoExternoId}");

                var resultadoCancelamento = await CancelarEventoViagem(cancelarEventoViagemRequest, empresaId);

                #endregion

                #region Preparar resposta

                if (resultadoCancelamento.Sucesso)
                {
                    lLog.Info($"Cancelamento realizado com sucesso para PagamentoEvento ID: {pagamentoEvento.Id}");

                    return new ViagemIntegrarResponse
                    {
                        Sucesso = true,
                        ViagemId = pagamentoEvento.ViagemId,
                        ViagemExternoId = pagamentoEvento.Viagem.ViagemExternoId,
                        StatusViagem = (int)pagamentoEvento.Viagem.Status,
                        Mensagem = "Pagamento cancelado com sucesso!",
                        Pagamento = new PagamentoViagemResponse
                        {
                            PagamentoEventoId = pagamentoEvento.Id,
                            PagamentoExternoId = pagamentoEvento.PagamentoExternoId,
                            ValorParcela = pagamentoEvento.Valor,
                            StatusPagamento = (int)StatusPagamento.Cancelado,
                            FormaPagamento = (int?)pagamentoEvento.FormaPagamento,
                            CódTransacao = pagamentoEvento.CodigoTransacao,
                            Mensagem = resultadoCancelamento.Mensagem,
                            Transacoes = pagamentoEvento.Transacao?
                                .Where(t => t.Tipo != Tipo.Tarifas)
                                .Select(t => Mapper.Map<PagamentoViagemTransacaoResponse>(t))
                                .ToList() ?? new List<PagamentoViagemTransacaoResponse>()
                        }
                    };
                }
                else
                {
                    lLog.Error($"Erro ao cancelar PagamentoEvento ID: {pagamentoEvento.Id}. Erro: {resultadoCancelamento.Mensagem}");
                    return new ViagemIntegrarResponse(false, $"Erro ao cancelar pagamento: {resultadoCancelamento.Mensagem}", pagamentoEvento);
                }

                #endregion
            }
            catch (Exception ex)
            {
                var  pagamentoEvento = await _pagamentoEventoReadRepository.GetByIdIncludeTransacoesEViagemAsync(pagamentoEventoId);
                LogManager.GetCurrentClassLogger().Error(ex, $"Erro ao cancelar pagamento para viagem {viagemIntegrarRequest.ViagemExternoId}");
                return new ViagemIntegrarResponse(false, $"Erro ao cancelar pagamento: {ex.Message}", pagamentoEvento);
            }
        }

        public async Task<ViagemIntegrarResponse> PagamentoViagemBaixa(PagamentoViagemIntegrarRequest viagemIntegrarRequest, string token = "",
            bool servicoReenvio = false, int pagamentoEventoId = 0)
        {
            try
            {
                var lLog = LogManager.GetCurrentClassLogger();
                var empresaId = Engine.User.EmpresaId;

                #region Buscar PagamentoEvento existente

                Domain.Models.PagamentoEvento.PagamentoEvento pagamentoEventoExistente = null;

                // Primeiro tenta buscar por pagamentoEventoId se foi informado
                if (pagamentoEventoId > 0)
                {
                    pagamentoEventoExistente = await _pagamentoEventoReadRepository.GetByIdIncludeTransacoesEViagemAsync(pagamentoEventoId);
                }
                // Se não encontrou, busca por PagamentoExternoId e ViagemExternoId
                else if (viagemIntegrarRequest.PagamentoExternoId.HasValue && viagemIntegrarRequest.ViagemExternoId.HasValue)
                {
                    pagamentoEventoExistente = await _pagamentoEventoReadRepository
                        .Where(x => x.PagamentoExternoId == viagemIntegrarRequest.PagamentoExternoId &&
                                   x.Viagem.ViagemExternoId == viagemIntegrarRequest.ViagemExternoId &&
                                   x.EmpresaId == empresaId &&
                                   x.Status == StatusPagamento.Aberto) // Só processa se estiver Aberto
                        .Include(x => x.Viagem)
                        .Include(x => x.Viagem.PortadorProprietario)
                        .Include(x => x.Viagem.PortadorMotorista)
                        .Include(x => x.Empresa)
                        .Include(x => x.Transacao)
                        .FirstOrDefaultAsync();
                }

                #endregion

                #region Cenário 1: PagamentoEvento NÃO existe - Chama IntegrarPagamentoViagem

                if (pagamentoEventoExistente == null)
                {
                    lLog.Info($"PagamentoEvento não encontrado para ViagemExternoId: {viagemIntegrarRequest.ViagemExternoId}, PagamentoExternoId: {viagemIntegrarRequest.PagamentoExternoId}. Chamando IntegrarPagamentoViagem.");
                    return await IntegrarPagamentoViagem(viagemIntegrarRequest, token, servicoReenvio, pagamentoEventoId);
                }

                #endregion

                #region Cenário 2: PagamentoEvento JÁ existe - Validar status e processar transações

                lLog.Info($"PagamentoEvento encontrado (ID: {pagamentoEventoExistente.Id}, Status: {pagamentoEventoExistente.Status}).");

                // Validar se o pagamento já foi processado ou cancelado
                if (pagamentoEventoExistente.Status == StatusPagamento.Fechado ||
                    pagamentoEventoExistente.Status == StatusPagamento.Processando ||
                    pagamentoEventoExistente.Status == StatusPagamento.Cancelado)
                {
                    var statusDescricao = pagamentoEventoExistente.Status == StatusPagamento.Fechado ? "Processado" :
                                         pagamentoEventoExistente.Status == StatusPagamento.Processando ? "Processando" : "Cancelado";

                    lLog.Warn($"PagamentoEvento ID: {pagamentoEventoExistente.Id} já está com status '{statusDescricao}'. Não é possível processar novamente.");

                    return new ViagemIntegrarResponse
                    {
                        Sucesso = false,
                        ViagemId = pagamentoEventoExistente.ViagemId,
                        ViagemExternoId = pagamentoEventoExistente.Viagem.ViagemExternoId,
                        StatusViagem = pagamentoEventoExistente.Viagem.Status.ToInt(),
                        Mensagem = $"Pagamento não pode ser processado. Status atual: {statusDescricao}",
                        Pagamento = new PagamentoViagemResponse
                        {
                            PagamentoEventoId = pagamentoEventoExistente.Id,
                            PagamentoExternoId = pagamentoEventoExistente.PagamentoExternoId,
                            ValorParcela = pagamentoEventoExistente.Valor,
                            StatusPagamento = (int)pagamentoEventoExistente.Status.Value,
                            FormaPagamento = (int?)pagamentoEventoExistente.FormaPagamento,
                            CódTransacao = pagamentoEventoExistente.CodigoTransacao,
                            Transacoes = pagamentoEventoExistente.Transacao?
                                .Where(t => t.Tipo != Tipo.Tarifas)
                                .Select(t => Mapper.Map<PagamentoViagemTransacaoResponse>(t))
                                .ToList() ?? new List<PagamentoViagemTransacaoResponse>()
                        }
                    };
                }

                lLog.Info($"PagamentoEvento ID: {pagamentoEventoExistente.Id} está apto para processamento. Executando transações bancárias.");

                // Atualizar campos do PagamentoEvento se necessário
                pagamentoEventoExistente.ChavePix = viagemIntegrarRequest.ChavePix ?? pagamentoEventoExistente.ChavePix;
                pagamentoEventoExistente.Agencia = viagemIntegrarRequest.Agencia ?? pagamentoEventoExistente.Agencia;
                pagamentoEventoExistente.Conta = viagemIntegrarRequest.Conta ?? pagamentoEventoExistente.Conta;
                pagamentoEventoExistente.CodigoBanco = viagemIntegrarRequest.CodigoBanco ?? pagamentoEventoExistente.CodigoBanco;
                pagamentoEventoExistente.TipoConta = (int?)viagemIntegrarRequest.TipoConta ?? pagamentoEventoExistente.TipoConta;
                pagamentoEventoExistente.RecebedorAutorizado = viagemIntegrarRequest.RecebedorAutorizado ?? pagamentoEventoExistente.RecebedorAutorizado;
                pagamentoEventoExistente.WebhookUrl = viagemIntegrarRequest.WebhookUrl ?? pagamentoEventoExistente.WebhookUrl;

                // Atualizar o PagamentoEvento
                await Engine.CommandBus.SendCommandAsync<Domain.Models.PagamentoEvento.PagamentoEvento>(pagamentoEventoExistente);

                // Executar as transações bancárias
                RespostaViagemPagamento retornoTransacoes;

                var empresa = pagamentoEventoExistente.Empresa;
                var chavePix = pagamentoEventoExistente.ChavePix;
                var agencia = pagamentoEventoExistente.Agencia;
                var conta = pagamentoEventoExistente.Conta;
                var codigoBanco = pagamentoEventoExistente.CodigoBanco;
                var tipoConta = pagamentoEventoExistente.TipoConta;
                
                if (pagamentoEventoExistente.FormaPagamento == FormaPagamentoEvento.Pix && pagamentoEventoExistente.StatusAntecipacaoParcelaProprietario != StatusAntecipacaoParcelaProprietario.AguardandoProcessamento && pagamentoEventoExistente.StatusAntecipacaoParcelaProprietario != StatusAntecipacaoParcelaProprietario.Aprovado)
                {
                    var recerbedorAutorizado = empresa.RecebedorAutorizado == true ||
                                               pagamentoEventoExistente?.RecebedorAutorizado != null;
                    retornoTransacoes = await GerarPagamentoEventoViagemPix(pagamentoEventoExistente, chavePix, agencia,
                        conta, codigoBanco, tipoConta,
                        recerbedorAutorizado,
                        recerbedorAutorizado ? 
                            viagemIntegrarRequest.RecebedorAutorizado : 
                            pagamentoEventoExistente.Viagem.PortadorProprietario?.CpfCnpj);
                }
                else
                {
                    if (pagamentoEventoExistente.UsaContaRetencao())
                    {
                        var contaRetencao = await BuscarContaRetencaoAr();
                        lLog.Info($"PagamentoEvento ID: {pagamentoEventoExistente.Id} - Usando método P2P com conta de retenção: {contaRetencao}");
                        retornoTransacoes = await GerarPagamentoEventoViagemP2PComRetencao(pagamentoEventoExistente, contaRetencao);
                    }
                    else
                    {
                        retornoTransacoes = await GerarPagamentoEventoViagemP2P(pagamentoEventoExistente);
                    }
                }
                
                if (retornoTransacoes.Sucesso)
                {
                    lLog.Info($"Transações processadas com sucesso para PagamentoEvento ID: {pagamentoEventoExistente.Id}");
                    pagamentoEventoExistente.DataBaixa = DateTime.Now;
                    pagamentoEventoExistente.Status = StatusPagamento.Fechado;

                    var statusAnterior = pagamentoEventoExistente.StatusAntecipacaoParcelaProprietario;
                    pagamentoEventoExistente.StatusAntecipacaoParcelaProprietario = StatusAntecipacaoParcelaProprietario.NaoDisponivel;

                    lLog.Info($"PagamentoEvento ID: {pagamentoEventoExistente.Id} - StatusAntecipacaoParcelaProprietario alterado: {statusAnterior} → NaoDisponivel");
                    var response = new ViagemIntegrarResponse
                    {
                        Sucesso = true,
                        ViagemId = pagamentoEventoExistente.ViagemId,
                        ViagemExternoId = pagamentoEventoExistente.Viagem.ViagemExternoId,
                        StatusViagem = (int)pagamentoEventoExistente.Viagem.Status,
                        Mensagem = "Pagamento baixado com sucesso!",
                        Pagamento = new PagamentoViagemResponse
                        {
                            PagamentoEventoId = pagamentoEventoExistente.Id,
                            PagamentoExternoId = pagamentoEventoExistente.PagamentoExternoId,
                            ValorParcela = pagamentoEventoExistente.Valor,
                            StatusPagamento = StatusPagamento.Fechado.ToInt(), // Status atualizado
                            FormaPagamento = pagamentoEventoExistente.FormaPagamento?.ToInt(),
                            CódTransacao = pagamentoEventoExistente.CodigoTransacao,
                            Transacoes = pagamentoEventoExistente.Transacao?
                                .Where(t => t.Tipo != Tipo.Tarifas)
                                .Select(t => Mapper.Map<PagamentoViagemTransacaoResponse>(t))
                                .ToList() ?? new List<PagamentoViagemTransacaoResponse>()
                        }
                    };

                    pagamentoEventoExistente.JsonRetorno = JsonConvert.SerializeObject(response);
                    pagamentoEventoExistente.DataRetorno = DateTime.Now;
                    
                    await Engine.CommandBus.SendCommandAsync<Domain.Models.PagamentoEvento.PagamentoEvento>(pagamentoEventoExistente);
                    await _pagamentoEventoAppSerivce.EnviaWebHookAtualizacaoStatus(pagamentoEventoExistente);

                    lLog.Info($"PagamentoEvento ID: {pagamentoEventoExistente.Id} atualizado - DataBaixa: {pagamentoEventoExistente.DataBaixa}, Status: Processando, JsonRetorno salvo");
                    return response;
                }
                else
                {
                    lLog.Error($"Erro ao processar transações para PagamentoEvento ID: {pagamentoEventoExistente.Id}. Erro: {retornoTransacoes.Mensagem}");
                    return new ViagemIntegrarResponse(false, $"Erro ao processar transações: {retornoTransacoes.Mensagem}");
                }

                #endregion
            }
            catch (Exception ex)
            {
                LogManager.GetCurrentClassLogger().Error(ex, $"Erro ao baixar pagamento para viagem {viagemIntegrarRequest.ViagemExternoId}");
                return new ViagemIntegrarResponse(false, $"Erro ao baixar pagamento: {ex.Message}");
            }
        }

        #endregion
    }
}
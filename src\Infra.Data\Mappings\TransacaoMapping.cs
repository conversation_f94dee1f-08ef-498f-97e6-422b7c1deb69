using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using SistemaInfo.BBC.Domain.Models.Transacao;
using SistemaInfo.Framework.EntityFramework.Configuration;

namespace SistemaInfo.BBC.Infra.Data.Mappings
{
    public class TransacaoMapping : EntityTypeConfiguration<Transacao>
    {
        public override void Map(EntityTypeBuilder<Transacao> builder)
        {
            builder.ToTable("Transacao");
            builder.HasKey(x => x.Id);
            
            builder.Property(b => b.Id).IsRequired().HasColumnName("Id").ValueGeneratedOnAdd();
            builder.Property(b => b.Valor).IsRequired().HasColumnName("Valor").HasColumnType("numeric(16,2)");
            builder.Property(b => b.Origem).HasColumnName("Origem").HasColumnType("int").IsRequired();
            builder.Property(b => b.Status).HasColumnName("Status").HasColumnType("int").IsRequired();
            builder.Property(b => b.DataCadastro).IsRequired().HasColumnName("DataCadastro").HasColumnType("timestamp");
            builder.Property(b => b.DataBaixa).HasColumnName("DataBaixa").HasColumnType("timestamp");
            builder.Property(b => b.FormaPagamento).HasColumnName("FormaPagamento").HasColumnType("int").IsRequired();
            builder.Property(b => b.Tipo).HasColumnName("Tipo").HasColumnType("int");
            builder.Property(b => b.Destino).HasColumnName("Destino").HasColumnType("int");
            builder.Property(b => b.Agencia).HasColumnName("Agencia").HasColumnType("varchar(150)");
            builder.Property(b => b.Conta).HasColumnName("Conta").HasColumnType("varchar(150)");
            builder.Property(b => b.TipoConta).HasColumnName("TipoConta").HasColumnType("int");
            builder.Property(b => b.CodigoBanco).HasColumnName("CodigoBanco").HasColumnType("varchar(150)");
            builder.Property(b => b.Descricao).HasColumnName("DescricaoMotivo").HasColumnType("varchar(100)");
            builder.Property(b => b.IdEndToEnd).HasColumnName("IdEndToEnd").HasColumnType("varchar(100)");
            builder.Property(b => b.DataCancelamento).HasColumnName("DataCancelamento").HasColumnType("timestamp");
            builder.Property(b => b.DataAlteracao).HasColumnName("DataAlteracao").HasColumnType("timestamp");
            builder.Property(b => b.Description).HasColumnName("DescriptionPagamentoDock").HasColumnType("varchar(1000)");
            
            builder.Property(b => b.JsonEnvioDockCancelamento).HasColumnName("JsonEnvioDockCancelamento").HasColumnType("json");
            builder.Property(b => b.JsonEnvioDock).HasColumnName("JsonEnvioDock").HasColumnType("json");
            builder.Property(b => b.JsonRespostaDock).HasColumnName("JsonRespostaDock").HasColumnType("json");
            builder.Property(b => b.JsonRespostaDockCancelamento).HasColumnName("JsonRespostaDockCancelamento").HasColumnType("json");
            builder.Property(b => b.ResponseCodeDockCancelamento).HasColumnName("ResponseCodeDockCancelamento").HasColumnType("int");
            builder.Property(b => b.ResponseCodeDock).HasColumnName("ResponseCodeDock").HasColumnType("int");
            builder.Property(b => b.DataRetornoDock).HasColumnName("DataRetornoDock").HasColumnType("timestamp");
            builder.Property(b => b.Qualificado).HasColumnName("Qualificado").HasColumnType("int").HasDefaultValue(0);

            builder.HasOne(c => c.PagamentoEvento)
                .WithMany(c => c.Transacao)
                .HasForeignKey(c => c.IdPagamentoEvento);
        }
    }
}
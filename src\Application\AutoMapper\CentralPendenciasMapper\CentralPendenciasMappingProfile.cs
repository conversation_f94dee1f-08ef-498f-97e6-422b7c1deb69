﻿using System.Collections.Generic;
using SistemaInfo.BBC.Application.Objects.Api.Viagem;
using SistemaInfo.BBC.Application.Objects.Web.CentralPendencias;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.BBC.Domain.Models.PagamentoEvento;
using SistemaInfo.BBC.Domain.Models.PagamentoEvento.Commands;
using SistemaInfo.BBC.Domain.Models.Transacao;
using SistemaInfo.BBC.Domain.Models.Viagem;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.AutoMapper.CentralPendenciasMapper
{
    public class CentralPendenciasMappingProfile : SistemaInfoMappingProfile
    {
        public CentralPendenciasMappingProfile()
        {
            CreateMap<CentralPendenciasRequest, PagamentoEventoSalvarCommand>();
            
            CreateMap<CentralPendenciasRequest, PagamentoEventoSalvarComRetornoCommand>();
            
            CreateMap<PagamentoEventoSalvarCommand, PagamentoEvento>();
            
            CreateMap<CodigoTransacaoSalvarCommand, PagamentoEvento>();
            
            CreateMap<ValorTransferenciaMotoristaCommand, PagamentoEvento>()
                .ForMember(a => a.ValorTransferenciaMotorista, opts => opts.MapFrom(d => d.ValorTransferenciaMotorista));

            CreateMap<PagamentoEventoSalvarComRetornoCommand, PagamentoEvento>()
                .ForMember(a => a.DataSolicitacaoCancelamento, opts => opts.MapFrom(d => d.DataSolicitacaoCancelamento));

            CreateMap<PagamentoEvento, ConsultarGridCentralPendencias>()
                .ForMember(dest => dest.Status, opts => opts.MapFrom(s => (int)s.Status))
                .ForMember(dest => dest.ViagemId, opts => opts.MapFrom(s => s.ViagemId))
                .ForMember(dest => dest.Ciot, opts => opts.MapFrom(s => 
                    s.Viagem.Ciot == null
                        ? null
                        : s.Viagem.Ciot + "/" + (string.IsNullOrWhiteSpace(s.Viagem.VerificadorCiot) ? "XXXX" : s.Viagem.VerificadorCiot)
                ))
                .ForMember(dest => dest.Id, opts => opts.MapFrom(s => s.Id))
                .ForMember(dest => dest.FilialExternoId, opts => opts.MapFrom(s => s.Viagem.FilialId))
                .ForMember(dest => dest.PagamentoExternoId, opts => opts.MapFrom(s => s.PagamentoExternoId))
                .ForMember(dest => dest.EmpresaNome, opts => opts.MapFrom(s => s.Viagem.Empresa.NomeFantasia))
                .ForMember(dest => dest.NomeProprietario, opts => opts.MapFrom(s => s.Viagem.PortadorProprietario.Nome))
                .ForMember(dest => dest.CpfcnpjProprietario, opts => opts.MapFrom(s => FormatUtils.CpfCnpj(s.Viagem.PortadorProprietario.CpfCnpj)))
                .ForMember(dest => dest.NomeMotorista, opts => opts.MapFrom(s => s.Viagem.PortadorMotorista.Nome))
                .ForMember(dest => dest.CpfcnpjMotorista, opts => opts.MapFrom(s => FormatUtils.CpfCnpj(s.Viagem.PortadorMotorista.CpfCnpj)))
                .ForMember(dest => dest.Tipo, opts => opts.MapFrom(s => (int?)s.Tipo))
                .ForMember(dest => dest.FormaPagamento, opts => opts.MapFrom(s => (int?)s.FormaPagamento))
                .ForMember(dest => dest.Valor, opts => opts.MapFrom(s => StringHelper.FormatMoney(s.Valor)));
            
            CreateMap<PagamentoEvento, CentralPendenciasResponse>()
                .ForMember(dest => dest.Valor, opts => opts.MapFrom(s => StringHelper.FormatMoney(s.Valor)));

            CreateMap<PagamentoEvento, CentralPendenciasDetalhesResponse>()
                .ForMember(dest => dest.Valor, opts => opts.MapFrom(s => StringHelper.FormatMoney(s.Valor)))
                .ForMember(dest => dest.StatusDescricao, opts => opts.MapFrom(s => s.Status.HasValue ? s.Status.ToString() : ""))
                .ForMember(dest => dest.TipoDescricao, opts => opts.MapFrom(s => s.Tipo.HasValue ? s.Tipo.ToString() : ""))
                .ForMember(dest => dest.ViagemExternoId, opts => opts.MapFrom(s => s.Viagem != null ? s.Viagem.ViagemExternoId : 0))
                .ForMember(dest => dest.DataPrevisaoPagamento, opts => opts.MapFrom(s => s.DataPrevisaoPagamento.HasValue ? s.DataPrevisaoPagamento.Value.ToString("dd/MM/yyyy") : ""))
                .ForMember(dest => dest.FormaPagamentoDescricao, opts => opts.MapFrom(s => s.FormaPagamento.HasValue ? s.FormaPagamento.ToString() : ""))
                .ForMember(dest => dest.NomeProprietario, opts => opts.MapFrom(s => s.Viagem != null && s.Viagem.PortadorProprietario != null ? s.Viagem.PortadorProprietario.Nome : ""))
                .ForMember(dest => dest.CpfCnpjProprietario, opts => opts.MapFrom(s => s.Viagem != null && s.Viagem.PortadorProprietario != null ? s.Viagem.PortadorProprietario.CpfCnpj : ""))
                .ForMember(dest => dest.RazaoSocialEmpresa, opts => opts.MapFrom(s => s.Viagem != null && s.Viagem.Empresa != null ? s.Viagem.Empresa.RazaoSocial : ""))
                .ForMember(dest => dest.CnpjEmpresa, opts => opts.MapFrom(s => s.Viagem != null && s.Viagem.Empresa != null ? s.Viagem.Empresa.Cnpj : ""))
                .ForMember(dest => dest.StatusAntecipacaoDescricao, opts => opts.MapFrom(s => s.StatusAntecipacaoParcelaProprietario.HasValue ? s.StatusAntecipacaoParcelaProprietario.ToString() : ""))
                .ForMember(dest => dest.AntecipacaoMotivo, opts => opts.MapFrom(s => s.AntecipacaoMotivo ?? ""))
                .ForMember(dest => dest.DataCadastroAntecipacao, opts => opts.MapFrom(s => s.DataCadastroAntecipacao))
                .ForMember(dest => dest.DataAlteracaoAntecipacao, opts => opts.MapFrom(s => s.DataAlteracaoAntecipacao))
                .ForMember(dest => dest.Transacoes, opts => opts.MapFrom(s => s.Transacao ?? new List<Transacao>()));

            CreateMap<Transacao, CentralPendenciasTransacaoResponse>()
                .ForMember(dest => dest.Valor, opts => opts.MapFrom(s => StringHelper.FormatMoney(s.Valor)))
                .ForMember(dest => dest.Status, opts => opts.MapFrom(s => s.Status.ToString()))
                .ForMember(dest => dest.StatusDescricao, opts => opts.MapFrom(s => s.Status.ToString()))
                .ForMember(dest => dest.DataCriacao, opts => opts.MapFrom(s => s.DataCadastro))
                .ForMember(dest => dest.DataProcessamento, opts => opts.MapFrom(s => s.DataBaixa))
                .ForMember(dest => dest.CodigoTransacao, opts => opts.MapFrom(s => s.IdEndToEnd ?? ""))
                .ForMember(dest => dest.JsonEnvio, opts => opts.MapFrom(s => s.JsonEnvioDock ?? ""))
                .ForMember(dest => dest.JsonResposta, opts => opts.MapFrom(s => s.JsonRespostaDock ?? ""))
                .ForMember(dest => dest.MensagemErro, opts => opts.MapFrom(s => ""));
            
            CreateMap<Viagem, PagamentoViagemIntegrarRequest>()
                .ForMember(dest => dest.CpfCnpjContratado, opts => opts.MapFrom(s => s.PortadorProprietario.CpfCnpj))
                .ForMember(dest => dest.CpfMotorista, opts => opts.MapFrom(s => s.PortadorMotorista.CpfCnpj))
                .ForMember(dest => dest.NomeContratado, opts => opts.MapFrom(s => s.PortadorProprietario.Nome))
                .ForMember(dest => dest.NomeMotorista, opts => opts.MapFrom(s => s.PortadorMotorista.Nome))
                .ForMember(dest => dest.IbgeOrigem, opts => opts.MapFrom(s => s.CidadeOrigem.Ibge))
                .ForMember(dest => dest.IbgeDestino, opts => opts.MapFrom(s => s.CidadeDestino.Ibge));
        }
    }
}
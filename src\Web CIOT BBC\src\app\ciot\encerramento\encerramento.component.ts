import { AfterViewInit, Component, ElementRef, EventEmitter, OnInit, Output, ViewChildren } from '@angular/core';
import { FormBuilder, FormControlName, FormGroup, Validators } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";

import { Observable } from "rxjs/Observable";
import 'rxjs/add/operator/debounceTime';
import 'rxjs/add/observable/fromEvent';
import 'rxjs/add/observable/merge';

import { GenericValidator } from "../../commom/generic.form.validator";
import { CiotService } from "../../ciot/services/ciot.service";
import { Subscription } from 'rxjs/Subscription';
import { DatePipe } from '@angular/common';
import { Viagem } from '../models/viagem';
import { ViagemEncerrar } from '../models/viagem';
import { Cidade } from '../models/cidade';
import { NaturezaCarga, NaturezaCargaEncerramento, NaturezaCargaResponse } from '../models/natureza-carga';
import { ConsultarSituacaoCiotReq } from "../models/consulta-ciot/consultar-situacao-ciot-request";
import { CiotResponse } from "../models/consulta-ciot/consulta-ciot-response";
import { Ciot } from "../models/ciot";
import { DadosEncerramento } from '../models/encerramento/dados-encerramento';
import {
    EncerrarOperacaoTransporteReq,
    EncerrarOperacaoTransporteResponse,
    ValoresEfetivos,
    ViagensOperacaoTransporte
} from '../models/encerramento/encerrar-operacao-transporte';
import { ToastsManager } from 'ng2-toastr';
import { AutoCompleteService } from '../services/autocomplete.service';
import { Mensagens, TipoViagem } from '../util/enums';
import { TypeaheadMatch } from 'ngx-bootstrap/typeahead';

import { BsDatepickerConfig, BsLocaleService } from 'ngx-bootstrap/datepicker';
import { defineLocale, listLocales } from 'ngx-bootstrap/chronos'
import { ptBrLocale } from 'ngx-bootstrap/locale';
import { ConsultarDadosEncerramentoReq } from '../models/encerramento/obj-encerrar';

@Component({
    selector: 'app-encerramento',
    templateUrl: './encerramento.component.html',
    styles: []
})
export class EncerramentoComponent implements OnInit, AfterViewInit {

    locale = ptBrLocale;
    locales = listLocales();

    @ViewChildren(FormControlName, { read: ElementRef }) formInputElements: ElementRef[];

    bsConfig: Partial<BsDatepickerConfig>;

    public errors: any[] = [];
    public encerramentoForm: FormGroup;
    public viagemForm: FormGroup;
    public displayMessage: { [key: string]: string } = {};
    private validationMessages: { [key: string]: { [key: string]: string } };
    private genericValidator: GenericValidator;
    private title;
    public sub: Subscription;
    public value: Date = new Date(2000, 2, 10);
    public viagemList: Array<Viagem> = [];
    public viagemEncerrarList: Array<ViagemEncerrar> = [];
    public viagem: Viagem;
    public cidadeOrigem: Cidade;
    public cidadeDestino: Cidade;
    public naturezaCarga: NaturezaCargaEncerramento;
    private request: EncerrarOperacaoTransporteReq;
    public ciotEncerramento: Ciot;
    private viagensOperacaoTransporte: ViagensOperacaoTransporte;
    public tipoViagemDescricao: string;
    public tipoViagem: number;

    ciotId: string;
    senha: string;
    @Output() resetForm = new EventEmitter();
    public codigoCidadeOrigem: string = "";
    public codigoCidadeDestino: string = "";
    public codigoNatureza: string = "";
    podeDigitar: boolean = false;

    public cidadeList: Array<Cidade> = [];
    naturezaCargaList: Array<NaturezaCarga> = [];

    noResultOrigem = false;
    noResultDestino = false;
    noResultNatureza = false;

    cidadeOrigemCompleterText: string;
    cidadeDestinoCompleterText: string;
    naturezaCompleterText: string;


    dataInicio = new Date();
    dataFim = new Date();

    constructor(private fb: FormBuilder,
        private service: CiotService,
        private autoCompleteService: AutoCompleteService,
        private router: Router,
        private route: ActivatedRoute,
        private toastr: ToastsManager,
        private localeService: BsLocaleService) {

        defineLocale("ptbr", ptBrLocale)

        this.bsConfig = Object.assign({}, { containerClass: "theme-red" });
        this.localeService.use("ptbr");

        this.service.consultarCidades("")
            .subscribe(
                result => {
                    if (result.sucesso && result.cidades) {
                        this.cidadeList = result.cidades;
                        this.podeDigitar = true;
                    } else {
                        this.cidadeList = [];
                        console.error('Erro ao consultar cidades:', result.excecao);
                    }
                },
                error => {
                    this.cidadeList = [];
                    console.error('Erro na requisição:', error);
                }
            );
        this.service.consultarNaturezaCarga("")
            .subscribe(
                result => {
                    if (result.sucesso && result.naturezasCarga) {
                        this.naturezaCargaList = result.naturezasCarga;
                    } else {
                        this.naturezaCargaList = []; 
                        console.error('Erro ao consultar natureza de carga:', result.excecao);
                    }
                },
                error => {
                    this.naturezaCargaList = [];
                    console.error('Erro na requisição:', error);
                }
            );

        this.validationMessages = {
            cidadeOrigem: {
                required: 'A cidade origem do contratante é obrigatório.'
            },
            cidadeDestino: {
                required: 'A cidade destino é obrigatório.',
            },
            naturezaCarga: {
                required: 'A natureza da carga é obrigatório.',
            },
            valorFrete: {
                required: 'O valor do frete é obrigatório.',
            },
            quantidadeTarifas: {
                required: 'A quantidade de tarifas é obrigatório.',
            },
            valorTotalTarifas: {
                required: 'O valor total das tarifas é obrigatório.',
            },
            placa: {
                required: 'O valor total das tarifas é obrigatório.',
            },
            rntrcVeiculo: {
                required: 'O valor total das tarifas é obrigatório.',
            }
        };
        this.genericValidator = new GenericValidator(this.validationMessages);
    }

    // AutoComplete métodos
    typeaheadNoResults(event: boolean): void {
        if (this.lastInputFocused === 'origem') {
            this.noResultOrigem = event;
            if (event) this.cidadeOrigem = null;
        } else if (this.lastInputFocused === 'destino') {
            this.noResultDestino = event;
            if (event) this.cidadeDestino = null;
        }
    }

    private lastInputFocused: string = '';

    onFocusCidadeOrigem() {
        this.lastInputFocused = 'origem';
    }

    onFocusCidadeDestino() {
        this.lastInputFocused = 'destino';
    }

    onSelectCidade(event: TypeaheadMatch): void {
        if (this.lastInputFocused === 'origem') {
            this.cidadeOrigem = new Cidade();
            this.cidadeOrigem.nome = event.item.nome;
            this.cidadeOrigem.uf = event.item.uf;
            this.cidadeOrigem.idCidade = event.item.idCidade;
            this.cidadeOrigem.estadoId = event.item.estadoId;
            this.cidadeOrigemCompleterText = `${event.item.nome} - ${event.item.uf}`;
        } else if (this.lastInputFocused === 'destino') {
            this.cidadeDestino = new Cidade();
            this.cidadeDestino.nome = event.item.nome;
            this.cidadeDestino.uf = event.item.uf;
            this.cidadeDestino.idCidade = event.item.idCidade;
            this.cidadeDestino.estadoId = event.item.estadoId;
            this.cidadeDestinoCompleterText = `${event.item.nome} - ${event.item.uf}`;
        }
    }

    onSelectNatureza(event: TypeaheadMatch): void {
        this.naturezaCarga = event.item;
    }

    consultarEncerramento() {
        this.ciotEncerramento = new Ciot();
        let a = new ConsultarSituacaoCiotReq();
        a.ciot = this.ciotId;
        a.senhaAlteracao = this.senha;

        this.service.consultarCiot(a)
            .subscribe(
                response => this.preencherDadosInformativos(response, a.ciot, a.senhaAlteracao),
                error => {
                    this.onError(error)
                }
            );
    }

    preencherDadosInformativos(ciotResponse: CiotResponse, ciotId: string, senha: string): void {

        this.ciotEncerramento.totalImposto = ciotResponse.totalImposto;
        this.ciotEncerramento.rntrc = ciotResponse.rntrcProprietario;
        this.ciotEncerramento.contratado = ciotResponse.nomeProprietario;
        this.ciotEncerramento.ciot = ciotResponse.ciot;
        this.ciotEncerramento.valorFrete = ciotResponse.valorFrete;
        this.ciotEncerramento.valorFretePago = ciotResponse.valorFrete;
        this.ciotEncerramento.totalPedagio = ciotResponse.totalPegadio;
        this.ciotEncerramento.encerrada = ciotResponse.encerrado;
        this.ciotEncerramento.valorDespesas = ciotResponse.valorDespesas;
        this.tipoViagemDescricao = Number(TipoViagem.Padrao) == ciotResponse.tipoViagem ? Mensagens.PADRAO : Mensagens.TAC_AGREGADO;
        this.tipoViagem = ciotResponse.tipoViagem

        var objRequest = new ConsultarDadosEncerramentoReq();
        objRequest.ciot = ciotId;
        objRequest.senhaAlteracao = senha;
        this.service.consultarEncerramento(objRequest)
            .subscribe(
                result => this.preencherDadosEncerrar(result))
        error => {
            this.onError(error)
        }
    }

    preencherDadosEncerrar(dadosEncerramento: DadosEncerramento): void {
        this.encerramentoForm.markAsDirty;
        this.dataInicio = new Date(new DatePipe('en-US').transform(String(dadosEncerramento.dataInicioViagem).substr(0, 10), "MM/dd/yyyy"));
        var dataFinal = new Date(new DatePipe('en-US').transform(String(dadosEncerramento.dataFimViagem).substr(0, 10), "MM/dd/yyyy"));
        var dataAgora = new Date();

        if (dataFinal > dataAgora)
            this.dataFim = dataAgora;
        else
            this.dataFim = dataFinal;

        this.codigoCidadeOrigem = dadosEncerramento.codigoMunicipioOrigem;
        this.codigoCidadeDestino = dadosEncerramento.codigoMunicipioDestino;

        this.codigoNatureza = dadosEncerramento.codigoNaturezaCarga;
        this.ciotEncerramento.ciot = dadosEncerramento.ciot;
        this.ciotEncerramento.pesoCarga = dadosEncerramento.pesoCarga ? dadosEncerramento.pesoCarga : 0;
        this.ciotEncerramento.senha = dadosEncerramento.senhaAlteracao;
        this.ciotEncerramento.tipoViagem = Number(dadosEncerramento.tipoViagem);

        this.ciotEncerramento.quantidadeTarifas = dadosEncerramento.quantidadeTarifas;
        this.ciotEncerramento.valorTotalTarifas = dadosEncerramento.valorTarifas;
        if (Number(TipoViagem.Padrao) == Number(dadosEncerramento.tipoViagem)) {
            this.encerramentoForm.controls['totalImposto'].disable();
            this.encerramentoForm.controls['totalPedagio'].disable();
            this.encerramentoForm.controls['valorFretePago'].disable();
            this.encerramentoForm.controls['valorDespesas'].disable();
        }

        this.encerramentoForm.patchValue(this.ciotEncerramento);

        this.service.consultarNaturezaCargaById(this.codigoNatureza)
            .subscribe(
                result => {
                    this.naturezaCarga = result;
                    if (this.naturezaCarga) {
                        this.naturezaCompleterText = this.naturezaCarga.descricao;
                    }
                },
                error => {
                    this.onError(error)
                }
            );
    }

    adicionarViagem() {
        if (this.viagemForm.dirty && this.viagemForm.valid) {
            if (!this.cidadeOrigem || !this.cidadeDestino || !this.naturezaCarga) {
                this.toastr.error("Preencha todos os campos obrigatórios", "Erro");
                return;
            }

            this.viagem = new Viagem();
            
            this.viagem.cidadeOrigem = this.cidadeOrigem;
            this.viagem.cidadeDestino = this.cidadeDestino;
            this.viagem.naturezaCarga = this.naturezaCarga;
            this.viagem.pesoCarga = this.viagemForm.value.pesoCarga;
            this.viagem.qtdViagens = this.viagemForm.value.qtdViagens;

            for (let index = 0; index < this.viagemList.length; index++) {
                if (this.viagemList[index].cidadeDestino.idCidade == this.viagem.cidadeDestino.idCidade
                    && this.viagemList[index].cidadeOrigem.idCidade == this.viagem.cidadeOrigem.idCidade
                    && this.viagemList[index].naturezaCarga.codigo == this.viagem.naturezaCarga.codigo) {
                    this.toastr.error("Esta viagem já foi adicionada", "Oops!");
                    return;
                }
            }

            this.viagemList.push(this.viagem);
            console.log('Viagem adicionada:', this.viagem);
            console.log('Lista de viagens:', this.viagemList);
            
            this.viagemForm.reset();
            this.encerramentoForm.markAsDirty();

            this.cidadeDestinoCompleterText = null;
            this.cidadeDestino = null;

            this.cidadeOrigemCompleterText = null;
            this.cidadeOrigem = null;

            this.naturezaCompleterText = null;
            this.naturezaCarga = null;
        } else {
            this.toastr.error("Preencha todos os campos corretamente", "Erro");
            
            Object.keys(this.viagemForm.controls).forEach(key => {
                const control = this.viagemForm.get(key);
                control.markAsTouched();
            });
        }
    }

    removerViagem(viagem) {
        console.log('Removendo viagem:', viagem);
        console.log('Lista antes da remoção:', [...this.viagemList]);
        
        const index = this.viagemList.findIndex(v => 
            v.cidadeOrigem.idCidade === viagem.cidadeOrigem.idCidade && 
            v.cidadeDestino.idCidade === viagem.cidadeDestino.idCidade &&
            v.naturezaCarga.codigo === viagem.naturezaCarga.codigo
        );
        
        if (index !== -1) {
            this.viagemList.splice(index, 1);
            console.log('Viagem removida com sucesso');
        } else {
            console.log('Viagem não encontrada na lista');
        }
        
        console.log('Lista após remoção:', [...this.viagemList]);
    }

    ngOnInit() {
        this.encerramentoForm = this.fb.group({
            ciot: ['', [Validators.required]],

            rntrc: ['',],
            contratado: ['',],
            valorFrete: ['',],
            totalImposto: ['',],
            totalPedagio: ['',],
            tipoViagem: ['',],
            senha: ['',],
            encerrada: ['',],
            pesoCarga: ['', [Validators.required]],
            qtdViagens: ['', [Validators.required]],
            quantidadeTarifas: ['',],
            valorTotalTarifas: ['',],
            valorFretePago: ['',],
            valorDespesas: ['',]

            // documentoContratante: ['', [Validators.required, Validators.minLength(11), Validators.maxLength(14)]],
        });
        this.viagemForm = this.fb.group({
            pesoCarga: ['', [Validators.required, Validators.maxLength(5)]],
            qtdViagens: ['', [Validators.required]],
        });
        this.viagem = new Viagem();

        this.sub = this.route.params.subscribe(
            params => {
                this.ciotId = params['ciot'];
                this.senha = params['senha'];
                this.consultarEncerramento();
            });
    }

    ngAfterViewInit(): void {
        let controlBlurs: Observable<any>[] = this.formInputElements
            .map((formControl: ElementRef) => Observable.fromEvent(formControl.nativeElement, 'blur'));

        Observable.merge(...controlBlurs).subscribe(value => {
            this.displayMessage = this.genericValidator.processMessages(this.encerramentoForm);
        });
    }

    encerrar() {
        this.request = new EncerrarOperacaoTransporteReq();
        this.request.valoresEfetivos = new ValoresEfetivos();
        this.request.ciot = this.ciotId;
        this.request.pesoCarga = this.encerramentoForm.value.pesoCarga;
        this.request.valoresEfetivos.quantidadeTarifas = this.encerramentoForm.value.quantidadeTarifas;
        this.request.senhaAlteracao = this.senha;
        this.request.valoresEfetivos.valorTarifas = this.encerramentoForm.value.valorTotalTarifas;

        let viagensOperacaoTransporteList = new Array<ViagensOperacaoTransporte>();
        this.viagemList.forEach(element => {
            let viagensOperacaoTransporte = new ViagensOperacaoTransporte();
            viagensOperacaoTransporte.codigoMunicipioOrigem = element.cidadeOrigem.idCidade;
            viagensOperacaoTransporte.codigoMunicipioDestino = element.cidadeDestino.idCidade;
            viagensOperacaoTransporte.codigoNaturezaCarga = element.naturezaCarga.codigo;
            viagensOperacaoTransporte.pesoCarga = element.pesoCarga;
            viagensOperacaoTransporte.quantidadeViagens = element.qtdViagens;
            viagensOperacaoTransporteList.push(viagensOperacaoTransporte);
        });

        this.request.viagensOperacaoTransporte = viagensOperacaoTransporteList;

        this.service.encerrarOperacaoTransporte(this.request)
            .subscribe(
                response => this.onSaveComplete(response),
                error => {
                    this.onError(error)
                }
            );
    }

    onSaveComplete(response: EncerrarOperacaoTransporteResponse): void {
        if (response.sucesso) {
            this.router.navigate(['/']).then(() => this.toastr.success('CIOT encerrado com sucesso!', Mensagens.SUCESSO));
        } else {
            if (response.excecao != null && response.excecao.Mensagem) {
                this.toastr.error(response.excecao.Mensagem, Mensagens.OOPS);
            } else {
                this.toastr.error('Ocorreu um erro ao encerrar o CIOT', Mensagens.OOPS);
            }
        }
    }

    onError(serviceReturn) {
        this.errors = Object.assign([], serviceReturn.error.errors);
    }

    voltar() {
        this.router.navigate(['/']);
    }
}

using BBC.Test.Tests.StatusVeiculoEnum.Fixture;
using SistemaInfo.BBC.Domain.Enum;
using System.ComponentModel;
using System.Reflection;
using Xunit;

namespace BBC.Test.Tests.StatusVeiculoEnum
{
    [Collection(nameof(StatusVeiculoEnumCollection))]
    public class StatusVeiculoEnumTest
    {
        private readonly StatusVeiculoEnumFixture _fixture;

        public StatusVeiculoEnumTest(StatusVeiculoEnumFixture fixture)
        {
            _fixture = fixture;
        }

        [Theory(DisplayName = "StatusVeiculo - deve conter valores corretos")]
        [Trait("StatusVeiculoEnum", "ValoresEnum")]
        [InlineData(0, "Bloqueado")]
        [InlineData(1, "Ativo")]
        public void StatusVeiculo_DeveConterValoresCorretos(int valorEsperado, string nomeEnum)
        {
            // Arrange & Act
            var enumValue = System.Enum.Parse<StatusVeiculo>(nomeEnum);

            // Assert
            Assert.Equal(valorEsperado, (int)enumValue);
        }

        [Theory(DisplayName = "StatusVeiculo - deve conter descrições corretas")]
        [Trait("StatusVeiculoEnum", "DescricoesEnum")]
        [InlineData("Bloqueado", "Bloqueado")]
        [InlineData("Ativo", "Ativo")]
        public void StatusVeiculo_DeveConterDescricoesCorretas(string nomeEnum, string descricaoEsperada)
        {
            // Arrange
            var enumValue = System.Enum.Parse<StatusVeiculo>(nomeEnum);
            var field = typeof(StatusVeiculo).GetField(enumValue.ToString());
            var attribute = field.GetCustomAttribute<DescriptionAttribute>();

            // Act & Assert
            Assert.NotNull(attribute);
            Assert.Equal(descricaoEsperada, attribute.Description);
        }

        [Fact(DisplayName = "StatusVeiculo - deve conter dois valores")]
        [Trait("StatusVeiculoEnum", "QuantidadeValores")]
        public void StatusVeiculo_DeveConterDoisValores()
        {
            // Arrange & Act
            var valores = System.Enum.GetValues(typeof(StatusVeiculo));

            // Assert
            Assert.Equal(2, valores.Length);
        }

        [Theory(DisplayName = "StatusVeiculo - deve permitir conversão para string")]
        [Trait("StatusVeiculoEnum", "ConversaoString")]
        [InlineData("Bloqueado")]
        [InlineData("Ativo")]
        public void StatusVeiculo_DevePermitirConversaoParaString(string nomeEsperado)
        {
            // Arrange & Act
            var enumValue = System.Enum.Parse<StatusVeiculo>(nomeEsperado);
            var resultado = enumValue.ToString();

            // Assert
            Assert.Equal(nomeEsperado, resultado);
        }

        [Theory(DisplayName = "StatusVeiculo - deve permitir conversão de string")]
        [Trait("StatusVeiculoEnum", "ConversaoDeString")]
        [InlineData("Bloqueado", StatusVeiculo.Bloqueado)]
        [InlineData("Ativo", StatusVeiculo.Ativo)]
        public void StatusVeiculo_DevePermitirConversaoDeString(string nomeEnum, StatusVeiculo enumEsperado)
        {
            // Arrange & Act
            var resultado = System.Enum.Parse<StatusVeiculo>(nomeEnum);

            // Assert
            Assert.Equal(enumEsperado, resultado);
        }

        [Theory(DisplayName = "StatusVeiculo - deve verificar se valor é definido")]
        [Trait("StatusVeiculoEnum", "ValorDefinido")]
        [InlineData(0, true)]  // Bloqueado
        [InlineData(1, true)]  // Ativo
        [InlineData(999, false)] // Valor inválido
        public void StatusVeiculo_DeveVerificarSeValorEDefinido(int valor, bool esperado)
        {
            // Arrange & Act
            var resultado = System.Enum.IsDefined(typeof(StatusVeiculo), valor);

            // Assert
            Assert.Equal(esperado, resultado);
        }

        [Fact(DisplayName = "StatusVeiculo - deve usar fixture para obter valores")]
        [Trait("StatusVeiculoEnum", "Fixture")]
        public void StatusVeiculo_DeveUsarFixtureParaObterValores()
        {
            // Arrange & Act
            var ativo = _fixture.ObterStatusVeiculoAtivo();
            var bloqueado = _fixture.ObterStatusVeiculoBloqueado();

            // Assert
            Assert.Equal(StatusVeiculo.Ativo, ativo);
            Assert.Equal(StatusVeiculo.Bloqueado, bloqueado);
        }
    }
}

﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using SistemaInfo.BBC.Application.Objects.Api.Viagem;
using SistemaInfo.BBC.Application.Objects.Mobile.Viagem.Response;
using SistemaInfo.BBC.Application.Objects.Web.Viagem;
using SistemaInfo.BBC.Application.Resolver;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Domain.External.CIOT.DTO;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.BBC.Domain.Models.PagamentoEvento;
using SistemaInfo.BBC.Domain.Models.PagamentoEvento.Commands;
using SistemaInfo.BBC.Domain.Models.Transacao;
using SistemaInfo.BBC.Domain.Models.Viagem;
using SistemaInfo.BBC.Domain.Models.Viagem.Commands;
using SistemaInfo.BBC.Domain.Models.ViagemVeiculos;
using SistemaInfo.BBC.Domain.Models.ViagemVeiculos.Commands;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.AutoMapper.ViagemMapper
{
    public class ViagemMappingProfile : SistemaInfoMappingProfile
    {
        public ViagemMappingProfile()
        {
            CreateMap<PagamentoViagemIntegrarRequest, ViagemSalvarCommand>()
                .ForMember(a => a.FilialId, opts => opts.MapFrom(d => d.FilialId))
                .ForMember(a => a.TipoConta, opts => opts.MapFrom(d => d.TipoConta.GetHashCode()));

            CreateMap<PagamentoViagemIntegrarRequest, ViagemSalvarComRetornoCommand>()
                .ForMember(a => a.FilialId, opts => opts.MapFrom(d => d.FilialId))
                .ForMember(a => a.Status, opts => opts.MapFrom(d => StatusViagem.Aberto))
                .ForMember(a => a.NomeProprietario, opts => opts.MapFrom(d => d.NomeContratado))
                .ForMember(a => a.TipoConta, opts => opts.MapFrom(d => d.TipoConta.GetHashCode()));

            CreateMap<PagamentoViagemIntegrarRequest, PagamentoEventoSalvarComRetornoCommand>()
                .ForMember(a => a.TipoConta, opts => opts.MapFrom(d => d.TipoConta.GetHashCode()))
                .ForMember(a => a.Status, opts => opts.MapFrom(d => StatusPagamento.Aberto));

            CreateMap<ViagemSalvarCommand, Viagem>()
                .ForMember(a => a.FilialId, opts => opts.MapFrom(d => d.FilialId));

            CreateMap<Viagem, EncerrarOperacaoTransporteViagem>()
                .ForMember(dest => dest.CodigoMunicipioOrigem, opt => opt.MapFrom(src => src.CidadeOrigem.Ibge ?? 0))
                .ForMember(dest => dest.CodigoMunicipioDestino, opt => opt.MapFrom(src => src.CidadeDestino.Ibge ?? 0))
                .ForMember(dest => dest.QuantidadeViagens, opt => opt.MapFrom(src => 1))
                .ForMember(a => a.CodigoNaturezaCarga, opts => opts.MapFrom(d => d.CodigoNaturezaCarga))
                .ForMember(dest => dest.PesoCarga, opts => opts.MapFrom(src => Math.Round(src.PesoCarga ?? 0, 2)));

            CreateMap<ViagemSalvarComRetornoCommand, Viagem>()
                .ForMember(a => a.FilialId, opts => opts.MapFrom(d => d.FilialId));
            ;

            CreateMap<Viagem, ViagemConsultarGridItem>()
                .ForMember(d => d.DataBaixa,
                    opts => opts.MapFrom(src => src.DataBaixa != null ? src.DataBaixa.ToDateTime().ToString("G") : ""))
                .ForMember(d => d.DataCadastro, opts => opts.MapFrom(src => src.DataCadastro.ToString("G")))
                .ForMember(d => d.DataAlteracao,
                    opts => opts.MapFrom(src =>
                        src.DataAlteracao != null ? src.DataAlteracao.ToDateTime().ToString("G") : ""))
                .ForMember(d => d.Status, opts => opts.MapFrom(src => src.Status.ToString()))
                .ForMember(d => d.FilialId, opts => opts.MapFrom(src => src.FilialId))
                .ForMember(d => d.CpfCnpjMotorista,
                    opts => opts.MapFrom(src => src.PortadorMotorista.CpfCnpj.ToCpfOrCnpj()))
                .ForMember(d => d.CpfCnpjProprietario,
                    opts => opts.MapFrom(src => src.PortadorProprietario.CpfCnpj.ToCpfOrCnpj()))
                .ForMember(d => d.CnpjEmpresa, opts => opts.MapFrom(src => src.Empresa.Cnpj.ToCpfOrCnpj()))
                .ForMember(d => d.RazaoSocialEmpresa, opts => opts.MapFrom(src => src.Empresa.RazaoSocial))
                .ForMember(d => d.Ciot,
                    opts => opts.MapFrom(src =>
                        src.Ciot.IsNullOrWhiteSpace()
                            ? null
                            : src.Ciot + "/" +
                              (src.VerificadorCiot.IsNullOrWhiteSpace() ? "XXXX" : src.VerificadorCiot)))
                .ForMember(d => d.ViagemId, opts => opts.MapFrom(src => src.Id))
                .ForMember(d => d.ViagemExternoId, opts => opts.MapFrom(src => src.ViagemExternoId))
                .ForMember(d => d.NaturezaCarga, opts => opts.MapFrom(src => src.CodigoNaturezaCarga))
                .ForMember(d => d.PesoCarga, opts => opts.MapFrom(src => src.PesoCarga.ToString()))
                ;

            CreateMap<ViagemAlterarStatusRequest, ViagemAlterarStatusCommand>();

            CreateMap<ViagemVeiculosSalvarCommand, ViagemVeiculos>();

            CreateMap<Viagem, ConsultarViagemCiotComboResponse>()
                .ForMember(a => a.CodigoExternoId, opts => opts.MapFrom(d => d.ViagemExternoId))
                .ForMember(a => a.NomeDestino, opts => opts.MapFrom(d => d.CidadeDestino.Nome))
                .ForMember(a => a.NomeOrigem, opts => opts.MapFrom(d => d.CidadeOrigem.Nome))
                .ForMember(a => a.IdMunicipioOrigem, opts => opts.MapFrom(d => d.CidadeOrigem.Id.ToIntSafe(0)))
                .ForMember(a => a.IdMunicipioDestino, opts => opts.MapFrom(d => d.CidadeDestino.Id.ToIntSafe(0)))
                .ForMember(a => a.CodigoMunicipioOrigem, opts => opts.MapFrom(d => d.CidadeOrigem.Ibge.ToIntSafe(0)))
                .ForMember(a => a.CodigoMunicipioDestino, opts => opts.MapFrom(d => d.CidadeDestino.Ibge.ToIntSafe(0)))
                .ForMember(a => a.CodigoNaturezaCarga, opts => opts.MapFrom(d => d.CodigoNaturezaCarga))
                .ForMember(a => a.PesoCarga, opts => opts.MapFrom(d => d.PesoCarga))
                .ForMember(a => a.ValorFrete, opts =>
                    opts.MapFrom(d => d.PagamentoEvento.SelectMany(x => x.Transacao)
                        .Where(x => x.Tipo != Tipo.Tarifas && x.Status == StatusPagamento.Fechado).Sum(x => x.Valor)
                        .FormatMonetario()));

            CreateMap<Viagem, ConsultarViagensCiotGridResponse>()
                .ForMember(a => a.NomeDestino, opts => opts.MapFrom(d => d.CidadeDestino.Nome))
                .ForMember(a => a.Id, opts => opts.MapFrom(d => d.Id))
                .ForMember(a => a.NomeOrigem, opts => opts.MapFrom(d => d.CidadeOrigem.Nome))
                .ForMember(a => a.CodigoMunicipioOrigem, opts => opts.MapFrom(d => d.CidadeOrigem.Ibge.ToIntSafe(0)))
                .ForMember(a => a.CodigoMunicipioDestino, opts => opts.MapFrom(d => d.CidadeDestino.Ibge.ToIntSafe(0)))
                .ForMember(a => a.CodigoNaturezaCarga, opts => opts.MapFrom(d => d.CodigoNaturezaCarga))
                .ForMember(a => a.Peso, opts => opts.MapFrom(d => d.PesoCarga))
                .ForMember(a => a.ValorFrete, opts => opts.MapFrom(d => d.PagamentoEvento.SelectMany(x => x.Transacao)
                    .Where(x => x.Tipo != Tipo.Tarifas).Sum(x => x.Valor).FormatMonetario()));

            CreateMap<Viagem, ViagemResponseItem>()
                .ForMember(a => a.ViagemId, opts => opts.MapFrom(d => d.Id))
                .ForMember(a => a.RazaoSocialEmpresa, opts => opts.MapFrom(d => d.Empresa.RazaoSocial))
                .ForMember(a => a.CnpjEmpresa, opts => opts.MapFrom(d => d.Empresa.Cnpj.FormatarCpfCnpj(false)))
                .ForMember(a => a.DataBaixa, opts => opts.MapFrom(d => d.DataBaixa))
                .ForMember(a => a.DataCadastro, opts => opts.MapFrom(d => d.DataCadastro))
                .ForMember(a => a.DataAlteracao, opts => opts.MapFrom(d => d.DataAlteracao))
                .ForMember(a => a.FilialId, opts => opts.MapFrom(d => d.FilialId))
                .ForMember(a => a.NomeProprietario, opts => opts.MapFrom(d => d.NomeProprietario))
                .ForMember(a => a.NomeMotorista, opts => opts.MapFrom(d => d.NomeMotorista))
                .ForMember(a => a.CpfCnpjProprietario, opts => opts.MapFrom(d => d.PortadorProprietario.CpfCnpj.FormatarCpfCnpj(false)))
                .ForMember(a => a.CpfCnpjMotorista, opts => opts.MapFrom(d => d.PortadorMotorista.CpfCnpj.FormatarCpfCnpj(false)))
                .ForMember(a => a.Status, opts => opts.MapFrom(d => d.Status.ToString()))
                .ForMember(a => a.CidadeOrigem, opts => opts.MapFrom(d => d.CidadeOrigem.Nome))
                .ForMember(a => a.CidadeDestino, opts => opts.MapFrom(d => d.CidadeDestino.Nome))
                .ForMember(a => a.CidadeIbgeOrigem, opts => opts.MapFrom(d => d.CidadeOrigem.Ibge))
                .ForMember(a => a.CidadeIbgeDestino, opts => opts.MapFrom(d => d.CidadeDestino.Ibge))
                .ForMember(a => a.Ciot, opts => opts.MapFrom(d => d.Ciot));

            CreateMap<ViagemIntegrarV2Request, ViagemSalvarCommand>()
                .ForMember(a => a.FilialId, opts => opts.MapFrom(d => d.Filial));

            CreateMap<ViagemIntegrarV2Request, ViagemSalvarComRetornoCommand>()
                .ForMember(a => a.FilialId, opts => opts.MapFrom(d => d.Filial))
                .ForMember(a => a.Status, opts => opts.MapFrom(d => StatusViagem.Aberto))
                .ForMember(a => a.PesoCarga, opts => opts.MapFrom(d => d.PesoCarga))
                .ForMember(a => a.TipoCiot, opts => opts.MapFrom(d => d.TipoViagem == 1 ? TipoCiot.Padrão : TipoCiot.Agregado))
                .ForMember(a => a.VersaoIntegracaoViagem, opt => opt.Ignore())
                
                .ForMember(a => a.ValorComplemento, opts => opts.MapFrom(d => d.Valores.Complementos))
                .ForMember(a => a.ValorFrete, opts => opts.MapFrom(d => d.Valores.ValorFrete))
                .ForMember(a => a.ValorSaldo, opts => opts.MapFrom(d => d.Valores.Saldo))
                .ForMember(a => a.ValorAdiantamento, opts => opts.MapFrom(d => d.Valores.Adiantamentos))
                .ForMember(a => a.QuantidadeTarifas, opts => opts.MapFrom(d => d.Valores.QuantidadeTarifas))
                .ForMember(a => a.ValorTarifas, opts => opts.MapFrom(d => d.Valores.ValorTarifas))
                .ForMember(a => a.ValorCombustivel, opts => opts.MapFrom(d => d.Valores.ValorCombustivel))
                .ForMember(a => a.ValorDespesa, opts => opts.MapFrom(d => d.Valores.ValorDespesas))
                .ForMember(a => a.TotalImposto, opts => opts.MapFrom(d => d.Valores.TotalImposto))
                .ForMember(a => a.TotalPedagio, opts => opts.MapFrom(d => d.Valores.TotalPegadio))
                .ForMember(a => a.CodigoNaturezaCarga, opts => opts.MapFrom(d => d.CodigoNaturezaCarga))
                .ForMember(a => a.NomeMotorista, opts => opts.MapFrom(d => d.Motorista.NomeRazaoSocial))
                .ForMember(a => a.NomeProprietario, opts => opts.MapFrom(d => d.Proprietario.NomeRazaoSocial))
                ;
       
           
            CreateMap<ViagemIntegrarV2Request, DeclararOperacaoTransporteReq>()
                .ForMember(dest => dest.Frete, opt => opt.MapFrom(src => src))
                .ForMember(dest => dest.Contratante, opt => opt.MapFrom(src => src.Contratante))
                .ForMember(dest => dest.Remetente, opt => opt.MapFrom(src => src.Remetente))
                .ForMember(dest => dest.Destinatario, opt => opt.MapFrom(src => src.Destinatario))
                .ForMember(dest => dest.Veiculos, opt => opt.MapFrom(src => src.Veiculos))
                .ForMember(dest => dest.Valores, opt => opt.MapFrom(src => src.Valores))
                .ForMember(dest => dest.Pagamento, opt => opt.ResolveUsing<PagamentoResolver>())
                .AfterMap((src, dest) =>
                {
                    if (src.DeclaraCiotNormal())
                    {
                        dest.Frete.PesoCarga = src.PesoCarga;
                        dest.Frete.CodigoNaturezaCarga = src.CodigoNaturezaCarga;
                    }
                    else
                    {
                        dest.Frete.CodigoMunicipioDestino = null;
                        dest.Frete.CodigoMunicipioOrigem = null;
                        dest.Remetente = null;
                        dest.Destinatario = null;
                        dest.Frete.DataInicioFrete = null;
                    }
                });
            
            CreateMap<ViagemIntegrarV2Request, Frete>()
                .ForMember(dest => dest.CodigoMunicipioOrigem,
                    opt => opt.MapFrom(src => (long?)src.CodigoMunicipioOrigem))
                .ForMember(dest => dest.CodigoMunicipioDestino,
                    opt => opt.MapFrom(src => (long?)src.CodigoMunicipioDestino))
                .ForMember(dest => dest.DataInicioFrete, opt => opt.MapFrom(src => (DateTime?)src.DataInicioFrete))
                .ForMember(dest => dest.DataTerminoFrete, opt => opt.MapFrom(src => src.DataTerminoFrete))
                .ForMember(dest => dest.CodigoNaturezaCarga, opt => opt.Ignore())
                .ForMember(dest => dest.PesoCarga, opt => opt.Ignore())
                .ForMember(dest => dest.TipoViagem, opt => opt.MapFrom(src => src.TipoViagem ?? 1))
                .ForMember(dest => dest.SubContratacao, opt => opt.MapFrom(src => false))
                .ForMember(dest => dest.Proprietario, opt => opt.MapFrom(src => src.Proprietario))
                .ForMember(dest => dest.Motorista, opt => opt.MapFrom(src => src.Motorista));
            
            CreateMap<PortadorV2Request, Proprietario>()
                .ForMember(dest => dest.NomeRazaoSocial, opt => opt.MapFrom(src => src.NomeRazaoSocial))
                .ForMember(dest => dest.CpfCnpj, opt => opt.MapFrom(src => src.CpfCnpj.OnlyNumbers()))
                .ForMember(dest => dest.TipoPessoa, opt => opt.MapFrom(src => src.TipoPessoa))
                .ForMember(dest => dest.RNTRC, opt => opt.MapFrom(src => src.RNTRC))
                .ForMember(dest => dest.Endereco, opt => opt.MapFrom(src => src.Endereco));
            
            CreateMap<PortadorV2Request, Motorista>()
                .ForMember(dest => dest.Nome, opt => opt.MapFrom(src => src.NomeRazaoSocial))
                .ForMember(dest => dest.CpfCnpj, opt => opt.MapFrom(src => src.CpfCnpj.OnlyNumbers()));
            
            CreateMap<PortadorV2Request, Contratante>()
                .ForMember(dest => dest.NomeRazaoSocial, opt => opt.MapFrom(src => src.NomeRazaoSocial))
                .ForMember(dest => dest.CpfCnpj, opt => opt.MapFrom(src => src.CpfCnpj.OnlyNumbers()))
                .ForMember(dest => dest.TipoPessoa, opt => opt.MapFrom(src => src.TipoPessoa))
                .ForMember(dest => dest.RNTRC, opt => opt.MapFrom(src => src.RNTRC))
                .ForMember(dest => dest.Endereco, opt => opt.MapFrom(src => src.Endereco));
            
            CreateMap<PortadorV2Request, Remetente>()
                .ForMember(dest => dest.NomeRazaoSocial, opt => opt.MapFrom(src => src.NomeRazaoSocial))
                .ForMember(dest => dest.CpfCnpj, opt => opt.MapFrom(src => src.CpfCnpj.OnlyNumbers()))
                .ForMember(dest => dest.TipoPessoa, opt => opt.MapFrom(src => src.TipoPessoa))
                .ForMember(dest => dest.Endereco, opt => opt.MapFrom(src => src.Endereco));
            
            CreateMap<PortadorV2Request, Destinatario>()
                .ForMember(dest => dest.NomeRazaoSocial, opt => opt.MapFrom(src => src.NomeRazaoSocial))
                .ForMember(dest => dest.CpfCnpj, opt => opt.MapFrom(src => src.CpfCnpj.OnlyNumbers()))
                .ForMember(dest => dest.TipoPessoa, opt => opt.MapFrom(src => src.TipoPessoa))
                .ForMember(dest => dest.Endereco, opt => opt.MapFrom(src => src.Endereco));
            
            CreateMap<EnderecoV2Request, Endereco>()
                .ForMember(dest => dest.CEP, opt => opt.MapFrom(src => src.Cep.OnlyNumbers()))
                .ForMember(dest => dest.CodigoMunicipio, opt => opt.MapFrom(src => (double?)src.CodigoMunicipio))
                .ForMember(dest => dest.Logradouro, opt => opt.MapFrom(src => src.Logradouro))
                .ForMember(dest => dest.Numero, opt => opt.MapFrom(src => src.Numero))
                .ForMember(dest => dest.Complemento, opt => opt.MapFrom(src => src.Complemento))
                .ForMember(dest => dest.Bairro, opt => opt.MapFrom(src => src.Bairro))
                .ForMember(dest => dest.Telefone, opt => opt.MapFrom(src => src.Telefone))
                .ForMember(dest => dest.Email, opt => opt.MapFrom(src => src.Email));
            
            CreateMap<VeiculoV2Request, Veiculo>()
                .ForMember(dest => dest.Placa, opt => opt.MapFrom(src => src.Placa))
                .ForMember(dest => dest.RNTRC, opt => opt.MapFrom(src => src.RNTRC));
            
            CreateMap<ValoresV2Request, ValoresFrete>()
                .ForMember(dest => dest.QuantidadeTarifas, opt => opt.MapFrom(src => src.QuantidadeTarifas ?? 0))
                .ForMember(dest => dest.ValorTarifas, opt => opt.MapFrom(src => src.ValorTarifas ?? 0))
                .ForMember(dest => dest.ValorCombustivel, opt => opt.MapFrom(src => src.ValorCombustivel ?? 0))
                .ForMember(dest => dest.ValorDespesas, opt => opt.MapFrom(src => src.ValorDespesas ?? 0))
                .ForMember(dest => dest.TotalImposto, opt => opt.MapFrom(src => src.TotalImposto ?? 0))
                .ForMember(dest => dest.TotalPegadio, opt => opt.MapFrom(src => src.TotalPegadio ?? 0))
                .ForMember(dest => dest.ValorFrete, opt => opt.MapFrom(src => src.ValorFrete));
            
            CreateMap<ViagemIntegrarV2Request, Pagamento>()
                .ForMember(dest => dest.FormaPagamento, opt => opt.MapFrom(src => 1)) // Padrão
                .ForMember(dest => dest.ParcelaUnica, opt => opt.MapFrom(src => true))
                .ForMember(dest => dest.Parcelas, opt => opt.Ignore()) // Será mapeado manualmente
                .ForMember(dest => dest.InfoPagamento, opt => opt.Ignore())
                .ForMember(dest => dest.BancoPagamento, opt => opt.Ignore())
                .ForMember(dest => dest.AgenciaPagamento, opt => opt.Ignore())
                .ForMember(dest => dest.ContaPagamento, opt => opt.Ignore());

            CreateMap<Viagem, CancelamentoInfoViagemV2Response>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.ViagemExternoId, opt => opt.MapFrom(src => src.ViagemExternoId ?? 0))
                .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Status.GetHashCode()))
                .ForMember(dest => dest.Ciot, opt => opt.MapFrom(src => src.Ciot))
                .ForMember(dest => dest.StatusCiot, opt => opt.MapFrom(src => src.StatusCiot.GetHashCode()))
                .ForMember(dest => dest.ValorFrete, opt => opt.MapFrom(src => Math.Round(src.ValorFrete ?? 0, 2)))
                .ForMember(dest => dest.PagamentoEventos, opt => opt.MapFrom(src =>
                    src.PagamentoEvento != null
                        ? src.PagamentoEvento.Where(pe => pe.Status == StatusPagamento.Cancelado)
                        : new List<PagamentoEvento>()));

            CreateMap<PagamentoEvento, CancelamentoViagemV2PagamentosEventoResponse>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.PagamentoExternoId, opt => opt.MapFrom(src => src.PagamentoExternoId ?? 0))
                .ForMember(dest => dest.StatusPagamento, opt => opt.MapFrom(src => src.Status.GetHashCode()))
                .ForMember(dest => dest.FormaPagamento, opt => opt.MapFrom(src => src.FormaPagamento.GetHashCode()))
                .ForMember(dest => dest.Tipo, opt => opt.MapFrom(src => src.Tipo.GetHashCode()))
                .ForMember(dest => dest.ValorParcela, opt => opt.MapFrom(src => Math.Round(src.Valor, 2)))
                .ForMember(dest => dest.ValorMotorista, opt => opt.MapFrom(src => Math.Round(src.ValorTransferenciaMotorista ?? 0, 2)))
                .ForMember(dest => dest.Transacoes, opt => opt.MapFrom(src =>
                    src.Transacao != null
                        ? src.Transacao.Where(tr => tr.Tipo != Tipo.Tarifas)
                        : new List<Transacao>()));

            CreateMap<Transacao, CancelamentoViagemV2TransacaoResponse>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.Valor, opt => opt.MapFrom(src => Math.Round(src.Valor, 2)))
                .ForMember(dest => dest.IdContaOrigem, opt => opt.MapFrom(src => src.Origem))
                .ForMember(dest => dest.IdContaDestino, opt => opt.MapFrom(src => src.Destino))
                .ForMember(dest => dest.DocumentoOrigem, opt => opt.MapFrom(src => GetDocumentoOrigem(src)))
                .ForMember(dest => dest.DocumentoDestino, opt => opt.MapFrom(src => GetDocumentoDestino(src)))
                .ForMember(dest => dest.CodigoTransacao, opt => opt.MapFrom(src => GetTransactionCode(src.JsonRespostaDock)))
                .ForMember(dest => dest.CodigoTransacaoCancelamento, opt => opt.MapFrom(src => GetTransactionCode(src.JsonRespostaDockCancelamento)))
                .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Status.GetHashCode()));

            CreateMap<Transacao, PagamentoViagemTransacaoResponse>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.Valor, opt => opt.MapFrom(src => Math.Round(src.Valor, 2)))
                .ForMember(dest => dest.IdContaOrigem, opt => opt.MapFrom(src => src.Origem))
                .ForMember(dest => dest.IdContaDestino, opt => opt.MapFrom(src => src.Destino))
                .ForMember(dest => dest.DocumentoOrigem, opt => opt.MapFrom(src => GetDocumentoOrigem(src)))
                .ForMember(dest => dest.DocumentoDestino, opt => opt.MapFrom(src => GetDocumentoDestino(src)))
                .ForMember(dest => dest.CodigoTransacao, opt => opt.MapFrom(src => GetTransactionCode(src.JsonRespostaDock)))
                .ForMember(dest => dest.CodigoTransacaoCancelamento, opt => opt.MapFrom(src => GetTransactionCode(src.JsonRespostaDockCancelamento)))
                .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Status.GetHashCode()))
                .ForMember(dest => dest.StatusEnum, opt => opt.MapFrom(src => src.Status))
                .ForMember(dest => dest.Agencia, opt => opt.MapFrom(src => src.Agencia))
                .ForMember(dest => dest.Conta, opt => opt.MapFrom(src => src.Conta))
                .ForMember(dest => dest.CodigoBanco, opt => opt.MapFrom(src => src.CodigoBanco));

            CreateMap<Transacao, PagamentoV2TransacaoResponse>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.Valor, opt => opt.MapFrom(src => Math.Round(src.Valor, 2)))
                .ForMember(dest => dest.IdContaOrigem, opt => opt.MapFrom(src => src.Origem))
                .ForMember(dest => dest.IdContaDestino, opt => opt.MapFrom(src => src.Destino))
                .ForMember(dest => dest.DocumentoOrigem, opt => opt.MapFrom(src => GetDocumentoOrigem(src)))
                .ForMember(dest => dest.DocumentoDestino, opt => opt.MapFrom(src => GetDocumentoDestino(src)))
                .ForMember(dest => dest.CodigoTransacao, opt => opt.MapFrom(src => GetTransactionCode(src.JsonRespostaDock)))
                .ForMember(dest => dest.CodigoTransacaoCancelamento, opt => opt.MapFrom(src => GetTransactionCode(src.JsonRespostaDockCancelamento)))
                .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Status.GetHashCode()))
                .ForMember(dest => dest.StatusEnum, opt => opt.MapFrom(src => src.Status))
                .ForMember(dest => dest.Agencia, opt => opt.MapFrom(src => src.Agencia))
                .ForMember(dest => dest.Conta, opt => opt.MapFrom(src => src.Conta))
                .ForMember(dest => dest.CodigoBanco, opt => opt.MapFrom(src => src.CodigoBanco));
            
            
            CreateMap<ViagemIntegrarV2Request, Viagem>()
                .ForMember(dest => dest.TipoCiot, opt => opt.MapFrom(src => src.TipoViagem == 1 ? Domain.Enum.TipoCiot.Padrão : TipoCiot.Agregado))
                .ForMember(dest => dest.VersaoIntegracaoViagem, opt => opt.MapFrom(src => EVersaoIntegracao.V2))
                .ForMember(dest => dest.DataAlteracao, opt => opt.MapFrom(_ => DateTime.Now))
                .ForMember(dest => dest.NomeMotorista, opt => opt.MapFrom(src => src.Motorista.NomeRazaoSocial))
                .ForMember(dest => dest.NomeProprietario, opt => opt.MapFrom(src => src.Proprietario.NomeRazaoSocial))
                .ForMember(dest => dest.ValorFrete, opt => opt.MapFrom(src => src.Valores.ValorFrete))
                .ForMember(dest => dest.ValorSaldo, opt => opt.MapFrom(src => src.Valores.Saldo))
                .ForMember(dest => dest.ValorAdiantamento, opt => opt.MapFrom(src => src.Valores.Adiantamentos))
                .ForMember(dest => dest.QuantidadeTarifas, opt => opt.MapFrom(src => src.Valores.QuantidadeTarifas))
                .ForMember(dest => dest.ValorTarifas, opt => opt.MapFrom(src => src.Valores.ValorTarifas))
                .ForMember(dest => dest.ValorCombustivel, opt => opt.MapFrom(src => src.Valores.ValorCombustivel))
                .ForMember(dest => dest.ValorDespesa, opt => opt.MapFrom(src => src.Valores.ValorDespesas))
                .ForMember(dest => dest.TotalImposto, opt => opt.MapFrom(src => src.Valores.TotalImposto))
                .ForMember(dest => dest.TotalPedagio, opt => opt.MapFrom(src => src.Valores.TotalPegadio));
        
            CreateMap<ViagemIntegrarV2Request, ConsultarSituacaoTransportadorReq>()
                .ForMember(dest => dest.CpfCnpjInteressado, opt => opt.MapFrom(src => src.Proprietario.CpfCnpj))
                .ForMember(dest => dest.CpfCnpjTransportador, opt => opt.MapFrom(src =>  src.Proprietario.CpfCnpj))
                .ForMember(dest => dest.RNTRCTransportador, opt => opt.MapFrom(src => src.Proprietario.RNTRC));

        }
        
        private static string GetTransactionCode(string jsonRespostaDock)
        {
            if (string.IsNullOrEmpty(jsonRespostaDock))
            {
                return null;
            }

            try
            {
                var json = JObject.Parse(jsonRespostaDock);
                var transactionCodeToken = json["transactionCode"];
                if (transactionCodeToken != null)
                {
                    return transactionCodeToken.Value<string>();
                }

                var dataToken = json["data"];
                return dataToken is { HasValues: true } ? dataToken["TransactionCode"]?.Value<string>() : null;
            }
            catch (JsonException ex)
            {
                Console.WriteLine($"Erro ao deserializar JsonRespostaDock para TransactionCode: {ex.Message}");
                return null;
            }
        }

        private static string GetDocumentoOrigem(Transacao transacao)
        {
            //transacao do proprietário
            if (transacao.Valor == transacao.PagamentoEvento.Valor)
            {
                return transacao.PagamentoEvento?.Empresa?.Cnpj;
            }

            //transacao do motorista
            return transacao.Valor == transacao.PagamentoEvento?.ValorTransferenciaMotorista 
                ? transacao.PagamentoEvento?.Viagem?.PortadorProprietario?.CpfCnpj 
                : null;
        }

        private static string GetDocumentoDestino(Transacao transacao)
        {
            if (transacao.FormaPagamento == FormaPagamentoEvento.Pix)
            {
                return !string.IsNullOrEmpty(transacao.PagamentoEvento?.RecebedorAutorizado)
                    ? transacao.PagamentoEvento?.RecebedorAutorizado
                    : transacao.PagamentoEvento?.Viagem?.PortadorProprietario?.CpfCnpj;
            }

            // Transação de retenção - extrair CPF/CNPJ da conta de retenção do campo Description
            if ((int)transacao.FormaPagamento == 6) // RetencaoAntecipacao
            {
                // Para transações de retenção, o CPF/CNPJ da conta de retenção é armazenado
                // no campo Description da transação durante a criação
                if (!string.IsNullOrEmpty(transacao.Description))
                {
                    try
                    {
                        // O Description contém um JSON com nationalRegistration da conta de retenção
                        var descriptionObj = Newtonsoft.Json.JsonConvert.DeserializeObject<dynamic>(transacao.Description);
                        var nationalRegistration = descriptionObj?.nationalRegistration?.ToString();

                        if (!string.IsNullOrEmpty(nationalRegistration))
                        {
                            return nationalRegistration;
                        }
                    }
                    catch
                    {
                        // Em caso de erro no parse do JSON, usar fallback
                    }
                }

                // Fallback: usar CPF/CNPJ do proprietário
                return transacao.PagamentoEvento?.Viagem?.PortadorProprietario?.CpfCnpj;
            }

            //transacao do proprietário
            if (transacao.Valor == transacao.PagamentoEvento.Valor)
                return transacao.PagamentoEvento.Viagem?.PortadorProprietario?.CpfCnpj;


            //transacao do motorista
            return transacao.Valor == transacao.PagamentoEvento.ValorTransferenciaMotorista
                ? transacao.PagamentoEvento.Viagem?.PortadorMotorista?.CpfCnpj
                : null;
        }
    }
}
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using AutoMapper.QueryableExtensions;
using Microsoft.EntityFrameworkCore;
using NLog;
using SistemaInfo.BBC.Application.Helpers;
using SistemaInfo.BBC.Application.Interface.GrupoEmpresa;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.GrupoEmpresa;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.BBC.Domain.Models.Empresa.Repository;
using SistemaInfo.BBC.Domain.Models.GrupoEmpresa.Commands;
using SistemaInfo.BBC.Domain.Models.GrupoEmpresa.Repository;
using SistemaInfo.BBC.Domain.Models.Usuario.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;

namespace SistemaInfo.BBC.Application.Services.GrupoEmpresa
{
    public class GrupoEmpresaAppService : AppService<Domain.Models.GrupoEmpresa.GrupoEmpresa,
            IGrupoEmpresaReadRepository, IGrupoEmpresaWriteRepository>,
        IGrupoEmpresaAppService
    {
        private readonly IEmpresaReadRepository _empresaReadRepository;
        private readonly IUsuarioReadRepository _usuarioReadRepository;

        public GrupoEmpresaAppService(IAppEngine engine,
            IGrupoEmpresaReadRepository readRepository,
            IGrupoEmpresaWriteRepository writeRepository,
            IEmpresaReadRepository empresaReadRepository,
            IUsuarioReadRepository usuarioReadRepository) : base(engine, readRepository, writeRepository)
        {
            _empresaReadRepository = empresaReadRepository;
            _usuarioReadRepository = usuarioReadRepository;
        }

        public ConsultarGrupoEmpresaResponse ConsultarPorId(int? idGrupoEmpresa)
        {
            try
            {
                var lGrupoEmpresa = Repository.Query.Where(x => x.Id == idGrupoEmpresa)
                    .Include(x => x.Empresa)
                    .ProjectTo<ConsultarGrupoEmpresaResponse>(Engine.Mapper.ConfigurationProvider)
                    .FirstOrDefault();

                return lGrupoEmpresa;
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                throw;
            }
        }

        public async Task<RespPadrao> ConsultarGrupoEmpresaUsuario()
        {
            try
            {
                new LogHelper().LogOperationStart("ConsultarGrupoEmpresaUsuario");
                var usuario = await _usuarioReadRepository.GetByIdAsync(User.Id);

                if (usuario.GrupoEmpresaId == null && usuario.EmpresaId == null) return new RespPadrao(true);

                if (usuario.EmpresaId == null)
                {
                    var grupoEmpresaInfo = await Repository.Query
                        .Where(x => x.Id == usuario.GrupoEmpresaId)
                        .ProjectTo<ConsultarGrupoEmpresaClientSecretResponse>(Engine.Mapper.ConfigurationProvider)
                        .FirstOrDefaultAsync();

                    return new RespPadrao(true, null, grupoEmpresaInfo);
                }

                var empresaInfo = await _empresaReadRepository
                    .Include(x => x.GrupoEmpresa)
                    .Where(x => x.Id == usuario.EmpresaId)
                    .ProjectTo<ConsultarGrupoEmpresaClientSecretResponse>(Engine.Mapper.ConfigurationProvider)
                    .FirstOrDefaultAsync();

                return new RespPadrao(true, null, empresaInfo);
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar ConsultarGrupoEmpresaUsuario");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("ConsultarGrupoEmpresaUsuario");
            }
        }

        public ConsultarGridGrupoEmpresaResponse ConsultarGridGrupoEmpresa(int take, int page,
            OrderFilters orderFilters,
            List<QueryFilters> filters, bool ativos = false)
        {
            
            #region Consulta personalizada filtros da grid

            foreach (var item in filters)
            {
                item.Valor = item.Campo switch
                {
                    "cnpj" => item.Valor.Replace(".", "").Replace("/", "").Replace("-", ""),
                    _ => item.Valor
                };
            }

            #endregion
            
            var lGrupoEmpresa = Repository.Query.GetAll();

            lGrupoEmpresa = lGrupoEmpresa.AplicarFiltrosDinamicos(filters);
            lGrupoEmpresa = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                ? lGrupoEmpresa.OrderByDescending(o => o.Id)
                : lGrupoEmpresa.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            if (ativos)
                lGrupoEmpresa = lGrupoEmpresa.Where(x => x.Ativo == 1);

            var a = lGrupoEmpresa.Count();

            var retorno = lGrupoEmpresa.Skip((page - 1) * take)
                .Take(take)
                .ProjectTo<ConsultarGrupoEmpresaGrid>(Engine.Mapper.ConfigurationProvider).ToList();

            return new ConsultarGridGrupoEmpresaResponse()
            {
                items = retorno,
                totalItems = a
            };
        }

        public async Task<ConsultarGridGrupoEmpresaResponse> ConsultarModalGrupoEmpresa(int take, int page,
            OrderFilters orderFilters,
            List<QueryFilters> filters)
        {
            var lGrupoEmpresa = Repository.Query.Where(x => x.Ativo == 1);

            var usuario = await _usuarioReadRepository.GetByIdAsync(Engine.User.Id);

            if (usuario.GrupoEmpresaId != null)
            {
                lGrupoEmpresa = lGrupoEmpresa.Where(c => c.Id == usuario.GrupoEmpresaId);
            }
            else
            {
                lGrupoEmpresa = lGrupoEmpresa.AplicarFiltrosDinamicos(filters);
                lGrupoEmpresa = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                    ? lGrupoEmpresa.OrderByDescending(o => o.Id)
                    : lGrupoEmpresa.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");
            }

            var a = lGrupoEmpresa.Count();

            var retorno = await lGrupoEmpresa
                .Skip((page - 1) * take)
                .Take(take)
                .ProjectTo<ConsultarGrupoEmpresaGrid>(Engine.Mapper.ConfigurationProvider)
                .ToListAsync();

            return new ConsultarGridGrupoEmpresaResponse()
            {
                items = retorno,
                totalItems = a
            };
        }

        public async Task<RespPadrao> SaveGrupoEmpresa(GrupoEmpresaRequest lModel)
        {
            try
            {
                if (lModel == null || lModel.id == 0)
                {
                    var lGrupoEmpresa = await Repository.Query
                        .FirstOrDefaultAsync(x => x.Cnpj == lModel.cnpj);

                    if (lGrupoEmpresa != null)
                    {
                        return new RespPadrao()
                        {
                            sucesso = false,
                            mensagem = "Cnpj já cadastrado para um grupo de empresa",
                            data = null
                        };
                    }

                    var lGrupoUsuarioInsert = Mapper.Map<GrupoEmpresaAdicionarCommand>(lModel);

                    await Engine.CommandBus.SendCommandAsync(lGrupoUsuarioInsert);
                }
                else
                {
                    var lGrupoUsuarioUpdate = Mapper.Map<GrupoEmpresaEditarCommand>(lModel);
                    await Engine.CommandBus.SendCommandAsync(lGrupoUsuarioUpdate);
                }

                return new RespPadrao()
                {
                    sucesso = true,
                    mensagem = "Grupo de empresa salvo com sucesso.",
                    data = null
                };
            }
            catch (Exception e)
            {
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = "Erro interno ao salvar grupo de empresa: " + e.Message,
                    data = null
                };
            }
        }

        public async Task AtivarInativar(int idGrupoEmpresa)
        {
            try
            {
                new LogHelper().LogOperationStart("AtivarInativar");
                var lGrupoEmpresa = await Repository.Query.GetByIdAsync(idGrupoEmpresa);

                if (lGrupoEmpresa != null)
                {
                    if (lGrupoEmpresa.Ativo == 1)
                    {
                        lGrupoEmpresa.Ativo = 0;
                        lGrupoEmpresa.DataBloqueio = DateTime.Now;
                        lGrupoEmpresa.UsuarioBloqueioId = Engine.User.Id;
                    }
                    else
                    {
                        lGrupoEmpresa.Ativo = 1;
                        lGrupoEmpresa.DataDesbloqueio = DateTime.Now;
                        lGrupoEmpresa.UsuarioDesbloqueioId = Engine.User.Id;
                    }

                    await Repository.Command.SaveChangesAsync();

                    await InativaDesativaEmpresasPorGrupo(idGrupoEmpresa, lGrupoEmpresa.Ativo);
                }
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar AtivarInativar");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("AtivarInativar");
            }
        }

        private async Task InativaDesativaEmpresasPorGrupo(int idGrupoEmpresa, int status)
        {
            var empresas = await _empresaReadRepository.GetByGrupoAsync(idGrupoEmpresa);

            foreach (var empresa in empresas)
                empresa.Ativo = status;

            await _empresaReadRepository.SaveChangesAsync();
        }
    }
}
import { Veiculo } from "../veiculo";

export class DadosEncerramento {
    ciot: string;
    codigoNaturezaCarga: string;
    dataInicioViagem: Date;
    dataFimViagem: Date;
    pesoCarga: number;
    senhaAlteracao: string;
    tipoViagem: string;
    quantidadeTarifas: number;
    valorTarifas: number;
    codigoMunicipioOrigem: string;
    codigoMunicipioDestino: string;
    sucesso: boolean;
    excecao: any;
    
    // Propriedade para compatibilidade com código existente
    get Retorno(): DadosResponseEncerrarCiot {
        const retorno = new DadosResponseEncerrarCiot();
        retorno.CIOT = this.ciot;
        retorno.CodigoNaturezaCarga = this.codigoNaturezaCarga;
        retorno.DataInicioViagem = this.dataInicioViagem;
        retorno.DataFimViagem = this.dataFimViagem;
        retorno.PesoCarga = this.pesoCarga;
        retorno.SenhaAlteracao = this.senhaAlteracao;
        retorno.TipoViagem = Number(this.tipoViagem);
        retorno.QuantidadeTarifas = this.quantidadeTarifas;
        retorno.ValorTarifas = this.valorTarifas;
        retorno.codigoMunicipioOrigem = this.codigoMunicipioOrigem;
        retorno.codigoMunicipioDestino = this.codigoMunicipioDestino;
        return retorno;
    }
    
    get Sucesso(): boolean {
        return this.sucesso;
    }
    
    get Mensagem(): string {
        return this.excecao ? this.excecao.mensagem : '';
    }
}

class DadosResponseEncerrarCiot {
    CIOT: string;
    CodigoNaturezaCarga: string;
    DataInicioViagem: Date;
    DataFimViagem: Date;
    PesoCarga: number;
    SenhaAlteracao: string;
    TipoViagem: number;
    QuantidadeTarifas: number;
    ValorTarifas: number;
    codigoMunicipioOrigem: string;
    codigoMunicipioDestino: string;
}

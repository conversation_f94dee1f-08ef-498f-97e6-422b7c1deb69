echo off
setlocal enabledelayedexpansion
cd %~dp0
set pasta_raiz=%~dp0
call utils\variaveis.bat

::===================================================================================
::Config site
set src_front=""
set dllPadrao=BBC
set origemBat="Api"
::===================================================================================
:input
	call :info Selecione o projeto de deploy
	call :opt 0 - Api
	call :opt 1 - ApiIntegracao
	call :opt 2 - ApiAbastecimento
	call :opt 3 - Mobile
	call :opt 4 - ApiCiot
	call :opt 5 - ApiCloud
	call :opt 6 - Mobile Pagamentos
	echo.
	set /p tipo_projeto=""
	set tipo_projeto=%tipo_projeto%
	call :info Projeto selecionado: %tipo_projeto%
	
if %tipo_projeto% == 0 (
set src_project=Api
set site=BBC-API
set src_pack=BBC - API
set packname=BBC - API\BBC - API
goto :input0
)
if %tipo_projeto% == 1 (
set src_project=ApiIntegracao
set site=BBC-API
set src_pack=BBC - API
set packname=BBC - API\BBC - API Integracao
goto :input0
)
if %tipo_projeto% == 2 (
set src_project=ApiAbastecimento
set site=BBC-API
set src_pack=BBC - API
set packname=BBC - API\BBC - API Abastecimento
goto :input0
)
if %tipo_projeto% == 3 (
set src_project=Mobile
set site=BBC-API
set src_pack=BBC - API
set packname=BBC - API\BBC - Mobile
goto :input0
)
if %tipo_projeto% == 4 (
set src_project=ApiCiot
set site=BBC-API
set src_pack=BBC - API
set packname=BBC - API\BBC - API Ciot
goto :input0
)
if %tipo_projeto% == 5 (
set src_project=ApiCloud
set site=BBC-API-Service
set src_pack=BBC - API Service
set packname=BBC - API Service
goto :input0
)
if %tipo_projeto% == 6 (
set src_project=Mobile.Pagamentos
set site=BBC-API
set src_pack=BBC - API
set packname=BBC - API\BBC - Mobile Pagamentos
goto :input0
)
else (
goto :input
)

:input0
	call :info Selecione o ambiente de deploy
	call :opt 0 - DEV
	call :opt 1 - HML
	call :opt 2 - PROD-SIMULADO
	echo.
	set /p tipo_ambiente=""
	set tipo_ambiente=%tipo_ambiente%
	call :info Ambiente selecionado: %tipo_ambiente%

if %tipo_ambiente% == 0 (
set ambiente=DEV
goto :input1
)
if %tipo_ambiente% == 1 (
set ambiente=HML
goto :input1
)
if %tipo_ambiente% == 2 (
set ambiente=PROD
goto :input1
)
else (
goto :input0
)

:input1
	call :info Digite o tipo de build existente
	call :opt 0 - Debug
	call :opt 1 - Release
	echo.
	set /p tipo_build=""
	set tipo_build=%tipo_build%
	call :info tipo_build selecionado: %tipo_build%

if %tipo_build% == 0 (
set tipo=Debug
goto :input2
)
if %tipo_build% == 1 (
set tipo=Release
goto :input2
)
else (
goto :input1
)

:input2
	call :info Digite o tipo de pacote a ser gerado
	call :opt 0 - dll padrao
	call :opt 1 - full dll
	call :opt 2 - pacote completo
	echo.
	set /p tipo_pacote=""
	set tipo_pacote=%tipo_pacote%
	call :info tipo_pacote selecionado: %tipo_pacote%

if %tipo_pacote%==0 (
	set extensao=*%dllPadrao%*.dll bin/*%dllPadrao%*.dll */*%dllPadrao%*.dll
	)
if %tipo_pacote%==1 (
	set extensao=*.dll bin/*.dll */*.dll
	)
if %tipo_pacote%==2 (
	set extensao=*
	)
if %tipo_pacote% gtr 2 (
	goto :input2
	)
	
:deploy
call %projeto%\scripts\deploy\utils\deploy.bat
goto :fim
exit 0

:: Sets up the ESC string for use later in this script
:setESC
    for /F "tokens=1,2 delims=#" %%a in ('"prompt #$H#$E# & echo on & for %%b in (1) do rem"') do (
      set ESC=%%b
      exit /B 0
    )
    exit /B 0
	
:opt
	call :setESC
	echo !ESC![92m %* !!ESC![0m
	exit /B 0
	
:log
	:: %~n0 = nome arquivo | %~x0 = extensão arquivo
	echo.
	call :setESC
	echo !ESC![95m===================================================================================================!!ESC![0m
	echo !ESC![95m	%*
	echo !ESC![95m===================================================================================================!!ESC![0m
	echo.
	exit /B 0
	
:info
	:: %~n0 = nome arquivo | %~x0 = extensão arquivo
	echo.
	call :setESC
	echo !ESC![94m===================================================================================================!!ESC![0m
	echo !ESC![95m	%*
	echo !ESC![94m===================================================================================================!!ESC![0m
	echo.
	exit /B 0

:warn
	:: %~n0 = nome arquivo | %~x0 = extensão arquivo
	echo.
	call :setESC
	echo !ESC![93m===================================================================================================!!ESC![0m
	echo !ESC![93m	%*
	echo !ESC![93m===================================================================================================!!ESC![0m
	echo.
	exit /B 0
	
:erro
	echo.
	call :setESC
	echo !ESC![91m===================================================================================================!!ESC![0m
	echo !ESC![91m	%~n0%~x0^> %*
	echo !ESC![91m===================================================================================================!!ESC![0m
	echo.
	if %NoStop%==False (pause)
	exit 1
	
:fim
	pause
	exit 0
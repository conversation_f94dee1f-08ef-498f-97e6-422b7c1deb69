using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.ClientSecretAdm;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Models.ClientSecretAdm.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.Interface.ClientSecretAdm
{
    public interface IClientSecretAdmAppService : IAppService<Domain.Models.ClientSecretAdm.ClientSecretAdm, IClientSecretAdmReadRepository,
                      IClientSecretAdmWriteRepository>    
    {
        Task<RespPadrao> ConsultarPorId(int idClientSecretAdm);
        Task<RespPadrao> ConsultarGridClientSecretAdm(int take, int page, OrderFilters orderFilters,
            List<QueryFilters> filters);
        Task<RespPadrao> SaveClientSecretAdm(ClientSecretAdmRequest lModel);    
        Task<RespPadrao> AlterarStatus(ClientSecretAdmAlterarStatusRequest lModel);    
    }
}

﻿using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using System;
using System.Collections.Generic;

namespace SistemaInfo.BBC.Infra.Data.Migrations
{
    public partial class SPRINT_ADD_MENU_USUARIO_FROTA : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            //adiciona menu usuario frota
            migrationBuilder.Sql("INSERT INTO \"BBC\".\"Menu\" (\"Id\", \"Descricao\", \"IsMenuPai\", \"Link\", \"MenuPaiId\", \"Sequencia\", \"IsMostraApenasAdmin\") VALUES(70, 'Usuário BBC Frota', 0, 'usuario-frota.index', 1, 3, 0)");
            
            //Adiciona menu usuario frota
            migrationBuilder.Sql("INSERT INTO \"BBC\".\"ModuloMenu\" (\"ModuloId\", \"MenuId\", \"Id\") VALUES (1, 70, 73)");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            
        }
    }
}

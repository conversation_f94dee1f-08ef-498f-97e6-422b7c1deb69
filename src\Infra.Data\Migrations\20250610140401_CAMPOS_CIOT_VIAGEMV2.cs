﻿using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using System;
using System.Collections.Generic;

namespace SistemaInfo.BBC.Infra.Data.Migrations
{
    public partial class CAMPOS_CIOT_VIAGEMV2 : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "CiotId",
                schema: "BBC",
                table: "Viagem",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DataCancelamento",
                schema: "BBC",
                table: "Viagem",
                type: "timestamp",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<DateTime>(
                name: "DataDeclaracaoCiot",
                schema: "BBC",
                table: "Viagem",
                type: "timestamp",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "DescricaoCiot",
                schema: "BBC",
                table: "Viagem",
                type: "varchar(500)",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "StatusCiot",
                schema: "BBC",
                table: "Viagem",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "TipoCiot",
                schema: "BBC",
                table: "Viagem",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "ValorAdiantamento",
                schema: "BBC",
                table: "Viagem",
                type: "numeric(16,2)",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "ValorFrete",
                schema: "BBC",
                table: "Viagem",
                type: "numeric(16,2)",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "ValorSaldo",
                schema: "BBC",
                table: "Viagem",
                type: "numeric(16,2)",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "VersaoIntegracaoViagem",
                schema: "BBC",
                table: "Viagem",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DataConsultaSituacao",
                schema: "BBC",
                table: "Portador",
                type: "timestamp",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "EquiparadoTac",
                schema: "BBC",
                table: "Portador",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "RntrcAtivo",
                schema: "BBC",
                table: "Portador",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "TipoTransportador",
                schema: "BBC",
                table: "Portador",
                type: "int",
                nullable: true);
            
            migrationBuilder.AddColumn<int>(
                name: "StatusCliente",
                schema: "BBC",
                table: "PagamentoEvento",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "VersaoIntegracaoViagem",
                schema: "BBC",
                table: "Empresa",
                type: "int",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CiotId",
                schema: "BBC",
                table: "Viagem");

            migrationBuilder.DropColumn(
                name: "DataCancelamento",
                schema: "BBC",
                table: "Viagem");

            migrationBuilder.DropColumn(
                name: "DataDeclaracaoCiot",
                schema: "BBC",
                table: "Viagem");

            migrationBuilder.DropColumn(
                name: "DescricaoCiot",
                schema: "BBC",
                table: "Viagem");

            migrationBuilder.DropColumn(
                name: "StatusCiot",
                schema: "BBC",
                table: "Viagem");

            migrationBuilder.DropColumn(
                name: "TipoCiot",
                schema: "BBC",
                table: "Viagem");

            migrationBuilder.DropColumn(
                name: "ValorAdiantamento",
                schema: "BBC",
                table: "Viagem");

            migrationBuilder.DropColumn(
                name: "ValorFrete",
                schema: "BBC",
                table: "Viagem");

            migrationBuilder.DropColumn(
                name: "ValorSaldo",
                schema: "BBC",
                table: "Viagem");

            migrationBuilder.DropColumn(
                name: "VersaoIntegracaoViagem",
                schema: "BBC",
                table: "Viagem");

            migrationBuilder.DropColumn(
                name: "DataConsultaSituacao",
                schema: "BBC",
                table: "Portador");

            migrationBuilder.DropColumn(
                name: "EquiparadoTac",
                schema: "BBC",
                table: "Portador");

            migrationBuilder.DropColumn(
                name: "RntrcAtivo",
                schema: "BBC",
                table: "Portador");

            migrationBuilder.DropColumn(
                name: "TipoTransportador",
                schema: "BBC",
                table: "Portador");
            
            migrationBuilder.DropColumn(
                name: "StatusCliente",
                schema: "BBC",
                table: "PagamentoEvento");

            migrationBuilder.DropColumn(
                name: "VersaoIntegracaoViagem",
                schema: "BBC",
                table: "Empresa");

            migrationBuilder.AlterColumn<int>(
                name: "Id",
                schema: "BBC",
                table: "EmpresaUsuario",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "serial")
                .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.SerialColumn)
                .OldAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.SerialColumn);
        }
    }
}

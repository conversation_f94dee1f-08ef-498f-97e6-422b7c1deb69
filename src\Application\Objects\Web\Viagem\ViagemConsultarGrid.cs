﻿using System;
using System.Collections.Generic;
using SistemaInfo.BBC.Domain.Enum;

namespace SistemaInfo.BBC.Application.Objects.Web.Viagem
{
    public class ViagemConsultarGridItem
    {
        public int ViagemId { get; set; }
        public string RazaoSocialEmpresa { get; set; }
        public string CnpjEmpresa { get; set; }
        public string Status { get; set; }
        public string FilialId { get; set; }
        public string CpfCnpjProprietario { get; set; }
        public string Ciot { get; set; }
        public string NaturezaCarga { get; set; }
        public string PesoCarga { get; set; }
        public string CpfCnpjMotorista { get; set; }
        public int? ViagemExternoId { get; set; }
        public string TipoBanco { get; set; }
        public int? PagamentoExternoId { get; set; }
        public string Agencia { get; set; }
        public string Conta { get; set; }
        /// <summary>
        /// ETipoContaDock Corrente = 1, Poupanca = 2, Salario = 3
        /// </summary>
        public int? TipoConta { get; set; }
        public string DataBaixa { get; set; }
        public string DataCadastro { get; set; }
        public string DataAlteracao { get; set; }
    }
    
    public class ConsultarGridViagemResponse
    {
        public int TotalItems { get; set; }
        public List<ViagemConsultarGridItem> Items { get; set; }
    }
}


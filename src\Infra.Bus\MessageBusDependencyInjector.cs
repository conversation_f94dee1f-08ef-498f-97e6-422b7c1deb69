﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using MassTransit;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using NLog;
using SistemaInfo.BBC.Domain.Contracts.Empresa;
using SistemaInfo.BBC.Domain.Contracts.MonitoramentoCiot;
using SistemaInfo.BBC.Domain.Contracts.Operacoes;
using SistemaInfo.BBC.Domain.Contracts.Parametro;
using SistemaInfo.BBC.Domain.Contracts.Pedagio;
using SistemaInfo.BBC.Domain.Contracts.Saldo;
using SistemaInfo.BBC.Domain.Contracts.Servidor;
using SistemaInfo.BBC.Domain.Models.OperacaoTransporteCiot.Commands;
using SistemaInfo.BBC.Domain.Models.TransacaoPedagio.Commands;
using SistemaInfo.BBC.Infra.Bus.Commands.Empresa;
using SistemaInfo.BBC.Infra.Bus.Commands.Operacoes;
using SistemaInfo.BBC.Infra.Bus.Commands.ParametroCiot;
using SistemaInfo.BBC.Infra.Bus.Commands.ParametroPedagio;
using SistemaInfo.BBC.Infra.Bus.Commands.Pedagio;
using SistemaInfo.BBC.Infra.Bus.Commands.Saldo;
using SistemaInfo.BBC.Infra.Bus.Commands.Servidor;
using SistemaInfo.BBC.Infra.Bus.Commands.Transacao;
using SistemaInfo.BBC.Infra.Bus.Interface.Ciot;
using SistemaInfo.BBC.Infra.Bus.Interface.Pedagio;
using SistemaInfo.BBC.Infra.Bus.Interface.Saldo;
using SistemaInfo.BBC.Infra.Bus.Publishers.Empresa;
using SistemaInfo.BBC.Infra.Bus.Publishers.MonitoramentoCiot;
using SistemaInfo.BBC.Infra.Bus.Publishers.OperacoesCiot;
using SistemaInfo.BBC.Infra.Bus.Publishers.Parametro;
using SistemaInfo.BBC.Infra.Bus.Publishers.Pedagio;
using SistemaInfo.BBC.Infra.Bus.Publishers.Transacao;
using SistemaInfo.Framework.CQRS;
using TransacaoConsultaGridMessage = SistemaInfo.BBC.Domain.Contracts.Transacao.TransacaoConsultaGridMessage;

namespace SistemaInfo.BBC.Infra.Bus
{
    /// <summary>
    ///
    /// </summary>
    public class MessageBusDependencyInjector
    {
        private static readonly string[] allowedAssemblysName = new [] {"Web", "ApiIntegracao", "ApiCiot"};

        /// <summary>
        /// Registrar serviços necessários para funcionamento da aplicação
        /// </summary>
        /// <param name="services"></param>
        /// <param name="configuration"></param>
        public static void RegisterServices(IServiceCollection services, IConfiguration configuration)
        {
            AddAppInfrastructure(services, configuration);
            AddAppServices(services);
        }

        /// <summary>
        /// Recuros ténicos para funcionamento da aplicação, framework's de baixo nível da aplicação
        /// </summary>
        /// <param name="services"></param>
        /// <param name="configuration"></param>
        private static void AddAppInfrastructure(IServiceCollection services, IConfiguration configuration)
        {
            services.AddScoped<IPedagioPublisher, PedagioPublisher>(); 
            services.AddScoped<IParametroPublisher, ParametroPublisher>();
            services.AddScoped<ITransacaoPublisher, TransacaoPublisher>();
            services.AddScoped<IMonitoramentoCiotPublisher, MonitoramentoCiotPublisher>();
            services.AddScoped<IEmpresaPublisher, EmpresaPublisher>();
            services.AddScoped<IOperacoesPublisher, OperacoesPublisher>();
            services.AddScoped<ISaldoPublisher, SaldoPublisher>();
            
            #region MassTransit
            
                try
                {
                    services.AddMassTransit(configuratorbus =>
                    {
                        configuratorbus.AddBus(provider => MassTransit.Bus.Factory.CreateUsingRabbitMq(cfg =>
                        {
                            cfg.Host(configuration["RabbitMQ:Host"] ?? "localhost", configuration["RabbitMQ:VirtualHost"]?? "msciot", configuration["RabbitMQ:ConnectionName"]??"BBC" , h =>
                            {
                                h.Username(configuration["RabbitMQ:User"]?? "guest");
                                h.Password(configuration["RabbitMQ:PassWord"]?? "guest");
                            });
                            cfg.ConfigureEndpoints(provider);
                        }));
                    });
                }
                catch (Exception e)
                {
                    LogManager.GetCurrentClassLogger().Error("A aplicação falhou ao conectar com o RabbitMq!");
                    LogManager.GetCurrentClassLogger().Error(e);
                }   
           
                services.AddSingleton<IBus>(provider => provider.GetRequiredService<IBusControl>());
                services.AddSingleton<IHostedService, BusService>();

                #endregion
        }

        /// <summary>
        /// Serviços de consultas para obter informações
        /// </summary>
        /// <param name="services"></param>
        private static void AddAppServices(IServiceCollection services)
        {
            services.AddScoped<IHandler<TransacaoConsultarMSCommand, TransacaoConsultaGridMessage>, TransacaoPedagioCommandHandler>();
            
            services.AddScoped<IHandler<PagamentoPedagioHistoricoGridMessageRequest, PagamentoPedagioHistoricoGridMessage>, PedagioMsCommandHandler>();
            
            services.AddScoped<IHandler<ReprocessarPedagioMessage>, PedagioMsCommandHandler>();
            services.AddScoped<IHandler<ServidorSincronizarMessage, ServidorSincronizarMessageResponse>, ServidorMsCiotCommandHandler>();
            services.AddScoped<IHandler<IntegrarPedagioMessage, IntegrarPedagioMessageResponse>, PedagioMsCommandHandler>();
            services.AddScoped<IHandler<SincronizarParametroPedagioMessage>, ParametroMsPedagioCommandHandler>();
            services.AddScoped<IHandler<ParametroSincronizarMessage>, ParametroMsPedagioCommandHandler>();
            services.AddScoped<IHandler<SincronizarParametroCiotMessage>, ParametroMsCiotCommandHandler>();
            services.AddScoped<IHandler<EmailClienteSincronizarMessage>, EmpresaMsCommandHandler>();
            services.AddScoped<IHandler<ServidoresConsultarMSCommand, ConsultarServidorCiotGridMessage>, ServidorMsCiotCommandHandler>();
            services.AddScoped<IHandler<ResumoDiaRequestMessage, ResumoDiaResponseMessage>, SaldoCommandHandler>();
            services.AddScoped<IHandler<ConsultarPagamentosDiaRequestMessage, ConsultarPagamentosDiaResponseMessage>, SaldoCommandHandler>();
            services.AddScoped<IHandler<ConsultaPagamentoGraficoRequestMessage, ConsultaPagamentoGraficoResponseMessage>, SaldoCommandHandler>();

            #region Operações

            services.AddScoped<IHandler<DeclararOperacaoTransporteReqMessage, DeclararOperacaoTransporteRespMessage>, OperacoesCommandHandler>();
            services.AddScoped<IHandler<ConsultarOperacaoTacAgregadoReqMessage, ConsultarOperacaoTacAgregadoRespMessage>, OperacoesCommandHandler>();
            services.AddScoped<IHandler<EncerrarOperacaoTransporteReqMessage, EncerrarOperacaoTransporteRespMessage>, OperacoesCommandHandler>();
            services.AddScoped<IHandler<EncerrarOperacaoTransporteBbcReqMessage, EncerrarOperacaoTransporteRespMessage>, OperacoesCommandHandler>();
            services.AddScoped<IHandler<CancelarOperacaoTransporteReqMessage, CancelarOperacaoTransporteRespMessage>, OperacoesCommandHandler>();
            services.AddScoped<IHandler<CancelarOperacaoTransporteReqMessage, CancelarOperacaoTransporteRespMessage>, OperacoesCommandHandler>();
            services.AddScoped<IHandler<ConsultarSituacaoTransportadorReqMessage, ConsultarSituacaoTransportadorRespMessage>, OperacoesCommandHandler>();
            services.AddScoped<IHandler<ConsultarFrotaTransportadorReqMessage, ConsultarFrotaTransportadorRespMessage>, OperacoesCommandHandler>();
            services.AddScoped<IHandler<ConsultarSituacaoCiotReqMessage, ConsultarSituacaoCiotRespMessage>, OperacoesCommandHandler>();
            services.AddScoped<IHandler<RetificarOperacaoTransporteReqMessage, RetificarOperacaoTransporteRespMessage>, OperacoesCommandHandler>();
            services.AddScoped<IHandler<OperacaoTransporteConsultarMsCommand, ConsultaGridOperacaoTransporteMessage>, OperacoesCommandHandler>();
            services.AddScoped<IHandler<OperacaoTransporteConsultarHistoricoMsCommand, ConsultaGridOperacaoTransporteHistoricoMessage>, OperacoesCommandHandler>();
            services.AddScoped<IHandler<ConsultarOperacaoTransportePorIdReqMessage, ConsultarOperacaoTransportePorIdRespMessage>, OperacoesCommandHandler>();
            services.AddScoped<IHandler<ConsultarVeiculoCiotReqMessage, ConsultarVeiculosCiotRespMessage>, OperacoesCommandHandler>();
            services.AddScoped<IHandler<ConsultarTacAgregadoRequestMessage, ConsultarTacAgregadoResponseMessage>, OperacoesCommandHandler>();

            #endregion
            
        }
    }
}

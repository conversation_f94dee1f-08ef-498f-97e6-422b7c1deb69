﻿using System.Collections.Generic;

namespace SistemaInfo.BBC.Application.Objects.Web.Viagem
{
    
    public class ConsultaGridPagamentoEventoViagemItem
    {
        public int Id { get; set; }
        public int PagamentoExternoId { get; set; }
        public string Status { get; set; }
        public string FormaPagamento { get; set; }
        public string Tipo { get; set; }
        public string ValorParcela { get; set; }
        public string ValorTransferenciaMotorista { get; set; }
        public string ValorTarifaBbc { get; set; }
        public string ValorTarifaPix { get; set; }
        public string Mensagem { get; set; }
        public int? ContadorReenvio { get; set; }
        public int? ContadorVerificacaoStatusPix { get; set; }
        public string DataCadastro { get; set; }
        public string DataAlteracao { get; set; }
        public string DataBaixa { get; set; }
        public string DataCancelamento { get; set; }

    }
    
    public class ConsultaGridPagamentoEventoHistoricoViagemItem
    {
        public int Id { get; set; }
        public int PagamentoExternoId { get; set; }
        public int PagamentoEventoId { get; set; }
        public string Status { get; set; }
        public string FormaPagamento { get; set; }
        public string Tipo { get; set; }
        public string ValorParcela { get; set; }
        public string ValorTransferenciaMotorista { get; set; }
        public string ValorTarifaBbc { get; set; }
        public string ValorTarifaPix { get; set; }
        public string Mensagem { get; set; }
        public int? ContadorReenvio { get; set; }
        public int? ContadorVerificacaoStatusPix { get; set; }
        public string DataCadastro { get; set; }
        public string DataAlteracao { get; set; }
        public string DataBaixa { get; set; }
        public string DataCancelamento { get; set; }

    }
    public class ConsultaGridPagamentoEventoViagemResponse
    {
            public int TotalItems { get; set; }
            public List<ConsultaGridPagamentoEventoViagemItem> Items{ get; set; }
    }
}
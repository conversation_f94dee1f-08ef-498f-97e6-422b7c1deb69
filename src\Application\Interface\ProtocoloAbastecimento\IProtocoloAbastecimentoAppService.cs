using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.PagamentoAbastecimento;
using SistemaInfo.BBC.Application.Objects.Web.ProtocoloAbastecimento;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Models.ProtocoloAbastecimento.Repository;
using SistemaInfo.BBC.Infra.Reports.Objects;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;

namespace SistemaInfo.BBC.Application.Interface.ProtocoloAbastecimento
{
    public interface IProtocoloAbastecimentoAppService : IAppService<Domain.Models.ProtocoloAbastecimento.ProtocoloAbastecimento, IProtocoloAbastecimentoReadRepository, IProtocoloAbastecimentoWriteRepository>
    {
        ConsultarGridProtocoloAbastecimentoResponse ConsultarGridProtocoloAbastecimento(ConsultarGridProtocoloAbastecimentoRequest request);
        CarregarDadosXmlResponse CarregarDadosXml(XmlAbastecimentoRequest lProtocoloAbastecimentoReq);
        ValidarXmlReponse ValidarXml(ValidacaoXmlRequest lProtocoloAbastecimentoReq);
        RespPadrao ValidarValorAbastecimento(ValidarAbastecimentoRequest lProtocoloAbastecimentoReq);
        Task<ConsultarGridProtocoloResponse> ConsultarGridPainelProtocoloAbastecimento(int empresaId, int status, DateTime dtInicial, DateTime dtFinal, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
        ConsultarGridProtocoloAbastecimentoRelatorio ConsultarGridPainelProtocoloAbastecimentoRelatorio(int empresaId, int status, DateTime dtInicial,
            DateTime dtFinal, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
        Task<RespPadrao> ReprovarProtocoloPendente(List<ProtocoloPendenteRequest> protocoloId);
        Task<RespPadrao> AprovarProtocoloPendente(List<ProtocoloPendenteRequest> protocoloId);
        Task<RespPadrao> ValidaXmlNota(ItensAbastecimentoValidaXml itensValidacao);
        Task<RespPadrao> ValidarXmlNota(ItensAbastecimentoValidarXml itensValidacao, int postoId);
        Task<IntegracaoSapResponse> IntegracaoSap(int protocoloId);
        ConsultarGridProtocoloReenvioSapResponse ConsultarGridProtocoloReenvioSap(string dataInicial,
            string dataFinal, int empresaId);
        ConsultarGridPainelPedidosPendentesResponse ConsultarGridProtocoloPedidosPendentes(DtoConsultaGridPedidosPendentes request);
        List<ExportObject<ProtocoloAbastecimentoPaiExport, ProtocoloAbastecimentoItemExport>> ExportarRelatorio(DtoExportarRelatorioProtocoloAbastecimento request);
        List<ExportObject<ProtocoloAbastecimentoControlePaiExport, ProtocoloAbastecimentoControleItemExport>> ExportarRelatorioControle(DtoExportarRelatorioProtocoloAbastecimentoControle request);
    }
}
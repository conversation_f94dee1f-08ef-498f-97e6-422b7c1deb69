using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Models;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Models.Validator;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Domain.Models.Viagem
{
    public class Viagem : Entity<Viagem, int, NotImplementedEntityValidator<Viagem>>
    {
        public int EmpresaId { get; set; }
        public StatusViagem? Status { get; set; }
        public string FilialId { get; set; }
        public int PortadorProprietarioId { get; set; }
        public string NomeProprietario { get; set; }
        public int? PortadorMotoristaId { get; set; }
        public string NomeMotorista { get; set; }
        public int? CiotViagemId { get; set; }
        public int? ViagemExternoId { get; set; }
        public int? CidadeOrigemId { get; set; }
        public int? CidadeDestinoId { get; set; }
        public TipoBanco? TipoBanco { get; set; }
        public int? PagamentoExternoId { get; set; }
        public string Agencia { get; set; }
        public string Conta { get; set; }
        /// <summary>
        /// ETipoContaDock Corrente = 1, Poupanca = 2, Salario = 3
        /// </summary>
        public int? TipoConta { get; set; }
        public DateTime? DataBaixa { get; set; }
        public int? UsuarioCadastroId { get; set; }
        public DateTime DataCadastro { get; set; }
        public int? UsuarioAlteracaoId { get; set; }
        public DateTime? DataAlteracao { get; set; }
        public virtual ICollection<PagamentoEvento.PagamentoEvento> PagamentoEvento { get; set; }
        public string Ciot { get; set; }
        public string VerificadorCiot { get; set; }
        public string CodigoNaturezaCarga { get; set; }
        public decimal? PesoCarga { get; set; }
        
        #region Viagem 2
        
        public int? CiotId { get; set; }
        public DateTime? DataDeclaracaoCiot { get; set; }
        public TipoCiot? TipoCiot { get; set; }
        public EVersaoIntegracao VersaoIntegracaoViagem { get; set; }
        public StatusCiot? StatusCiot { get; set; }
        public string DescricaoCiot { get; set; }
        public DateTime DataCancelamento { get; set; }
        public decimal? ValorComplemento { get; set; }
        public decimal? ValorFrete { get; set; }
        public decimal? ValorSaldo { get; set; }
        public decimal? ValorAdiantamento { get; set; }
        public int? QuantidadeTarifas { get; set; }
        public decimal? ValorTarifas { get; set; }
        public decimal? ValorCombustivel { get; set; }
        public decimal? ValorDespesa { get; set; }
        public decimal? TotalImposto { get; set; }
        public decimal? TotalPedagio { get; set; }
        
        #endregion
        
        #region Logs

        [MaxLength(16000)]
        public string JsonEnvio { get; set; }
        [MaxLength(16000)]
        public string JsonRetorno { get; set; }
        [MaxLength(16000)]
        public string JsonRetornoCancelamento { get; set; }
        [MaxLength(16000)]
        public string JsonEnvioCancelamento { get; set; }
        public DateTime? DataRetorno { get; set; }
        public DateTime? DataCadastroCancelamento { get; set; }
        public DateTime? DataRetornoCancelamento { get; set; }

        #endregion
        
        #region Propriedades de Navegacao
        
        public virtual Usuario.Usuario UsuarioCadastro { get; set; }
        public virtual Usuario.Usuario UsuarioAlteracao { get; set; }
        public virtual Empresa.Empresa Empresa { get; set; }
        public virtual Portador.Portador PortadorMotorista { get; set; }
        public virtual Portador.Portador PortadorProprietario { get; set; }
        public virtual Cidade.Cidade CidadeDestino { get; set; }
        public virtual Cidade.Cidade CidadeOrigem { get; set; }
        public virtual CiotViagem.CiotViagem CiotViagem { get; set; }
        
        #endregion


        public bool DeclarouCiot()
        {
            return !Ciot.IsEmpty();
        }
    }
}
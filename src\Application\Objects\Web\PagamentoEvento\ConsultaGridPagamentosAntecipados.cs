using System;
using System.Collections.Generic;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Domain.Enum;

namespace SistemaInfo.BBC.Application.Objects.Web.PagamentoEvento
{
    public class ConsultaGridPagamentosAntecipadosItem
    {
        public int Id { get; set; }
        public int ViagemId { get; set; }
        public string Ciot { get; set; }
        public decimal Valor { get; set; }
        public StatusPagamento Status { get; set; }
        public string StatusDescricao { get; set; }
        public Tipo? Tipo { get; set; }
        public string TipoDescricao { get; set; }
        public FormaPagamentoEvento? FormaPagamento { get; set; }
        public string FormaPagamentoDescricao { get; set; }
        public StatusAntecipacaoParcelaProprietario? StatusAntecipacaoParcelaProprietario { get; set; }
        public string StatusAntecipacaoDescricao { get; set; }
        public string CpfCnpjProprietario { get; set; }
        public string NomeProprietario { get; set; }
        public string CpfCnpjMotorista { get; set; }
        public string NomeMotorista { get; set; }
        public string DataPrevisaoPagamento { get; set; }
        public string DataCadastro { get; set; }
        public string DataAlteracao { get; set; }
        public string DataBaixa { get; set; }
        public string DataCancelamento { get; set; }
        public int? EmpresaId { get; set; }
        public string RazaoSocialEmpresa { get; set; }
        public string CnpjEmpresa { get; set; }
    }

    public class ConsultaGridPagamentosAntecipadosResponse
    {
        public int totalItems { get; set; }
        public List<ConsultaGridPagamentosAntecipadosItem> items { get; set; }
    }

    public class ConsultaGridPagamentosAntecipadosRequest : BaseGridRequest
    {
        public string dataInicial { get; set; }
        public string dataFinal { get; set; }
        public int? EmpresaId { get; set; }
        public string CpfCnpjProprietario { get; set; }
    }

    public class ConsultaGridCentralPendenciaPagamentosAntecipadosItem
    {
        public int Id { get; set; }
        public int ViagemId { get; set; }
        public string Ciot { get; set; }
        public decimal Valor { get; set; }
        public StatusPagamento Status { get; set; }
        public string StatusDescricao { get; set; }
        public Tipo? Tipo { get; set; }
        public string TipoDescricao { get; set; }
        public FormaPagamentoEvento? FormaPagamento { get; set; }
        public string FormaPagamentoDescricao { get; set; }
        public StatusAntecipacaoParcelaProprietario? StatusAntecipacaoParcelaProprietario { get; set; }
        public string StatusAntecipacaoDescricao { get; set; }
        public string CpfCnpjProprietario { get; set; }
        public string NomeProprietario { get; set; }
        public string CpfCnpjMotorista { get; set; }
        public string NomeMotorista { get; set; }
        public string DataPrevisaoPagamento { get; set; }
        public string DataCadastro { get; set; }
        public string DataAlteracao { get; set; }
        public string DataBaixa { get; set; }
        public string DataCancelamento { get; set; }
        public int? EmpresaId { get; set; }
        public string RazaoSocialEmpresa { get; set; }
        public string CnpjEmpresa { get; set; }
        
        public int PagamentoExternoId { get; set; }
        public int ViagemExternoId { get; set; }
        
        public string MotivoPendencia { get; set; }
        
        public string Descricao { get; set; }
        
        public string AntecipacaoMotivo { get; set; }
    }

    public class ConsultaGridCentralPendenciaPagamentosAntecipadosResponse
    {
        public int totalItems { get; set; }
        public List<ConsultaGridCentralPendenciaPagamentosAntecipadosItem> items { get; set; }

        // Totais calculados no backend
        public decimal totalPagamentos { get; set; }
        public decimal totalSemana { get; set; }
        public decimal totalMes { get; set; }
        public decimal totalPeriodoSelecionado { get; set; }
    }

    public class ConsultaGridCentralPendenciaPagamentosAntecipadosRequest : BaseGridRequest
    {
        public string dataInicial { get; set; }
        public string dataFinal { get; set; }
        public int? EmpresaId { get; set; }
        public string CpfCnpjProprietario { get; set; }
    }
}

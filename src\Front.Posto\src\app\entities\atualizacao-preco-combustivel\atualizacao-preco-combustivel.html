﻿<style>
    .ml-n-30 {
        margin-left: -30px;
    }
    .mt-p-6 {
        margin-top: 6px;
    }
    .h-p-30 {
        height: 30px !important;
    }
</style>
<div ng-controller="AtualizacaoPrecoCombustivelController as vm">
    <form-header items="vm.headerItems" head="'Atualização preço combustíveis'">
    </form-header>
    <div class="wrapper wrapper-content animated fadeIn mb-0 pb-0">
        <div class="row">
            <div class="col-lg-12">
                <div class="ibox animated fadeIn">
                    <div class="ibox-title">
                        <h5>Atualização preço combustíveis</h5>
                        <div ibox-tools></div>
                    </div>
                    <div class="ibox-content">
                        <div class="row">
                            <div class="col-lg-12">    
                                <div class="col-xs-12 col-sm-12 col-md-5 col-lg-4 mb-5">
                                    <label class="col-xs-12 col-sm-12 col-md-4 col-lg-3 control-label mt-10">Período:</label>
                                    <div class="col-xs-12 col-sm-12 col-md-8 col-lg-9 input-group ">
                                        <input date-range-picker class="form-control date-picker"
                                               ui-date-mask="DD/MM/YYYY - DD/MM/YYYY" type="text" ng-model="vm.date"
                                               options="vm.dateOptions" id="periodoDatePicker"/>
                                    </div>
                                </div>
                                <div class="col-xs-12 col-sm-12 col-md-7 col-lg-8">
                                    <button type='button' class="btn btn-labeled btn-primary mr-5 mb-5"
                                            ng-click="vm.gridOptions.dataSource.refresh();"
                                            tooltip-placement="top">
                                        <i class="fa fa-refresh"></i>
                                        <span class="pl-5 ">Consultar</span>
                                    </button>
                                    <button type='button' class="btn btn-labeled btn-success mb-5 pull-right"
                                            ng-click="vm.abrirModalNovaSolicitacao();"
                                            tooltip-placement="top">
                                        <i class="fa fa-plus"></i>
                                        <span class="pl-5 ">Nova solicitação</span>
                                    </button>
                                </div>
                            </div>         
                        </div>
                        <hr/>
                        <div ui-grid="vm.gridOptions" ng-style="{height: vm.gridOptions.getGridHeight()}" class="grid"
                             ui-grid-pinning 
                             ui-grid-save-state
                             ui-grid-pagination 
                             ui-grid-auto-resize
                             ui-grid-resize-columns 
                             ui-grid-grouping>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
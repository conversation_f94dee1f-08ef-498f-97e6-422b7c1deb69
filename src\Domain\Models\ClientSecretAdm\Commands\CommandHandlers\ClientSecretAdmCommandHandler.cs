using System;
using System.Threading.Tasks;
using SistemaInfo.BBC.Domain.Models.ClientSecretAdm.Repository;
using SistemaInfo.Framework.CQRS;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using SistemaInfo.Framework.DomainDrivenDesign.Infra.CQRS;

namespace SistemaInfo.BBC.Domain.Models.ClientSecretAdm.Commands.CommandHandlers
{
    public class ClientSecretAdmCommandHandler : CommandHandler<ClientSecretAdm,
            IClientSecretAdmReadRepository, IClientSecretAdmWriteRepository>,
        IHandler<ClientSecretAdmAdicionarCommand>,
        IHandler<ClientSecretAdmEditarCommand, ClientSecretAdm>,
        IHandler<ClientSecretAdmAlterarStatusCommand, ClientSecretAdm>
    {
        public IClientSecretAdmReadRepository ClientSecretAdmReadRepository { get; }
        public IClientSecretAdmWriteRepository ClientSecretAdmWriteRepository { get; }

        public ClientSecretAdmCommandHandler(IAppEngine engine, IClientSecretAdmReadRepository readRepository,
            IClientSecretAdmWriteRepository writeRepository) : base(engine, readRepository, writeRepository)
        {
            ClientSecretAdmReadRepository = readRepository;
            ClientSecretAdmWriteRepository = writeRepository;
        }

        public async Task HandlerAsync(ClientSecretAdmAdicionarCommand command)
        {
            if(command == null || command.Id == 0)
            {
                var lClientSecretAdm = Mapper.Map<ClientSecretAdm>(command);
                await Repository.Command.AddAsync(lClientSecretAdm);
            }
            
            await SaveChangesAsync();        
        }
        
        public async Task<ClientSecretAdm> HandlerAsync(ClientSecretAdmEditarCommand command)
        {
            var lClientSecretAdm = Repository.Query.FirstOrDefault(x => x.Id == command.Id);
            
            lClientSecretAdm.DataAlteracao = DateTime.Now;
            lClientSecretAdm.UsuarioAlteracaoId = Engine.User.Id;
            lClientSecretAdm.Login = command.Login;
            lClientSecretAdm.Senha = string.IsNullOrWhiteSpace(command.Senha) ? lClientSecretAdm.Senha : command.Senha;
            lClientSecretAdm.ClientSecret = lClientSecretAdm.ClientSecret;
            lClientSecretAdm.Descricao = command.Descricao;
            
            Repository.Command.Update(lClientSecretAdm);
            
            await SaveChangesAsync();
            return lClientSecretAdm;
        }

        public async Task<ClientSecretAdm> HandlerAsync(ClientSecretAdmAlterarStatusCommand command)
        {
            var lClientSecretAdm = Repository.Query.FirstOrDefault(x => x.Id == command.Id);
            
            lClientSecretAdm.DataAlteracao = DateTime.Now;
            lClientSecretAdm.UsuarioAlteracaoId = Engine.User.Id;
            lClientSecretAdm.Ativo = command.Ativo;
            
            Repository.Command.Update(lClientSecretAdm);
            await SaveChangesAsync();
            
            return lClientSecretAdm;
        }
    }
}

using System.Threading.Tasks;
using SistemaInfo.BBC.Application.Objects.Mobile.Viagem.Request;
using SistemaInfo.BBC.Application.Objects.Mobile.Viagem.Response;
using SistemaInfo.BBC.Domain.Models.Viagem.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;

namespace SistemaInfo.BBC.Application.Interface.Viagem
{
    public interface IViagemMobileAppService : IAppService<Domain.Models.Viagem.Viagem, 
        IViagemReadRepository, IViagemWriteRepository>
    {
        #region MobilePagamentos
        
        Task<ConsultarViagensResponse> ConsultarViagemMobile(ConsultarViagensRequest request);
        Task<ConsultarPagamentoResponse> ConsultarPagamentoMobile(ConsultarPagamentosRequest request);
        Task<PagamentoMobileResponse> AlterarStatusAntecipacao(AtualizarStatusAntecipacaoRequest request);
        
        #endregion
    }
}    
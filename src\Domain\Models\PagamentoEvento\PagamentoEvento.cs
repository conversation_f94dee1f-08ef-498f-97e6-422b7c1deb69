using System;
using System.Collections.Generic;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Models;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Models.Validator;

namespace SistemaInfo.BBC.Domain.Models.PagamentoEvento
{
    public class PagamentoEvento : Entity<PagamentoEvento, int, NotImplementedEntityValidator<PagamentoEvento>>
    {
        public int? EmpresaId { get; set; }
        public decimal Valor { get; set; }
        public StatusPagamento? Status { get; set; }
        public string MotivoPendencia { get; set; }
        public string CodigoTransacao { get; set; }
        public string ChavePix { get; set; }
        public string Agencia { get; set; }
        public string Conta { get; set; }
        public string CodigoBanco { get; set; }
        /// <summary>
        /// ETipoContaDock Corrente = 1, Poupanca = 2, Salario = 3
        /// </summary>
        public int? TipoConta { get; set; }
        public Tipo? Tipo { get; set; }
        public FormaPagamentoEvento? FormaPagamento { get; set; }
        public int? ContadorReenvio { get; set; }
        public int ViagemId { get; set; }
        public DateTime? DataBaixa { get; set; }
        public int? PagamentoExternoId { get; set; }
        public DateTime? DataSolicitacaoCancelamento { get; set; }
        public DateTime? DataCancelamento { get; set; }
        public int? UsuarioCacelamentoId { get; set; }
        public decimal? ValorCancelamento { get; set; }
        public decimal? ValorTransferenciaMotorista { get; set; }
        public int UsuarioCadastroId { get; set; }
        public DateTime DataCadastro { get; set; }
        public int? UsuarioAlteracaoId { get; set; }
        public DateTime? DataAlteracao { get; set; }
        public decimal? ValorTarifaBbc { get; set; }
        public decimal? ValorTarifaPix { get; set; }
        public decimal? TarifaBbc { get; set; }
        public decimal? TarifaPix { get; set; }
        public int? CobrancaTarifa { get; set; }
        public int? ContadorVerificacaoStatusPix { get; set; }
        public DateTime? DataTerceiraVerificacaoStatusPix { get; set; }
        public string Ocorrencia { get; set; }
        public string RecebedorAutorizado { get; set; }
        public string WebhookUrl { get; set; }

        #region Logs

        public string JsonEnvio { get; set; }
        public string JsonRetorno { get; set; }
        public string JsonRetornoCancelamento { get; set; }
        public string JsonEnvioCancelamento { get; set; }
        public DateTime? DataRetorno { get; set; }
        public DateTime? DataCadastroCancelamento { get; set; }
        public DateTime? DataRetornoCancelamento { get; set; }

        #endregion
        
        #region  Viagem V2
        public DateTime? DataPrevisaoPagamento { get; set; }
        public StatusAntecipacaoParcelaProprietario? StatusAntecipacaoParcelaProprietario { get; set; }
        
        public string AntecipacaoMotivo { get; set; }
        
        public DateTime? DataCadastroAntecipacao { get; set; }
        
        public DateTime? DataAlteracaoAntecipacao { get; set; }
        
        #endregion
        
        public string Descricao { get; set; }
        
        #region Propriedades de Navegacao
        
        public virtual Usuario.Usuario UsuarioCadastro { get; set; }
        public virtual Usuario.Usuario UsuarioAlteracao { get; set; }
        public virtual Viagem.Viagem Viagem { get; set; }
        public virtual Empresa.Empresa Empresa { get; set; }
        public virtual ICollection<Notificacao.Notificacao> Notificacao { get; set; }
        public virtual ICollection<Transacao.Transacao> Transacao { get; set; }
        public virtual ICollection<PagamentoEventoHistorico.PagamentoEventoHistorico> PagamentoEventoHistorico { get; set; }
        
        #endregion
    }
}
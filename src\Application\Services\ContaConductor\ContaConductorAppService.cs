using System;
using System.Threading.Tasks;
using SistemaInfo.BBC.Application.Objects.Api.ContaConductor;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Net;
using System.Net.Http;
using System.Text;
using Newtonsoft.Json;
using NLog;
using SistemaInfo.BBC.Application.Helpers;
using SistemaInfo.BBC.Application.Objects.Web.IntegrarConta;
using SistemaInfo.BBC.Domain.External.Conductor.Interface;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.BBC.Domain.Models.ContasConductor.Repository;
using SistemaInfo.BBC.Domain.Models.Parametros.Repository;
using SistemaInfo.BBC.Domain.Models.Portador.Commands;
using SistemaInfo.BBC.Domain.Models.Portador.Repository;
using SistemaInfo.Framework.Utils;
using ContaConductorAdicionarCommand = SistemaInfo.BBC.Domain.Models.ContasConductor.Commands.ContaConductorAdicionarCommand;
using IContaConductorAppService = SistemaInfo.BBC.Application.Interface.ContaConductor.IContaConductorAppService;
using IContaConductorReadRepository = SistemaInfo.BBC.Domain.Models.ContasConductor.Repository.IContaConductorReadRepository;

namespace SistemaInfo.BBC.Application.Services.ContaConductor
{
    public class ContaConductorAppService : AppService<Domain.Models.ContasConductor.ContaConductor, IContaConductorReadRepository, IContaConductorWriteRepository>, IContaConductorAppService
    {
        private readonly IParametrosReadRepository _parametrosReadRepository;
        private readonly IPortadorReadRepository _portadorReadRepository;
        private readonly ICartaoRepository _cartaoRepository;
        
        public ContaConductorAppService(IAppEngine engine, IContaConductorReadRepository readRepository, IContaConductorWriteRepository writeRepository, IParametrosReadRepository parametrosReadRepository, IPortadorReadRepository portadorReadRepository, ICartaoRepository cartaoRepository) 
            : base(engine, readRepository, writeRepository)
        {
            _parametrosReadRepository = parametrosReadRepository;
            _portadorReadRepository = portadorReadRepository;
            _cartaoRepository = cartaoRepository;
        }
      

        public  async Task<RespPadrao> Save(ContaConductorRequest contaConductorRequest)
        {
            try
            {
                var lContaConductor = Mapper.Map<ContaConductorAdicionarCommand>(contaConductorRequest);
                lContaConductor.CpfCnpj = StringUtils.OnlyNumbers(lContaConductor.CpfCnpj);
                lContaConductor.ValidarCadastro();
                var result = await Engine.CommandBus.SendCommandAsync<Domain.Models.ContasConductor.ContaConductor>(lContaConductor);

                var contaExiste = Repository.Query.FirstOrDefault(x => x.CpfCnpj == lContaConductor.CpfCnpj);

                if (contaExiste == null)
                {
                    return new RespPadrao()
                    {
                        id = result.Id,
                        sucesso = true,
                        mensagem = "Dados salvo com sucesso!"
                    };
                }
                
                return new RespPadrao()
                {
                    id = result.Id,
                    sucesso = true,
                    mensagem = "Dados alterado com sucesso!"
                };
            }
            catch (Exception e)
            {
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = e.Message
                };
            }
        }

        public async Task<RespPadraoApi> Integrar(ContaConductorRequest request)
        {
            try
            {
                new LogHelper().LogOperationStart("Integrar");
                var response = await Save(request);
                return new RespPadraoApi(response);
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar Integrar");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("Integrar");
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="take"></param>
        /// <param name="page"></param>
        /// <param name="orderFilters"></param>
        /// <param name="filters"></param>
        /// <param name="ativo"></param>
        /// <returns></returns>
        public GridContasConductorResponse DadosGridContaConductor(int take, int page, OrderFilters orderFilters,
            List<QueryFilters> filters, bool ativo)
        {
            try
            {
                new LogHelper().LogOperationStart("DadosGridContaConductor");

                var contasConductor = Repository.Query.GetAll();


                contasConductor = contasConductor.AplicarFiltrosDinamicos(filters);
                contasConductor = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                    ? contasConductor.OrderByDescending(o => o.Id)
                    : contasConductor.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

                var response = contasConductor.Select(o => new GridContaConductorRegistrosResponse
                    {
                        Id = o.Id,
                        Agencia = o.Agencia,
                        Conta = o.Conta,
                        CpfCnpj = o.CpfCnpj.Length == 11
                            ? o.CpfCnpj.OnlyNumbers().ToCPFFormato()
                            : o.CpfCnpj.OnlyNumbers().ToCNPJFormato(),
                        IdBanco = o.IdBanco,
                        IdConta = o.IdConta,
                        IdCartao = o.IdCartao,
                        DigitoVerificadorConta = o.DigitoVerificadorConta,
                        DigitoVerificadorAgencia = o.DigitoVerificadorAgencia,
                        Nome = o.Nome,
                        Ativo = ativo
                    })
                    .Skip((page - 1) * take).Take(take);

                return new GridContasConductorResponse
                    { Registros = response.ToList(), TotalRegistros = contasConductor.Count() };
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar DadosGridContaConductor");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("DadosGridContaConductor");
            }
        }

        public async Task<RespPadrao> ImportarContasExcel(List<ImportarContaExcelRequest> request)
        {
            try
            {
                if (request?.Count > 0)
                {
                    Domain.Models.ContasConductor.ContaConductor contaSalva = null;
                    foreach (var conta in request)
                    {
                        var lContaConductor = Mapper.Map<ContaConductorAdicionarCommand>(conta);
                        lContaConductor.ValidarCadastro();
                        contaSalva =  Engine.CommandBus.SendCommand<Domain.Models.ContasConductor.ContaConductor>(lContaConductor);
                        
                        if (contaSalva != null)
                        {
                            var existePortador =
                                await _portadorReadRepository.FirstOrDefaultAsync(x => x.CpfCnpj == contaSalva.CpfCnpj);
                           
                            var lNovoPortador = new PortadorSalvarCommand();
                            lNovoPortador.CpfCnpj = contaSalva.CpfCnpj.OnlyNumbers();
                            lNovoPortador.Nome = contaSalva.Nome;
                            lNovoPortador.CidadeId = null;
                            lNovoPortador.EstadoId = null;
                            
                            if (existePortador != null)
                            {
                                lNovoPortador.Id = existePortador.Id;
                            }
                            
                            lNovoPortador.Bairro = "Portador cadastrado via importação";
                            lNovoPortador.RepLegaisList = new List<PortadorRepLegalCommand>();
                            Engine.CommandBus.SendCommand<Domain.Models.Portador.Portador>(lNovoPortador);
                        }
                    }

                    
                    return new RespPadrao()
                    {
                        sucesso = true,
                        mensagem = "Dados salvo com sucesso!"
                    };
                }
                
                return new RespPadrao()
                {
                    sucesso = false
                };
            }
            catch (Exception e)
            {
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = e.Message
                };
            }
        }

        public async Task<RespPadrao> VerificarContaBbc(string documento)
        {
            try
            {
                if (!documento.ValidaCPF() && !documento.ValidaCNPJ())
                    return new RespPadrao(false, "CPF ou CPNJ inválido.");
                
                var contaAliasBank =  await _cartaoRepository.ConsultarContaBbcPorCpfCnpj(documento);
                if (contaAliasBank.items == null || contaAliasBank.items?.Count==0) return new RespPadrao(false, "Conta não identificada");

                return contaAliasBank.items != null && contaAliasBank.items.Any(x => x.bankAccountStatus == "ACTIVE")
                    ? new RespPadrao(true, "Conta identificada com sucesso")
                    : new RespPadrao(false, "Conta não identificada");
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return new RespPadrao(false, e.Message);
            }
        }

        public RespPadrao EnviarContas(EnviarContasRequest request)
        {
            try
            {
                new LogHelper().LogOperationStart("EnviarContas");
                List<Domain.Models.ContasConductor.ContaConductor> lContas;
                List<int> lContasNaoSeremEnviadas;
                if (request.AtivarTodasConta)
                {
                    lContasNaoSeremEnviadas = request.Contas.Where(x => !x.Ativo).Select(x => x.Id).ToList();
                    lContas = Repository.Query.Where(x => !lContasNaoSeremEnviadas.Contains(x.Id)).ToList();
                }
                else
                {
                    lContasNaoSeremEnviadas = request.Contas.Where(x => !x.Ativo).Select(x => x.Id).ToList();
                    var lContasSeremEnviadas = request.Contas.Where(x => x.Ativo).Select(x => x.Id).ToList();
                    lContas = Repository.Query.Where(x =>
                        !lContasNaoSeremEnviadas.Contains(x.Id) && lContasSeremEnviadas.Contains(x.Id)).ToList();
                }

                var lSucesso = true;

                if (lContas.Any())
                {
                    var lLink = _parametrosReadRepository.GetParametrosListAsync(
                        Domain.Models.Parametros.Parametros.ParametroGeralId,
                        Domain.Models.Parametros.Parametros.TipoDoParametro.CodigoLinkEmpresa,
                        Domain.Models.Parametros.Parametros.TipoDoValor.String).Result;


                    foreach (var conta in lContas)
                    {
                        foreach (var link in lLink)
                        {
                            var requestJsl = JsonConvert.SerializeObject(new
                            {
                                cpfCnpj = conta.CpfCnpj.OnlyNumbers(),
                                idContaConductor = conta.Id,
                                idBanco = conta.IdBanco,
                                agencia = conta.Agencia,
                                conta = conta.Conta,
                                digitoVerificadorConta = conta.DigitoVerificadorConta,
                                digitoVerificadorAgencia = conta.DigitoVerificadorAgencia,
                                idConta = conta.IdConta,
                                idCartao = conta.IdCartao
                            });

                            var response = new HttpClient();
                            var content = new StringContent(requestJsl, Encoding.UTF8, "application/json");

                            var result = response.PostAsync(link.Valor, content).Result;
                            var lRetorno = result.StatusCode == HttpStatusCode.OK ? 200 : result.StatusCode.ToIntSafe();

                            if (lRetorno != 200) lSucesso = false;
                        }
                    }
                }

                if (lSucesso)
                {
                    return new RespPadrao()
                    {
                        sucesso = true,
                        mensagem = "Operação realizada com sucesso!"
                    };
                }

                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = "Falha em realizar o processo!"
                };
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar EnviarContas");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("EnviarContas");
            }
        }
    }
}

using System;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;
using Microsoft.IdentityModel.Tokens;
using NLog;
using SistemaInfo.BBC.Application.Helpers;
using SistemaInfo.BBC.Application.Interface.AuthSession;
using SistemaInfo.BBC.Application.Interface.Posto;
using SistemaInfo.BBC.Application.Interface.Usuario;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.Posto;
using SistemaInfo.BBC.Application.Objects.Web.Usuario;
using SistemaInfo.BBC.Domain.Models.AuthSession.Commands.CommandHandlers;
using SistemaInfo.BBC.Domain.Models.AuthSession.Repository;
using SistemaInfo.BBC.Domain.Models.Parametros.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.Services.AuthSession
{
    public class AuthSessionAppService :
        AppService<BBC.Domain.Models.AuthSession.AuthSession, IAuthSessionReadRepository, IAuthSessionWriteRepository>,
        IAuthSessionAppService
    {
        private IPostoAppService _postoAppService;
        private IParametrosReadRepository _parametrosReadRepository;
        private IUsuarioAppService _usuarioAppService;
        
        
        public AuthSessionAppService(IAppEngine engine, IAuthSessionReadRepository readRepository,
            IAuthSessionWriteRepository writeRepository, IPostoAppService postoAppService, IParametrosReadRepository parametrosReadRepository, IUsuarioAppService usuarioAppService) : base(engine, readRepository, writeRepository)
        {
            _postoAppService = postoAppService;
            _parametrosReadRepository = parametrosReadRepository;
            _usuarioAppService = usuarioAppService;

        }

        public Domain.Models.AuthSession.AuthSession GetAuthSessionByToken(string token)
        {
            if (token.IsNullOrWhiteSpace())
            {
                throw new Exception("Token inválido");
            }
            
            return Repository.Query
                .Where(x => x.Token == token).FirstOrDefault();
        }

        public async Task<RespPadrao> UpdateUltimaReq(string token)
        {
            try
            {
                var lAuthSession = await Repository.Query
                    .FirstOrDefaultAsync(x => x.Token == token);
                 
                var lCommand = Mapper.Map<AuthSessionEditarCommand>(lAuthSession);
                await Engine.CommandBus.SendCommandAsync<Domain.Models.AuthSession.AuthSession>(lCommand);
               return new RespPadrao( true, "");
            }
            catch (Exception e)
            {
                return new RespPadrao(false, e.Message);
            }
        }

        public async Task<RespPadrao> SaveTokenControle(int idUsuario, string token)
        {
            try
            {
                new LogHelper().LogOperationStart("SaveTokenControle");
                var authSession = await Repository.Query
                    .FirstOrDefaultAsync(x => x.IdUsuario == idUsuario && x.IdPosto == null);

                if (authSession == null)
                {
                    authSession = new Domain.Models.AuthSession.AuthSession()
                    {
                        Token = token,
                        IdUsuario = idUsuario,
                        DataUltimaReq = DateTime.Now
                    };

                    await Repository.Command.AddAsync(authSession);
                }
                else
                {
                    authSession.Token = token;
                    authSession.DataUltimaReq = DateTime.Now;
                }

                await Repository.Command.SaveChangesAsync();

                return new RespPadrao(true, null, authSession);
            }
            catch (Exception e)
            {
                new LogHelper().Error(e, "Erro ao executar SaveTokenControle");
                return new RespPadrao(false, e.Message);
            }
            finally
            {
                new LogHelper().LogOperationEnd("SaveTokenControle");
            }
        }

        public async Task<RespPadrao> SaveTokenPosto(int idPosto, string token, int idUsuario = 1)
        {
            try
            {
                new LogHelper().LogOperationStart("SaveTokenPosto");
                var authSession = await Repository.Query
                    .FirstOrDefaultAsync(x => x.IdPosto == idPosto && x.IdUsuario == idUsuario);

                if (authSession == null)
                {
                    authSession = new Domain.Models.AuthSession.AuthSession()
                    {
                        Token = token,
                        IdUsuario = idUsuario,
                        IdPosto = idPosto,
                        DataUltimaReq = DateTime.Now
                    };
                
                    await Repository.Command.AddAsync(authSession);
                }
                else
                {
                    authSession.Token = token;
                    authSession.DataUltimaReq = DateTime.Now;
                }
                
                await Repository.Command.SaveChangesAsync();
                
                return new RespPadrao(true, null, authSession);
            }
            catch (Exception e)
            {
                new LogHelper().Error(e, "Erro ao executar SaveTokenPosto");
                return new RespPadrao(false, e.Message);
            }finally
            {
                new LogHelper().LogOperationEnd("SaveTokenPosto");
            }
        }

        public async Task<RespPadrao> SaveTokenMobile(int idPortador, string token)
        {
            try
            {
                new LogHelper().LogOperationStart("SaveTokenMobile");
                var authSession = await Repository.Query
                    .FirstOrDefaultAsync(x => x.PortadorId == idPortador);

                if (authSession == null)
                {
                    authSession = new Domain.Models.AuthSession.AuthSession()
                    {
                        Token = token,
                        IdUsuario = 1,
                        PortadorId = idPortador,
                        DataUltimaReq = DateTime.Now
                    };
                    
                    await Repository.Command.AddAsync(authSession);
                }
                else
                {
                    authSession.Token = token;
                    authSession.DataUltimaReq = DateTime.Now;
                }
            
                await Repository.Command.SaveChangesAsync();

                return new RespPadrao(true, null, authSession);
            }
            catch (Exception e)
            {
                new LogHelper().Error(e, "Erro ao executar SaveTokenMobile");
                return new RespPadrao(false, e.Message);
            }
            finally
            {
                new LogHelper().LogOperationEnd("GerarToken");
            }
        }

        public async Task<RespPadrao> GerarTokenPosto(PostoLoginRequest postoLoginRequest)
        {
            try
            {
                new LogHelper().LogOperationStart("GerarTokenPosto");
                PostoAutenticacaoViewModel postoLogin;
            
                postoLogin = postoLoginRequest.CpfCnpj.Length > 11
                    ? _postoAppService.ValidarPosto(postoLoginRequest.CpfCnpj, postoLoginRequest.Senha)
                    : _postoAppService.ValidarUsuarioPosto(postoLoginRequest.CpfCnpj, postoLoginRequest.Senha);
            
                if (postoLogin.senhaErrada)
                {
                    return new RespPadrao(false, postoLogin.mensagem);
                }
            
                if (postoLogin.posto.Ativo == 0)
                {
                    return new RespPadrao(false, "Usuário sem permissão de acesso ao sistema!");
                }
            
                var retornoValidarCredenciamento = await _postoAppService.ValidarStatusCredenciamentoPosto(postoLogin.posto.IdPosto);
            
                if (!retornoValidarCredenciamento.sucesso)
                {
                    return new RespPadrao(false, retornoValidarCredenciamento.mensagem);
                }
            
                var claims = new Claim[3];
            
                claims[0] = new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString("N"));
                claims[1] = new Claim("PostoId", postoLogin.posto.IdPosto.ToString());
            
            
                if (postoLogin.posto.UsuarioId != null)
                {
                    claims[2] = new Claim("UserIdPosto", postoLogin.posto.UsuarioId.ToString());
                }
            
                var secretKey = "ThisIsDotNetCoreSecretKey";
            
                var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(secretKey));
                var credential = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);
            
                var tempoDeExpiracao = await _parametrosReadRepository.GetTempoInatividadeUsuarioAsync();
                var intervaloValidade = 15;
            
                if (tempoDeExpiracao != 0)
                {
                    intervaloValidade = tempoDeExpiracao*60;
                }
            
                var dataCriacao = DateTime.UtcNow;
                var dataExpiracao = dataCriacao + TimeSpan.FromSeconds(intervaloValidade);
            
                var token = new JwtSecurityToken
                (
                    claims: claims,
                    signingCredentials: credential,
                    expires: dataExpiracao,
                    issuer: "MyServer",
                    audience: "EveryApplication"
                );
            
                var jwtToken = new
                {
                    token = new JwtSecurityTokenHandler().WriteToken(token),
                    expiration = dataExpiracao.ToString("yyyy-MM-dd HH:mm:ss"),
                };
            
                var lSaveToken = await SaveTokenPosto(postoLogin.posto.IdPosto, jwtToken.token, postoLogin.posto.UsuarioId ?? 1);
            
                if (!lSaveToken.sucesso)
                {
                    return new RespPadrao(false, "Erro ao salvar token!");
                }
            
                postoLogin.token = jwtToken.token;
            
                return new RespPadrao(true, "Operação concluida com sucesso.", postoLogin);
            }
            catch (Exception e)
            {
                new LogHelper().Error(e, "Erro ao executar GerarTokenPosto", postoLoginRequest);
                return new RespPadrao(false, "Erro ao efetuar seu login.", e.Message);
            } finally
            {
                new LogHelper().LogOperationEnd("GerarTokenPosto");
            }
        }

        public async Task<RespPadrao> GerarToken(UsuarioLoginRequest aLogin)
        {
            try
            {
                new LogHelper(LogManager.GetCurrentClassLogger()).LogOperationStart("GerarToken");
                var usuarioLogin = await _usuarioAppService.ValidarUsuario(aLogin.Usuario, aLogin.Senha);
                
                if (usuarioLogin.SenhaErrada)
                {
                    return new RespPadrao(false, usuarioLogin.Mensagem);
                }

                if (usuarioLogin.Usuario.Ativo == 0)
                {
                    return new RespPadrao(false, $"Erro: Usuário '{aLogin.Usuario}' inativo.");
                }

                var statusSenha = await _usuarioAppService.ValidarStatusSenha(usuarioLogin.Usuario.IdUsuario);

                if (!statusSenha.sucesso)
                {
                    return new RespPadrao(false, statusSenha.mensagem);
                }


                var teste = _usuarioAppService.GetUsuarioById(206);
                
                var claims = new[]
                {
                    new Claim("UserId", usuarioLogin.Usuario.IdUsuario.ToString()),
                    new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString("N"))
                };

                var secretKey = "ThisIsDotNetCoreSecretKey";

                var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(secretKey));
                var credential = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

                var tempoDeExpiracao = await _parametrosReadRepository.GetTempoInatividadeUsuarioAsync();
                var intervaloValidade = 15;

                if (tempoDeExpiracao != 0)
                {
                    intervaloValidade = tempoDeExpiracao * 60;
                }

                var dataCriacao = DateTime.UtcNow;
                var dataExpiracao = dataCriacao + TimeSpan.FromSeconds(intervaloValidade);

                var token = new JwtSecurityToken
                (
                    claims: claims,
                    signingCredentials: credential,
                    expires: dataExpiracao,
                    issuer: "MyServer",
                    audience: "EveryApplication"
                );

                var jwtToken = new
                {
                    token = new JwtSecurityTokenHandler().WriteToken(token),
                    expiration = dataExpiracao.ToString("yyyy-MM-dd HH:mm:ss")
                };

                var lSaveToken = await SaveTokenControle(usuarioLogin.Usuario.IdUsuario, jwtToken.token);

                if (!lSaveToken.sucesso)
                {
                    return new RespPadrao(false, "Erro ao efetuar seu login. Tente novamente.");
                }

                usuarioLogin.Token = jwtToken.token;

                return new RespPadrao(true, "Operação concluida com sucesso.", usuarioLogin);
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar GerarToken", aLogin);
                return new RespPadrao(false, "Erro ao efetuar seu login.", ex.Message);
            }
            finally
            {
                new LogHelper().LogOperationEnd("GerarToken");
            }
        }

        public RespPadrao GetByToken(string token)
        {
            try
            {
                new LogHelper().LogOperationStart("GetByToken");
                var lAuthSession = Repository.Query
                    .Where(x => x.Token == token).FirstOrDefault();
                
                return new RespPadrao()
                {
                    sucesso = true,
                    mensagem = "",
                    data = lAuthSession
                };
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar GetByToken");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("GetByToken");
            }
        }
    }
}

﻿using System;
using System.IO;
using System.Threading.Tasks;
using DinkToPdf.Contracts;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using NLog;
using SistemaInfo.BBC.ApiCiotPublico.Controllers.Base;
using SistemaInfo.BBC.Application.Interface.Operacoes;
using SistemaInfo.BBC.Application.Objects.Web;
using SistemaInfo.BBC.Domain.External.CIOT.DTO;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;

namespace SistemaInfo.BBC.ApiCiotPublico.Controllers
{
    public class OperacoesController : ApiControllerBase
    {
        private readonly IOperacoesAppService _operacoesAppService;
        private readonly IConverter _converter;
        public OperacoesController(IAppEngine engine, IOperacoesAppService operacoesAppService, IConverter converter) : base(engine)
        {
            _operacoesAppService = operacoesAppService;
            _converter = converter;
        }

        [Produces("application/json")]
        [HttpPost("DeclararOperacaoTransporte")]
        public async Task<RespBase> DeclararOperacaoTransporte([FromBody] DeclararOperacaoTransporteReq req)
        {
            try
            {
                return await _operacoesAppService.DeclararOperacaoTransporte(req, true);
            }
            catch (Exception e) { return RetornaExcecao(e.Message); }
        }

        [Produces("application/json")]
        [HttpPost("EncerrarOperacaoTransporte")]
        public async Task<RespBase> EncerrarOperacaoTransporte([FromBody] EncerrarOperacaoTransporteReq req)
        {
            try 
            { 
                return await _operacoesAppService.EncerrarOperacaoTransporte(req, true); 
            }
            catch (Exception e) { return RetornaExcecao(e.Message); }
        }

        [Produces("application/json")]
        [HttpPost("ConsultarSituacaoTransportador")]
        public async Task<RespBase> ConsultarSituacaoTransportador([FromBody] ConsultarSituacaoTransportadorReq req)
        {
            try
            {
                return await _operacoesAppService.ConsultarSituacaoTransportador(req, true);
            }
            catch (Exception e)
            {
                return RetornaExcecao(e.Message);
            }
        }

        [Produces("application/json")]
        [HttpPost("CancelarOperacaoTransporte")]
        public async Task<RespBase> CancelarOperacaoTransporte([FromBody] CancelarOperacaoTransporteReq req)
        {
            try
            {
                return await _operacoesAppService.CancelarOperacaoTransporte(req, true);
            }
            catch (Exception e) { return RetornaExcecao(e.Message); }
        }

        [Produces("application/json")]
        [HttpPost("RetificarOperacaoTransporte")]
        public async Task<RespBase> RetificarOperacaoTransporte([FromBody] RetificarOperacaoTransporteReq req)
        {
            try
            {
                return await _operacoesAppService.RetificarOperacaoTransporte(req, true);
            }
            catch (Exception e) { return RetornaExcecao(e.Message); }
        }

        [Produces("application/json")]
        [HttpPost("ConsultarSituacaoCiot")]
        public async Task<RespBase> ConsultarSituacaoCiot([FromBody] ConsultarSituacaoCiotReq req)
        {
            try
            {
                return await _operacoesAppService.ConsultarSituacaoCiot(req, true);
            }
            catch (Exception e) { return  RetornaExcecao(e.Message); }
        }
        
        #region Exclusivo ciot público

        [Produces("application/json")]
        [HttpGet("GetBancos")]
        public async Task<RespBase> ConsultarBancos()
        {
            try
            {
                return await _operacoesAppService.ConsultarBancos();
            }
            catch (Exception e) { return  RetornaExcecao(e.Message); }
        }

        [Produces("application/json")]
        [HttpGet("GetEstados")]
        public async Task<RespBase> ConsultarEstados()
        {
            try
            {
                return await _operacoesAppService.ConsultarEstados();
            }
            catch (Exception e) { return  RetornaExcecao(e.Message); }
        }

        [Produces("application/json")]
        [HttpGet("GetTiposCarga")]
        public async Task<RespBase> ConsultarTiposCarga(string txt)
        {
            try
            {
                return await _operacoesAppService.ConsultarTiposCarga(txt);
            }
            catch (Exception e) { return  RetornaExcecao(e.Message); }
        }


        [Produces("application/json")]
        [HttpGet("GetCidades")]
        public async Task<RespBase> ConsultarCidades(string nome)
        {
            try
            {
                return await _operacoesAppService.ConsultarCidades(nome);
            }
            catch (Exception e) { return  RetornaExcecao(e.Message); }
        }

        [Produces("application/json")]
        [HttpGet("GetNaturezasCarga")]
        public async Task<RespBase> ConsultarNaturezasCarga(string descricao)
        {
            try
            {
                return await _operacoesAppService.ConsultarNaturezasCarga(descricao);
            }
            catch (Exception e) { return  RetornaExcecao(e.Message); }
        }

        [Produces("application/json")]
        [HttpGet("GetNaturezasCargaById")]
        public async Task<RespBase> ConsultarNaturezasCargaPorId(string codigo)
        {
            try
            {
                return await _operacoesAppService.ConsultarNaturezasCargaPorId(codigo);
            }
            catch (Exception e) { return  RetornaExcecao(e.Message); }
        }

        [Produces("application/json")]
        [HttpPost("GetDadosEncerrar")]
        public async Task<RespBase> ConsultarDadosEncerramento([FromBody] ConsultarDadosEncerramentoReq req)
        {
            try
            {
                return await _operacoesAppService.ConsultarDadosEncerramento(req);
            }
            catch (Exception e) { return  RetornaExcecao(e.Message); }
        }

        [Produces("application/json")]
        [HttpPost("GetDadosRetificar")]
        public async Task<RespBase> ConsultarDadosRetificacao([FromBody] ConsultarDadosRetificacaoReq req)
        {
            try
            {
                return await _operacoesAppService.ConsultarDadosRetificacao(req);
            }
            catch (Exception e) { return  RetornaExcecao(e.Message); }
        }

        #region Impressão

        [HttpPost("GestaoCiot")]
        public async Task<ActionResult> GerarRelatorioGestaoCiot(string json)
        {
            try
            {
                var requestDeserialized = JsonConvert.DeserializeObject<ImpressaoSituacaoCiotReq>(json,
                    new JsonSerializerSettings { DateTimeZoneHandling = DateTimeZoneHandling.Local });

                var document = await _operacoesAppService.GerarRelatorioGestaoCiot(requestDeserialized.CIOT, requestDeserialized.SenhaAlteracao);
                var customAssemblyLoadContext = new CustomAssemblyLoadContext();
                
                var architecture = IntPtr.Size == 8 ? "x64" : "x86";
                var dllPath = Path.Combine(AppContext.BaseDirectory, architecture, "libwkhtmltox.dll");

                if (!System.IO.File.Exists(dllPath))
                    throw new FileNotFoundException($"A DLL libwkhtmltox não foi encontrada em: {dllPath}");

                customAssemblyLoadContext.LoadUnmanagedLibrary(dllPath);

                var fileContents = _converter.Convert(document);

                Response.Headers.Add("content-disposition", "attachment;filename=Ciot.pdf");
                return File(fileContents, "application/pdf");
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                throw;
            }
            finally
            {
                Response.Clear();
            }
        }

        #endregion

        #endregion

        private RespBase RetornaExcecao(string eMessage)
        {
            return new RespBase
            {
                Sucesso = false,
                Excecao = new Excecao
                {
                    Mensagem = "Não foi possível realizar a operação. Mensagem: " + eMessage
                }
            };
        }
    }
}
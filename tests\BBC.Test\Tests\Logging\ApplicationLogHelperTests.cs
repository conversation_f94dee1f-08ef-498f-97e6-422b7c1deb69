using System;
using SistemaInfo.BBC.Application.Helpers;
using Xunit;

namespace BBC.Test.Tests.Logging
{
    public class ApplicationLogHelperTests
    {
        [Fact]
        public void LogOperationStart_ShouldLogCorrectMessage()
        {
            // Arrange
            var operationName = "TesteOperation";
            var details = "Teste detalhes";

            // Act & Assert - Verificação indireta através do comportamento
            // Como LogManager é estático e GetLoggerFromCaller é privado, não podemos mocá-los diretamente
            // Este teste verifica se o método não lança exceções
            var exception = Record.Exception(() => new LogHelper().LogOperationStart(operationName, details));
            
            // Assert
            Assert.Null(exception);
        }

        [Fact]
        public void LogOperationStart_WithoutDetails_ShouldLogCorrectMessage()
        {
            // Arrange
            var operationName = "TesteOperation";

            // Act & Assert
            var exception = Record.Exception(() => new LogHelper().LogOperationStart(operationName));
            
            // Assert
            Assert.Null(exception);
        }

        [Fact]
        public void LogOperationEnd_ShouldLogCorrectMessage()
        {
            // Arrange
            var operationName = "TesteOperation";
            var details = "Teste detalhes";

            // Act & Assert
            var exception = Record.Exception(() => new LogHelper().LogOperationEnd(operationName, details));
            
            // Assert
            Assert.Null(exception);
        }

        [Fact]
        public void LogOperationEnd_WithoutDetails_ShouldLogCorrectMessage()
        {
            // Arrange
            var operationName = "TesteOperation";

            // Act & Assert
            var exception = Record.Exception(() => new LogHelper().LogOperationEnd(operationName));
            
            // Assert
            Assert.Null(exception);
        }

        [Fact]
        public void Info_ShouldLogCorrectMessage()
        {
            // Arrange
            var message = "Teste messagem info";

            // Act & Assert
            var exception = Record.Exception(() => new LogHelper().Info(message));
            
            // Assert
            Assert.Null(exception);
        }

        [Fact]
        public void Info_WithObject_ShouldLogCorrectMessage()
        {
            // Arrange
            var message = "Teste messagem info";
            var testObject = new { Id = 1, Name = "Teste" };

            // Act & Assert
            var exception = Record.Exception(() => new LogHelper().Info(message, testObject));
            
            // Assert
            Assert.Null(exception);
        }

        [Fact]
        public void Error_ShouldLogCorrectMessage()
        {
            // Arrange
            var message = "Teste message de erro";

            // Act & Assert
            var exception = Record.Exception(() => new LogHelper().Error(message));
            
            // Assert
            Assert.Null(exception);
        }

        [Fact]
        public void Error_WithException_ShouldLogCorrectMessage()
        {
            // Arrange
            var testException = new Exception("Teste message de erro");
            var message = "Teste message de erro";

            // Act & Assert
            var exception = Record.Exception(() => new LogHelper().Error(testException, message));
            
            // Assert
            Assert.Null(exception);
        }

        [Fact]
        public void Error_WithExceptionAndObject_ShouldLogCorrectMessage()
        {
            // Arrange
            var testException = new Exception("Teste message de erro");
            var message = "Test error message";
            var testObject = new { Id = 1, Name = "Teste" };

            // Act & Assert
            var exception = Record.Exception(() => new LogHelper().Error(testException, message, testObject));
            
            // Assert
            Assert.Null(exception);
        }

        [Fact]
        public void Error_WithObject_ShouldLogCorrectMessage()
        {
            // Arrange
            var message = "Teste message de erro";
            var testObject = new { Id = 1, Name = "Teste" };

            // Act & Assert
            var exception = Record.Exception(() => new LogHelper().Error(message, testObject));
            
            // Assert
            Assert.Null(exception);
        }
    }
}

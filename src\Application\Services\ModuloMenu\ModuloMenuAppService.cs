using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using SistemaInfo.BBC.Application.Helpers;
using SistemaInfo.BBC.Application.Interface.ModuloMenu;
using SistemaInfo.BBC.Application.Objects.Api.ModuloMenu;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Domain.Models.ModuloMenu.Commands;
using SistemaInfo.BBC.Domain.Models.ModuloMenu.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;

namespace SistemaInfo.BBC.Application.Services.ModuloMenu
{
    public class ModuloMenuAppService :
        AppService<Domain.Models.ModuloMenu.ModuloMenu, IModuloMenuReadRepository, IModuloMenuWriteRepository>,
        IModuloMenuAppService
    {
        public ModuloMenuAppService(IAppEngine engine, IModuloMenuReadRepository readRepository,
            IModuloMenuWriteRepository writeRepository) : base(engine, readRepository, writeRepository)
        {
        }

        public List<ConsultaModuloMenuResponse> ConsultaModuloMenuApi()
        {
            try
            {
                new LogHelper().LogOperationStart("ConsultaModuloMenuApi");
                return Mapper.Map<List<ConsultaModuloMenuResponse>>(Repository.Query.GetAll().ToList());
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar ConsultaModuloMenuApi");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("ConsultaModuloMenuApi");
            }
        }

        public async Task<RespPadraoApi> IntegrarModuloMenuApi(ModuloMenuIntegrarApiRequest request)
        {
            try
            {
                new LogHelper().LogOperationStart("IntegrarModuloMenuApi");
                var modulomenuBd =
                    Repository.Query.FirstOrDefault(x =>
                        x.MenuId == request.NovoMenuId && x.ModuloId == request.NovoModuloId);

                if (modulomenuBd != null && request.NovoModuloMenuId)
                {
                    if (modulomenuBd.MenuId == request.NovoMenuId && modulomenuBd.ModuloId == request.NovoModuloId)
                    {
                        return new RespPadraoApi()
                        {
                            sucesso = false,
                            mensagem = "Modulo menu já cadastrado!"
                        };
                    }
                }

                if (!request.NovoModuloMenuId)
                {
                    modulomenuBd.MenuId = request.NovoMenuId;
                    modulomenuBd.ModuloId = request.NovoModuloId;

                    var moduloMenuUpd = Mapper.Map<Domain.Models.ModuloMenu.ModuloMenu>(modulomenuBd);

                    Repository.Command.Update(modulomenuBd);
                    Repository.Command.SaveChanges();

                    if (moduloMenuUpd.Id > 0)
                    {
                        return new RespPadraoApi()
                        {
                            id = modulomenuBd.Id,
                            sucesso = true,
                            mensagem = "Registro salvo com sucesso!"
                        };
                    }
                }

                var command = Mapper.Map<ModuloMenuSalvarComRetornoCommand>(request);
                var retorno = await Engine.CommandBus.SendCommandAsync<Domain.Models.ModuloMenu.ModuloMenu>(command);

                if (retorno.Id > 0)
                {
                    return new RespPadraoApi()
                    {
                        id = retorno.Id,
                        sucesso = true,
                        mensagem = "Registro salvo com sucesso!"
                    };
                }

                return new RespPadraoApi()
                {
                    sucesso = false,
                    mensagem = "Erro ao integrar!"
                };
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar IntegrarModuloMenuApi");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("IntegrarModuloMenuApi");
            }
        }
    }
}
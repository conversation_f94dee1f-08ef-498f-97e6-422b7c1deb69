using System;
using System.Threading.Tasks;
using BBC.Test.Tests.Viagem.Fixture;
using Moq;
using SistemaInfo.BBC.Application.Services.Viagem;
using SistemaInfo.BBC.Domain.Models.ViagemVeiculos.Repository;
using Xunit;

namespace BBC.Test.Tests.Viagem
{
    [Collection(nameof(ViagemCollection))]
    public class ViagemAppServiceConsultarVeiculosViagemTest
    {
        private readonly ViagemFixture _fixture;
        private readonly ViagemAppService _appService;

        public ViagemAppServiceConsultarVeiculosViagemTest(ViagemFixture fixture)
        {
            _fixture = fixture;
            _appService = fixture.Mocker.CreateInstance<ViagemAppService>();
        }

        [Fact(DisplayName = "ConsultarVeiculosViagem - ID zero - Deve retornar erro")]
        [Trait("ViagemAppService", "ConsultarVeiculosViagem")]
        public async Task ConsultarVeiculosViagem_IdZero_DeveRetornarErro()
        {
            // Arrange
            const int viagemId = 0;

            // Act
            var resultado = await _appService.ConsultarVeiculosViagem(viagemId);

            // Assert
            Assert.False(resultado.sucesso);
            Assert.Equal("Id inválido.", resultado.mensagem);
            Assert.Null(resultado.data);
        }

        [Fact(DisplayName = "ConsultarVeiculosViagem - ID negativo - Deve retornar erro")]
        [Trait("ViagemAppService", "ConsultarVeiculosViagem")]
        public async Task ConsultarVeiculosViagem_IdNegativo_DeveRetornarErro()
        {
            // Arrange
            const int viagemId = -1;

            // Act
            var resultado = await _appService.ConsultarVeiculosViagem(viagemId);

            // Assert
            Assert.False(resultado.sucesso);
            Assert.Equal("Id inválido.", resultado.mensagem);
            Assert.Null(resultado.data);
        }

        [Fact(DisplayName = "ConsultarVeiculosViagem - Exceção no repositório - Deve retornar erro")]
        [Trait("ViagemAppService", "ConsultarVeiculosViagem")]
        public async Task ConsultarVeiculosViagem_ExcecaoNoRepositorio_DeveRetornarErro()
        {
            // Arrange
            const int viagemId = 1;
            const string mensagemErro = "Erro de conexão com banco de dados";

            _fixture.Mocker.GetMock<IViagemVeiculosReadRepository>()
                .Setup(x => x.Include(It.IsAny<System.Linq.Expressions.Expression<System.Func<SistemaInfo.BBC.Domain.Models.ViagemVeiculos.ViagemVeiculos, object>>>()))
                .Throws(new Exception(mensagemErro));

            // Act
            var resultado = await _appService.ConsultarVeiculosViagem(viagemId);

            // Assert
            Assert.False(resultado.sucesso);
            Assert.Equal(mensagemErro, resultado.mensagem);
            Assert.Null(resultado.data);
        }

        [Theory(DisplayName = "ConsultarVeiculosViagem - Diferentes IDs válidos - Deve processar corretamente")]
        [Trait("ViagemAppService", "ConsultarVeiculosViagem")]
        [InlineData(1)]
        [InlineData(100)]
        [InlineData(999999)]
        public async Task ConsultarVeiculosViagem_DiferentesIdsValidos_DeveProcessarCorretamente(int viagemId)
        {
            // Arrange
            _fixture.Mocker.GetMock<IViagemVeiculosReadRepository>()
                .Setup(x => x.Include(It.IsAny<System.Linq.Expressions.Expression<System.Func<SistemaInfo.BBC.Domain.Models.ViagemVeiculos.ViagemVeiculos, object>>>()))
                .Throws(new Exception("Nenhum veículo encontrado para esta viagem."));

            // Act
            var resultado = await _appService.ConsultarVeiculosViagem(viagemId);

            // Assert
            Assert.False(resultado.sucesso);
            Assert.NotNull(resultado.mensagem);
        }

        [Fact(DisplayName = "ConsultarVeiculosViagem - ID muito grande - Deve retornar erro")]
        [Trait("ViagemAppService", "ConsultarVeiculosViagem")]
        public async Task ConsultarVeiculosViagem_IdMuitoGrande_DeveRetornarErro()
        {
            // Arrange
            const int viagemId = int.MaxValue;

            _fixture.Mocker.GetMock<IViagemVeiculosReadRepository>()
                .Setup(x => x.Include(It.IsAny<System.Linq.Expressions.Expression<System.Func<SistemaInfo.BBC.Domain.Models.ViagemVeiculos.ViagemVeiculos, object>>>()))
                .Throws(new Exception("Viagem não encontrada."));

            // Act
            var resultado = await _appService.ConsultarVeiculosViagem(viagemId);

            // Assert
            Assert.False(resultado.sucesso);
            Assert.Equal("Viagem não encontrada.", resultado.mensagem);
            Assert.Null(resultado.data);
        }

        [Fact(DisplayName = "ConsultarVeiculosViagem - Timeout na consulta - Deve retornar erro")]
        [Trait("ViagemAppService", "ConsultarVeiculosViagem")]
        public async Task ConsultarVeiculosViagem_TimeoutNaConsulta_DeveRetornarErro()
        {
            // Arrange
            const int viagemId = 1;

            _fixture.Mocker.GetMock<IViagemVeiculosReadRepository>()
                .Setup(x => x.Include(It.IsAny<System.Linq.Expressions.Expression<System.Func<SistemaInfo.BBC.Domain.Models.ViagemVeiculos.ViagemVeiculos, object>>>()))
                .Throws(new TimeoutException("Timeout na consulta ao banco de dados"));

            // Act
            var resultado = await _appService.ConsultarVeiculosViagem(viagemId);

            // Assert
            Assert.False(resultado.sucesso);
            Assert.Equal("Timeout na consulta ao banco de dados", resultado.mensagem);
            Assert.Null(resultado.data);
        }

        [Fact(DisplayName = "ConsultarVeiculosViagem - Argumentos inválidos - Deve retornar erro")]
        [Trait("ViagemAppService", "ConsultarVeiculosViagem")]
        public async Task ConsultarVeiculosViagem_ArgumentosInvalidos_DeveRetornarErro()
        {
            // Arrange
            const int viagemId = 1;

            _fixture.Mocker.GetMock<IViagemVeiculosReadRepository>()
                .Setup(x => x.Include(It.IsAny<System.Linq.Expressions.Expression<System.Func<SistemaInfo.BBC.Domain.Models.ViagemVeiculos.ViagemVeiculos, object>>>()))
                .Throws(new ArgumentException("Argumentos inválidos fornecidos"));

            // Act
            var resultado = await _appService.ConsultarVeiculosViagem(viagemId);

            // Assert
            Assert.False(resultado.sucesso);
            Assert.Contains("The source IQueryable doesn't implement", resultado.mensagem);
            Assert.Null(resultado.data);
        }
    }
}

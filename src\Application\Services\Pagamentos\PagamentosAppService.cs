using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Text;
using System.Threading.Tasks;
using AutoMapper.QueryableExtensions;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using NLog;
using SistemaInfo.BBC.Application.External.Captalys.Interface;
using SistemaInfo.BBC.Application.External.Mobile2You.Interface;
using SistemaInfo.BBC.Application.Helpers;
using SistemaInfo.BBC.Application.Interface.Pagamentos;
using SistemaInfo.BBC.Application.Interface.Parametros;
using SistemaInfo.BBC.Application.Interface.Retencao;
using SistemaInfo.BBC.Application.Objects.Api.Pagamento;
using SistemaInfo.BBC.Application.Objects.Api.Retencao;
using SistemaInfo.BBC.Application.Objects.Api.Viagem;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.External.Captalys;
using SistemaInfo.BBC.Application.Objects.Web.Pagamentos;
using SistemaInfo.BBC.Application.Objects.Web.PainelPagamento;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Domain.External.Conductor.DTO.Cartao;
using SistemaInfo.BBC.Domain.External.Conductor.DTO.Transferencia;
using SistemaInfo.BBC.Domain.External.Conductor.Interface;
using SistemaInfo.BBC.Domain.External.Mobile2You.DTO;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.BBC.Domain.Models.BloqueioSpd.Repository;
using SistemaInfo.BBC.Domain.Models.Empresa.Repository;
using SistemaInfo.BBC.Domain.Models.Emprestimo;
using SistemaInfo.BBC.Domain.Models.Emprestimo.Repository;
using SistemaInfo.BBC.Domain.Models.Pagamentos.Commands;
using SistemaInfo.BBC.Domain.Models.Pagamentos.Repository;
using SistemaInfo.BBC.Domain.Models.Parametros.Repository;
using SistemaInfo.BBC.Domain.Models.Portador.Repository;
using SistemaInfo.BBC.Domain.Models.Retencao;
using SistemaInfo.BBC.Domain.Models.Usuario.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.Services.Pagamentos
{
    public class PagamentosAppService :
        AppService<Domain.Models.Pagamentos.Pagamentos, IPagamentosReadRepository, IPagamentosWriteRepository>,
        IPagamentosAppService
    {
        private readonly ICartaoRepository _cartaoRepository;
        private readonly IEmpresaReadRepository _empresaRepository;
        private readonly IEmprestimoReadRepository _emprestimoReadRepository;
        private readonly IParametrosAppService _parametrosAppService;
        private readonly IPortadorReadRepository _portadorReadRepository;
        private IPortadorWriteRepository _portadorWriteRepository;
        private readonly IRetencaoAppService _retencaoAppService;
        private readonly IRetencaoCaptalysAppService _retencaoCaptalysAppService;
        private readonly ITransacaoAppService _transacaoAppService;
        private readonly ITransferenciaRepository _transferenciaRepository;
        private readonly IParametrosReadRepository _parametrosReadRepository;
        private readonly IUsuarioReadRepository _usuarioReadRepository;

        private readonly IBloqueioSpdReadRepository _liberacaoBloqueioSpdTReadRepository;

        public PagamentosAppService(IAppEngine engine, IPagamentosReadRepository readRepository,
            IPagamentosWriteRepository writeRepository,
            ITransferenciaRepository transferenciaRepository, IEmpresaReadRepository empresaRepository,
            ICartaoRepository cartaoRepository, IPortadorReadRepository portadorReadRepository,
            IEmprestimoReadRepository emprestimoReadRepository, IRetencaoAppService retencaosAppService,
            IParametrosAppService parametrosAppService, IRetencaoCaptalysAppService retencaoCaptalysAppService,
            IPortadorWriteRepository portadorWriteRepository, ITransacaoAppService transacaoAppService,
            IParametrosReadRepository parametrosReadRepository,
            IBloqueioSpdReadRepository liberacaoBloqueioSpdReadRepository, IUsuarioReadRepository usuarioReadRepository)
            : base(engine, readRepository, writeRepository)
        {
            _transferenciaRepository = transferenciaRepository;
            _empresaRepository = empresaRepository;
            _cartaoRepository = cartaoRepository;
            _portadorReadRepository = portadorReadRepository;
            _emprestimoReadRepository = emprestimoReadRepository;
            _retencaoAppService = retencaosAppService;
            _parametrosAppService = parametrosAppService;
            _retencaoCaptalysAppService = retencaoCaptalysAppService;
            _portadorWriteRepository = portadorWriteRepository;
            _transacaoAppService = transacaoAppService;
            _parametrosReadRepository = parametrosReadRepository;
            _liberacaoBloqueioSpdTReadRepository = liberacaoBloqueioSpdReadRepository;
            _usuarioReadRepository = usuarioReadRepository;
        }

        /*
         public ConsultaGridPagamentoResponse ConsultarGridPainelPagamento(DateTime dtInicial, DateTime dtFinal, Status status, int take, int page,
            OrderFilters orderFilters, List<QueryFilters> filters)
        {
            foreach (var filter in filters)
                if (filter.Campo == "Portador.cpfCnpj")
                    filter.Valor = filter.Valor.OnlyNumbers();

            var pagamentos = DadosGridRelatorioPagamento(dtInicial, dtFinal, status, orderFilters, filters);
            var lCount = pagamentos.Count();

            var pag = pagamentos.Skip((page - 1) * take)
                .Take(take)
                .ProjectTo<ConsultaGridPagamentoItem>(Engine.Mapper.ConfigurationProvider).ToList();

            if (orderFilters != null)
                pag = AplicaOrdencao(pag, orderFilters);

            return new ConsultaGridPagamentoResponse
            {
                items = pag,
                totalItems = lCount
            };
        }
         */


        public ConsultaGridPagamentoResponse ConsultarGridPainelPagamento(string dataInicial, string dataFinal,
            Status status, int empresaId, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters)
        {
            foreach (var filter in filters.Where(filter => filter.Campo == "Portador.cpfCnpj"))
                filter.Valor = filter.Valor.OnlyNumbers();

            var pagamentos =
                DadosGridRelatorioPagamento(dataInicial, dataFinal, status, empresaId, orderFilters, filters);
            var lCount = pagamentos.Count();

            var pag = pagamentos.Skip((page - 1) * take)
                .Take(take)
                .ProjectTo<ConsultaGridPagamentoItem>(Engine.Mapper.ConfigurationProvider).ToList();

            if (orderFilters != null)
                pag = AplicaOrdencao(pag, orderFilters);

            return new ConsultaGridPagamentoResponse
            {
                items = pag,
                totalItems = lCount
            };
        }

        public List<ConsultaGridPagamentoItem> DadosRelatorioGridPagamentos(String dataInicial, String dataFinal,
            Status status, int empresaId,
            OrderFilters orderFilters, List<QueryFilters> filters)
        {
            var dados = DadosGridRelatorioPagamento(dataInicial, dataFinal, status, empresaId, orderFilters, filters);
            return dados.ProjectTo<ConsultaGridPagamentoItem>(Engine.Mapper.ConfigurationProvider).ToList();
        }

        /*
         public List<ConsultaGridPagamentoItem> DadosRelatorioGridPagamentos(DateTime dtInicial, DateTime dtFinal,
            Status status, OrderFilters orderFilters,
            List<QueryFilters> filters)
        {
            var dados = DadosGridRelatorioPagamento(dtInicial, dtFinal, status, orderFilters, filters);
            return dados.ProjectTo<ConsultaGridPagamentoItem>(Engine.Mapper.ConfigurationProvider).ToList();
        }
         */


        public Task SavePagamento(PagamentosRequest lModel)
        {
            try
            {
                new LogHelper().LogOperationStart("SavePagamento");
                var lBaixado = false;

                if (lModel.FormaPagamento == 1)
                    if (lModel.DataPrevisaoPagamento.ToDateTime() < DateTime.Today)
                        throw new Exception("Data inferior a data atual.");

                if (!string.IsNullOrEmpty(lModel.DataPrevisaoPagamento))
                    if (lModel.DataPrevisaoPagamento.ToDateTime().ToShortDateString() ==
                        DateTime.Today.ToShortDateString())
                    {
                        var ret = RealizaTransferenciaEntreContas(new Transferencia
                        {
                            originalAccount = lModel.contaOrigem.ToInt(),
                            destinationAccount = lModel.contaDestino.ToInt(),
                            amount = Decimal.Parse(lModel.Valor, NumberStyles.Any, new CultureInfo("pt-BR")),
                            description = "Transferência CIOT: " + lModel.CiotId
                        }).Result;

                        if (ret != null && ret.Sucesso)
                        {
                            lBaixado = true;
                            lModel.DataPrevisaoPagamento = DateTime.Now.ToString("dd/MM/yyyy");

                            //P2PTRANSFER
                            var requestP2P = new P2PRequest
                            {
                                pagamentoId = "Auto",
                                amount = Decimal.Parse(lModel.Valor, NumberStyles.Any, new CultureInfo("pt-BR")),
                                originalAccount = lModel.contaOrigem.ToInt(),
                                destinationAccount = lModel.contaDestino.ToInt(),
                                idAdjustment = ret.idAdjustment ?? 0,
                                idAdjustmentDestination = ret.idAdjustmentDestination ?? 0
                            };
                            _transacaoAppService.P2P(requestP2P);
                        }
                    }

                if (lModel.Id == "Auto")
                {
                    if (lModel.FormaPagamento == 1 && !lBaixado)
                    {
                        lModel.Status = Status.Aberto;
                    }
                    else
                    {
                        lModel.Status = Status.Baixado;
                    }

                    lModel.Id = null;
                    var lPagamento = Mapper.Map<PagamentosAdicionarCommand>(lModel);
                    return Engine.CommandBus.SendCommandAsync(lPagamento);
                }
                else
                {
                    var lPagamento = Mapper.Map<PagamentosAtualizarCommand>(lModel);
                    return Engine.CommandBus.SendCommandAsync(lPagamento);
                }
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar SavePagamento");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("SavePagamento");
            }
        }

        public ConsultarPorIdPagamentoResponse BuscarPorId(int id)
        {
            try
            {
                new LogHelper().LogOperationStart("BuscarPorId");
                return Mapper.Map<ConsultarPorIdPagamentoResponse>(Repository.Query.Where(x => x.Id == id)
                    .Include(x => x.Empresa).Include(x => x.Portador).Include(x => x.Banco).FirstOrDefault());
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar BuscarPorId");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("BuscarPorId");
            }
        }

        public Task<ConsultarContaResp> ConsultaContaCPF(string cpfCnpj)
        {
            try
            {
                new LogHelper().LogOperationStart("ConsultaContaCPF");

                if (cpfCnpj.Length > 11)
                {
                    var aPj = _cartaoRepository.ConusltaContaPessoaJuridica(cpfCnpj);

                    if (aPj.Result.results?.FirstOrDefault()?.statusSPD != null)
                    {
                        foreach (var lBloqueioSpd in aPj.Result.results?.FirstOrDefault()?.statusSPD)
                        {
                            if (lBloqueioSpd.statusId == 1 && lBloqueioSpd.createDate < DateTime.Now.AddDays(-15))
                            {
                                throw new Exception("Erro StautsSPD Origem Data");
                            }
                            else if (lBloqueioSpd.statusId != 4 && lBloqueioSpd.statusId != 10 &&
                                     lBloqueioSpd.statusId != 11 && lBloqueioSpd.statusId != 12 &&
                                     lBloqueioSpd.statusId != 18 && lBloqueioSpd.statusId != 19 &&
                                     lBloqueioSpd.statusId != 20 && lBloqueioSpd.statusId != 1)
                            {
                                throw new Exception("Erro StautsSPD Origem");
                            }
                        }
                    }

                    return _cartaoRepository.ConsultarContas(null, null, cpfCnpj);
                }
                else
                {
                    var a = _cartaoRepository.ConsultarContas(null, null, cpfCnpj);
                    var lValidacaoStatusSpdContaOrigem = _cartaoRepository.ConsultarPessoa(a?.Result?.content
                        ?.FirstOrDefault(x => StatusConta().Contains(x.idStatusConta))?.idPessoa.ToDecimalSafe() ?? 0);

                    if (lValidacaoStatusSpdContaOrigem.Result.statusSPD != null)
                    {
                        foreach (var lBloqueioSpd in lValidacaoStatusSpdContaOrigem.Result.statusSPD)
                        {
                            if (lBloqueioSpd.statusId != 4 && lBloqueioSpd.statusId != 10 &&
                                lBloqueioSpd.statusId != 11 && lBloqueioSpd.statusId != 12 &&
                                lBloqueioSpd.statusId != 18 && lBloqueioSpd.statusId != 19 &&
                                lBloqueioSpd.statusId != 20)
                            {
                                throw new Exception("Erro StautsSPD Origem");
                            }
                        }
                    }

                    return _cartaoRepository.ConsultarContas(null, null, cpfCnpj);
                }
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar ConsultaContaCPF");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("ConsultaContaCPF");
            }
        }

        public async Task AlterarStatus(PagamentosStatusRequest lPagamentoStatus)
        {
            try
            {
                new LogHelper().LogOperationStart("AlterarStatus");
                var lPagamento = Mapper.Map<PagamentosAlterarStatusCommand>(lPagamentoStatus);
                await Engine.CommandBus.SendCommandAsync(lPagamento);
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar AlterarStatus");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("AlterarStatus");
            }
        }

        public Task<IntegrarPagamentoResponse> ReenviarPagamento(PagamentosReenviarRequest lReenviarPagamento)
        {
            try
            {
                new LogHelper().LogOperationStart("ReenviarPagamento");
                var lPagamento = Repository.Query
                    .Include(a => a.Empresa)
                    .Include(a => a.Portador)
                    .FirstOrDefaultAsync(a => a.Id == lReenviarPagamento.Id).Result;
                var lPagamentoReenviar = Mapper.Map<PagamentoIntegrarRequest>(lPagamento);
                return IntegrarPagamento(lPagamentoReenviar);
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar ReenviarPagamento");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("ReenviarPagamento");
            }
        }

        public async Task<IntegrarPagamentoResponse> IntegrarPagamento(
            PagamentoIntegrarRequest pagamentoIntegrarRequest)
        {
            var log = LogManager.GetCurrentClassLogger();
            log.Info("Inpicio IntegrarPagamento empresa: " + Engine.User.EmpresaId +
                     ", cpf destino: " + pagamentoIntegrarRequest.CpfContaDestino + ", " +
                     "conta destino: " + pagamentoIntegrarRequest.IdContaDestino + ", " +
                     "conta origem: " + pagamentoIntegrarRequest.IdContaOrigem + ", " +
                     "valor: " + pagamentoIntegrarRequest.Valor + ", " +
                     "Descricao: " + pagamentoIntegrarRequest.Descricao + ", " +
                     "percentual: " + pagamentoIntegrarRequest.PercentualTransferencia);


            var lPagamentoAdicionar = new PagamentosAdicionarCommand();
            var lPagamentoAtualizar = new PagamentosAtualizarCommand();
            var lEmpresa = _empresaRepository.GetById(Engine.User.EmpresaId);
            var lLiberacaoBloqueiosSpd =
                _liberacaoBloqueioSpdTReadRepository.Where(lb => lb.EmpresaId == lEmpresa.Id && lb.Ativo == 1)
                    .Select(lb => lb.Codigo).ToList();

            #region Validações

            if (pagamentoIntegrarRequest.CpfContaDestino.IsNullOrWhiteSpace())
                throw new Exception("CPF do destinatário não foi informado!");

            var lPortador = _portadorReadRepository.Where(x => x.CpfCnpj == pagamentoIntegrarRequest.CpfContaDestino)
                .FirstOrDefault();

            if (lPortador == null) throw new Exception("Portador não encontrado.");

            if (pagamentoIntegrarRequest.FormaPagamento == FormaPagamento.Deposito.ToInt32() &&
                (string.IsNullOrWhiteSpace(pagamentoIntegrarRequest.Agencia) ||
                 string.IsNullOrWhiteSpace(pagamentoIntegrarRequest.Conta) ||
                 string.IsNullOrWhiteSpace(pagamentoIntegrarRequest.Banco) ||
                 string.IsNullOrWhiteSpace(pagamentoIntegrarRequest.TipoConta)))
                throw new Exception("Informe corretamente os campos para depósito.");

            if (!pagamentoIntegrarRequest.IdPagamentoExterno.HasValue)
                throw new Exception("Código pagamento externo não informado.");

            #endregion

            #region Mapeamentos

            var lPagamentoIntegrar = new PagamentoIntegrar();
            var lpagamentoExistente = new Domain.Models.Pagamentos.Pagamentos();

            //pagamento pendente
            lpagamentoExistente = Repository.Query.Where(x => x.IdPagamentoExterno == pagamentoIntegrarRequest
                                                                  .IdPagamentoExterno &&
                                                              x.PortadorId == lPortador.Id &&
                                                              x.EmpresaId == Engine.User.EmpresaId).FirstOrDefault();

            if (lpagamentoExistente == null)
            {
                log.Info("Pagamento existente");
                lPagamentoIntegrar = Mapper.Map<PagamentoIntegrar>(pagamentoIntegrarRequest);

                lPagamentoIntegrar.Id = null;

                lPagamentoAdicionar = Mapper.Map<PagamentosAdicionarCommand>(lPagamentoIntegrar);
                lPagamentoAdicionar.OrigemPagamento = ETipoOrigemPagamento.OrigemJSLFrete;
                log.Info("portador " + lPortador.Id);
                lPagamentoAdicionar.portadorId = lPortador.Id;
                lPagamentoAdicionar.Status = Status.Processamento;
            }
            else
            {
                log.Info("Pagamento inexistente");
                if (lpagamentoExistente.Status == Status.Baixado)
                {
                    return new IntegrarPagamentoResponse
                    {
                        Id = lpagamentoExistente.Id
                    };
                }

                if (lpagamentoExistente.Status == Status.Cancelado)
                {
                    return new IntegrarPagamentoResponse
                    {
                        Id = lpagamentoExistente.Id
                    };
                }

                if (lpagamentoExistente.Status == Status.Processamento)
                {
                    var ParametroTempoParaReintegracao = _parametrosAppService.GetParametrosAsync(-1,
                        Domain.Models.Parametros.Parametros.TipoDoParametro.CodigoPeriodoMaximoProcessamento,
                        Domain.Models.Parametros.Parametros.TipoDoValor.Number).Result;

                    if (lpagamentoExistente.DataCadastro.AddMinutes(ParametroTempoParaReintegracao.Valor.ToIntSafe()) >
                        DateTime.Now)
                    {
                        throw new Exception("Pagamento em processamento não pode ser reenviado!");
                    }
                }

                lPagamentoIntegrar = Mapper.Map<PagamentoIntegrar>(lpagamentoExistente);

                lPagamentoIntegrar.IdContaDestino = lPortador.Id;

                lPagamentoAtualizar = Mapper.Map<PagamentosAtualizarCommand>(lPagamentoIntegrar);
                lPagamentoAtualizar.OrigemPagamento = ETipoOrigemPagamento.OrigemJSLFrete;
                lPagamentoAtualizar.portadorId = lPortador.Id;
                lPagamentoAtualizar.Status = Status.Processamento;
            }

            #region Salvar/Editar

            //Salvar/Editar pagamento
            int pagamentoId;

            try
            {
                pagamentoId = Engine.CommandBus
                    .SendCommandAsync<Domain.Models.Pagamentos.Pagamentos>(lpagamentoExistente == null
                        ? (object)lPagamentoAdicionar
                        : lPagamentoAtualizar).Result.Id;
                log.Info("Salvar/Editar pagamento: " + pagamentoId);
            }
            catch (Exception e)
            {
                var lErro = e.Message.Length > 250 ? e.Message.Substring(0, 250) : e.Message;
                throw new Exception("Erro ao salvar pagamento, Mensagem: " + lErro);
            }

            #endregion


            lPagamentoIntegrar.Id = pagamentoId.ToString();

            //CONTA ORIGEM
            if (!pagamentoIntegrarRequest.IdContaOrigem.HasValue)
            {
                try
                {
                    log.Info("Inicio Validacao de empresa!");
                    var pagamentoDescricao =
                        JsonConvert.DeserializeObject<IntegrarPagamentoDescricaoRequest>(pagamentoIntegrarRequest
                            .Descricao);
                    log.Info("Inicio Validacao de empresa! Deserialize!");
                    if (!pagamentoDescricao.branchNumber.IsNullOrWhiteSpace())
                    {
                        string filialPagamento = pagamentoDescricao.branchNumber.Substring(0, 1);
                        //Obtém empresas habilitadas para pagamento do parametro do banco de dados

                        log.Info("Inicio Validacao de empresa! Antes consulta");
                        var parametroEmpresaPagamento = _parametrosReadRepository
                            .GetParametrosAsync(-1,
                                Domain.Models.Parametros.Parametros.TipoDoParametro.EmpresaPagamento,
                                Domain.Models.Parametros.Parametros.TipoDoValor.Criptografia)
                            .Result;

                        log.Info("Inicio Validacao de empresa! Depois consulta");
                        log.Info(parametroEmpresaPagamento);
                        if (parametroEmpresaPagamento == null)
                        {
                            log.Info("Cripto empresa erro!");
                            throw new Exception("Erro ao validar empresa do pagamento!");
                        }

                        var empresaPagamentos = Encoding.UTF8
                            .GetString(Convert.FromBase64String(parametroEmpresaPagamento.ValorCriptografado))
                            .Split(char.Parse("|"));
                        ;

                        //Percorre empresas habilitadas para pagamento do appsettings
                        foreach (var empresa in empresaPagamentos)
                        {
                            //Verifica se empresa está habilitada para pagamento e se sigla é a mesma da desccrição do pagamento 
                            if (lEmpresa.Cnpj == empresa.Split(char.Parse(","))[0] &&
                                empresa.Split(char.Parse(","))[1] != filialPagamento)
                            {
                                await _atualizaEtapaErroIntegracao(lPagamentoIntegrar, 1);
                                log.Info(
                                    "Empresa de pagamento difere da empresa descrita na origem do pagamento! engine:: " +
                                    Engine.User.Login + " , empresaid: " + Engine.User.EmpresaId +
                                    ", descricaoPagamento:" + pagamentoIntegrarRequest.Descricao);
                                throw new Exception(
                                    "Empresa de pagamento difere da empresa descrita na origem do pagamento!");
                            }
                        }
                    }
                }
                catch (Exception e)
                {
                    log.Error(e, "Erro ao validar empresa do pagamento: ");
                    await _atualizaEtapaErroIntegracao(lPagamentoIntegrar, 1);
                    throw new Exception("Erro ao validar empresa do pagamento!");
                }

                var lContaOrigem = _cartaoRepository.ConsultarContas(null, null, lEmpresa.Cnpj);
                if (lContaOrigem == null)
                {
                    await _atualizaEtapaErroIntegracao(lPagamentoIntegrar, 1);
                    throw new Exception("Conta origem não foi definida!");
                }

                log.Info("Obtendo conta origem: " + lContaOrigem?.Result?.content
                    ?.FirstOrDefault(x => StatusConta().Contains(x.idStatusConta))?.id);
                pagamentoIntegrarRequest.IdContaOrigem = lContaOrigem?.Result?.content
                    ?.FirstOrDefault(x => StatusConta().Contains(x.idStatusConta))?.id;

                if (pagamentoIntegrarRequest.IdContaOrigem == null)
                    throw new Exception("Conta origem não foi encontrada!");
            }

            //CONTA DESTINO
            if (!pagamentoIntegrarRequest.IdContaDestino.HasValue)
            {
                if (!pagamentoIntegrarRequest.CpfContaDestino.IsNullOrWhiteSpace())
                {
                    var lContaDestino =
                        _cartaoRepository.ConsultarContas(null, null, pagamentoIntegrarRequest.CpfContaDestino);
                    if (lContaDestino == null)
                    {
                        await _atualizaEtapaErroIntegracao(lPagamentoIntegrar, 1);
                        throw new Exception("Conta destino não foi definida!");
                    }

                    pagamentoIntegrarRequest.IdContaDestino = lContaDestino?.Result?.content
                        ?.FirstOrDefault(x => StatusConta().Contains(x.idStatusConta))?.id;
                    log.Info("conta destino: " + pagamentoIntegrarRequest.IdContaDestino);

                    if (pagamentoIntegrarRequest.CpfContaDestino.Length > 11)
                    {
                        var aPj = _cartaoRepository.ConusltaContaPessoaJuridica(
                            pagamentoIntegrarRequest.CpfContaDestino);

                        if (aPj.Result.results?.FirstOrDefault()?.statusSPD != null)
                        {
                            foreach (var lBloqueioSpd in aPj.Result.results?.FirstOrDefault()?.statusSPD)
                            {
                                if (lBloqueioSpd.statusId == 1 && lBloqueioSpd.createDate < DateTime.Now.AddDays(-15))
                                {
                                    await _atualizaEtapaErroIntegracao(lPagamentoIntegrar, 1);
                                    throw new Exception("Conta Destino Bloqueada!");
                                }
                                else if (lBloqueioSpd.statusId != 4 && lBloqueioSpd.statusId != 10 &&
                                         lBloqueioSpd.statusId != 11 && lBloqueioSpd.statusId != 12 &&
                                         lBloqueioSpd.statusId != 18 && lBloqueioSpd.statusId != 19 &&
                                         lBloqueioSpd.statusId != 20 && lBloqueioSpd.statusId != 1)
                                {
                                    await _atualizaEtapaErroIntegracao(lPagamentoIntegrar, 1);
                                    throw new Exception("Conta Destino Bloqueada!");
                                }
                            }
                        }
                    }
                    else
                    {
                        var lValidacaoStatusSpdContaOrigem =
                            _cartaoRepository.ConsultarPessoa(
                                lContaDestino?.Result?.content
                                    ?.FirstOrDefault(x => StatusConta().Contains(x.idStatusConta))?.idPessoa
                                    .ToDecimalSafe() ?? 0);

                        if (lValidacaoStatusSpdContaOrigem.Result.statusSPD != null)
                        {
                            foreach (var lBloqueioSpd in lValidacaoStatusSpdContaOrigem.Result.statusSPD)
                            {
                                if (lBloqueioSpd.statusId != 4 && lBloqueioSpd.statusId != 10 &&
                                    lBloqueioSpd.statusId != 11 && lBloqueioSpd.statusId != 12 &&
                                    lBloqueioSpd.statusId != 18 && lBloqueioSpd.statusId != 19 &&
                                    lBloqueioSpd.statusId != 20)
                                {
                                    await _atualizaEtapaErroIntegracao(lPagamentoIntegrar, 1);
                                    throw new Exception("Conta Destino Bloqueada!");
                                }
                            }
                        }
                    }

                    if (pagamentoIntegrarRequest.IdContaDestino == null)
                        throw new Exception("Conta destino não foi encontrada!");
                }
                else
                {
                    await _atualizaEtapaErroIntegracao(lPagamentoIntegrar, 1);
                    throw new Exception("Conta destino não foi definida!");
                }
            }

            //CONTA Trasferencia
            if (!pagamentoIntegrarRequest.IdContaTransferencia.HasValue)
            {
                log.Info("IdContaTransferencia request: " + pagamentoIntegrarRequest.IdContaTransferencia);
                if (!pagamentoIntegrarRequest.CpfContaTransferencia.IsNullOrWhiteSpace())
                {
                    var lContaTransferencia =
                        _cartaoRepository.ConsultarContas(null, null, pagamentoIntegrarRequest.CpfContaTransferencia);

                    var lConta = lContaTransferencia?.Result?.content
                        ?.FirstOrDefault(x => StatusConta().Contains(x.idStatusConta))?.id;

                    if (lConta == null)
                    {
                        await _atualizaEtapaErroIntegracao(lPagamentoIntegrar, 1);
                        throw new Exception("Conta transferência não foi encontrada!");
                    }

                    log.Info("IdContaTransferencia: " + lContaTransferencia?.Result?.content
                        ?.FirstOrDefault(x => StatusConta().Contains(x.idStatusConta))?.id);

                    if (pagamentoIntegrarRequest.CpfContaTransferencia.Length > 11)
                    {
                        var aPj = _cartaoRepository.ConusltaContaPessoaJuridica(pagamentoIntegrarRequest
                            .CpfContaTransferencia);

                        if (aPj.Result.results?.FirstOrDefault()?.statusSPD != null)
                        {
                            foreach (var lBloqueioSpd in aPj.Result.results?.FirstOrDefault()?.statusSPD)
                            {
                                if (lBloqueioSpd.statusId == 1 && lBloqueioSpd.createDate < DateTime.Now.AddDays(-15))
                                {
                                    await _atualizaEtapaErroIntegracao(lPagamentoIntegrar, 1);
                                    throw new Exception("Conta Transferência Bloqueada!");
                                }
                                else if (lBloqueioSpd.statusId != 4 && lBloqueioSpd.statusId != 10 &&
                                         lBloqueioSpd.statusId != 11 && lBloqueioSpd.statusId != 12 &&
                                         lBloqueioSpd.statusId != 18 && lBloqueioSpd.statusId != 19 &&
                                         lBloqueioSpd.statusId != 20 && lBloqueioSpd.statusId != 1)
                                {
                                    await _atualizaEtapaErroIntegracao(lPagamentoIntegrar, 1);
                                    throw new Exception("Conta Transferência Bloqueada!");
                                }
                            }
                        }
                    }
                    else
                    {
                        var lValidacaoStatusSpdContaOrigem =
                            _cartaoRepository.ConsultarPessoa(
                                lContaTransferencia?.Result?.content
                                    ?.FirstOrDefault(x => StatusConta().Contains(x.idStatusConta))?.idPessoa
                                    .ToDecimalSafe() ?? 0);

                        if (lValidacaoStatusSpdContaOrigem.Result.statusSPD != null)
                        {
                            foreach (var lBloqueioSpd in lValidacaoStatusSpdContaOrigem.Result.statusSPD)

                            {
                                if (lBloqueioSpd.statusId != 4 && lBloqueioSpd.statusId != 10 &&
                                    lBloqueioSpd.statusId != 11 && lBloqueioSpd.statusId != 12 &&
                                    lBloqueioSpd.statusId != 18 && lBloqueioSpd.statusId != 19 &&
                                    lBloqueioSpd.statusId != 20)
                                {
                                    await _atualizaEtapaErroIntegracao(lPagamentoIntegrar, 1);
                                    throw new Exception("Conta Transferência Bloqueada!");
                                }
                            }
                        }
                    }
                }
            }

            #endregion

            #region ValidaPagamentoDuplicadoDock

            if (lpagamentoExistente != null && !lpagamentoExistente.Descricao.IsNullOrWhiteSpace())
            {
                var consultaRequest = new ConsultarPagamentosContaRequest()
                {
                    ContaDestino = pagamentoIntegrarRequest.IdContaDestino.ToString(),
                    ContaOrigem = pagamentoIntegrarRequest.IdContaOrigem.ToString(),
                    Valor = pagamentoIntegrarRequest.Valor.ToString().Replace(',', '.'),
                    DataPagamento = lpagamentoExistente.DataCadastro.ToString("yyyy-MM-dd") + " 00:00:00"
                };
                log.Info("ValidaPagamentoDuplicadoDock");
                var lRetornoExtratoPagamento =
                    await _cartaoRepository.ConsultarPagamentosConta(consultaRequest);

                var lExtraoPagamentos = JsonConvert.DeserializeObject<List<Transferencia>>(lRetornoExtratoPagamento,
                    new JsonSerializerSettings { DateTimeZoneHandling = DateTimeZoneHandling.Local });

                foreach (var lPagamento in lExtraoPagamentos)
                {
                    if (lPagamento.description == lpagamentoExistente.Descricao)
                    {
                        lpagamentoExistente.EtapaErroIntegracao = null;
                        lpagamentoExistente.Status = Status.Baixado;
                        lpagamentoExistente.DataBaixa = DateTime.Now;

                        Repository.Command.Update(lpagamentoExistente);
                        Repository.Query.SaveChanges();

                        return new IntegrarPagamentoResponse
                        {
                            Id = lpagamentoExistente.Id
                        };
                    }
                }
            }

            #endregion


            #region Transferência / Retenção

            log.Info("Transferência / Retenção");
            var etapa1 = new TransferenciaEntreContaResp(); //Transf. Origem X Destino
            var etapa2 = new TransferenciaEntreContaResp(); //Transf. valor retino
            var etapa3 = new RespPadrao(); //Cadastra retenção
            var etapa4 = new RespPadrao(); //Integrar retenção captalys
            var etapa5 = new TransferenciaEntreContaResp(); //Transf. Dest. X TRANF.
            var lRealizaTransfRetido = false;
            var lSucessTodasEtapas = false;

            lPagamentoIntegrar.DataPrevisaoPagamento =
                lPagamentoIntegrar.DataPrevisaoPagamento?.StartOfDay();

            if (lPagamentoIntegrar.DataPrevisaoPagamento != null &&
                lPagamentoIntegrar.DataPrevisaoPagamento <= DateTime.Today &&
                lPagamentoIntegrar.FormaPagamento == FormaPagamento.Cartao.ToInt32())
            {
                #region Tranf. Origem x Dest.

                log.Info("Transferência / Retenção / previsão");
                //Verificar etapa qual parou caso erro
                if (lPagamentoIntegrar.EtapaErroIntegracao <= 1 || lPagamentoIntegrar.EtapaErroIntegracao == null)
                {
                    try
                    {
                        log.Info("Transferência / Retenção erro integração");
                        etapa1 =
                            RealizaTransferenciaEntreContas(Mapper.Map<Transferencia>(pagamentoIntegrarRequest)).Result;
                    }
                    catch (Exception e)
                    {
                        log.Info("Transferência / Retenção erro integração erro:" + e.Message);
                        await _atualizaEtapaErroIntegracao(lPagamentoIntegrar, 1);

                        var lErro = e.Message.Length > 250 ? e.Message.Substring(0, 250) : e.Message;
                        throw new Exception("Erro ao processar pagamento!" + lErro);
                    }

                    if (!etapa1.Sucesso)
                    {
                        await _atualizaEtapaErroIntegracao(lPagamentoIntegrar, 1);

                        var lErroetapa1 = etapa1.message.Length > 250
                            ? etapa1.message.Substring(0, 250)
                            : etapa1.message;
                        throw new Exception(lErroetapa1);
                    }
                    else
                    {
                        lPagamentoIntegrar.EtapaErroIntegracao = null;
                    }

                    if (lpagamentoExistente == null)
                        lpagamentoExistente = Repository.Query.Where(x => x.Id == pagamentoId).FirstOrDefault();

                    lpagamentoExistente.ContaOrigem = pagamentoIntegrarRequest.IdContaOrigem.ToStringSafe();
                    lpagamentoExistente.ContaDestino = pagamentoIntegrarRequest.IdContaDestino.ToStringSafe();

                    Repository.Command.Update(lpagamentoExistente);
                    Repository.Query.SaveChanges();
                }

                #endregion

                #region Empréstimo/Retenção - cartão

                //VERIFICA EMPRÉSTIMO/RETENÇAO e se Cartão
                var lEmprestimo = _emprestimoReadRepository
                    .Include(x => x.Retencoes).FirstOrDefault(x =>
                        x.CpfCnpjPortador == lPortador.CpfCnpj && x.Status != StatusEmprestimo.Quitada);

                decimal lValorRetencao = 0;

                if (lEmprestimo != null &&
                    pagamentoIntegrarRequest.FormaPagamento == FormaPagamento.Cartao.ToInt32() &&
                    lEmprestimo.Status != StatusEmprestimo.Quitada)
                {
                    log.Info("Área de empréstimo");
                    var lTaxaRetencao = lEmprestimo.TaxaRetencao;
                    var lValorPagamento = Decimal.Parse(lPagamentoIntegrar.Valor, NumberStyles.Any,
                        new CultureInfo("pt-BR"));
                    lValorRetencao = lTaxaRetencao * lValorPagamento / 100;
                    var lSomaRetencoes = lEmprestimo.Retencoes.Sum(r => r.Valor);
                    var lsaldoDevedor = lEmprestimo.ValorAquisicao - lSomaRetencoes;


                    //valida valor da aquisição
                    if (lEmprestimo.ValorAquisicao < lEmprestimo.ValorPago || lsaldoDevedor <= 0)
                    {
                    }
                    else
                    {
                        lRealizaTransfRetido = true;
                        log.Info("Área de empréstimo transf retido");
                        if (lValorRetencao > lsaldoDevedor) lValorRetencao = lsaldoDevedor;

                        //Tranferir valor retido
                        var lIdContaParametro = _parametrosAppService.GetParametrosAsync(-1,
                            Domain.Models.Parametros.Parametros.TipoDoParametro.CodigoContaTransferenciaValorRetencao,
                            Domain.Models.Parametros.Parametros.TipoDoValor.Number).Result.Valor;

                        //Verificar etapa qual parou caso erro
                        if (lPagamentoIntegrar.EtapaErroIntegracao <= 2 ||
                            lPagamentoIntegrar.EtapaErroIntegracao == null)
                        {
                            log.Info("Área de empréstimo transf retido origem: " +
                                     pagamentoIntegrarRequest.IdContaDestino);
                            etapa2 = RealizaTransferenciaEntreContas(new Transferencia
                            {
                                amount = lValorRetencao,
                                description = "Transferência de valor retido",
                                originalAccount = pagamentoIntegrarRequest.IdContaDestino ?? 0,
                                destinationAccount = lIdContaParametro.ToInt32()
                            }).Result;

                            if (!etapa2.Sucesso)
                            {
                                await _atualizaEtapaErroIntegracao(lPagamentoIntegrar, 2);

                                var lErroetapa2 = etapa2.message.Length > 250
                                    ? etapa2.message.Substring(0, 250)
                                    : etapa2.message;
                                throw new Exception(lErroetapa2);
                            }
                            else
                            {
                                lPagamentoIntegrar.EtapaErroIntegracao = null;
                            }

                            log.Info("Área de empréstimo p2p retido origem: " +
                                     pagamentoIntegrarRequest.IdContaDestino);

                            //P2PTRANSFER
                            var requestP2P = new P2PRequest
                            {
                                pagamentoId = pagamentoId.ToString(),
                                amount = lValorRetencao,
                                originalAccount = pagamentoIntegrarRequest.IdContaDestino ?? 0,
                                destinationAccount = lIdContaParametro.ToInt32(),
                                idAdjustment = etapa2.idAdjustment ?? 0,
                                idAdjustmentDestination = etapa2.idAdjustmentDestination ?? 0
                            };
                            await _transacaoAppService.P2P(requestP2P);
                        }

                        //Verificar etapa qual parou caso erro
                        if (lPagamentoIntegrar.EtapaErroIntegracao <= 3 ||
                            lPagamentoIntegrar.EtapaErroIntegracao == null)
                        {
                            var lRetencao = new RetencaoCadastrarRequest
                            {
                                Valor = lValorRetencao,
                                Status = StatusRetencao.Aberta,
                                MensagemIntegracao = "Retenção de empréstimo automatizado",
                                EmprestimoId = lEmprestimo.Id,
                                PagamentoId = pagamentoId
                            };

                            log.Info("Área Retenção de empréstimo automatizado");

                            //cadastra retenção no bbc
                            etapa3 = _retencaoAppService.Cadastrar(lRetencao).Result;

                            if (!etapa3.sucesso)
                            {
                                await _atualizaEtapaErroIntegracao(lPagamentoIntegrar, 3);

                                var lErroetapa3 = etapa3.mensagem.Length > 250
                                    ? etapa3.mensagem.Substring(0, 250)
                                    : etapa3.mensagem;
                                throw new Exception(lErroetapa3);
                            }
                            else
                            {
                                lPagamentoIntegrar.EtapaErroIntegracao = null;
                            }
                        }

                        if (!string.IsNullOrWhiteSpace(lEmprestimo.IdState))
                            if (lPagamentoIntegrar.EtapaErroIntegracao <= 4 ||
                                lPagamentoIntegrar.EtapaErroIntegracao == null)
                            {
                                log.Info("ÁRetenção de empréstimo automatizado CAPTALYS");
                                //INTEGRAR COM CAPTALYS
                                var lListRet = new List<RetencaoReq>();
                                lListRet.Add(new RetencaoReq
                                {
                                    CnpjConta = lPortador.CpfCnpj,
                                    IdOperacao = lEmprestimo.IdState,
                                    NumeroConta = pagamentoIntegrarRequest.IdContaDestino.ToString(),
                                    IdLancamento = pagamentoId.ToString(),
                                    ValorLiquido = lValorRetencao,
                                    DataLancamento = DateTime.Now,
                                    DescricaoLancamento = "Retenção de empréstimo automatizado"
                                });
                                etapa4 = _retencaoCaptalysAppService.IntegrarRetencao(lListRet).Result;

                                if (!etapa4.sucesso)
                                {
                                    await _atualizaEtapaErroIntegracao(lPagamentoIntegrar, 4);

                                    var lErroetapa4 = etapa4.mensagem.Length > 250
                                        ? etapa4.mensagem.Substring(0, 250)
                                        : etapa4.mensagem;
                                    throw new Exception(lErroetapa4);
                                }
                                else
                                {
                                    lPagamentoIntegrar.EtapaErroIntegracao = null;
                                }

                                var lRetencao =
                                    _retencaoAppService.Repository.Query.FirstOrDefault(x =>
                                        x.PagamentoId == pagamentoId && x.Status == StatusRetencao.Aberta);

                                //atualiza status retenção
                                if (etapa3.sucesso && lRetencao != null)
                                {
                                    log.Info("ÁRetenção de empréstimo automatizado etapa3");
                                    lRetencao.Status = StatusRetencao.Integrada;
                                    lRetencao.DataIntegracao = DateTime.Now;
                                    lRetencao.MensagemIntegracao = "Integração realizada com sucesso";

                                    _retencaoAppService.Repository.Command.Update(lRetencao);
                                    _retencaoAppService.Repository.Command.SaveChanges();
                                }
                            }
                    }
                }

                #endregion

                #region Transf. DEST X TRANSF.

                //Verificar etapa qual parou caso erro
                if (lPagamentoIntegrar.EtapaErroIntegracao <= 5 || lPagamentoIntegrar.EtapaErroIntegracao == null)
                {
                    //CONTA TRANSFERENCIA
                    if (pagamentoIntegrarRequest.IdContaTransferencia == 0 ||
                        pagamentoIntegrarRequest.IdContaTransferencia == null)
                        if (!pagamentoIntegrarRequest.CpfContaTransferencia.IsNullOrWhiteSpace())
                        {
                            log.Info("Transf. DEST X TRANSF. EtapaErroIntegracao<=5");
                            var lContaTransf =
                                _cartaoRepository.ConsultarContas(null, null,
                                    pagamentoIntegrarRequest.CpfContaTransferencia);

                            pagamentoIntegrarRequest.IdContaTransferencia =
                                lContaTransf.Result.content.FirstOrDefault(x => StatusConta().Contains(x.idStatusConta))
                                    ?.id;
                        }

                    if (pagamentoIntegrarRequest.IdContaTransferencia != null &&
                        pagamentoIntegrarRequest.PercentualTransferencia > 0)
                    {
                        log.Info("Percentual transf.");
                        //TRANFERENCIA CONTA DEST X TRANF
                        double lValorTransf;
                        var lValor = Convert.ToDouble(pagamentoIntegrarRequest.Valor, new CultureInfo("pt-BR"));
                        if (pagamentoIntegrarRequest.PercentualTransferencia < 100)
                        {
                            lValorTransf = (double)Decimal.Round(
                                (decimal)((lValor * pagamentoIntegrarRequest.PercentualTransferencia) / 100), 2);
                        }
                        else
                        {
                            lValorTransf = lValor;
                        }

                        if (lEmprestimo != null) //Caso tenha retenção subtrai c/ valor
                            lValorTransf = lValorTransf - Convert.ToDouble(lValorRetencao, new CultureInfo("pt-BR"));

                        try
                        {
                            log.Info("Percentual transf. origem :" + pagamentoIntegrarRequest.IdContaDestino +
                                     "destino: " + pagamentoIntegrarRequest.IdContaTransferencia);

                            etapa5 = RealizaTransferenciaEntreContas(new Transferencia
                            {
                                amount = string.Format("{0:0.00}", lValorTransf).ToDecimal(),
                                description = pagamentoIntegrarRequest.Descricao,
                                originalAccount = pagamentoIntegrarRequest.IdContaDestino ?? 0,
                                destinationAccount = pagamentoIntegrarRequest.IdContaTransferencia ?? 0
                            }).Result;
                        }
                        catch (Exception e)
                        {
                            await _atualizaEtapaErroIntegracao(lPagamentoIntegrar, 5);

                            var lErro = e.Message.Length > 250 ? e.Message.Substring(0, 250) : e.Message;
                            throw new Exception("Erro ao processar pagamento!" + lErro);
                        }

                        if (!etapa5.Sucesso)
                        {
                            await _atualizaEtapaErroIntegracao(lPagamentoIntegrar, 5);

                            var lErroetapa5 = etapa5.message.Length > 250
                                ? etapa5.message.Substring(0, 250)
                                : etapa5.message;
                            throw new Exception(lErroetapa5);
                        }
                        else
                        {
                            lPagamentoIntegrar.EtapaErroIntegracao = null;
                        }

                        log.Info("var requestP2P = new P2PRequest:" + pagamentoIntegrarRequest.IdContaDestino +
                                 "destino: " + pagamentoIntegrarRequest.IdContaTransferencia);
                        //P2PTRANSFER
                        var requestP2P = new P2PRequest
                        {
                            pagamentoId = pagamentoId.ToString(),
                            amount = string.Format("{0:0.00}", lValorTransf).ToDecimal(),
                            originalAccount = pagamentoIntegrarRequest.IdContaDestino ?? 0,
                            destinationAccount = pagamentoIntegrarRequest.IdContaTransferencia ?? 0,
                            idAdjustment = etapa5.idAdjustment ?? 0,
                            idAdjustmentDestination = etapa5.idAdjustmentDestination ?? 0
                        };
                        await _transacaoAppService.P2P(requestP2P);

                        if (lpagamentoExistente == null)
                            lpagamentoExistente = Repository.Query.Where(x => x.Id == pagamentoId).FirstOrDefault();

                        lpagamentoExistente.PercentualTransferencia =
                            pagamentoIntegrarRequest.PercentualTransferencia.ToDecimal();
                        lpagamentoExistente.IdContaTransferencia =
                            pagamentoIntegrarRequest.IdContaTransferencia.ToStringSafe();
                        lpagamentoExistente.CpfContaTransferencia = pagamentoIntegrarRequest.CpfContaTransferencia;

                        Repository.Command.Update(lpagamentoExistente);
                        Repository.Query.SaveChanges();
                    }
                }

                #endregion

                #region Atualiza status

                log.Info("Finalizando");
                if (lPagamentoIntegrar.EtapaErroIntegracao == null)
                    if (etapa1.Sucesso)
                    {
                        lSucessTodasEtapas = true;

                        if (lRealizaTransfRetido)
                            if (!etapa2.Sucesso || !etapa3.sucesso ||
                                !string.IsNullOrWhiteSpace(lEmprestimo.IdState) && !etapa4.sucesso)
                                lSucessTodasEtapas = false;

                        if (pagamentoIntegrarRequest.IdContaTransferencia != null && !etapa5.Sucesso)
                            lSucessTodasEtapas = false;
                    }

                if (lpagamentoExistente == null)
                    lpagamentoExistente = Repository.Query.Where(x => x.Id == pagamentoId).FirstOrDefault();

                lpagamentoExistente.Status = Status.Aberto;

                if (lSucessTodasEtapas)
                {
                    lpagamentoExistente.EtapaErroIntegracao = null;
                    lpagamentoExistente.Status = Status.Baixado;
                    lpagamentoExistente.DataBaixa = DateTime.Now;
                }

                Repository.Command.Update(lpagamentoExistente);
                Repository.Query.SaveChanges();

                #endregion
            }

            #endregion


            return new IntegrarPagamentoResponse
            {
                Id = pagamentoId
            };
        }

        public async Task<RespPadrao> ValidarCancelamento(int[] pagamentosId)
        {
            try
            {
                new LogHelper().LogOperationStart("ValidarCancelamento");
                var lConta = "";
                decimal lvalor = 0;

                foreach (var idPagamento in pagamentosId)
                {
                    var lPagamento = await Repository.Query.Where(x => x.Id == idPagamento).FirstOrDefaultAsync();

                    var lRetencao = _retencaoAppService.Repository.Query.Where(r =>
                        r.PagamentoId == idPagamento);

                    if (lPagamento != null)
                    {
                        lvalor += lPagamento.Valor;
                        lConta = lPagamento.ContaOrigem;
                    }

                    if (lRetencao.Any())
                        return new RespPadrao
                        {
                            sucesso = false,
                            mensagem = "Pagamento com retenção aberta."
                        };
                }

                var lSaldoConta = _cartaoRepository.ConsultarSaldo(lConta);

                if (lvalor > lSaldoConta.Result.saldoDisponivelGlobal)
                    return new RespPadrao
                    {
                        sucesso = false,
                        mensagem = "Saldo insuficiente para efetuar o cancelamento!"
                    };

                return new RespPadrao
                {
                    sucesso = true,
                    mensagem = "Cancelamento validado com sucesso!"
                };
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar ValidarCancelamento");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("ValidarCancelamento");
            }
        }

        public async Task<RespPadrao> CancelarPagamento(int pagamentoId)
        {
            try
            {
                new LogHelper().LogOperationStart("CancelarPagamento");
                var lPagto = Repository.Query.Where(x => x.Id == pagamentoId)
                    .Include(x => x.Empresa)
                    .Include(x => x.Portador).FirstOrDefault();

                if (lPagto == null)
                    return new RespPadrao
                    {
                        sucesso = false,
                        mensagem = "Pagamento não encontrado!"
                    };

                if (lPagto.Status != Status.Aberto && lPagto.Status != Status.Baixado)
                    return new RespPadrao
                    {
                        sucesso = false,
                        mensagem = "Não foi possível cancelar o pagamento. Status " + lPagto.Status
                    };

                var lRetencao = _retencaoAppService.Repository.Query.Where(r =>
                    r.Status == StatusRetencao.Aberta && r.PagamentoId == lPagto.Id);

                if (lRetencao.Any())
                    return new RespPadrao
                    {
                        sucesso = false,
                        mensagem = "Pagamento com retenção aberta."
                    };

                if (lPagto.FormaPagamento == FormaPagamento.Cartao)
                {
                    if (lPagto.TransferenciaCancelamento == false || lPagto.TransferenciaCancelamento == null)
                    {
                        if (lPagto.IdContaTransferencia.IsNullOrWhiteSpace() || lPagto.IdContaTransferencia == "0")
                            if (!lPagto.CpfContaTransferencia.IsNullOrWhiteSpace())
                            {
                                var lContaTransf =
                                    _cartaoRepository.ConsultarContas(null, null,
                                        lPagto.CpfContaTransferencia).Result;

                                lPagto.IdContaTransferencia = lContaTransf.content
                                    .FirstOrDefault(x => StatusConta().Contains(x.idStatusConta))?.id.ToString();
                            }

                        if (!lPagto.IdContaTransferencia.IsNullOrWhiteSpace() && lPagto.IdContaTransferencia != "0")
                        {
                            var lTransDestino = await RealizaTransferenciaEntreContas(new Transferencia
                            {
                                amount = lPagto.Valor.Round(),
                                description = lPagto.Descricao,
                                originalAccount = lPagto.IdContaTransferencia.ToIntSafe(),
                                destinationAccount = lPagto.ContaDestino.ToIntSafe()
                            });

                            if (!lTransDestino.Sucesso)
                            {
                                lPagto.TransferenciaCancelamento = false;
                                Repository.Command.Update(lPagto);
                                Repository.Command.SaveChanges();

                                return new RespPadrao
                                {
                                    sucesso = false,
                                    mensagem = "Erro na transferência entre a conta transf. para a conta dest."
                                };
                            }

//                        //P2PTRANSFER
//                        var requestP2P = new P2PRequest
//                        {
//                            pagamentoId = pagamentoId.ToString(),
//                            amount = lPagto.Valor.Round(),
//                            originalAccount = lPagto.IdContaTransferencia.ToIntSafe(),
//                            destinationAccount = lPagto.ContaDestino.ToIntSafe(),
//                            idAdjustment = lTransDestino.idAdjustment ?? 0,
//                            idAdjustmentDestination = lTransDestino.idAdjustmentDestination ?? 0
//                        };
//
//                        await _transacaoAppService.P2P(requestP2P);

                            lPagto.TransferenciaCancelamento = true;
                            Repository.Command.Update(lPagto);
                            Repository.Command.SaveChanges();
                        }
                    }

                    if (lPagto.CargaCancelamento == false || lPagto.CargaCancelamento == null)
                    {
                        var lDestinoOrigem = await RealizaTransferenciaEntreContas(new Transferencia
                        {
                            amount = lPagto.Valor.Round(),
                            description = lPagto.Descricao,
                            originalAccount = lPagto.ContaDestino.ToIntSafe(),
                            destinationAccount = lPagto.ContaOrigem.ToIntSafe()
                        });

                        if (!lDestinoOrigem.Sucesso)
                        {
                            lPagto.CargaCancelamento = false;
                            Repository.Command.Update(lPagto);
                            Repository.Command.SaveChanges();

                            return new RespPadrao
                            {
                                sucesso = false,
                                mensagem = "Erro na transferência entre a conta dest. para a conta origem"
                            };
                        }

                        //P2PTRANSFER
                        var requestP2P = new P2PRequest
                        {
                            pagamentoId = pagamentoId.ToString(),
                            amount = lPagto.Valor.Round(),
                            originalAccount = lPagto.ContaDestino.ToIntSafe(),
                            destinationAccount = lPagto.ContaOrigem.ToIntSafe(),
                            idAdjustment = lDestinoOrigem.idAdjustment ?? 0,
                            idAdjustmentDestination = lDestinoOrigem.idAdjustmentDestination ?? 0
                        };

                        await _transacaoAppService.P2P(requestP2P);

                        lPagto.CargaCancelamento = true;
                        Repository.Command.Update(lPagto);
                        Repository.Command.SaveChanges();
                    }
                }

                lPagto.Status = Status.Cancelado;
                Repository.Command.Update(lPagto);
                Repository.Command.SaveChanges();

                return new RespPadrao
                {
                    sucesso = true,
                    mensagem = "Cancelamento de pagamento efetuado com sucesso!"
                };
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar CancelarPagamento");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("CancelarPagamento");
            }
        }

        public async Task<RespPadrao> BloquearPagamento(int pagamentoId)
        {
            try
            {
                new LogHelper().LogOperationStart("BloquearPagamento");
                var lPagto = await Repository.Query
                    .Where(x => x.Id == pagamentoId)
                    .Include(x => x.Empresa)
                    .FirstOrDefaultAsync();
                if (lPagto == null)
                    throw new Exception("Pagamento não encontrado!");
                if (lPagto.Status != Status.Aberto)
                    throw new Exception("Não foi possível bloquear o pagamento.\n Status \"" + lPagto.Status + "\"");
                lPagto.Status = Status.Bloqueado;
                await Repository.Command.UpdateAsync(lPagto);
                await Repository.Command.SaveChangesAsync();
                return new RespPadrao
                {
                    sucesso = true,
                    mensagem = "Bloqueio de pagamento efetuado com sucesso!"
                };
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar BloquearPagamento");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("BloquearPagamento");
            }
        }

        public ConsultarPagamentosResponse ConsultarPagamentos(ConsultarPagamentoRequest request)
        {
            try
            {
                new LogHelper().LogOperationStart("ConsultarPagamentos");
                var idEmpresa = Engine.User.EmpresaId;

                var lPagamentos = Repository.Query.GetAll();

                if (idEmpresa > 0)
                    lPagamentos = lPagamentos.Where(p => p.EmpresaId == idEmpresa);

                if (request.Id.HasValue && request.Id != 0)
                    lPagamentos = lPagamentos.Where(p => p.Id == request.Id);

                if (request.CiotId.HasValue && request.CiotId != 0)
                    lPagamentos = lPagamentos.Where(p => p.CiotId == request.CiotId);

                if (!request.cpfCnpjPortador.IsNullOrWhiteSpace())
                    lPagamentos = lPagamentos.Where(p => p.Portador.CpfCnpj == request.cpfCnpjPortador);

                if (request.DataInicio.HasValue && request.DataFim.HasValue)
                {
                    request.DataInicio = request.DataInicio.Value.StartOfDay();
                    request.DataFim = request.DataFim.Value.EndOfDay();
                    lPagamentos = lPagamentos.Where(p =>
                        p.DataCadastro >= request.DataInicio && p.DataCadastro <= request.DataFim);
                }

                var lCount = lPagamentos.Count();

                var retorno = lPagamentos.Skip((request.Page - 1) * request.Take)
                    .Take(request.Take)
                    .Include(a => a.Ciot)
                    .ProjectTo<ConsultarPagamentos>().ToList();

                return new ConsultarPagamentosResponse
                {
                    Items = retorno,
                    TotalItems = lCount
                };
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar ConsultarPagamentos");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("ConsultarPagamentos");
            }
        }

        public async Task<decimal> ConsultarValorPagamentosDia(DateTime dtInicio, DateTime dtFim, int empresaId)
        {
            try
            {
                return await Repository.Query.Where(x =>
                    x.EmpresaId == empresaId && x.Status == Status.Baixado && x.DataBaixa >= dtInicio &&
                    x.DataBaixa <= dtFim).SumAsync(x => x.Valor);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                throw;
            }
        }

        public async Task ServiceRealizarPagamentosAgendados()
        {
            var lLog = LogManager.GetCurrentClassLogger();
            try
            {
                var lPagamentosAgendados = await Repository.Query.Where(x =>
                        x.DataPrevisaoPagamento < DateTime.Now.AddMinutes(-30) &&
                        x.DataPrevisaoPagamento > DateTime.Now.AddDays(-7) &&
                        x.Status == Status.Aberto)
                    .Include(x => x.Empresa)
                    .ToListAsync();

                if (lPagamentosAgendados == null || !lPagamentosAgendados.Any()) return;

                foreach (var lPagamento in lPagamentosAgendados)
                {
                    var lRetornoTransferenica = await RealizaTransferenciaEntreContas(new Transferencia
                    {
                        amount = lPagamento.Valor,
                        destinationAccount = lPagamento.ContaDestino.ToInt(),
                        originalAccount = lPagamento.ContaOrigem.ToInt(),
                        description = lPagamento.Tipo.GetDescription() + lPagamento.CiotId
                    });

                    if (!lRetornoTransferenica.Sucesso)
                    {
                        lLog.Error(
                            $"BAT_PGTO_03 ERRO: Falha na transferência do pagamento {lPagamento.Id}. Retorno: {JsonConvert.SerializeObject(lRetornoTransferenica)}");
                        continue;
                    }

                    lPagamento.Status = Status.Baixado;
                    await Repository.Command.SaveChangesAsync();

                    var lP2PTransferRequest = new P2PRequest
                    {
                        pagamentoId = lPagamento.Id.ToString(),
                        amount = lPagamento.Valor,
                        originalAccount = lPagamento.ContaOrigem.ToInt(),
                        destinationAccount = lPagamento.ContaDestino.ToInt(),
                        idAdjustment = lRetornoTransferenica.idAdjustment ?? 0,
                        idAdjustmentDestination = lRetornoTransferenica.idAdjustmentDestination ?? 0
                    };

                    await _transacaoAppService.P2P(lP2PTransferRequest);
                }
            }
            catch (Exception e)
            {
                lLog.Error(e, "BAT_PGTO_03 ERRO");
            }
        }

        public Task<IEnumerable<ConsultarPorIdPagamentoResponse>> BuscarTodos()
        {
            throw new NotImplementedException();
        }

        public bool Existe(int id)
        {
            try
            {
                new LogHelper().LogOperationStart("Existe");
                throw new NotImplementedException();
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar Existe");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("Existe");
            }
        }

        private List<ConsultaGridPagamentoItem> AplicaOrdencao(List<ConsultaGridPagamentoItem> pag,
            OrderFilters orderFilters)
        {
            if (orderFilters.Campo == "Tipo")
                return orderFilters.Operador == EOperadorOrder.Descending
                    ? pag.OrderByDescending(x => x.Tipo).ToList()
                    : pag.OrderBy(x => x.Tipo).ToList();

            if (orderFilters.Campo == "Formapagamento")
                return orderFilters.Operador == EOperadorOrder.Descending
                    ? pag.OrderByDescending(x => x.FormaPagamento).ToList()
                    : pag.OrderBy(x => x.FormaPagamento).ToList();

            if (orderFilters.Campo == "Status")
                return orderFilters.Operador == EOperadorOrder.Descending
                    ? pag.OrderByDescending(x => x.Status).ToList()
                    : pag.OrderBy(x => x.Status).ToList();

            return pag;
        }

        public async Task _atualizaEtapaErroIntegracao(PagamentoIntegrar lPagamentoIntegrar, int? etapa)
        {
            try
            {
                new LogHelper().LogOperationStart("_atualizaEtapaErroIntegracao");
                lPagamentoIntegrar.EtapaErroIntegracao = etapa;

                var lPagamento = Mapper.Map<PagamentosAlterarEtapaCommand>(lPagamentoIntegrar);

                await Engine.CommandBus.SendCommandAsync(lPagamento);
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar _atualizaEtapaErroIntegracao");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("_atualizaEtapaErroIntegracao");
            }
        }

        private IQueryable<Domain.Models.Pagamentos.Pagamentos> DadosGridRelatorioPagamento(String dataInicial,
            String dataFinal, Status status, int empresaIdEvento, OrderFilters orderFilters,
            List<QueryFilters> filters)
        {
            var dtIni = dataInicial.ToDateTime();
            var dtFim = dataFinal.ToDateTime().AddDays(1).AddSeconds(-1);

            var userEmpresaId = Engine.User.EmpresaId;
            var pagamentos = Repository.Query.GetAll();

            pagamentos = pagamentos.Where(p => p.DataCadastro >= dtIni && p.DataCadastro <= dtFim)
                .Include(a => a.Portador)
                .Include(a => a.Ciot);

            if (status != Status.Todos)
            {
                pagamentos = pagamentos.Where(p => p.Status == status);
            }

            if (empresaIdEvento != 0)
                pagamentos = pagamentos.Where(e => e.EmpresaId == empresaIdEvento);
            
            var listaEmpresasIds = _usuarioReadRepository.GetEmpresasAcessoUsuario(Engine.User.Id).Result;
            if (!listaEmpresasIds.IsEmpty())
                pagamentos = pagamentos.Where(o => listaEmpresasIds.Contains(o.EmpresaId));
            
            var filtroCiot = filters.FirstOrDefault(o => o.Campo == "Ciot.Ciot");
            var filtroValor = filters.FirstOrDefault(o => o.Campo == "Valor");

            if (filtroCiot != null)
            {
                var valorConsulta = filtroCiot.Valor;

                if (valorConsulta.Contains("/"))
                {
                    var valorQuebrado = valorConsulta.Split('/');

                    pagamentos = pagamentos.Where(o =>
                        o.Ciot.Ciot.Contains(valorQuebrado[0]) && o.Ciot.Verificador.Contains(valorQuebrado[1]));
                }
                else
                {
                    pagamentos = pagamentos.Where(o => o.Ciot.Ciot.Contains(valorConsulta));
                }

                filters.Remove(filtroCiot);
            }
            
            pagamentos = pagamentos.AplicarFiltrosDinamicos(filters);

            pagamentos = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                ? pagamentos.OrderByDescending(o => o.Id)
                : pagamentos.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            return pagamentos;
        }


        private Task<TransferenciaEntreContaResp> RealizaTransferenciaEntreContas(Transferencia transf)
        {
            return _transferenciaRepository.RealizaTransferenciaEntreContas(transf);
        }

        public async Task<RespPadrao> TransferenciaP2P(TransferenciaP2PRequest request)
        {
            var log = LogManager.GetCurrentClassLogger();
            try
            {
                log.Info("Inicio do pagamento da conta origem: " + request.IdContaOrigem + " conta destino: " +
                         request.IdContaOrigem + " valor: " + request.Valor);
                var lTransDestino = await RealizaTransferenciaEntreContas(Mapper.Map<Transferencia>(request));

                if (!lTransDestino.Sucesso)
                {
                    log.Info("Erro do pagamento da conta origem: " + request.IdContaOrigem + " conta destino: " +
                             request.IdContaOrigem + " valor: " + request.Valor + " Erro: " + lTransDestino.message);
                    return new RespPadrao
                    {
                        sucesso = false,
                        mensagem = "Erro ao realizar transferência DOCK: " + lTransDestino.message
                    };
                }

                log.Info("Sucesso do pagamento da conta origem: " + request.IdContaOrigem + " conta destino: " +
                         request.IdContaOrigem + " valor: " + request.Valor);
                return new RespPadrao
                {
                    sucesso = true,
                    mensagem = "Transferencia realizada com sucesso!"
                };
            }
            catch (Exception e)
            {
                log.Error(e, "Erro ao realizar transação!");
                throw;
            }
        }

        private List<int> StatusConta()
        {
            return new List<int>()
            {
                0,
                200,
                10,
                109
            };
        }
    }
}
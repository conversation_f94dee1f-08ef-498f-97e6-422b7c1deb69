using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Domain.Models.CiotViagem;
using SistemaInfo.BBC.Domain.Models.DeclaracaoCiot.Repository;
using SistemaInfo.BBC.Infra.Data.Context;
using SistemaInfo.Framework.DomainDrivenDesign.Infra.Repository;

namespace SistemaInfo.BBC.Infra.Data.Repository.DeclaracaoCiot
{
    public abstract class DeclaracaoCiotBaseReadRepository<TContext, TDeclaracaoCiotEntity> : ReadOnlyRepository<TDeclaracaoCiotEntity, TContext>, IDeclaracaoCiotBaseReadRepository<TDeclaracaoCiotEntity>
        where TContext : DbContext
        where TDeclaracaoCiotEntity : Domain.Models.DeclaracaoCiot.DeclaracaoCiot
    {
        public DeclaracaoCiotBaseReadRepository(TContext context) : base(context)
        {
        }
    }
    public class DeclaracaoCiotReadRepository : DeclaracaoCiotBaseReadRepository<ConfigContext, Domain.Models.DeclaracaoCiot.DeclaracaoCiot>, IDeclaracaoCiotReadRepository
    {
        public DeclaracaoCiotReadRepository(ConfigContext context) : base(context)
        {
        }

        public IQueryable<Domain.Models.DeclaracaoCiot.DeclaracaoCiot> GetCiot()
        {
            return DbSet.Where(x => x.Id > 0);
        }

        public IQueryable<Domain.Models.DeclaracaoCiot.DeclaracaoCiot> GetCiotsEncerrados()
        {
            return Context.DeclaracaoCiot.Where(c => c.DataFim < DateTime.Now &&
                                                     c.Tipo == TipoCiot.Agregado &&
                                                     c.Status == StatusCiot.Aberto);
        }

        public IQueryable<Domain.Models.DeclaracaoCiot.DeclaracaoCiot> GetCiotsCancelados()
        {
            return Context.DeclaracaoCiot
                .Include(a => a.CiotViagem)
                .Where(a => a.DataCadastro > DateTime.Now.AddHours(-24) &&
                            a.DataCadastro < DateTime.Now.AddHours(-23) &&
                            a.Status == StatusCiot.Aberto &&
                            a.CiotViagem.Count <= 0);
        }

        public IQueryable<Domain.Models.Veiculo.Veiculo> GetVeiculos(int id)
        {
            return Context.CiotVeiculo.Include(a=>a.Veiculo).Where(a => a.DeclaracaoCiotId == id).Select(a=>a.Veiculo);
        }

        public IQueryable<CiotViagem> GetCiotViagens(int id)
        {
            return Context.CiotViagem
                .Include(a => a.NaturezaCarga)
                .Include(a => a.ClienteConsignatario)
                .Include(a => a.ClienteDestinatario)
                .Include(a => a.ClienteRemetente)
                .Include(a => a.CidadeOrigem)
                .Include(a => a.CidadeDestino).Where(a => a.DeclaracaoCiotId == id);
        }
        
        public async Task<Domain.Models.DeclaracaoCiot.DeclaracaoCiot> GetCiotIntegracaoViagem(int portadorContatadoId, int portadorMotoristaId)
        {
            return await Context.DeclaracaoCiot
                .Include(a=> a.CiotVeiculo)
                .Include(a=> a.CiotViagem)
                .Include(a=> a.PortadorMot)
                .Include(a=> a.PortadorProp)
                .Include(a=> a.Filial)
                .FirstOrDefaultAsync(a => a.PortadorPropId == portadorContatadoId && a.PortadorMotId == portadorMotoristaId);
        }
    }
}
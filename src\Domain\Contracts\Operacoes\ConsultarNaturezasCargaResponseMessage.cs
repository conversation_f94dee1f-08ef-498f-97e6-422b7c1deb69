using System.Collections.Generic;
using SistemaInfo.BBC.Domain.External.CIOT.DTO;

namespace SistemaInfo.BBC.Domain.Contracts.Operacoes
{
    
    public class ConsultarNaturezasCargaReqMessage
    {
        public string Descricao { get; set; }
    }
    
    public class ConsultarNaturezasCargaRespMessage
    {
        public ConsultarNaturezasCargaRespMessage(bool sucesso, string excecao)
        {
            Sucesso = sucesso;
            Erro = new Excecao()
            {
                Mensagem = excecao
            };
        }
        public ConsultarNaturezasCargaRespMessage() { }
        public bool Sucesso { get; set; }
        public Excecao Erro { get; set; }
        public List<NaturezaCarga> NaturezasCarga { get; set; }
    }
}
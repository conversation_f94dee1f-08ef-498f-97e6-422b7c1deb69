import { Cidade } from "../cidade";
import { NaturezaCarga } from "../natureza-carga";
import { Tipos } from "../tipoCarga";
import { CheckDestinacaoComercial } from "../../declaracao/salvar-declaracao/salvar-declaracao.component";

export class ViagemDTO {
    // cepOrigem: string;
    // cepDestino: string;
    naturezaCarga: NaturezaCarga;
    pesoCarga: number;
    qtdViagens: number;
    dtInicioFrete: Date;
    dtTerminoFrete: Date;
    valorFrete: number;
    valorDespesas: number;
    totalImposto: number;
    valorCombustivel: number;
    totalPedagio: number;
    quantidadeTarifas: number;
    valorTotalTarifas: number;
    codigoIBGECidade: Cidade;

    destinacaoComercial: CheckDestinacaoComercial;

}
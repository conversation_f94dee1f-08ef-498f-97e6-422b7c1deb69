﻿using System;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using Newtonsoft.Json;
using NLog;
using SistemaInfo.BBC.Application.Interface.AuthSessionApi;
using SistemaInfo.BBC.Application.Interface.ClientSecret;
using SistemaInfo.BBC.Application.Objects.Api.Token;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Domain.Models.AuthSessionApi.Repository;
using SistemaInfo.BBC.Domain.Models.ClientSecretAdm.Repository;
using SistemaInfo.BBC.Domain.Models.Empresa.Repository;
using SistemaInfo.BBC.Domain.Models.GrupoEmpresa.Repository;
using SistemaInfo.BBC.Domain.Models.Parametros.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.Services.AuthSessionApi
{
    public class AuthSessionApiAppService :
        AppService<Domain.Models.AuthSessionApi.AuthSessionApi, IAuthSessionApiReadRepository, IAuthSessionApiWriteRepository>,
        IAuthSessionApiAppService
    {
        private readonly IConfiguration _config;
        private readonly IEmpresaReadRepository _empresaReadRepository;
        private readonly IGrupoEmpresaReadRepository _grupoEmpresaReadRepository;
        private readonly IClientSecretAppService _clientSecretAppService;
        private readonly IParametrosReadRepository _parametrosReadRepository;
        private readonly IClientSecretAdmReadRepository _clientSecretAdmReadRepository;
        public AuthSessionApiAppService(IAppEngine engine, 
            IAuthSessionApiReadRepository readRepository,
            IAuthSessionApiWriteRepository writeRepository, 
            IConfiguration config, 
            IEmpresaReadRepository empresaReadRepository, 
            IGrupoEmpresaReadRepository grupoEmpresaReadRepository,
            IParametrosReadRepository parametrosReadRepository,
            IClientSecretAppService clientSecretAppService,
            IClientSecretAdmReadRepository authClientSecretReadRepository) 
            : base(engine, readRepository, writeRepository)
        {
            _config = config;
            _empresaReadRepository = empresaReadRepository;
            _grupoEmpresaReadRepository = grupoEmpresaReadRepository;
            _clientSecretAppService = clientSecretAppService;
            _parametrosReadRepository = parametrosReadRepository;
            _clientSecretAdmReadRepository = authClientSecretReadRepository;
        }

        
        public async Task<RespPadrao> GerarToken(TokenRequestIntegracao tokenRequest)
        {
            var log = LogManager.GetCurrentClassLogger();

            log.Info(JsonConvert.SerializeObject(tokenRequest));
            
            try
            {
                if(string.IsNullOrWhiteSpace(tokenRequest.Cnpj)) //Cnpj é obrigatório
                {
                    return new RespPadrao()
                    {
                        sucesso = false,
                        data = null,
                        mensagem = "Autenticação inválida!"
                    };
                }
                
                if(string.IsNullOrWhiteSpace(tokenRequest.Empresa) && string.IsNullOrWhiteSpace(tokenRequest.Cnpj)) //Ambos grupo empresa e empresa nulos
                {
                    return new RespPadrao()
                    {
                        sucesso = false,
                        data = null,
                        mensagem = "Autenticação inválida!"
                    };
                }

                var lEmpresa = new Domain.Models.Empresa.Empresa();

                var lGrupoEmpresa = new Domain.Models.GrupoEmpresa.GrupoEmpresa();

                var lSenhaApiHash = tokenRequest.SenhaApi.GetHashSha1();

                if(!tokenRequest.Cnpj.IsNullOrWhiteSpace() && !tokenRequest.Empresa.IsNullOrWhiteSpace())
                {
                    
                    var grupoEmpresa = await _grupoEmpresaReadRepository
                        .Include(x => x.Empresa)
                        .Include(x => x.ClientSecret)
                        .FirstOrDefaultAsync(x => x.Cnpj == tokenRequest.Cnpj && x.Ativo == 1);
                    
                    if (grupoEmpresa != null) //Cnpj é de grupo de empresa, campos especificos de Empresa se tornam obrigatórios
                    {
                        var lValidacaoGrupoEmpresa = ValidaGrupoEmpresa(grupoEmpresa, tokenRequest, lSenhaApiHash);

                        lGrupoEmpresa = grupoEmpresa;
                        lEmpresa = null;
                        
                        if (!lValidacaoGrupoEmpresa.sucesso)
                        {
                            return lValidacaoGrupoEmpresa;
                        }
                    }
                    
                }
                else
                {
                    var empresa = await _empresaReadRepository
                        .Include(x => x.ClientSecret)
                        .FirstOrDefaultAsync(x => x.Cnpj == tokenRequest.Cnpj && x.Ativo == 1);

                    if (empresa != null) //Cnpj não é de Grupo de Empresa, usar os campos Cnpj, SenhaAPI e ClientSecret para validações
                    {
                        var lValidacaoEmpresa = ValidaEmpresa(empresa, tokenRequest.ClientSecret, lSenhaApiHash);

                        lEmpresa = empresa;
                        lGrupoEmpresa = null;

                        if (!lValidacaoEmpresa.sucesso)
                        {
                            return lValidacaoEmpresa;
                        }
                    }
                }
                
                var claims = new[]
                {
                    new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString("N"))
                };

                var secretKey = Encoding.UTF8.GetString(Convert.FromBase64String(_config["Authentication:SecretKey"]));
                var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(secretKey));
                var credential = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);
                var intervaloValidade = 600;
                var dataCriacao = DateTime.UtcNow;
                var dataExpiracao = dataCriacao + TimeSpan.FromSeconds(intervaloValidade);

                var token = new JwtSecurityToken
                (
                    claims: claims,
                    signingCredentials: credential,
                    expires: dataExpiracao,
                    issuer: "MyServer",
                    audience: "EveryApplication"
                );

                var jwtToken = new
                {
                    token = new JwtSecurityTokenHandler().WriteToken(token),
                    expiration = dataExpiracao.ToString("yyyy-MM-dd HH:mm:ss")
                };

                await SaveToken(lEmpresa ?? lGrupoEmpresa?.Empresa.FirstOrDefault(a => a.Cnpj == tokenRequest.Empresa), jwtToken.token);
                await _clientSecretAppService.DesativarClientSecretExpirada();
                return new RespPadrao()
                {
                    sucesso = true,
                    mensagem = "",
                    data = new {jwtToken.token}
                };
            }
            catch (Exception e)
            {
                log.Error(e);
                return new RespPadrao()
                {
                    sucesso = false,
                    data = null,
                    mensagem = "Autenticação invalida!"
                };
            }
        }
        
        public async Task<RespPadrao> Login(TokenRequestIntegracao tokenRequest)
        {
            var log = LogManager.GetCurrentClassLogger();

            log.Info(JsonConvert.SerializeObject(tokenRequest));
            
            try
            {
                if(string.IsNullOrWhiteSpace(tokenRequest.Cnpj)) //Cnpj é obrigatório
                {
                    return new RespPadrao()
                    {
                        sucesso = false,
                        data = null,
                        mensagem = "Autenticação inválida!"
                    };
                }
                
                if(string.IsNullOrWhiteSpace(tokenRequest.Empresa) && string.IsNullOrWhiteSpace(tokenRequest.Cnpj)) //Ambos grupo empresa e empresa nulos
                {
                    return new RespPadrao()
                    {
                        sucesso = false,
                        data = null,
                        mensagem = "Autenticação inválida!"
                    };
                }
                
                var lSenhaApiHash = tokenRequest.SenhaApi.GetHashSha1();

                #region Remove caracteres especiais dos campos de cnpj

                tokenRequest.Cnpj = tokenRequest.Cnpj.Replace("-", "").Replace(".", "").Replace(@"/", "");
                if (!string.IsNullOrWhiteSpace(tokenRequest.Empresa))
                {
                    tokenRequest.Empresa = tokenRequest.Empresa.Replace("-", "").Replace(".", "").Replace(@"/", "");
                }

                #endregion
                
                if(!tokenRequest.Cnpj.IsNullOrWhiteSpace() && !tokenRequest.Empresa.IsNullOrWhiteSpace())
                {
                    
                    var grupoEmpresa = await _grupoEmpresaReadRepository
                        .Include(x => x.Empresa)
                        .Include(x => x.ClientSecret)
                        .FirstOrDefaultAsync(x => x.Cnpj == tokenRequest.Cnpj && x.Ativo == 1);
                    
                    
                    if (grupoEmpresa != null) //Cnpj é de grupo de empresa, campos especificos de Empresa se tornam obrigatórios
                    {
                        var lValidacaoGrupoEmpresa = ValidaGrupoEmpresa(grupoEmpresa, tokenRequest, lSenhaApiHash);
                        
                        
                        if (!lValidacaoGrupoEmpresa.sucesso)
                        {
                            return lValidacaoGrupoEmpresa;
                        }
                    }
                    else
                    {
                        return new RespPadrao()
                        {
                            sucesso = false,
                            data = null,
                            mensagem = "Autenticação inválida!"
                        };
                    }

                }
                else
                {
                    var empresa = await _empresaReadRepository
                        .Include(x => x.ClientSecret)
                        .FirstOrDefaultAsync(x => x.Cnpj == tokenRequest.Cnpj && x.Ativo == 1);

                    if (empresa != null) //Cnpj não é de Grupo de Empresa, usar os campos Cnpj, SenhaAPI e ClientSecret para validações
                    {
                        var lValidacaoEmpresa = ValidaEmpresa(empresa, tokenRequest.ClientSecret, lSenhaApiHash);
                        
                        if (!lValidacaoEmpresa.sucesso)
                        {
                            return lValidacaoEmpresa;
                        }
                    } 
                    else
                    {
                        return new RespPadrao()
                        {
                            sucesso = false,
                            data = null,
                            mensagem = "Autenticação inválida!"
                        };
                    }
                }

                var claims = new[]
                {
                    new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString("N"))
                };

                var secretKey = Encoding.UTF8.GetString(Convert.FromBase64String(_config["Authentication:SecretKey"]));
                var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(secretKey));
                var credential = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);
                var intervaloValidade = 600;
                var dataCriacao = DateTime.UtcNow;
                var dataExpiracao = dataCriacao + TimeSpan.FromSeconds(intervaloValidade);

                var token = new JwtSecurityToken
                (
                    claims: claims,
                    signingCredentials: credential,
                    expires: dataExpiracao,
                    issuer: "MyServer",
                    audience: "EveryApplication"
                );

                var jwtToken = new
                {
                    token = new JwtSecurityTokenHandler().WriteToken(token),
                    expiration = dataExpiracao.ToString("yyyy-MM-dd HH:mm:ss")
                };
                
                return new RespPadrao()
                {
                    sucesso = true,
                    mensagem = "Sucesso na autenticação!",
                    data = new {jwtToken.token}
                };
            }
            catch (Exception e)
            {
                log.Error(e);
                return new RespPadrao()
                {
                    sucesso = false,
                    data = null,
                    mensagem = "Autenticação invalida!"
                };
            }
        }

        public async Task<RespPadrao> GerarToken(TokenRequestMobilePagamentos tokenRequest)
        {
            var log = LogManager.GetCurrentClassLogger();
            log.Info(JsonConvert.SerializeObject(tokenRequest));
            
            try
            {
                if(string.IsNullOrWhiteSpace(tokenRequest.Login))
                {
                    return new RespPadrao() { sucesso = false, data = null, mensagem = "Autenticação inválida!" };
                }
                
                if(string.IsNullOrWhiteSpace(tokenRequest.Senha))
                {
                    return new RespPadrao(){ sucesso = false, data = null, mensagem = "Autenticação inválida!" };
                }
                
                if(string.IsNullOrWhiteSpace(tokenRequest.ClientSecret))
                {
                    return new RespPadrao(){ sucesso = false, data = null, mensagem = "Autenticação inválida!" };
                }
                
                var authSessionClientSecret = await _clientSecretAdmReadRepository.ObterPorLoginAsync(tokenRequest.Login);
                if (authSessionClientSecret == null 
                    || !authSessionClientSecret.LoginAtivo()
                    || !authSessionClientSecret.LoginValido(tokenRequest.Login)
                    || !authSessionClientSecret.SenhaValida(tokenRequest.Senha)
                    || !authSessionClientSecret.ClientSecretValida(tokenRequest.ClientSecret))
                {
                    return new RespPadrao(){ sucesso = false, data = null, mensagem = "Autenticação inválida!" };
                }

                var claims = new[]
                {
                    new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString("N"))
                };

                var secretKey = Encoding.UTF8.GetString(Convert.FromBase64String(_config["Authentication:SecretKey"]));
                var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(secretKey));
                var credential = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);
                var intervaloValidade = 600;
                var dataCriacao = DateTime.UtcNow;
                var dataExpiracao = dataCriacao + TimeSpan.FromSeconds(intervaloValidade);

                var token = new JwtSecurityToken
                (
                    claims: claims,
                    signingCredentials: credential,
                    expires: dataExpiracao,
                    issuer: "MyServer",
                    audience: "EveryApplication"
                );

                var jwtToken = new
                {
                    token = new JwtSecurityTokenHandler().WriteToken(token),
                    expiration = dataExpiracao.ToString("yyyy-MM-dd HH:mm:ss")
                };

                await SaveTokenCWI(authSessionClientSecret,jwtToken.token);
                await _clientSecretAppService.DesativarClientSecretExpirada();
                return new RespPadrao()
                {
                    sucesso = true,
                    mensagem = "",
                    data = new {jwtToken.token}
                };
            }
            catch (Exception e)
            {
                log.Error(e);
                return new RespPadrao()
                {
                    sucesso = false,
                    data = null,
                    mensagem = "Autenticação invalida!"
                };
            }
        }

        private RespPadrao ValidaEmpresa(Domain.Models.Empresa.Empresa empresa, string clientSecretEmpresa, string tokenSenha)
        {
            if (empresa.GrupoEmpresaId != null)
            {
                return new RespPadrao()
                {
                    mensagem = "Autenticação inválida!",
                    sucesso = false
                };
            }
            
            var lClientSecret = empresa.ClientSecret.FirstOrDefault(x =>
                x.SecretKey == clientSecretEmpresa && x.Senha == tokenSenha && x.Ativo == 1 &&
                x.IdEmpresa == empresa.Id);
            
            
            if (lClientSecret == null)
            {
                return new RespPadrao()
                {
                    mensagem = "Autenticação inválida!",
                    sucesso = false
                };
            }

            if (lClientSecret.DataExpiracao.HasValue)
            {
                if (lClientSecret.DataExpiracao < DateTime.Now)
                {
                    return new RespPadrao()
                    {
                        mensagem = "Autenticação inválida!",
                        sucesso = false
                    };
                }
            }
            
            return new RespPadrao()
            {
                sucesso = true,
                mensagem = "Autenticado com sucesso!"
            };
        }

        private RespPadrao ValidaGrupoEmpresa(Domain.Models.GrupoEmpresa.GrupoEmpresa grupoEmpresa,
            TokenRequestIntegracao tokenRequest, string tokenSenha)
        {
            //Se tem algum ClientSecret expirado para o grupo
            var lClientSecret = grupoEmpresa.ClientSecret.FirstOrDefault(x =>
                x.SecretKey == tokenRequest.ClientSecret && x.Senha == tokenSenha &&
                x.Ativo == 1);
            
            if (lClientSecret == null)
            {
                return new RespPadrao()
                {
                    mensagem = "Autenticação inválida!",
                    sucesso = false
                };
            }
            
            if (lClientSecret.DataExpiracao.HasValue)
            {
                if (lClientSecret.DataExpiracao < DateTime.Now)
                {
                    return new RespPadrao()
                    {
                        mensagem = "Autenticação inválida!",
                        sucesso = false
                    };
                }
            }

            var empresa = grupoEmpresa.Empresa
                .FirstOrDefault(x => x.Cnpj == tokenRequest.Empresa && x.Ativo == 1);

            if (empresa == null)
            {
                return new RespPadrao()
                {
                    mensagem = "Autenticação invalida!",
                    sucesso = false
                };
            }

            return new RespPadrao()
            {
                sucesso = true,
                mensagem = "Autenticado com sucesso!"
            };
        }

        public async Task<RespPadrao> SaveToken(Domain.Models.Empresa.Empresa empresa, string token)
        {
            
            var lAuthSession = Repository.Query
                .Where(x => x.EmpresaId == empresa.Id)?.FirstOrDefault();
        
            if (lAuthSession == null)
            {
                
                lAuthSession = new BBC.Domain.Models.AuthSessionApi.AuthSessionApi()
                {
                    Token = token,
                    EmpresaId = empresa.Id,
                    DataCadastro = DateTime.Now,
                    GrupoEmpresaId = empresa.GrupoEmpresaId != null ? empresa.GrupoEmpresaId : null,
                };
                
                await Repository.Command.AddAsync(lAuthSession);
            }
            else
            {
                lAuthSession.Token = token;
                lAuthSession.DataUltimaReq = DateTime.Now;
                Repository.Command.Update(lAuthSession);
            }
        
            await Repository.Command.SaveChangesAsync();
        
            return new RespPadrao()
            {
                sucesso = true,
                mensagem = "",
                data = lAuthSession
            };
        }
        
        public async Task<RespPadrao> SaveTokenCWI(Domain.Models.ClientSecretAdm.ClientSecretAdm configuracaoTokenMobileLogin,
            string token)
        {
            var lAuthSession = Repository.Query
                .Where(x => x.Login == configuracaoTokenMobileLogin.Login)?.FirstOrDefault();
        
            if (lAuthSession == null)
            {
                lAuthSession = new BBC.Domain.Models.AuthSessionApi.AuthSessionApi()
                {
                    Token = token,
                    DataCadastro = DateTime.Now,
                    Login = configuracaoTokenMobileLogin.Login
                };
                await Repository.Command.AddAsync(lAuthSession);
            }
            else
            {
                lAuthSession.Token = token;
                lAuthSession.DataUltimaReq = DateTime.Now;
                Repository.Command.Update(lAuthSession);
            }
            await Repository.Command.SaveChangesAsync();
            return new RespPadrao()
            {
                sucesso = true,
                mensagem = "",
                data = lAuthSession
            };
        }

        
        public RespPadrao GetByToken(string token)
        {
            var lAuthSessionApi = Repository.Query
                .Where(x => x.Token == token).FirstOrDefault();

            return new RespPadrao()
            {
                sucesso = true,
                mensagem = "",
                data = lAuthSessionApi
            };
        }
    }
}
using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using NLog;
using SistemaInfo.BBC.ApiIntegracao.Controllers.Base;
using SistemaInfo.BBC.Application.Interface.ContaConductor;
using SistemaInfo.BBC.Application.Interface.Pix;
using SistemaInfo.BBC.Application.Interface.Retencao;
using SistemaInfo.BBC.Application.Interface.Viagem;
using SistemaInfo.BBC.Infra.Data.Context;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;

namespace SistemaInfo.BBC.ApiIntegracao.Controllers
{
    /// <summary>
    /// Controller responsável por verificar a saúde da API de Integração
    /// </summary>
    [Route("Checkup")]
    public class CheckupController : ApiControllerBase
    {
        private readonly IContaConductorAppService _contaConductorAppService;
        private readonly IPixAppService _pixAppService;
        private readonly IRetencaoAppService _retencaoAppService;
        private readonly IViagemAppService _viagemAppService;
        private readonly ConfigContext _configContext;
        private static readonly Logger Logger = LogManager.GetCurrentClassLogger();

        /// <summary>
        /// Construtor com injeção de dependências
        /// </summary>
        /// <param name="engine"></param>
        /// <param name="contaConductorAppService"></param>
        /// <param name="pixAppService"></param>
        /// <param name="retencaoAppService"></param>
        /// <param name="viagemIntegracaoAppService"></param>
        /// <param name="configContext"></param>
        public CheckupController(IAppEngine engine,
            IContaConductorAppService contaConductorAppService,
            IPixAppService pixAppService,
            IRetencaoAppService retencaoAppService,
            IViagemAppService viagemAppService,
            ConfigContext configContext) : base(engine)
        {
            _contaConductorAppService = contaConductorAppService;
            _pixAppService = pixAppService;
            _retencaoAppService = retencaoAppService;
            _viagemAppService = viagemAppService;
            _configContext = configContext;
        }

        /// <summary>
        /// Método de checkup que verifica a saúde dos serviços críticos da API de Integração
        /// Retorna 200 (OK) se todos os serviços estão funcionando ou 500 (Erro Interno) se algum falhar
        /// </summary>
        /// <returns>Status HTTP 200 ou 500</returns>
        [AllowAnonymous]
        [HttpGet]
        public async Task<IActionResult> Get()
        {
            try
            {
                Logger.Info("Iniciando checkup da API de Integração");

                // Teste 1: Verificar se o serviço de conta conductor está respondendo
                await TestContaConductorService();
                Logger.Info("Checkup - Serviço de conta conductor: OK");

                // Teste 2: Verificar se o serviço PIX está respondendo
                await TestPixService();
                Logger.Info("Checkup - Serviço PIX: OK");

                // Teste 3: Verificar se o serviço de retenção está respondendo
                await TestRetencaoService();
                Logger.Info("Checkup - Serviço de retenção: OK");

                // Teste 4: Verificar se o serviço de integração de viagem está respondendo
                await TestViagemIntegracaoService();
                Logger.Info("Checkup - Serviço de integração de viagem: OK");

                // Teste 5: Verificar conectividade com banco de dados
                await TestDatabaseConnection();
                Logger.Info("Checkup - Conexão com banco de dados: OK");

                Logger.Info("Checkup da API de Integração concluído com sucesso");
                return Ok(new { status = "OK", message = "Todos os serviços estão funcionando corretamente" });
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Erro durante o checkup da API de Integração");
                return StatusCode(500, new { status = "ERROR", message = "Erro interno nos serviços" });
            }
        }

        /// <summary>
        /// Testa o serviço de conta conductor
        /// </summary>
        private async Task TestContaConductorService()
        {
            try
            {
                // Tenta executar uma operação simples do serviço de conta conductor
                // Usando um documento de teste que não deve existir para não afetar dados reais
                var testResult = await _contaConductorAppService.VerificarContaBbc("00000000000");
                
                // Se chegou até aqui, o serviço está respondendo
                Logger.Debug("Teste do serviço de conta conductor executado com sucesso");
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Falha no teste do serviço de conta conductor");
                throw new Exception("Serviço de conta conductor não está respondendo", ex);
            }
        }

        /// <summary>
        /// Testa o serviço PIX
        /// </summary>
        private async Task TestPixService()
        {
            try
            {
                // Tenta executar uma operação simples do serviço PIX
                // Usando dados de teste que não devem existir para não afetar dados reais
                var testResult = await _pixAppService.ConsultarChave("00000000000", "<EMAIL>");
                
                // Se chegou até aqui, o serviço está respondendo
                Logger.Debug("Teste do serviço PIX executado com sucesso");
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Falha no teste do serviço PIX");
                throw new Exception("Serviço PIX não está respondendo", ex);
            }
        }

        /// <summary>
        /// Testa o serviço de retenção
        /// </summary>
        private async Task TestRetencaoService()
        {
            try
            {
                // Verifica se o serviço de retenção está instanciado e acessível
                if (_retencaoAppService == null)
                {
                    throw new Exception("Serviço de retenção não está disponível");
                }
                
                Logger.Debug("Teste do serviço de retenção executado com sucesso");
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Falha no teste do serviço de retenção");
                throw new Exception("Serviço de retenção não está respondendo", ex);
            }
        }

        /// <summary>
        /// Testa o serviço de integração de viagem
        /// </summary>
        private async Task TestViagemIntegracaoService()
        {
            try
            {
                // Verifica se o serviço de integração de viagem está instanciado e acessível
                if (_viagemAppService == null)
                {
                    throw new Exception("Serviço de integração de viagem não está disponível");
                }
                
                Logger.Debug("Teste do serviço de integração de viagem executado com sucesso");
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Falha no teste do serviço de integração de viagem");
                throw new Exception("Serviço de integração de viagem não está respondendo", ex);
            }
        }

        /// <summary>
        /// Testa a conectividade com o banco de dados
        /// </summary>
        private async Task TestDatabaseConnection()
        {
            try
            {
                // Testa a conexão com o banco executando uma query simples
                await _configContext.Database.ExecuteSqlCommandAsync("SELECT 1");

                Logger.Debug("Teste de conectividade com banco de dados executado com sucesso");
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Falha no teste de conectividade com banco de dados");
                throw new Exception("Banco de dados não está acessível", ex);
            }
        }
    }
}

﻿using System;
using System.Collections.Generic;
using JetBrains.Annotations;
using SistemaInfo.BBC.Domain.External.CIOT.DTO;
using Cidade = SistemaInfo.BBC.Domain.Models.Cidade.Cidade;

namespace SistemaInfo.BBC.Domain.Contracts.Operacoes
{
    public class ConsultarOperacaoTransportePorIdReqMessage
    {
        public int IdOperacaoTransporte { get; set; }
    }
    
    public class ConsultarOperacaoTransportePorIdRespMessage
    {
        public ConsultarOperacaoTransportePorIdRespMessage(bool sucesso, string mensagem)
        {
            Sucesso = sucesso;
            Mensagem = mensagem;
        }

        public bool Sucesso { get; set; }
        public string Mensagem { get; set; }
        public OperacaoTransporteMessageResponseInfo Data { get; set; }
    }
    public class OperacaoTransporteMessageResponseInfo
    {
        
        // PRINCIPAL
        public int Tipo { get; set; }
        public string Ciot { get; set; }
        public Proprietario Proprietario { get; set; }
        public Contratante Contratante { get; set; }
        public string CpfCnpjProp { get; set; }
        public Motorista Motorista { get; set; }
        public string CpfCnpjMot { get; set; }
        public DateTime DataInicioFrete { get; set; }
        public DateTime DataFim { get; set; }
        public int? QuantidadeTarifas { get; set; }
        public string ValorTarifas { get; set; }
        public List<Veiculo> VeiculosList { get; set; }

        // VIAGEM
        public List<ViagemOperacao> ViagensList { get; set; }
        public Remetente Remetente { get; set; }
        public Consignatario Consignatario { get; set; }
        public Destinatario Destinatario { get; set; }
        public int? IbgeCidadeOrigem { get; set; }
        public int? IbgeCidadeDestino { get; set; }
        public Cidade CidadeOrigemPadrao { get; set; }
        public Cidade CidadeDestinoPadrao { get; set; }
        public string Peso { get; set; }
        public string CodNaturezaCarga { get; set; }
        public string ValorFrete { get; set; }
        public string ValorImposto { get; set; }
        public string ValorDespesas { get; set; }
        public string ValorCombustivel { get; set; }
        public string ValorPedagio { get; set; }
        
        
        // PAGAMENTO
        public Pagamento Pagamento { get; set; }
        


    }
}
using BBC.Test.Tests.StatusCadastroEnum.Fixture;
using SistemaInfo.BBC.Domain.Enum;
using System.ComponentModel;
using System.Reflection;
using Xunit;

namespace BBC.Test.Tests.StatusCadastroEnum
{
    [Collection(nameof(StatusCadastroEnumCollection))]
    public class StatusCadastroEnumTest
    {
        private readonly StatusCadastroEnumFixture _fixture;

        public StatusCadastroEnumTest(StatusCadastroEnumFixture fixture)
        {
            _fixture = fixture;
        }

        [Theory(DisplayName = "StatusCadastro - deve conter valores corretos")]
        [Trait("StatusCadastroEnum", "ValoresEnum")]
        [InlineData(0, "Bloqueado")]
        [InlineData(1, "Ativo")]
        [InlineData(2, "PendentedeValidação")]
        public void StatusCadastro_DeveConterValoresCorretos(int valorEsperado, string nomeEnum)
        {
            // Arrange & Act
            var enumValue = System.Enum.Parse<StatusCadastro>(nomeEnum);

            // Assert
            Assert.Equal(valorEsperado, (int)enumValue);
        }

        [Theory(DisplayName = "StatusCadastro - deve conter descrições corretas")]
        [Trait("StatusCadastroEnum", "DescricoesEnum")]
        [InlineData("Bloqueado", "Bloqueado")]
        [InlineData("Ativo", "Ativo")]
        [InlineData("PendentedeValidação", "Pendente de validação")]
        public void StatusCadastro_DeveConterDescricoesCorretas(string nomeEnum, string descricaoEsperada)
        {
            // Arrange
            var enumValue = System.Enum.Parse<StatusCadastro>(nomeEnum);
            var field = typeof(StatusCadastro).GetField(enumValue.ToString());
            var attribute = field.GetCustomAttribute<DescriptionAttribute>();

            // Act & Assert
            Assert.NotNull(attribute);
            Assert.Equal(descricaoEsperada, attribute.Description);
        }

        [Fact(DisplayName = "StatusCadastro - deve conter três valores")]
        [Trait("StatusCadastroEnum", "QuantidadeValores")]
        public void StatusCadastro_DeveConterTresValores()
        {
            // Arrange & Act
            var valores = System.Enum.GetValues(typeof(StatusCadastro));

            // Assert
            Assert.Equal(3, valores.Length);
        }

        [Theory(DisplayName = "StatusCadastro - deve permitir conversão para string")]
        [Trait("StatusCadastroEnum", "ConversaoString")]
        [InlineData("Bloqueado")]
        [InlineData("Ativo")]
        [InlineData("PendentedeValidação")]
        public void StatusCadastro_DevePermitirConversaoParaString(string nomeEsperado)
        {
            // Arrange & Act
            var enumValue = System.Enum.Parse<StatusCadastro>(nomeEsperado);
            var resultado = enumValue.ToString();

            // Assert
            Assert.Equal(nomeEsperado, resultado);
        }

        [Theory(DisplayName = "StatusCadastro - deve permitir conversão de string")]
        [Trait("StatusCadastroEnum", "ConversaoDeString")]
        [InlineData("Bloqueado", StatusCadastro.Bloqueado)]
        [InlineData("Ativo", StatusCadastro.Ativo)]
        [InlineData("PendentedeValidação", StatusCadastro.PendentedeValidação)]
        public void StatusCadastro_DevePermitirConversaoDeString(string nomeEnum, StatusCadastro enumEsperado)
        {
            // Arrange & Act
            var resultado = System.Enum.Parse<StatusCadastro>(nomeEnum);

            // Assert
            Assert.Equal(enumEsperado, resultado);
        }

        [Theory(DisplayName = "StatusCadastro - deve verificar se valor é definido")]
        [Trait("StatusCadastroEnum", "ValorDefinido")]
        [InlineData(0, true)]  // Bloqueado
        [InlineData(1, true)]  // Ativo
        [InlineData(2, true)]  // PendentedeValidação
        [InlineData(999, false)] // Valor inválido
        public void StatusCadastro_DeveVerificarSeValorEDefinido(int valor, bool esperado)
        {
            // Arrange & Act
            var resultado = System.Enum.IsDefined(typeof(StatusCadastro), valor);

            // Assert
            Assert.Equal(esperado, resultado);
        }

        [Fact(DisplayName = "StatusCadastro - deve usar fixture para obter valores")]
        [Trait("StatusCadastroEnum", "Fixture")]
        public void StatusCadastro_DeveUsarFixtureParaObterValores()
        {
            // Arrange & Act
            var ativo = _fixture.ObterStatusCadastroAtivo();
            var bloqueado = _fixture.ObterStatusCadastroBloqueado();
            var pendente = _fixture.ObterStatusCadastroPendente();

            // Assert
            Assert.Equal(StatusCadastro.Ativo, ativo);
            Assert.Equal(StatusCadastro.Bloqueado, bloqueado);
            Assert.Equal(StatusCadastro.PendentedeValidação, pendente);
        }
    }
}

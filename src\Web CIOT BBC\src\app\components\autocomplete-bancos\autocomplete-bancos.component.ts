// https://github.com/sengirab/ngAutocomplete

import { Component, ViewChild, Output, EventEmitter } from "@angular/core";
import { CreateNewAutocompleteGroup, SelectedAutocompleteItem, NgAutocompleteComponent } from "ng-auto-complete";
import { CiotService } from "../../ciot/services/ciot.service";
import { ArrayObservable } from "rxjs/observable/ArrayObservable";
import { Banco, BancoResponse } from "../../ciot/models/banco"; 
import { ObjBancos } from "./objBancos";

@Component({
  selector: 'app-autocomplete-bancos',
  templateUrl: './autocomplete-bancos.component.html',
  styles: []
})

export class AutocompleteBancosComponent {

  @ViewChild(NgAutocompleteComponent) public completer: NgAutocompleteComponent;
  static bancoList: Array<Banco> = [];
  static inicializado: boolean = false;
  static arrayBancos: Array<ObjBancos> = [];

  @Output() choosedBanco = new EventEmitter();

  public group = [
    CreateNewAutocompleteGroup(
      "Selecione",
      'completer',
      AutocompleteBancosComponent.arrayBancos,
      { titleKey: 'nome', childrenKey: null }
    ),
  ];

  constructor(private service: CiotService) {
    this.consultarBancos();
  }

  consultarBancos(): void {
    if (!AutocompleteBancosComponent.inicializado) {
      AutocompleteBancosComponent.inicializado = true;
      this.service.consultarBancos()
        .subscribe(
          result => {
            if (result.sucesso && result.bancos) {
              AutocompleteBancosComponent.bancoList = result.bancos;

              AutocompleteBancosComponent.arrayBancos = [];
              result.bancos.forEach(item => {
                var obj = new ObjBancos();
                obj.nome = item.nome;
                obj.id = item.codigo;

                AutocompleteBancosComponent.arrayBancos.push(obj);
              });

              if (this.completer != undefined) {
                this.completer.SetValues("completer", AutocompleteBancosComponent.arrayBancos);
              }
            } else {
              console.error('Erro ao consultar bancos:', result.excecao);
            }
          },
          error => {
            console.error('Erro na requisição:', error);
          }
        );
    } else if (AutocompleteBancosComponent.arrayBancos.length == 0) {
      setTimeout(() => { this.consultarBancos(); }, 500);
    } else {
      if (this.completer != undefined) {
        this.completer.SetValues("completer", AutocompleteBancosComponent.arrayBancos);
      }
    }
  }

  /**
   *
   * @param item
   * @constructor
   */
  Selected(item: SelectedAutocompleteItem) {
    if (item == undefined || item.item == null) return;
    this.choosedBanco.emit(item.item.original);
  }
}

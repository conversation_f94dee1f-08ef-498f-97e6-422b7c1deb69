using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using SistemaInfo.BBC.Application.Objects.Api.Viagem;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Mobile.Viagem;
using SistemaInfo.BBC.Application.Objects.Mobile.Viagem.Request;
using SistemaInfo.BBC.Application.Objects.Mobile.Viagem.Response;
using SistemaInfo.BBC.Application.Objects.Web.Transacao;
using SistemaInfo.BBC.Application.Objects.Web.Viagem;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Domain.External.Conductor.DTO.Transferencia;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Models.Viagem.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;

namespace SistemaInfo.BBC.Application.Interface.Viagem
{
    public interface IViagemAppService : IAppService<Domain.Models.Viagem.Viagem, 
        IViagemReadRepository, IViagemWriteRepository>
    {
        Task<ViagemIntegrarResponse> IntegrarPagamentoViagem(PagamentoViagemIntegrarRequest viagemIntegrarRequest, string token = "", bool servicoReenvio = false,  int pagamentoEventoId = 0);
        Task<ViagemIntegrarResponse> IntegrarPagamentoViagemV1(PagamentoViagemIntegrarRequest viagemIntegrarRequest, string token = "", bool servicoReenvio = false,  int pagamentoEventoId = 0);
        Task<Domain.Models.PagamentoEvento.PagamentoEvento> RegistrarPendenciaPagamento(int pagamentoEventoId, string pendenciaPagamento,
            StatusPagamento status, Tipo? tipoEvento, decimal? valorCancelamentoReq, DateTime? dataSolicitacao, bool contador = false, bool cancelamento = false, bool reenvioAutomatico = false);
        Task<CancelamentoEventoViagemResponse> CancelarEventoViagemV1(CancelamentoEventoViagemRequest cancelarEventoViagemRequest, int empresaId);
        Task<string> RetornarMensagemDockIntegracaoViagem(string mensagemTraduzida, TransferenciaEntreContaResp retornoTransferencia);
        
        #region Tarefas
        
        Task ServiceCancelarPagamentoEventos();

        #endregion

        #region Web
        
        Task<ConsultarGridViagemResponse>ConsultarGridViagem(ConsultarGridViagemRequest request);
        Task<ConsultaGridPagamentoEventoViagemResponse> ConsultarPagamentosViagem(int viagemId, int requestTake, int requestPage, OrderFilters requestOrder, List<QueryFilters> requestFilters);
        RespPadrao ConsultarPagamentosHistoricoViagem(int viagemId, int requestTake, int requestPage, OrderFilters requestOrder, List<QueryFilters> requestFilters);
        Task<ConsultarGridTransacaoPagamentoResponse> ConsultarTransacoesPagamento(int requestPagamentoId, int requestTake, int requestPage, OrderFilters requestOrder, List<QueryFilters> requestFilters);
        Task<ConsultarGridTransacaoPagamentoHistoricoResponse> ConsultarTransacoesPagamentoHistorico(int pagamentoHistoricoId, int pagamentoEventoId, int requestTake, int requestPage, OrderFilters requestOrder, List<QueryFilters> requestFilters); 
        Task<RespPadrao> ConsultarTransacaoPorId(int idTransacao);
        Task<RespPadrao> ConsultarGridViagemCombo(int requestTake, int requestPage, OrderFilters requestOrder, List<QueryFilters> requestFilters);
        Task<RespPadrao> ConsultarViagensCiot(string ciot);
        
        #endregion
        
        #region Telao

        Task<List<ConsultarPagamentoDiaResponse>> ConsultarPagamentosDia(DateTime dtInicio, DateTime dtFim,
            int empresaId, bool incluiTarifa = false);
        Task<List<TotalizadorItem>> ConsultarPagamentosPorDiaGrid(int empresaId, DateTime database);
        
        #endregion

        #region Viagem V2

        Task<ViagemIntegrarResponse> PagamentoViagemAbrir(PagamentoViagemIntegrarRequest viagemIntegrarRequest, string token = "", bool servicoReenvio = false,  int pagamentoEventoId = 0);

        Task<ViagemIntegrarResponse> PagamentoViagemCancelar(PagamentoViagemIntegrarRequest viagemIntegrarRequest, string token = "", bool servicoReenvio = false,  int pagamentoEventoId = 0);

        Task<ViagemIntegrarResponse> PagamentoViagemBaixa(PagamentoViagemIntegrarRequest viagemIntegrarRequest, string token = "", bool servicoReenvio = false,  int pagamentoEventoId = 0);

        #endregion
    }
}    
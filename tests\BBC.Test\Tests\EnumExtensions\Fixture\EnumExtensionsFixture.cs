using SistemaInfo.BBC.Domain.Enum;
using Xunit;

namespace BBC.Test.Tests.EnumExtensions.Fixture
{
    [CollectionDefinition(nameof(EnumExtensionsCollection))]
    public class EnumExtensionsCollection : ICollectionFixture<EnumExtensionsFixture>
    {
        
    }

    public class EnumExtensionsFixture : MockEngine
    {
        public SistemaInfo.BBC.Domain.Enum.ETipoPessoa ObterTipoPessoaFisica()
        {
            return SistemaInfo.BBC.Domain.Enum.ETipoPessoa.Fisica;
        }

        public SistemaInfo.BBC.Domain.Enum.ETipoPessoa ObterTipoPessoaJuridica()
        {
            return SistemaInfo.BBC.Domain.Enum.ETipoPessoa.Juridica;
        }

        public ESexo ObterSexoMasculino()
        {
            return ESexo.Masculino;
        }

        public ESexo ObterSexoFeminino()
        {
            return ESexo.Feminino;
        }

        public ESexo ObterSexoOutros()
        {
            return ESexo.Outros;
        }

        public ESexo ObterSexoIndefinido()
        {
            return ESexo.Indefinido;
        }

        public StatusCadastro ObterStatusCadastroAtivo()
        {
            return StatusCadastro.Ativo;
        }

        public StatusCadastro ObterStatusCadastroBloqueado()
        {
            return StatusCadastro.Bloqueado;
        }

        public StatusCadastro ObterStatusCadastroPendente()
        {
            return StatusCadastro.PendentedeValidacao;
        }

        public SistemaInfo.BBC.Domain.Enum.TipoEmpresa ObterTipoEmpresaJSL()
        {
            return SistemaInfo.BBC.Domain.Enum.TipoEmpresa.JSL;
        }

        public SistemaInfo.BBC.Domain.Enum.TipoEmpresa ObterTipoEmpresaMovida()
        {
            return SistemaInfo.BBC.Domain.Enum.TipoEmpresa.Movida;
        }

        public SistemaInfo.BBC.Domain.Enum.TipoEmpresa ObterTipoEmpresaBBC()
        {
            return SistemaInfo.BBC.Domain.Enum.TipoEmpresa.BBC;
        }
    }
}

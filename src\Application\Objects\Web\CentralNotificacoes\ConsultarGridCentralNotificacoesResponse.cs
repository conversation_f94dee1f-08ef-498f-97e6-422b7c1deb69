using System.Collections.Generic;

namespace SistemaInfo.BBC.Application.Objects.Web.CentralNotificacoes
{
    public class ConsultarGridCentralNotificacoes
    {
        public int Id { get; set; }
        public int Status { get; set; }
        public int ViagemId { get; set; }
        public string Ciot { get; set; }
        public int PagamentoEventoId { get; set; }
        public int PagamentoExternoId { get; set; }
        public string DataAlteracao { get; set; }
        public string FilialExternoId { get; set; }
        public string FilialNome { get; set; }
        public string EmpresaNome  {get; set; }
        public string NomeProprietario { get; set; }
        public string CpfcnpjProprietario { get; set; }
        public string NomeMotorista { get; set; }
        public string CpfcnpjMotorista { get; set; }
        public int Tipo { get; set; }
        public string Valor { get; set; }
        public string Descricao { get; set; }
        public string ValorTransferenciaMotorista { get; set; }
        public int? FormaPagamento { get; set; }

    }

    
    public class ConsultarGridCentralNotificacoesResponse
    {
        public int totalItems { get; set; }
        public List<ConsultarGridCentralNotificacoes> items{ get; set; }
    }
}
using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NLog;
using SistemaInfo.BBC.Api.Controllers.Base;
using SistemaInfo.BBC.Application.Interface.Pagamentos;
using SistemaInfo.BBC.Application.Interface.Viagem;
using SistemaInfo.BBC.Infra.Data.Context;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;

namespace SistemaInfo.BBC.Api.Controllers
{
    /// <summary>
    /// Controller responsável por verificar a saúde da API principal
    /// </summary>
    [Route("Checkup")]
    public class CheckupController : ApiControllerBase
    {
        private readonly IPagamentosAppService _pagamentosAppService;
        private readonly IViagemAppService _viagemAppService;
        private readonly ConfigContext _configContext;
        private static readonly Logger Logger = LogManager.GetCurrentClassLogger();

        /// <summary>
        /// Construtor com injeção de dependências
        /// </summary>
        /// <param name="engine"></param>
        /// <param name="pagamentosAppService"></param>
        /// <param name="viagemAppService"></param>
        /// <param name="configContext"></param>
        public CheckupController(IAppEngine engine,
            IPagamentosAppService pagamentosAppService,
            IViagemAppService viagemAppService,
            ConfigContext configContext) : base(engine)
        {
            _pagamentosAppService = pagamentosAppService;
            _viagemAppService = viagemAppService;
            _configContext = configContext;
        }

        /// <summary>
        /// Método de checkup que verifica a saúde dos serviços críticos da API
        /// Retorna 200 (OK) se todos os serviços estão funcionando ou 500 (Erro Interno) se algum falhar
        /// </summary>
        /// <returns>Status HTTP 200 ou 500</returns>
        [AllowAnonymous]
        [HttpGet]
        public async Task<IActionResult> Get()
        {
            try
            {
                Logger.Info("Iniciando checkup da API principal");

                // Teste 1: Verificar se o serviço de pagamentos está respondendo
                await TestPagamentosService();
                Logger.Info("Checkup - Serviço de pagamentos: OK");

                // Teste 2: Verificar se o serviço de viagens está respondendo
                await TestViagemService();
                Logger.Info("Checkup - Serviço de viagens: OK");

                // Teste 3: Verificar conectividade com banco de dados
                await TestDatabaseConnection();
                Logger.Info("Checkup - Conexão com banco de dados: OK");

                Logger.Info("Checkup da API principal concluído com sucesso");
                return Ok(new { status = "OK", message = "Todos os serviços estão funcionando corretamente" });
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Erro durante o checkup da API principal");
                return StatusCode(500, new { status = "ERROR", message = "Erro interno nos serviços" });
            }
        }

        /// <summary>
        /// Testa o serviço de pagamentos
        /// </summary>
        private async Task TestPagamentosService()
        {
            try
            {
                // Tenta executar uma operação simples do serviço de pagamentos
                // Usando um CPF/CNPJ de teste que não deve existir para não afetar dados reais
                var testResult = _pagamentosAppService.ConsultaContaCPF("00000000000");
                
                // Se chegou até aqui, o serviço está respondendo
                Logger.Debug("Teste do serviço de pagamentos executado com sucesso");
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Falha no teste do serviço de pagamentos");
                throw new Exception("Serviço de pagamentos não está respondendo", ex);
            }
        }

        /// <summary>
        /// Testa o serviço de viagens
        /// </summary>
        private async Task TestViagemService()
        {
            try
            {
                // Tenta executar uma operação simples do serviço de viagens
                // Usando um ID de teste que não deve existir para não afetar dados reais
                var testResult = await _viagemAppService.ConsultarValoresViagem(0);
                
                // Se chegou até aqui, o serviço está respondendo
                Logger.Debug("Teste do serviço de viagens executado com sucesso");
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Falha no teste do serviço de viagens");
                throw new Exception("Serviço de viagens não está respondendo", ex);
            }
        }

        /// <summary>
        /// Testa a conectividade com o banco de dados
        /// </summary>
        private async Task TestDatabaseConnection()
        {
            try
            {
                // Testa a conexão com o banco através do ConfigContext injetado
                // Isso verifica se o contexto do banco está acessível
                var canConnect = await _configContext.Database.CanConnectAsync();

                if (!canConnect)
                {
                    throw new Exception("Não foi possível conectar ao banco de dados");
                }

                Logger.Debug("Teste de conectividade com banco de dados executado com sucesso");
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Falha no teste de conectividade com banco de dados");
                throw new Exception("Banco de dados não está acessível", ex);
            }
        }
    }
}

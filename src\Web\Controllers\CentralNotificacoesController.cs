using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using SistemaInfo.BBC.Application.Interface.CentralNotificacoes;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.CentralNotificacoes;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Web.Attributes;
using SistemaInfo.BBC.Web.Controllers.Base;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;

namespace SistemaInfo.BBC.Web.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [Route("CentralNotificacoes")]
    public class CentralNotificacoesController : WebControllerBase<ICentralNotificacoesAppService>
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="engine"></param>
        /// <param name="appService"></param>
        public CentralNotificacoesController(IAppEngine engine, ICentralNotificacoesAppService appService) : base(engine, appService)
        {
        }
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("ConsultarGridCentralNotificacoes")]
        [Menu(new[] { EMenus.CentralNotificacoes })]
        public async Task<JsonResult> ConsultarGridCentralNotificacoes([FromBody]DtoConsultaGridNotificacoes request)
        {
            var consultarGridCentralNotificacoes = await AppService.ConsultarGridCentralNotificacoes( request.EmpresaId, request.dataInicial, request.dataFinal, request.Perfil, request.Take, request.Page, request.Order, request.Filters);
            return ResponseBase.ResponderSucesso(consultarGridCentralNotificacoes);
        }
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("ConsultarGridCentralNotificacoesValePedagio")]
        [Menu(new[] { EMenus.CentralNotificacoes })]
        public async Task<JsonResult> ConsultarGridCentralNotificacoesValePedagio([FromBody]ConsultarGridCentralNotificacoesValePedagioRequest request)
        {
            var response = await AppService.ConsultarGridCentralNotificacoesValePedagio(request);
            return ResponseBase.ResponderSucesso(response);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("Salvar")]
        public async Task<JsonResult> SaveCentralNotificacoes([FromBody]CentralNotificacoesRequest request) => 
            ResponseBase.Responder(await AppService.Save(request));

        /// <summary>
        /// 
        /// </summary>
        /// <param name="idCentralNotificacoes"></param>
        /// <returns></returns>
        [HttpGet("ConsultarPorId")]
        [Menu(new[] { EMenus.CentralNotificacoes })]
        public JsonResult ConsultarPorId(int idCentralNotificacoes)
        {
            try
            {
                var consultarCentralNotificacoes = AppService.ConsultarPorId(idCentralNotificacoes);
                return ResponseBase.ResponderSucesso(consultarCentralNotificacoes);
            }
            catch (Exception e)
            {
                return ResponseBase.ResponderErro("Notificações não encontradas! Mensagem: " + e.Message);
            }
        }
    }
}
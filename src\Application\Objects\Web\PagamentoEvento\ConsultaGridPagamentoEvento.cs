﻿using System;
using System.Collections.Generic;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Domain.Enum;

namespace SistemaInfo.BBC.Application.Objects.Web.PagamentoEvento
{
    
    public class ConsultaGridPagamentoEventoItem
    {
        public int Id { get; set; }
        public int PagamentoExternoId { get; set; }
        public int ViagemExternoId { get; set; }
        public string Valor { get; set; }
        public string Status { get; set; }
        public Tipo? Tipo { get; set; }
        public int ViagemId { get; set; }
        public string MotivoPendencia { get; set; }
        public string FormaPagamento { get; set; }
        public string CpfCnpjProprietario { get; set; }
        public string CpfCnpjMotorista { get; set; }
        public int? ContadorReenvio { get; set; }
        public string ValorTransferenciaMotorista { get; set; }
        public int? ContadorVerificacaoStatusPix { get; set; }
        public string HorasDesdeTerceiraVerificacaoStatusPix { get; set; }
        public string Ocorrencia { get; set; }
        public string DataCadastro { get; set; }
        public string DataAlteracao { get; set; }
        public string DataBaixa { get; set; }
        public string DataCancelamento { get; set; }
        public int? EmpresaId { get; set; }
        public string RazaoSocialEmpresa { get; set; }
        public string CnpjEmpresa { get; set; }
        public int VersaoIntegracao { get; set; }
        public string DataPrevisaoPagamento { get; set; }

    }
    public class ConsultaGridPagamentoEventoResponse
    {
            public int totalItems { get; set; }
            public List<ConsultaGridPagamentoEventoItem> items{ get; set; }
    }
    
    public class ConsultaGridPagamentoEventoRequest : BaseGridRequest
    {
        public String dataInicial { get; set; }
       
        public String dataFinal { get; set; }

        public int EmpresaId { get; set; } = 0;
        
        public StatusPagamento Status { get; set; }
    }
}
using System;
using SistemaInfo.BBC.Domain.Enum;

namespace SistemaInfo.BBC.Domain.Models.Viagem.Commands.Base
{
    public class BaseViagemCommand
    {
        public int Id { get; set; }
        public int EmpresaId { get; set; }
        public StatusViagem? Status { get; set; }
        public string FilialId { get; set; }
        public int PortadorProprietarioId { get; set; }
        public string NomeProprietario { get; set; }
        public int? PortadorMotoristaId { get; set; }
        public string NomeMotorista { get; set; }
        public int? CiotViagemId { get; set; }
        public int? ViagemExternoId { get; set; }
        public int? CidadeOrigemId { get; set; }
        public int? CidadeDestinoId { get; set; }
        public TipoBanco? TipoBanco { get; set; }
        public int? PagamentoExternoId { get; set; }
        public string Agencia { get; set; }
        public string Conta { get; set; }
        /// <summary>
        /// ETipoContaDock Corrente = 1, Poupanca = 2, Salario = 3
        /// </summary>
        public int? TipoConta { get; set; }
        public DateTime? DataBaixa { get; set; }
        
        public string Ciot { get; set; }
        public string VerificadorCiot { get; set; }
        public string CodigoNaturezaCarga { get; set; }
        public decimal? PesoCarga { get; set; }
        
        #region Viagem 2
        public int? CiotId { get; set; }
        public DateTime? DataDeclaracaoCiot { get; set; }
        public TipoCiot? TipoCiot { get; set; }
        public EVersaoIntegracao? VersaoIntegracaoViagem { get; set; }
        public StatusCiot? StatusCiot { get; set; }
        public string DescricaoCiot { get; set; }
        public DateTime DataCancelamento { get; set; }
        public decimal? ValorComplemento { get; set; }
        public decimal? ValorFrete { get; set; }
        public decimal? ValorSaldo { get; set; }
        public decimal? ValorAdiantamento { get; set; }
        public int? QuantidadeTarifas { get; set; }
        public decimal? ValorTarifas { get; set; }
        public decimal? ValorCombustivel { get; set; }
        public decimal? ValorDespesa { get; set; }
        public decimal? TotalImposto { get; set; }
        public decimal? TotalPedagio { get; set; }
        #endregion
    }
}
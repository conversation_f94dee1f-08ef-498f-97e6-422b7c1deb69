using System.Threading.Tasks;
using SistemaInfo.BBC.Application.Objects.Api.Viagem;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Domain.External.CIOT.DTO;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;

namespace SistemaInfo.BBC.Application.Interface.Viagem
{
    /// <summary>
    /// Interface para serviços de integração de viagem V2
    /// </summary>
    public interface IViagemIntegracaoAppService : IAppService
    {
        /// <summary>
        /// Método responsável por cancelar um pagamento de viagem na versão 2 da API
        /// </summary>
        /// <param name="cancelarEventoViagemRequest">Dados da viagem para cancelar</param>
        /// <returns>Resposta da integração</returns>
        Task<CancelamentoEventoViagemV2Response> Cancelar(CancelamentoEventoViagemV2Request cancelarEventoViagemRequest, int empresaId);

        /// <summary>
        /// Método responsável por integrar uma viagem completa na versão 2 da API
        /// Inclui controle de CIOT, múltiplos pagamentos, veículos e portadores
        /// </summary>
        /// <param name="viagemIntegrarRequest">Dados completos da viagem para integração</param>
        /// <param name="token">Token de autenticação</param>
        /// <returns>Resposta da integração completa</returns>
        Task<RespPadrao> Integrar(ViagemIntegrarV2Request viagemIntegrarRequest, string token = "");
        
        
        /// <summary>
        /// Método responsável por integrar uma viagem completa na versão 2 da API
        /// Inclui controle de CIOT, múltiplos pagamentos, veículos e portadores
        /// </summary>
        /// <param name="request">Dados completos de proprietário motorista</param>
        /// <returns>Resposta da se tem tac agregado</returns>
        Task<RespPadrao> ConsultarTacAgregado(ConsultarTacAgregadoRequest request);
    }
}

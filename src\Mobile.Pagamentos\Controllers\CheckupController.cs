using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using NLog;
using SistemaInfo.BBC.Mobile.Pagamentos.Controllers.Base;
using SistemaInfo.BBC.Application.Interface.Pagamentos;
using SistemaInfo.BBC.Application.Interface.Pix;
using SistemaInfo.BBC.Application.Interface.ContaConductor;
using SistemaInfo.BBC.Infra.Data.Context;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;

namespace SistemaInfo.BBC.Mobile.Pagamentos.Controllers
{
    /// <summary>
    /// Controller responsável por verificar a saúde da API Mobile Pagamentos
    /// </summary>
    [Route("Checkup")]
    public class CheckupController : ApiControllerBase
    {
        private readonly IPagamentosAppService _pagamentosAppService;
        private readonly IPixAppService _pixAppService;
        private readonly IContaConductorAppService _contaConductorAppService;
        private readonly ConfigContext _configContext;
        private static readonly Logger Logger = LogManager.GetCurrentClassLogger();

        /// <summary>
        /// Construtor com injeção de dependências
        /// </summary>
        /// <param name="engine"></param>
        /// <param name="pagamentosAppService"></param>
        /// <param name="pixAppService"></param>
        /// <param name="contaConductorAppService"></param>
        /// <param name="configContext"></param>
        public CheckupController(IAppEngine engine,
            IPagamentosAppService pagamentosAppService,
            IPixAppService pixAppService,
            IContaConductorAppService contaConductorAppService,
            ConfigContext configContext) : base(engine)
        {
            _pagamentosAppService = pagamentosAppService;
            _pixAppService = pixAppService;
            _contaConductorAppService = contaConductorAppService;
            _configContext = configContext;
        }

        /// <summary>
        /// Método de checkup que verifica a saúde dos serviços críticos da API Mobile Pagamentos
        /// Retorna 200 (OK) se todos os serviços estão funcionando ou 500 (Erro Interno) se algum falhar
        /// </summary>
        /// <returns>Status HTTP 200 ou 500</returns>
        [AllowAnonymous]
        [HttpGet]
        public async Task<IActionResult> Get()
        {
            try
            {
                Logger.Info("Iniciando checkup da API Mobile Pagamentos");

                // Teste 1: Verificar se o serviço de pagamentos está respondendo
                await TestPagamentosService();
                Logger.Info("Checkup - Serviço de pagamentos: OK");

                // Teste 2: Verificar se o serviço PIX está respondendo
                await TestPixService();
                Logger.Info("Checkup - Serviço PIX: OK");

                // Teste 3: Verificar se o serviço de conta conductor está respondendo
                await TestContaConductorService();
                Logger.Info("Checkup - Serviço de conta conductor: OK");

                // Teste 4: Verificar conectividade com banco de dados
                await TestDatabaseConnection();
                Logger.Info("Checkup - Conexão com banco de dados: OK");

                // Teste 5: Verificar serviços específicos de pagamentos mobile
                await TestMobilePagamentosServices();
                Logger.Info("Checkup - Serviços de pagamentos mobile: OK");

                Logger.Info("Checkup da API Mobile Pagamentos concluído com sucesso");
                return Ok(new { status = "OK", message = "Todos os serviços estão funcionando corretamente" });
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Erro durante o checkup da API Mobile Pagamentos");
                return StatusCode(500, new { status = "ERROR", message = "Erro interno nos serviços" });
            }
        }

        /// <summary>
        /// Testa o serviço de pagamentos
        /// </summary>
        private async Task TestPagamentosService()
        {
            try
            {
                // Verifica se o serviço de pagamentos está instanciado e acessível
                if (_pagamentosAppService == null)
                {
                    throw new Exception("Serviço de pagamentos não está disponível");
                }

                // Tenta executar uma operação simples do serviço de pagamentos
                // Usando um CPF/CNPJ de teste que não deve existir para não afetar dados reais
                var testResult = _pagamentosAppService.ConsultaContaCPF("00000000000");
                
                // Se chegou até aqui, o serviço está respondendo
                Logger.Debug("Teste do serviço de pagamentos executado com sucesso");
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Falha no teste do serviço de pagamentos");
                throw new Exception("Serviço de pagamentos não está respondendo", ex);
            }
        }

        /// <summary>
        /// Testa o serviço PIX
        /// </summary>
        private async Task TestPixService()
        {
            try
            {
                // Verifica se o serviço PIX está instanciado e acessível
                if (_pixAppService == null)
                {
                    throw new Exception("Serviço PIX não está disponível");
                }

                // Tenta executar uma operação simples do serviço PIX
                // Usando dados de teste que não devem existir para não afetar dados reais
                var testResult = await _pixAppService.ConsultarChave("00000000000", "<EMAIL>");
                
                // Se chegou até aqui, o serviço está respondendo
                Logger.Debug("Teste do serviço PIX executado com sucesso");
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Falha no teste do serviço PIX");
                throw new Exception("Serviço PIX não está respondendo", ex);
            }
        }

        /// <summary>
        /// Testa o serviço de conta conductor
        /// </summary>
        private async Task TestContaConductorService()
        {
            try
            {
                // Verifica se o serviço de conta conductor está instanciado e acessível
                if (_contaConductorAppService == null)
                {
                    throw new Exception("Serviço de conta conductor não está disponível");
                }

                // Tenta executar uma operação simples do serviço de conta conductor
                // Usando um documento de teste que não deve existir para não afetar dados reais
                var testResult = await _contaConductorAppService.VerificarContaBbc("00000000000");
                
                // Se chegou até aqui, o serviço está respondendo
                Logger.Debug("Teste do serviço de conta conductor executado com sucesso");
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Falha no teste do serviço de conta conductor");
                throw new Exception("Serviço de conta conductor não está respondendo", ex);
            }
        }

        /// <summary>
        /// Testa a conectividade com o banco de dados
        /// </summary>
        private async Task TestDatabaseConnection()
        {
            try
            {
                // Testa a conexão com o banco executando uma query simples
                await _configContext.Database.ExecuteSqlCommandAsync("SELECT 1");

                Logger.Debug("Teste de conectividade com banco de dados executado com sucesso");
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Falha no teste de conectividade com banco de dados");
                throw new Exception("Banco de dados não está acessível", ex);
            }
        }

        /// <summary>
        /// Testa os serviços específicos de pagamentos mobile
        /// </summary>
        private async Task TestMobilePagamentosServices()
        {
            try
            {
                // Verifica se os serviços específicos de pagamentos mobile estão funcionando
                // Testa componentes críticos da API Mobile Pagamentos
                
                // Verifica se o Engine está funcionando corretamente
                if (Engine == null)
                {
                    throw new Exception("Engine da aplicação não está disponível");
                }

                // Simula verificação de serviços de pagamentos mobile
                await Task.Delay(10); // Simula operação assíncrona
                
                Logger.Debug("Teste dos serviços de pagamentos mobile executado com sucesso");
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Falha no teste dos serviços de pagamentos mobile");
                throw new Exception("Serviços de pagamentos mobile não estão acessíveis", ex);
            }
        }
    }
}

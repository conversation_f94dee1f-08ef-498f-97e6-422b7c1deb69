﻿using System;
using System.Globalization;
using System.Linq;
using SistemaInfo.BBC.Application.Objects.Mobile.Viagem.Response;
using SistemaInfo.BBC.Application.Objects.Web.PagamentoEvento;
using SistemaInfo.BBC.Application.Objects.Web.Transacao;
using SistemaInfo.BBC.Application.Objects.Web.Viagem;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.BBC.Domain.Models.PagamentoEvento;
using SistemaInfo.BBC.Domain.Models.PagamentoEventoHistorico;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.AutoMapper.PagamentoEventoMapper
{
    public class PagamentoEventoMappingProfile : SistemaInfoMappingProfile
    {
        public PagamentoEventoMappingProfile()
        {
            CreateMap<PagamentoEvento, ConsultaGridPagamentoEventoItem>()
                .ForMember(dest => dest.PagamentoExternoId, opts => opts.MapFrom(s => s.PagamentoExternoId))
                .ForMember(dest => dest.ViagemExternoId, opts => opts.MapFrom(s => s.Viagem.ViagemExternoId))
                .ForMember(dest => dest.Status, opts => opts.MapFrom(s => s.Status.ToString()))
                .ForMember(dest => dest.FormaPagamento, opts => opts.MapFrom(s => s.FormaPagamento.ToString()))
                .ForMember(dest => dest.Valor, opt => opt.MapFrom(s => StringHelper.FormatMoney(s.Valor)))
                .ForMember(dest => dest.ValorTransferenciaMotorista, opt => opt.MapFrom(s => s.ValorTransferenciaMotorista.HasValue ? s.ValorTransferenciaMotorista.Value.ToString("C2", CultureInfo.GetCultureInfo("pt-BR")) : "Valor não disponível"))
                .ForMember(dest => dest.CpfCnpjProprietario, opts => opts.MapFrom(s => FormatUtils.CpfCnpj(s.Viagem.PortadorProprietario.CpfCnpj)))
                .ForMember(dest => dest.CpfCnpjMotorista, opts => opts.MapFrom(s => FormatUtils.CpfCnpj(s.Viagem.PortadorMotorista.CpfCnpj)))
                .ForMember(dest => dest.HorasDesdeTerceiraVerificacaoStatusPix, opts => opts.MapFrom(s => s.DataTerceiraVerificacaoStatusPix.HasValue ? (DateTime.Now - (DateTime) s.DataTerceiraVerificacaoStatusPix).TotalHours.ToString("N0") : null))
                .ForMember(dest => dest.DataCadastro, opts => opts.MapFrom(s => s.DataCadastro.FormatDateTimeBr()))
                .ForMember(dest => dest.DataAlteracao, opts => opts.MapFrom(s => s.DataAlteracao.FormatDateTimeBr()))
                .ForMember(dest => dest.DataBaixa, opts => opts.MapFrom(s => s.DataBaixa.FormatDateTimeBr()))
                .ForMember(dest => dest.DataCancelamento, opts => opts.MapFrom(s => s.DataCancelamento.FormatDateTimeBr()))
                .ForMember(dest => dest.EmpresaId, opts => opts.MapFrom(s => s.Empresa.Id))
                .ForMember(dest => dest.CnpjEmpresa, opts => opts.MapFrom(s => FormatUtils.CpfCnpj(s.Empresa.Cnpj)))
                .ForMember(dest => dest.RazaoSocialEmpresa, opts => opts.MapFrom(s => FormatUtils.CpfCnpj(s.Empresa.RazaoSocial)));
            
            
            CreateMap<PagamentoEvento, ConsultaGridPagamentoEventoViagemItem>()
                .ForMember(dest => dest.Id, opts => opts.MapFrom(s => s.Id))
                .ForMember(dest => dest.PagamentoExternoId, opts => opts.MapFrom(s => s.PagamentoExternoId))
                .ForMember(dest => dest.Status, opts => opts.MapFrom(s => s.Status.ToString()))
                .ForMember(dest => dest.FormaPagamento, opts => opts.MapFrom(s => s.FormaPagamento.ToString()))
                .ForMember(dest => dest.Tipo, opts => opts.MapFrom(s => s.Tipo.ToString()))
                .ForMember(dest => dest.ValorParcela, opt => opt.MapFrom(s => StringHelper.FormatMoney(s.Valor)))
                .ForMember(dest => dest.ValorTransferenciaMotorista, opt => opt.MapFrom(s => s.ValorTransferenciaMotorista.HasValue ? s.ValorTransferenciaMotorista.Value.ToString("C2", CultureInfo.GetCultureInfo("pt-BR")) : ""))
                .ForMember(dest => dest.ValorTarifaBbc, opts => opts.MapFrom(s => s.ValorTarifaBbc.HasValue ? s.ValorTarifaBbc.Value.ToString("C2", CultureInfo.GetCultureInfo("pt-BR")) : ""))
                .ForMember(dest => dest.ValorTarifaPix, opts => opts.MapFrom(s => s.ValorTarifaPix.HasValue ? s.ValorTarifaPix.Value.ToString("C2", CultureInfo.GetCultureInfo("pt-BR")) : ""))
                .ForMember(dest => dest.Mensagem, opts => opts.MapFrom(s => s.MotivoPendencia))
                .ForMember(dest => dest.ContadorReenvio, opts => opts.MapFrom(s => s.ContadorReenvio))
                .ForMember(dest => dest.ContadorVerificacaoStatusPix, opts => opts.MapFrom(s => s.ContadorVerificacaoStatusPix))
                .ForMember(dest => dest.DataCadastro, opts => opts.MapFrom(s => s.DataCadastro.FormatDateTimeBr()))
                .ForMember(dest => dest.DataAlteracao, opts => opts.MapFrom(s => s.DataAlteracao.FormatDateTimeBr()))
                .ForMember(dest => dest.DataBaixa, opts => opts.MapFrom(s => s.DataBaixa.FormatDateTimeBr()))
                .ForMember(dest => dest.DataCancelamento, opts => opts.MapFrom(s => s.DataCancelamento.FormatDateTimeBr()));
            
            CreateMap<PagamentoEventoHistorico, ConsultaGridPagamentoEventoHistoricoViagemItem>()
                .ForMember(dest => dest.Id, opts => opts.MapFrom(s => s.Id))
                .ForMember(dest => dest.PagamentoExternoId, opts => opts.MapFrom(s => s.PagamentoExternoId))
                .ForMember(dest => dest.PagamentoEventoId, opts => opts.MapFrom(s => s.PagamentoEventoId))
                .ForMember(dest => dest.Status, opts => opts.MapFrom(s => s.Status.ToString()))
                .ForMember(dest => dest.FormaPagamento, opts => opts.MapFrom(s => s.FormaPagamento.ToString()))
                .ForMember(dest => dest.Tipo, opts => opts.MapFrom(s => s.Tipo.ToString()))
                .ForMember(dest => dest.ValorParcela, opt => opt.MapFrom(s => StringHelper.FormatMoney(s.Valor)))
                .ForMember(dest => dest.ValorTransferenciaMotorista, opt => opt.MapFrom(s => s.ValorTransferenciaMotorista.HasValue ? s.ValorTransferenciaMotorista.Value.ToString("C2", CultureInfo.GetCultureInfo("pt-BR")) : ""))
                .ForMember(dest => dest.ValorTarifaBbc, opts => opts.MapFrom(s => s.ValorTarifaBbc.HasValue ? s.ValorTarifaBbc.Value.ToString("C2", CultureInfo.GetCultureInfo("pt-BR")) : ""))
                .ForMember(dest => dest.ValorTarifaPix, opts => opts.MapFrom(s => s.ValorTarifaPix.HasValue ? s.ValorTarifaPix.Value.ToString("C2", CultureInfo.GetCultureInfo("pt-BR")) : ""))
                .ForMember(dest => dest.Mensagem, opts => opts.MapFrom(s => s.Ocorrencia))
                .ForMember(dest => dest.ContadorReenvio, opts => opts.MapFrom(s => s.ContadorReenvio))
                .ForMember(dest => dest.ContadorVerificacaoStatusPix, opts => opts.MapFrom(s => s.ContadorVerificacaoStatusPix))
                .ForMember(dest => dest.DataCadastro, opts => opts.MapFrom(s => s.DataCadastro.FormatDateTimeBr()))
                .ForMember(dest => dest.DataAlteracao, opts => opts.MapFrom(s => s.DataAlteracao.FormatDateTimeBr()))
                .ForMember(dest => dest.DataBaixa, opts => opts.MapFrom(s => s.DataBaixa.FormatDateTimeBr()))
                .ForMember(dest => dest.DataCancelamento, opts => opts.MapFrom(s => s.DataCancelamento.FormatDateTimeBr()));
            

            CreateMap<PagamentoEvento, LogsPagamento>()
                .ForMember(dest => dest.DataCadastro, opts => opts.MapFrom(s => s.DataCadastro.FormatDateTimeBr()))
                .ForMember(dest => dest.DataRetorno, opts => opts.MapFrom(s => s.DataRetorno.FormatDateTimeBr()))
                .ForMember(dest => dest.DataCadastroCancelamento, opts => opts.MapFrom(s => s.DataCadastroCancelamento.FormatDateTimeBr()))
                .ForMember(dest => dest.DataRetornoCancelamento, opts => opts.MapFrom(s => s.DataRetornoCancelamento.FormatDateTimeBr()))
                .ForMember(dest => dest.JsonEnvio, opts => opts.MapFrom(s => s.JsonEnvio))
                .ForMember(dest => dest.JsonRetorno, opts => opts.MapFrom(s => s.JsonRetorno))
                .ForMember(dest => dest.JsonEnvioCancelamento, opts => opts.MapFrom(s => s.JsonEnvioCancelamento))
                .ForMember(dest => dest.JsonRetornoCancelamento, opts => opts.MapFrom(s => s.JsonRetornoCancelamento));
            
            
            CreateMap<PagamentoEventoHistorico, LogsPagamentoHistorico>()
                .ForMember(dest => dest.DataCadastro, opts => opts.MapFrom(s => s.DataCadastro.FormatDateTimeBr()))
                .ForMember(dest => dest.DataRetorno, opts => opts.MapFrom(s => s.DataRetorno.FormatDateTimeBr()))
                .ForMember(dest => dest.JsonEnvio, opts => opts.MapFrom(s => s.JsonEnvio))
                .ForMember(dest => dest.JsonRetorno, opts => opts.MapFrom(s => s.JsonRetorno));
            
            CreateMap<PagamentoEvento, PagamentoMobileResponse>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.ViagemId, opt => opt.MapFrom(src => src.ViagemId))
                .ForMember(dest => dest.ViagemExternoId, opt => opt.MapFrom(src => src.Viagem.ViagemExternoId))
                .ForMember(dest => dest.RazaoSocialEmpresa, opt => opt.MapFrom(src => src.Empresa.RazaoSocial))
                .ForMember(dest => dest.PagamentoExternoId, opt => opt.MapFrom(src => src.PagamentoExternoId))
                .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Status.ToString()))
                .ForMember(dest => dest.FormaPagamento, opt => opt.MapFrom(src => src.FormaPagamento.ToString()))
                .ForMember(dest => dest.RecebedorAutorizado, opt => opt.MapFrom(src => src.RecebedorAutorizado))
                .ForMember(dest => dest.Tipo, opt => opt.MapFrom(src => src.Tipo.ToString()))
                .ForMember(dest => dest.ValorParcela, opt => opt.MapFrom(src => src.Valor))
                .ForMember(dest => dest.ValorTransferenciaMotorista, opt => opt.MapFrom(src => src.ValorTransferenciaMotorista))
                .ForMember(dest => dest.DataCadastro, opt => opt.MapFrom(src => src.DataCadastro))
                .ForMember(dest => dest.DataAlteracao, opt => opt.MapFrom(src => src.DataAlteracao.HasValue ? src.DataAlteracao.Value.ToString("yyyy-MM-ddTHH:mm:ss") : null))
                .ForMember(dest => dest.DataBaixa, opt => opt.MapFrom(src => src.DataBaixa.HasValue ? src.DataBaixa.Value.ToString("yyyy-MM-ddTHH:mm:ss") : null))
                .ForMember(dest => dest.DataCancelamento, opt => opt.MapFrom(src => src.DataCancelamento.HasValue ? src.DataCancelamento.Value.ToString("yyyy-MM-ddTHH:mm:ss") : null))
                .ForMember(dest => dest.StatusAntecipacaoParcelaProprietario, opt => opt.MapFrom(src => src.StatusAntecipacaoParcelaProprietario))
                .ForMember(dest => dest.DataPrevisaoPagamento, opt => opt.MapFrom(src => src.DataPrevisaoPagamento  ))
                .ForMember(dest => dest.Transacoes, opt => opt.Ignore()) 
                .AfterMap((src, dest, context) =>
                {
                    dest.Transacoes = src.Transacao?
                        .Where(t => t.Tipo != Tipo.Tarifas 
                                    && (t.FormaPagamento != FormaPagamentoEvento.RetencaoAntecipacao 
                                        && t.FormaPagamento != FormaPagamentoEvento.Antecipacao))
                        .Select(t =>
                    {
                        var transacao = context.Mapper.Map<TransacaoMobileResponse>(t);
                        transacao.DataAlteracao = src.DataAlteracao?.ToString("yyyy-MM-ddTHH:mm:ss");
                        transacao.DataCancelamento = src.DataCancelamento?.ToString("yyyy-MM-ddTHH:mm:ss");
                        return transacao;
                    }).ToList();
                });
            
            
        }
    }
}
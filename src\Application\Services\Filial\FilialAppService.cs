using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using AutoMapper.QueryableExtensions;
using Microsoft.EntityFrameworkCore;
using NLog;
using SistemaInfo.BBC.Application.Helpers;
using SistemaInfo.BBC.Application.Interface.Cidade;
using SistemaInfo.BBC.Application.Interface.Filial;
using SistemaInfo.BBC.Application.Objects.Api.Filial;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.Filial;
using SistemaInfo.BBC.Application.Objects.Web.FilialCentroCusto;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.BBC.Domain.Models.CentroCusto.Commands;
using SistemaInfo.BBC.Domain.Models.CentroCusto.Repository;
using SistemaInfo.BBC.Domain.Models.Filial.Commands;
using SistemaInfo.BBC.Domain.Models.Filial.Repository;
using SistemaInfo.BBC.Domain.Models.Usuario.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;

namespace SistemaInfo.BBC.Application.Services.Filial
{
    public class FilialAppService :
        AppService<Domain.Models.Filial.Filial, IFilialReadRepository, IFilialWriteRepository>, IFilialAppService
    {
        private readonly ICidadeAppService _cidadeAppService;
        private readonly ICentroCustoReadRepository _centroCustoReadRepository;
        private readonly IUsuarioReadRepository _usuarioReadRepository;


        public FilialAppService(IAppEngine engine,
            IFilialReadRepository readRepository,
            IFilialWriteRepository writeRepository,
            ICidadeAppService cidadeAppService,
            IUsuarioReadRepository usuarioReadRepository,
            ICentroCustoReadRepository centroCustoReadRepository)
            : base(engine, readRepository, writeRepository)
        {
            _cidadeAppService = cidadeAppService;
            _centroCustoReadRepository = centroCustoReadRepository;
            _usuarioReadRepository = usuarioReadRepository;
        }

        public async Task<RespPadrao> CadastrarFilialParaEmpresaAsync(FilialParaEmpresaRequest request)
        {
            try
            {
                var filialCadastrada = Repository.Query.FirstOrDefault(o => o.EmpresaId == request.EmpresaId);

                if (filialCadastrada != null)
                    return new RespPadrao
                    {
                        sucesso = false,
                        mensagem = "Empresa já vinculada a uma filial."
                    };

                var filial = Mapper.Map<FilialSaveCommand>(request);
                await Engine.CommandBus.SendCommandAsync(filial);

                return new RespPadrao
                {
                    sucesso = true,
                    mensagem = ""
                };
            }
            catch (Exception e)
            {
                return new RespPadrao
                {
                    sucesso = false,
                    mensagem = e.Message
                };
            }
        }

        public ConsultarGridFilialResponse ConsultarGridFilial(int take, int page, OrderFilters orderFilters,
            List<QueryFilters> filters)
        {
            try
            {
                new LogHelper().LogOperationStart("ConsultarGridFilial");
               
                #region Consulta personalizada filtros da grid

                foreach (var item in filters)
                {
                    item.Valor = item.Campo switch
                    {
                        "cnpj" => item.Valor.Replace(".", "").Replace("/", "").Replace("-", ""),
                        _ => item.Valor
                    };
                }
                #endregion
                
            
                var lFilial = Repository.Query.GetAll();
                
                var filtrarUsuario = filters.Find(q => q.Campo == "FiltrarUsuario")?.Valor == "true";
                filters.Remove(filters.Find(q => q.Campo == "FiltrarUsuario"));

                if (filtrarUsuario)
                {
                    var lUser = _usuarioReadRepository.GetUsuarioFull(User.Id);

                    if (lUser.UsuarioCentroCusto != null && User.EmpresaId > 0)
                    {
                        var lCentroCustoUsuario = lUser.UsuarioCentroCusto.Select(x => x.CentroCustoId).ToList();
                        lFilial = lFilial
                            .Where(a => lCentroCustoUsuario.Contains(a.CentroCustos.FirstOrDefault().Id));
                    }

                    if (lUser.UsuarioFilial != null && User.EmpresaId > 0)
                    {
                        var lFilialUsuario = lUser.UsuarioFilial.Select(x => x.FilialId).ToList();
                        lFilial = lFilial
                            .Where(a => lFilialUsuario.Contains(a.Id));
                    }
                }

                lFilial = lFilial.AplicarFiltrosDinamicos(filters);
                lFilial = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                    ? lFilial.OrderByDescending(o => o.Id)
                    : lFilial.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

                var lCount = lFilial.Count();

                var retorno = lFilial.Skip((page - 1) * take)
                    .Take(take)
                    .ProjectTo<ConsultarGridFilial>(Engine.Mapper.ConfigurationProvider).ToList();

                return new ConsultarGridFilialResponse
                {
                    items = retorno,
                    totalItems = lCount
                };
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar ConsultarGridFilial");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("ConsultarGridFilial");
            }
        }

        public async Task<RespPadrao> Save(FilialRequest lFilialReq)
        {
            try
            {
                var lFilial = Mapper.Map<FilialSaveComRetornoCommand>(lFilialReq);

                if (lFilialReq.FilialCentroCusto != null)
                {
                    var lCentrosCustos = _centroCustoReadRepository.GetAll()
                        .Where(x => lFilialReq.FilialCentroCusto.Any(y => y.centroCustoId == x.Id)).ToList();
                    lFilial.FilialCentroCusto = lCentrosCustos;
                }

                lFilial.ValidarCadastro();

                var result = await Engine.CommandBus.SendCommandAsync<Domain.Models.Filial.Filial>(lFilial);

                return new RespPadrao
                {
                    id = result.Id,
                    sucesso = true,
                    mensagem = ""
                };
            }
            catch (Exception e)
            {
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = e.Message
                };
            }
        }

        public FilialResponse ConsultarPorId(int idFilial)
        {
            try
            {
                if (idFilial < 0)
                {
                    throw new Exception("Id inválido!");
                }

                var lDados = Repository.Query.GetByIdWithIncludAll(idFilial)?.Result;

                var lFilial = Mapper.Map<FilialResponse>(lDados);

                lFilial.FilialCentroCusto = Mapper.Map<List<FilialCentroCustoResp>>(lDados?.CentroCustos);

                return lFilial;
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                throw;
            }
        }

        public async Task AlterarStatus(FilialStatusRequest lFilialStatus)
        {
            try
            {
                new LogHelper().LogOperationStart("AlterarStatus");
                await Engine.CommandBus.SendCommandAsync(Mapper.Map<FilialAlterarStatusCommand>(lFilialStatus));
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar AlterarStatus");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("AlterarStatus");
            }
        }

        public async Task<FilialConsultarApiResponse> Consultar(FilialConsultarApiRequest request)
        {
            try
            {
                new LogHelper().LogOperationStart("Consultar");
                var consulta = Repository.Query.GetAll().Where(f => f.EmpresaId == User.EmpresaId);

                if (!string.IsNullOrEmpty(request.Cnpj))
                    consulta = consulta.Where(f => f.Cnpj == request.Cnpj);

                var lista = await consulta.ProjectTo<FilialApiResponse>().ToListAsync();
                var count = lista.Count;

                return new FilialConsultarApiResponse()
                {
                    TotalItems = count,
                    Page = request.Page,
                    Items = lista.Skip((request.Page - 1) * request.Take).Take(request.Take).ToList()
                };
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar Consultar");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("Consultar");
            }
        }

        public async Task<RespPadraoApi> Integrar(FilialIntegrarApiRequest request)
        {
            try
            {
                new LogHelper().LogOperationStart("Integrar");
                var cidade = _cidadeAppService.ConsultarPorIbge(request.IbgeCidade).Result;

                var filialRequest = Mapper.Map<FilialRequest>(request);

                if (cidade != null)
                {
                    filialRequest.CidadeId = cidade.Id;
                    filialRequest.EstadoId = cidade.EstadoId;
                }

                var response = await Save(filialRequest);
                return new RespPadraoApi(response);
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar Integrar");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("Integrar");
            }
        }

        public async Task<RespPadrao> ExcluirCentroCusto(List<Domain.Models.CentroCusto.CentroCusto> lCentroCusto)
        {
            try
            {
                foreach (var item in lCentroCusto)
                {
                    var excluirCentroCusto = Mapper.Map<CentroCustoExcluirVinculoCommand>(item);

                    _centroCustoReadRepository.Detach(lCentroCusto);

                    await Engine.CommandBus.SendCommandAsync(excluirCentroCusto);
                }

                return new RespPadrao()
                {
                    sucesso = true,
                    mensagem = ""
                };
            }
            catch (Exception e)
            {
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = e.Message
                };
            }
        }
    }
}
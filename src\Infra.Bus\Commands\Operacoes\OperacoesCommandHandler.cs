﻿using System.Threading.Tasks;
using SistemaInfo.BBC.Domain.Contracts.Operacoes;
using SistemaInfo.BBC.Infra.Bus.Interface.Ciot;
using SistemaInfo.BBC.Domain.Models.OperacaoTransporteCiot.Commands;
using SistemaInfo.Framework.CQRS;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using SistemaInfo.Framework.DomainDrivenDesign.Infra.CQRS;

namespace SistemaInfo.BBC.Infra.Bus.Commands.Operacoes;

public class OperacoesCommandHandler : Command<PERSON><PERSON><PERSON>,
    IHandler<DeclararOperacaoTransporteReqMessage, DeclararOperacaoTransporteRespMessage>,
    IHandler<RetificarOperacaoTransporteReqMessage, RetificarOperacaoTransporteRespMessage>,
    IHandler<EncerrarOperacaoTransporteReqMessage, EncerrarOperacaoTransporteRespMessage>,
    I<PERSON>andler<EncerrarOperacaoTransporteBbcReqMessage, EncerrarOperacaoTransporteRespMessage>,
    I<PERSON>andler<CancelarOperacaoTransporteReqMessage, CancelarOperacaoTransporteRespMessage>,
    IHandler<ConsultarSituacaoCiotReqMessage, ConsultarSituacaoCiotRespMessage>,
    IHandler<ConsultarOperacaoTacAgregadoReqMessage, ConsultarOperacaoTacAgregadoRespMessage>,
    IHandler<ConsultarTacAgregadoRequestMessage, ConsultarTacAgregadoResponseMessage>,
    IHandler<ConsultarSituacaoTransportadorReqMessage, ConsultarSituacaoTransportadorRespMessage>,
    IHandler<ConsultarFrotaTransportadorReqMessage, ConsultarFrotaTransportadorRespMessage>,
    IHandler<OperacaoTransporteConsultarMsCommand, ConsultaGridOperacaoTransporteMessage>,
    IHandler<OperacaoTransporteConsultarHistoricoMsCommand, ConsultaGridOperacaoTransporteHistoricoMessage>,
    IHandler<ConsultarOperacaoTransportePorIdReqMessage, ConsultarOperacaoTransportePorIdRespMessage>,
    IHandler<ConsultarVeiculoCiotReqMessage, ConsultarVeiculosCiotRespMessage>
{
    private readonly IOperacoesPublisher _operacoesPublisher;
    public OperacoesCommandHandler(IAppEngine engine, IOperacoesPublisher operacoesPublisher) : base(engine)
    {
        _operacoesPublisher = operacoesPublisher;
    }

    public async Task<DeclararOperacaoTransporteRespMessage> HandlerAsync(DeclararOperacaoTransporteReqMessage command)
    {
        return await _operacoesPublisher.DeclararOperacaoTransporte(command);
    }

    public async Task<RetificarOperacaoTransporteRespMessage> HandlerAsync(RetificarOperacaoTransporteReqMessage command)
    {
        return await _operacoesPublisher.RetificarOperacaoTransporte(command);
    }

    public async Task<EncerrarOperacaoTransporteRespMessage> HandlerAsync(EncerrarOperacaoTransporteReqMessage command)
    {
        return await _operacoesPublisher.EncerrarOperacaoTransporte(command);
    }
    
    public async Task<EncerrarOperacaoTransporteRespMessage> HandlerAsync(EncerrarOperacaoTransporteBbcReqMessage command)
    {
        return await _operacoesPublisher.EncerrarOperacaoTransporteBbc(command);
    }

    public async Task<ConsultarSituacaoCiotRespMessage> HandlerAsync(ConsultarSituacaoCiotReqMessage command)
    {
        return await _operacoesPublisher.ConsultarSituacaoCiot(command);
    }

    public async Task<ConsultarOperacaoTacAgregadoRespMessage> HandlerAsync(ConsultarOperacaoTacAgregadoReqMessage command)
    {
        return await _operacoesPublisher.ConsultarOperacaoTacAgregado(command);
    }

    public async Task<ConsultarFrotaTransportadorRespMessage> HandlerAsync(ConsultarFrotaTransportadorReqMessage command)
    {
        return await _operacoesPublisher.ConsultarFrotaTransportador(command);
    }

    public async Task<CancelarOperacaoTransporteRespMessage> HandlerAsync(CancelarOperacaoTransporteReqMessage command)
    {
        return await _operacoesPublisher.CancelarOperacaoTransporte(command);
    }

    public async Task<ConsultarSituacaoTransportadorRespMessage> HandlerAsync(ConsultarSituacaoTransportadorReqMessage command)
    {
        return await _operacoesPublisher.ConsultarSituacaoTransportador(command);
    }

    public async Task<ConsultaGridOperacaoTransporteMessage> HandlerAsync(OperacaoTransporteConsultarMsCommand command)
    {
        return await _operacoesPublisher.ConsultarOperacoesTransporte(Mapper.Map<ConsultaGridOperacaoTransporteMessageRequest>(command));
    }
    
    public async Task<ConsultaGridOperacaoTransporteHistoricoMessage> HandlerAsync(OperacaoTransporteConsultarHistoricoMsCommand command)
    {
        return await _operacoesPublisher.ConsultarOperacoesTransporteHistorico(Mapper.Map<ConsultaGridOperacaoTransporteHistoricoMessageRequest>(command));
    }

    public async Task<ConsultarOperacaoTransportePorIdRespMessage> HandlerAsync(ConsultarOperacaoTransportePorIdReqMessage command)
    {
        return await _operacoesPublisher.ConsultarOperacaoTransportePorId(command);
    }
    public async Task<ConsultarVeiculosCiotRespMessage> HandlerAsync(ConsultarVeiculoCiotReqMessage command)
    {
        return await _operacoesPublisher.ConsultarVeiculoCiot(command);
    }

    public async Task<ConsultarTacAgregadoResponseMessage> HandlerAsync(ConsultarTacAgregadoRequestMessage command)
    {
        return await _operacoesPublisher.VerificarOperacaoTacAgregado(command);
    }
}
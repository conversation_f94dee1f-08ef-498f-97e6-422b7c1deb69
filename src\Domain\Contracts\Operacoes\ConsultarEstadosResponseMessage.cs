using System.Collections.Generic;
using SistemaInfo.BBC.Domain.External.CIOT.DTO;

namespace SistemaInfo.BBC.Domain.Contracts.Operacoes
{
    
    public class ConsultarEstadosReqMessage
    {
    }
    
    public class ConsultarEstadosRespMessage
    {
        public ConsultarEstadosRespMessage(bool sucesso, string excecao)
        {
            Sucesso = sucesso;
            Erro = new Excecao()
            {
                Mensagem = excecao
            };
        }
        public ConsultarEstadosRespMessage() { }
        public bool Sucesso { get; set; }
        public Excecao Erro { get; set; }
        public List<Estado> Estados { get; set; }
    }
}
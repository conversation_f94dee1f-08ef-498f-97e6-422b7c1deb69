using System.Collections.Generic;
using System.Threading.Tasks;
using SistemaInfo.Framework.DomainDrivenDesign.Infra.Repository;

namespace SistemaInfo.BBC.Domain.Models.Parametros.Repository
{
    public interface IParametrosBaseReadRepository<TParametrosEntity> : IReadOnlyRepository<TParametrosEntity>
        where TParametrosEntity : Parametros 
    {
    }

    public interface IParametrosReadRepository : IReadOnlyRepository<Parametros>
    {
        Task<int> GetPeriodoMaximoInatividadeSenhaProvisoria();
        Task<int> GetPeriodoMaximoInatividadePortador();
        Task<int> GetTempoInatividadeUsuarioAsync();
        Task<decimal> GetMargemErroXml();
        Task<decimal> GetMargemTotalAbastecimentoXml();
        Task<int> GetTotalTentativaErroSenhaPortadorFrota();
        Task<decimal> GetMargemLitragemXml();
        /// <summary>
        /// </summary>
        /// <param name="id"></param>
        /// <param name="tipoDoParametro"></param>
        /// <param name="tipoDoValor"></param>
        /// <returns></returns>
        Task<Parametros> GetParametrosAsync(int id, Parametros.TipoDoParametro tipoDoParametro, Parametros.TipoDoValor tipoDoValor);
        Task<List<Parametros>> GetParametrosListAsync(int id, Parametros.TipoDoParametro tipoDoParametro, Parametros.TipoDoValor tipoDoValor);
        Task<Parametros> GetByTipoDoParametroAsync(Parametros.TipoDoParametro tipoDoParametro);
        Task<Parametros> GetParametroLimitePagamentoPedagio();
        Task<Parametros> GetPrazoMaximoCancelamentoPagamento();
        Task<Parametros> GetParametroVoidAsync(int id, Parametros.TipoDoParametro tipoDoParametro, Parametros.TipoDoValor tipoDoValor);

        
    } 
}
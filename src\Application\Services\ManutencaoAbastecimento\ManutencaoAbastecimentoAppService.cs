using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using AutoMapper.QueryableExtensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Extensions.Internal;
using Remotion.Linq.Clauses;
using SistemaInfo.BBC.Application.Interface.Abastecimento;
using SistemaInfo.BBC.Application.Interface.AutorizacaoAbastecimento;
using SistemaInfo.BBC.Application.Interface.ManutencaoAbastecimento;
using SistemaInfo.BBC.Application.Interface.Cliente;
using SistemaInfo.BBC.Application.Interface.Fabricante;
using SistemaInfo.BBC.Application.Interface.PagamentoAbastecimento;
using SistemaInfo.BBC.Application.Interface.Veiculo;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.Abastecimento;
using SistemaInfo.BBC.Application.Objects.Web.ManutencaoAbastecimento;
using SistemaInfo.BBC.Application.Objects.Web.Fabricante;
using SistemaInfo.BBC.Application.Objects.Web.PostoCombustivel;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.BBC.Domain.Models.Abastecimento.Repository;
using SistemaInfo.BBC.Domain.Models.Abastecimento.Commands;
using SistemaInfo.BBC.Domain.Models.Abastecimento.Repository;
using SistemaInfo.BBC.Domain.Models.AutorizacaoAbastecimento.Commands;
using SistemaInfo.BBC.Domain.Models.PostoCombustivel.Commands;
using SistemaInfo.BBC.Domain.Models.Veiculo.Commands;
using SistemaInfo.BBC.Domain.Models.Veiculo.Repository;
using SistemaInfo.BBC.Domain.Models.VeiculoEmpresa;
using SistemaInfo.BBC.Domain.Models.VeiculoEmpresa.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;
using SistemaInfo.BBC.Infra.CrossCutting.Logging;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using SistemaInfo.Framework.Utils;
using SuperSocket.Common;

namespace SistemaInfo.BBC.Application.Services.ManutencaoAbastecimento
{
    public class ManutencaoAbastecimentoAppService : AppService<Domain.Models.Abastecimento.Abastecimento,
        IAbastecimentoReadRepository, IAbastecimentoWriteRepository>, IManutencaoAbastecimentoAppService
    {
        
        private readonly IVeiculoAppService _veiculoAppService;
        
        public ManutencaoAbastecimentoAppService(
            IAppEngine engine,
            IAbastecimentoReadRepository readRepository,
            IAbastecimentoWriteRepository writeRepository,
            IVeiculoAppService veiculoAppService) : base(
            engine, readRepository, writeRepository)
        {
            _veiculoAppService = veiculoAppService;
        }


            return new ConsultarGridHistoricoManutencaoAbastecimentoResponse
            {
                items = retorno,
                totalItems = lCount
            };
        }

        public List<ManutencaoAbastecimentoResponse> ConsultarAbastecimentos(int idEmpresa, DateTime startDate, DateTime endDate)
        {
            try
            {
                LogHelper.LogOperationStart("ConsultarAbastecimentos");
            var idPosto = Engine.User.AdministradoraId;

            List<Domain.Models.Veiculo.Veiculo> veiculoEmpresa;
            
            if (idEmpresa != 0)
            {
                veiculoEmpresa = _veiculoAppService.Repository.Query.GetAll().Where(e => e.EmpresaId == idEmpresa)
                    .ToList();
            }
            else
            {
                veiculoEmpresa = _veiculoAppService.Repository.Query.GetAll()
                    .ToList();
            }

            var abastecimentos =  Repository.Query
                .Include(c => c.Combustivel)
                .Include(v=>v.Veiculo)
                .Include(u=>u.UsuarioAlteracao)
                .Include(u=>u.UsuarioCadastro)
                .Where(a => a.DataCadastro >= startDate.StartOfDay() && a.DataCadastro <= endDate && 
                            a.PostoId == idPosto && 
                            (a.Status == EStatusAbastecimento.AguardandoAprovacao || 
                             a.Status == EStatusAbastecimento.Aprovado))
                .ToList();

            var lAbastecimento = new List<ManutencaoAbastecimentoResponse>();
            foreach (var veiculo in veiculoEmpresa)
            {
                foreach (var abasecimento in abastecimentos)
                {
                    if (veiculo.Id == abasecimento.VeiculoId)
                    {
                        lAbastecimento.Add(Mapper.Map<ManutencaoAbastecimentoResponse>(abasecimento));
                    }
                }
            }

            return lAbastecimento;
            }
            catch (Exception ex)
            {
                LogHelper.Error(ex, "Erro ao executar ConsultarAbastecimentos");
                throw;
            }
            finally
            {
                LogHelper.LogOperationEnd("ConsultarAbastecimentos");
            }        }
        
        public async Task<RespPadrao> SaveCancelamento(ManutencaoAbastecimentoCancelamentoRequest abastecimento, bool integracao)
        {
            try
            {
                
                var abastecimentoConsulta = Repository.Query.Where(x => x.Id == abastecimento.Id).FirstOrDefault();

                if (abastecimentoConsulta.DataCadastro.ToString("dd/MM/yyyy") == DateTime.Now.ToString("dd/MM/yyyy"))
                {
                    abastecimentoConsulta.Status = EStatusAbastecimento.Cancelado;
                }
                else
                {
                    return new RespPadrao
                    {
                        sucesso = false,
                        mensagem = "Prazo maximo para cancelamento atingido!"
                    };
                }
                

                var abstecimentoUpd = Mapper.Map<Domain.Models.Abastecimento.Abastecimento>(abastecimentoConsulta);

                abstecimentoUpd.Status = EStatusAbastecimento.Cancelado;
                
                Repository.Command.Update(abstecimentoUpd);
                await Repository.Command.SaveChangesAsync();
                
                if (abstecimentoUpd.Id <= 0)
                {
                    throw new Exception();
                }

                #region reversao de informacoes de veiculo e autorizacao viculadas ao abastecimento
                
                var lAutorizacaoAbastecimento = _autorizacaoAbastecimentoAppService.Repository.Query
                    .FirstOrDefault(x => x.VeiculoId == abstecimentoUpd.VeiculoId &&
                                         x.CombustivelId == abstecimentoUpd.CombustivelId &&
                                         x.DataCadastro.Month == DateTime.Now.Month);

                if (lAutorizacaoAbastecimento != null)
                {
                    var autorizacaoList =
                        Mapper.Map<AutorizacaoAbastecimentoSalvarCommand>(
                            lAutorizacaoAbastecimento);

                    _autorizacaoAbastecimentoAppService.Repository.Query.Detach(lAutorizacaoAbastecimento);

                    autorizacaoList.LitragemUtilizada = lAutorizacaoAbastecimento.LitragemUtilizada -
                                                        abstecimentoUpd.Litragem;
                    await _autorizacaoAbastecimentoAppService.Engine.CommandBus
                        .SendCommandAsync<Domain.Models.AutorizacaoAbastecimento.AutorizacaoAbastecimento>(
                            autorizacaoList);

                    if (abstecimentoUpd.Odometro != abstecimentoUpd.OdometroAnterior)
                    {
                        var veiculoRetornoOdometro =
                            _veiculoAppService.Repository.Query.FirstOrDefault(x => x.Id == abstecimentoUpd.VeiculoId);

                        var veiculoList =
                            Mapper.Map<VeiculoSalvarCommand>(veiculoRetornoOdometro);

                        _veiculoAppService.Repository.Query.Detach(veiculoRetornoOdometro);

                        veiculoList.Odometro = abstecimentoUpd.OdometroAnterior;

                        await _veiculoAppService.Engine.CommandBus
                            .SendCommandAsync<Domain.Models.Veiculo.Veiculo>(
                                veiculoList);
                    }
                }

                #endregion

                return new RespPadrao
                {
                    sucesso = true,
                    mensagem = "Abastecimento cancelado!"
                };
            }
            catch (Exception e)
            {
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = e.Message
                };
            }
        }
        
        public async Task<RespPadrao> ReimpresaoProtocolo(ManutencaoAbastecimentoReimpresaoRequest abastecimento, bool integracao)
        {
            try
            {
                return new RespPadrao
                {
                    sucesso = true,
                    mensagem = "Recurso desabilitado! aguardando implementações futuras."
                };
            }
            catch (Exception e)
            {
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = e.Message
                };
            }
        }
    }
}


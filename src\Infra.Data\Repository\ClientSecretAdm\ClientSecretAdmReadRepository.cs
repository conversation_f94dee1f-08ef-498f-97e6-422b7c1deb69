using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using SistemaInfo.BBC.Domain.Models.ClientSecretAdm.Repository;
using SistemaInfo.BBC.Infra.Data.Context;
using SistemaInfo.Framework.DomainDrivenDesign.Infra.Repository;

namespace SistemaInfo.BBC.Infra.Data.Repository.ClientSecretAdm
{
    public class ClientSecretAdmBaseReadRepository<TContext, TClientSecretAdmEntity> : ReadOnlyRepository<TClientSecretAdmEntity, TContext>
        where TContext : DbContext
        where TClientSecretAdmEntity : Domain.Models.ClientSecretAdm.ClientSecretAdm
    {
        public ClientSecretAdmBaseReadRepository(TContext context) : base(context)
        {
        }

        // public async Task<bool> AnyAtivoAsync(string login)
        // {
        //     return await DbLoggerCategory.Query.AnyAsync(x => x.Login == login && x.Ativo == 1);
        // }
        //
        // public async Task<TClientSecretAdmEntity> ObterPorLoginAsync(string login)
        // {
        //     return await DbLoggerCategory.Query.FirstOrDefaultAsync(x => x.Login == login && x.Ativo == 1);
        // }
    }

    public class ClientSecretAdmReadRepository : ClientSecretAdmBaseReadRepository<ConfigContext, Domain.Models.ClientSecretAdm.ClientSecretAdm>, IClientSecretAdmReadRepository
    {
        public ClientSecretAdmReadRepository(ConfigContext context) : base(context)
        {
        }

        public async Task<bool> AnyAtivoAsync(string login)
        {
            var auth = await Where(c => c.Login == login).FirstOrDefaultAsync();
            return auth != null && auth.LoginAtivo();
        }

        public async Task<Domain.Models.ClientSecretAdm.ClientSecretAdm> ObterPorLoginAsync(string login)
        {
            return await Where(c => c.Login == login && c.Ativo == 1).FirstOrDefaultAsync();
        }
    }
}

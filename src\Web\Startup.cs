﻿using AutoMapper;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc.Authorization;
using Microsoft.AspNetCore.Mvc.Cors.Internal;
using Microsoft.AspNetCore.Mvc.Formatters;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using SistemaInfo.BBC.Infra.CrossCutting.IoC;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using SistemaInfo.Framework.DomainDrivenDesign.Web.Filters;
using SistemaInfo.Framework.DomainDrivenDesign.Web.Secutiry.UserSession;
using SistemaInfo.Framework.DomainDrivenDesign.Web.Secutiry.UserSession.Swagger;
using SistemaInfo.Framework.DomainDrivenDesign.Web.Swagger;
using SistemaInfo.Framework.Web.Filters;
using SistemaInfo.Framework.Web.Utils.ApplicationBuilder;
using Swashbuckle.AspNetCore.Swagger;
using System;
using System.IO;
using System.Text;
using DinkToPdf;
using DinkToPdf.Contracts;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.FileProviders;
using Microsoft.IdentityModel.Tokens;
using Newtonsoft.Json.Converters;
using NLog;
using SistemaInfo.BBC.Infra.Bus;
using SistemaInfo.BBC.Infra.Data.Context;
using SistemaInfo.BBC.Web.Controllers;
using SistemaInfo.Framework.Utils.AppConfiguration;

namespace SistemaInfo.BBC.Web
{
    /// <summary>
    ///
    /// </summary>
    public class Startup
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="configuration"></param>
        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        /// <summary>
        ///
        /// </summary>
        public IConfiguration Configuration { get; }

        /// <summary>
        ///
        /// </summary>
        /// <param name="services"></param>
        public void ConfigureServices(IServiceCollection services)
        {
            services
                .AddMvc(opts =>
                {
                    opts.Filters.Add(new CorsAuthorizationFilterFactory("CorsPolicy"));
                    opts.Filters.Add<ApiActionExceptionFilter>();
                    opts.Filters.Add<ApiFaultResultCodeFilter>();
                    opts.Filters.Add<Web.Filters.ApiBeforeActionFilter>();
                    opts.Filters.Add(new AuthorizeFilter("AppToken"));
                    opts.OutputFormatters.Add(new XmlDataContractSerializerOutputFormatter());
                    opts.InputFormatters.Add(new XmlDataContractSerializerInputFormatter());
                })
                .AddJsonOptions(opts => opts.SerializerSettings.Converters.Add(new StringEnumConverter()));

            services.AddAuthorization(opts =>
                opts.AddPolicy("AppToken", policy =>
                    policy.Requirements.Add(new WebTokenAuthorizationRequirement())));


            services.AddAutoMapper();

            services.AddSwaggerGen(c =>
            {
                c.SwaggerDoc("v1", new Info {Title = "/BBC/Web", Version = "v1"});
                c.OperationFilter<FileUploadOperation>();
                c.OperationFilter<WebSessionTokenHeaderParameter>();
                //c.OperationFilter<AuditUserDocHeaderParameter>(); // AuditUserDocHeaderParameter está momentaneamente, o correto é gerar o web session token para idnetificação da sessão do usuário, e envia-lo no campo acima
                c.SchemaFilter<SwaggerExcludeFilter>();
                c.DescribeAllEnumsAsStrings();

                var basePath = AppContext.BaseDirectory;
                var lXmlPath = Path.Combine(basePath, "App_Data", System.Reflection.Assembly.GetExecutingAssembly().GetName().Name + ".xml");
                c.IncludeXmlComments(lXmlPath);
            });

            services.AddCors(opts => opts.AddPolicy("CorsPolicy", builder => builder.WithAppSettingsConfig()));

            services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
                .AddJwtBearer(options =>
                {
                    options.TokenValidationParameters = new TokenValidationParameters
                    {
                        ValidateIssuer = true,
                        ValidIssuer = "MyServer",

                        ValidateAudience = true,
                        ValidAudience = "EveryApplication",

                        ValidateIssuerSigningKey = true,
                        IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes("ThisIsDotNetCoreSecretKey")),

                        RequireExpirationTime = true,
                        ValidateLifetime = true,
                        ClockSkew = TimeSpan.Zero
                    };
                });
            //
            
            services.AddSingleton(typeof(IConverter), new SynchronizedConverter(new PdfTools()));
            RegisterServices(services,Configuration);
            using (var intermediateServiceProvider = services.BuildServiceProvider())
            {
                OnServicesConfigured(intermediateServiceProvider);
            }
        }
        
        private static void OnServicesConfigured(ServiceProvider serviceProvider) {

            using (var db = serviceProvider.GetRequiredService<ConfigContext>()) {
                var lLog = LogManager.GetCurrentClassLogger();
                try
                {
                    db.Database.OpenConnection();
                    db.Database.CloseConnection();
                    lLog.Info($"Conexão com banco validado com sucesso!");
                    Console.WriteLine($"Conexão com banco validado com sucesso!");
                }
                catch (Exception e)
                {
                    lLog.Error(e, "Erro ao verificar conexão com o banco: ");
                    Console.WriteLine($"Erro ao verificar conexão com o banco: {e}");
                }
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="app"></param>
        /// <param name="env"></param>
        /// <param name="httpAccessor"></param>
        /// <param name="appConfiguration"></param>
        /// <param name="serviceProvider"></param>
     public void Configure(IApplicationBuilder app, IHostingEnvironment env, IHttpContextAccessor httpAccessor, AppConfiguration appConfiguration, IServiceProvider serviceProvider)
         {
             appConfiguration.PrintInConsole();
             app.UseSistemaInfoFramework(() => httpAccessor?.HttpContext?.RequestServices ?? serviceProvider);
 
             app.UsePathBase("/BBC/Web");

             app.UseStatusCodePages();
             app.UseCustomDefaultFiles();
             app.UseCustomStaticFiles();
             
             app.UseCustomStatusCodePages();
             app.UseCustomDefaultFiles();
             app.UseCustomStaticFiles();
             
             app.UseMiddleware<AuthenticationExceptionHandlingMiddleware>();
             app.UseMvc(routes =>
             {
                 routes.MapRoute("DefaultApi", "BBC/Web/{controller}/{action}/{id}");
             });
 
             SetSwagger(app);
 
             app.UseAuthentication();
             app.UseCors("CorsPolicy");
         }

        private void SetSwagger(IApplicationBuilder app)
        {
            app.UseSwagger();

            var swaggerEndpoint = "/Web/swagger/v1/swagger.json";
            
            if (!string.IsNullOrWhiteSpace(Configuration["Swagger:SwaggerEndpoint"]))
                swaggerEndpoint = Configuration["Swagger:SwaggerEndpoint"];

            app.UseSwaggerUI(c => { c.SwaggerEndpoint(swaggerEndpoint, "/BBC/Web"); });
        }

        private static void RegisterServices(IServiceCollection serviceCollection, IConfiguration configuration)
        {
            DependencyInjector.RegisterServices(serviceCollection);
            DependencyInjectorCartaoWeb.RegisterServices(serviceCollection);
            MessageBusDependencyInjector.RegisterServices(serviceCollection, configuration);
        }
    }
}
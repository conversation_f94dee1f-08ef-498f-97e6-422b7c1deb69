﻿using Bogus;
using SistemaInfo.BBC.Application.Objects.Web.TipoEmpresa;
using Xunit;

namespace BBC.Test.Tests.TipoEmpresa.Fixture
{
    [CollectionDefinition(nameof(TipoEmpresaCollection))]
    public class TipoEmpresaCollection : ICollectionFixture<TipoEmpresaFixture>
    {
        
    }

    public class TipoEmpresaFixture : MockEngine
    {
        public SistemaInfo.BBC.Domain.Models.TipoEmpresa.TipoEmpresa GerarTipoEmpresa()
        {
            var lTipoEmpresa = new Faker<SistemaInfo.BBC.Domain.Models.TipoEmpresa.TipoEmpresa>("pt_BR")
                .CustomInstantiator(f => new SistemaInfo.BBC.Domain.Models.TipoEmpresa.TipoEmpresa
                {
                    Nome = f.Company.CompanyName(),
                    Ativo = f.Random.Number(0, 1),
                    UsuarioCadastroId = f.Random.Number(1, 100),
                    DataCadastro = f.Date.Past(),
                    DataBloqueio = f.Date.Past(),
                    DataDesbloqueio = f.Date.Past(),
                    UsuarioBloqueioId = f.Random.Number(1, 100),
                    UsuarioDesbloqueioId = f.Random.Number(1, 100)
                });

            return lTipoEmpresa;
        }
        
        public TipoEmpresaRequest GerarTipoEmpresaRequest()
        {
            var lTipoEmpresa = new Faker<TipoEmpresaRequest>("pt_BR")
                .CustomInstantiator(f => new TipoEmpresaRequest
                {
                    Nome = f.Company.CompanyName(),
                    Ativo = f.Random.Number(0, 1),
                    Id = f.Random.Number(1, 99999)
                });

            return lTipoEmpresa;
        }
    }
}
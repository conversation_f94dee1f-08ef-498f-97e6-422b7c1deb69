﻿using System;
using BBC.Test.Tests.TipoEmpresa.Fixture;
using Moq;
using SistemaInfo.BBC.Application.Services.TipoEmpresa;
using SistemaInfo.BBC.Domain.Models.TipoEmpresa.Commands;
using SistemaInfo.BBC.Domain.Models.TipoEmpresa.Repository;
using SistemaInfo.Framework.CQRS.Bus;
using Xunit;

namespace BBC.Test.Tests.TipoEmpresa
{
    [Collection(nameof(TipoEmpresaCollection))]
    public class TipoEmpresaAppServiceSaveTest
    {
        private readonly TipoEmpresaFixture _fixture;
        private readonly TipoEmpresaAppService _appService;
        private readonly Mock<ITipoEmpresaReadRepository> _readRepository;
        public TipoEmpresaAppServiceSaveTest(TipoEmpresaFixture fixture)
        {
            _fixture = fixture;
            _appService = fixture.Mocker.CreateInstance<TipoEmpresaAppService>();
            _readRepository = fixture.Mocker.GetMock<ITipoEmpresaReadRepository>();
        }
        
        [Fact(DisplayName = "Save com sucesso")]
        [Trait(nameof(TipoEmpresaAppService), nameof(TipoEmpresaAppService.Save))]
        public async void Save_RetornaSucesso()
        {
            var lMensagemEsperada = "Registro salvo com sucesso!";
            var lSucessoEsperado = true;
            var lDataEsperado = (object) null;
            
            var lRequest = _fixture.GerarTipoEmpresaRequest();
            var lTipoEmpresa = _fixture.GerarTipoEmpresa();

            _fixture.Mocker.GetMock<ICommandBus>()
                .Setup(c => c.SendCommandAsync<SistemaInfo.BBC.Domain.Models.TipoEmpresa.TipoEmpresa>(It.IsAny<TipoEmpresaSalvarComRetornoCommand>()))
                .ReturnsAsync(lTipoEmpresa);
            
            // Arrange
            var lResponse = await _appService.Save(lRequest, false);

            // Assert
            Assert.NotNull(lResponse);
            Assert.Equal(lTipoEmpresa.Id, lResponse.id);
            Assert.Equal(lMensagemEsperada, lResponse.mensagem);
            Assert.Equal(lDataEsperado, lResponse.data);
            Assert.Equal(lSucessoEsperado, lResponse.sucesso);
        }
        
        [Fact(DisplayName = "Save joga excecao")]
        [Trait(nameof(TipoEmpresaAppService), nameof(TipoEmpresaAppService.Save))]
        public async void Save_JogaExcecao_RetornaFalha()
        {
            var lMensagemEsperada = "EXCECAO";
            var lSucessoEsperado = false;
            var lDataEsperado = (object) null;
            
            var lRequest = _fixture.GerarTipoEmpresaRequest();

            _fixture.Mocker.GetMock<ICommandBus>()
                .Setup(c => c.SendCommandAsync<SistemaInfo.BBC.Domain.Models.TipoEmpresa.TipoEmpresa>(It.IsAny<TipoEmpresaSalvarComRetornoCommand>()))
                .ThrowsAsync(new Exception(lMensagemEsperada));
            
            // Arrange
            var lResponse = await _appService.Save(lRequest, false);

            // Assert
            Assert.NotNull(lResponse);
            Assert.Equal(lMensagemEsperada, lResponse.mensagem);
            Assert.Equal(lDataEsperado, lResponse.data);
            Assert.Equal(lSucessoEsperado, lResponse.sucesso);
        }
    }
}
using System;
using System.Collections.Generic;
using SistemaInfo.BBC.Domain.External.CIOT.DTO;

namespace SistemaInfo.BBC.Domain.Contracts.Operacoes
{
    
    public class ConsultarDadosRetificacaoReqMessage
    {
        public string Ciot { get; set; }
        public string SenhaAlteracao { get; set; }
    }
    
    public class ConsultarDadosRetificacaoRespMessage
    {
        public ConsultarDadosRetificacaoRespMessage(bool sucesso, string excecao)
        {
            Sucesso = sucesso;
            Erro = new Excecao()
            {
                Mensagem = excecao
            };
        }
        public ConsultarDadosRetificacaoRespMessage() { }
        public bool Sucesso { get; set; }
        public Excecao Erro { get; set; }
        public string CIOT { get; set; }
        public string SenhaAlteracao { get; set; }
        public List<VeiculoRetificar> Veiculos { get; set; }
        public int TipoViagem { get; set; }
        public int? QuantidadeTarifas { get; set; }
        public decimal? ValorTarifas { get; set; }
        public decimal? PesoCarga { get; set; }
    }
}
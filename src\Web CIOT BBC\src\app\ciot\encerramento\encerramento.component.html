<div class="container main-container">
    <div>
        <div class="container main-container">
            <form novalidate (ngSubmit)="encerrar()" [formGroup]="encerramentoForm">
                <div class="form-horizontal">
                    <div class="panel panel-default">
                        <div class="panel-body">
                            <fieldset class="scheduler-border">
                                <legend class="scheduler-border">Encerrar declaração de transporte</legend>
                                <div class="row">
                                    <div class="col-sm-12 col-md-6 col-lg-3">
                                        <div class="form-group " [ngClass]="{'has-error': displayMessage.ciot }">
                                            <label class="control-label" for="ciot">CIOT:</label>
                                            <input type="text" class="form-control" id="ciot" formControlName="ciot"
                                                disabled style="font-weight: bold;" />
                                            <span class="text-danger" *ngIf="displayMessage.ciot">
                                                <p [innerHTML]="displayMessage.ciot"></p>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-sm-12 col-md-6 col-lg-3">
                                        <div class="form-group " [ngClass]="{'has-error': displayMessage.contratado }">
                                            <label class="control-label" for="contratado">Contratado</label>
                                            <input type="text" class="form-control" id="contratado"
                                                formControlName="contratado" disabled />
                                            <span class="text-danger" *ngIf="displayMessage.contratado">
                                                <p [innerHTML]="displayMessage.contratado"></p>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="col-sm-12 col-md-6 col-lg-3">
                                        <div class="form-group " [ngClass]="{'has-error': displayMessage.rntrc }">
                                            <label class="control-label" for="rntrc">RNTRC</label>
                                            <input type="text" class="form-control" id="rntrc" formControlName="rntrc"
                                                disabled maxlength="9" RntrcMask />
                                            <span class="text-danger" *ngIf="displayMessage.rntrc">
                                                <p [innerHTML]="displayMessage.rntrc"></p>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="col-sm-12 col-md-6 col-lg-3">
                                        <div class="form-group " [ngClass]="{'has-error': displayMessage.tipoViagem }">
                                            <label class="control-label" for="tipoViagem">Tipo de viagem</label>
                                            <input type="text" class="form-control" id="tipoViagem"
                                                [(ngModel)]="tipoViagemDescricao" [ngModelOptions]="{standalone: true}"
                                                disabled />
                                            <span class="text-danger" *ngIf="displayMessage.tipoViagem">
                                                <p [innerHTML]="displayMessage.tipoViagem"></p>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-sm-12 col-md-6 col-lg-3">
                                        <div class="form-group "
                                            [ngClass]="{'has-error': displayMessage.dtInicioFrete }">
                                            <label class="control-label" for="dtInicioFrete">Início do frete</label>
                                            <input type="text" class="form-control" #dp="bsDatepicker" bsDatepicker
                                                [bsValue]="dataInicio" [bsConfig]="bsConfig" [(ngModel)]="dataInicio"
                                                [disabled]="true" [ngModelOptions]="{standalone: true}">
                                            <small id="dtInicioFreteHelp" class="form-text text-muted">Ex:
                                                01/01/2001</small>
                                            <span class="text-danger" *ngIf="displayMessage.dtInicioFrete">
                                                <p [innerHTML]="displayMessage.dtInicioFrete"></p>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="col-sm-12 col-md-6 col-lg-3">
                                        <div class="form-group "
                                            [ngClass]="{'has-error': displayMessage.dtTerminoFrete }">
                                            <label class="control-label" for="dtTerminoFrete">Final do frete</label>
                                            <input type="text" class="form-control" #dp="bsDatepicker" bsDatepicker
                                                [bsValue]="dataFim" [bsConfig]="bsConfig" [(ngModel)]="dataFim"
                                                [disabled]="true" [ngModelOptions]="{standalone: true}">
                                            <small id="dtTerminoFreteHelp" class="form-text text-muted">Ex:
                                                01/01/2001</small>
                                            <span class="text-danger" *ngIf="displayMessage.dtTerminoFrete">
                                                <p [innerHTML]="displayMessage.dtTerminoFrete"></p>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="col-sm-12 col-md-6 col-lg-3">
                                        <div class="form-group " [ngClass]="{'has-error': displayMessage.valorFrete }">
                                            <label class="control-label" for="valorFrete">Valor do frete</label>
                                            <input currencyMask
                                                [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
                                                maxlength="20" type="text" class="form-control" id="valorFrete"
                                                formControlName="valorFrete" placeholder="R$0.00" disabled />
                                            <span class="text-danger" *ngIf="displayMessage.valorFrete">
                                                <p [innerHTML]="displayMessage.valorFrete"></p>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-sm-12 col-md-6 col-lg-3">
                                        <div class="form-group "
                                            [ngClass]="{'has-error': displayMessage.totalImposto }">
                                            <label class="control-label" for="totalImposto">Total impostos</label>
                                            <input currencyMask
                                                [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
                                                maxlength="20" type="text" [disabled]="1 == tipoViagem"
                                                class="form-control" id="totalImposto" formControlName="totalImposto"
                                                placeholder="R$0.00" />
                                            <span class="text-danger" *ngIf="displayMessage.totalImposto">
                                                <p [innerHTML]="displayMessage.totalImposto"></p>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="col-sm-12 col-md-6 col-lg-3">
                                        <div class="form-group "
                                            [ngClass]="{'has-error': displayMessage.totalPedagio }">
                                            <label class="control-label" for="totalPedagio">Total do pedágio</label>
                                            <input currencyMask
                                                [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
                                                maxlength="20" type="text" [disabled]="1 == tipoViagem"
                                                class="form-control" id="totalPedagio" formControlName="totalPedagio"
                                                placeholder="R$0.00" />
                                            <span class="text-danger" *ngIf="displayMessage.totalPedagio">
                                                <p [innerHTML]="displayMessage.totalPedagio"></p>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="col-sm-12 col-md-6 col-lg-3">
                                        <div class="form-group ">
                                            <label class="control-label" for="valorFretePago">Valor do frete
                                                pago</label>
                                            <input currencyMask
                                                [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
                                                maxlength="20" type="text" [disabled]="1 == tipoViagem"
                                                class="form-control" id="valorFretePago"
                                                formControlName="valorFretePago" placeholder="R$0.00" />
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-sm-12 col-md-6 col-lg-3">
                                        <div class="form-group ">
                                            <label class="control-label" for="valorDespesas">Valor de despesas</label>
                                            <input currencyMask
                                                [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
                                                maxlength="20" type="text" [disabled]="1 == tipoViagem"
                                                class="form-control" id="valorDespesas" formControlName="valorDespesas"
                                                placeholder="R$0.00" />
                                        </div>
                                    </div>
                                    <div class="col-sm-12 col-md-6 col-lg-3">
                                        <div class="form-group "
                                            [ngClass]="{'has-error': displayMessage.quantidadeTarifas }">
                                            <label class="control-label" for="quantidadeTarifas">Quantidade de
                                                tarifas</label>
                                            <input RntrcMask type="text" class="form-control" id="quantidadeTarifas"
                                                maxlength="5" formControlName="quantidadeTarifas" />
                                            <span class="text-danger" *ngIf="displayMessage.quantidadeTarifas">
                                                <p [innerHTML]="displayMessage.quantidadeTarifas"></p>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="col-sm-12 col-md-6 col-lg-3">
                                        <div class="form-group "
                                            [ngClass]="{'has-error': displayMessage.valorTotalTarifas }">
                                            <label class="control-label" for="valorTotalTarifas">Valor total das
                                                tarifas</label>
                                            <input currencyMask
                                                [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
                                                maxlength="20" type="text" class="form-control" id="valorTotalTarifas"
                                                formControlName="valorTotalTarifas" placeholder="R$0.00" />
                                            <span class="text-danger" *ngIf="displayMessage.valorTotalTarifas">
                                                <p [innerHTML]="displayMessage.valorTotalTarifas"></p>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="row" *ngIf="tipoViagem == 1">
                                    <div class="col-sm-12 col-md-6 col-lg-3">
                                        <div class="form-group ">
                                            <label class="control-label" for="pesoCarga">Peso total</label>
                                            <input OnlyNumber type="text" class="form-control" id="pesoCarga"
                                                formControlName="pesoCarga" />
                                        </div>
                                    </div>
                                </div>
                            </fieldset>
                        </div>
                    </div>
                    <br />
                    <div class="panel panel-default">
                        <div *ngIf="tipoViagem == 3" class="panel-body">
                            <fieldset class="scheduler-border">
                                <legend class="scheduler-border">Adicionar viagens</legend>
                                <form novalidate (ngSubmit)="adicionarViagem()" [formGroup]="viagemForm">
                                    <div class="row">
                                        <div class="col-sm-12 col-md-6 col-lg-3">
                                            <div class="form-group ">
                                                <label class="control-label">Cidade origem</label>
                                                <div class="alert alert-danger" *ngIf="noResultOrigem">Cidade não
                                                    encontrada</div>
                                                <input [(ngModel)]="cidadeOrigemCompleterText"
                                                    [typeahead]="cidadeList"
                                                    (typeaheadNoResults)="typeaheadNoResults($event)"
                                                    (typeaheadOnSelect)="onSelectCidade($event)"
                                                    (focus)="onFocusCidadeOrigem()"
                                                    typeaheadOptionField="nomeSiglaDescricao" class="form-control"
                                                    [disabled]="!podeDigitar" [ngModelOptions]="{standalone: true}">
                                            </div>
                                        </div>
                                        <div class="col-sm-12 col-md-6 col-lg-3">
                                            <div class="form-group ">
                                                <label class="control-label">Cidade destino</label>
                                                <div class="alert alert-danger" *ngIf="noResultDestino">Cidade não
                                                    encontrada</div>
                                                <input [(ngModel)]="cidadeDestinoCompleterText"
                                                    [typeahead]="cidadeList"
                                                    (typeaheadNoResults)="typeaheadNoResults($event)"
                                                    (typeaheadOnSelect)="onSelectCidade($event)"
                                                    (focus)="onFocusCidadeDestino()"
                                                    typeaheadOptionField="nomeSiglaDescricao" class="form-control"
                                                    [disabled]="!podeDigitar" [ngModelOptions]="{standalone: true}">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group " [ngClass]="{'has-error': displayMessage.naturezaCarga}">
                                        <label class="control-label" for="naturezaCarga">Natureza da carga</label>
                                        <div class="alert alert-danger" *ngIf="noResultNatureza">Natureza não encontrada
                                        </div>
                                        <input [(ngModel)]="naturezaCompleterText"
                                            [typeahead]="naturezaCargaList"
                                            (typeaheadNoResults)="typeaheadNoResultsNatureza($event)"
                                            (typeaheadOnSelect)="onSelectNatureza($event)"
                                            typeaheadOptionField="descricao" class="form-control"
                                            [ngModelOptions]="{standalone: true}">
                                    </div>
                                    <div class="row">
                                        <div class="col-sm-12 col-md-6 col-lg-3">
                                            <div class="form-group "
                                                [ngClass]="{'has-error': displayMessage.pesoCarga}">
                                                <label class="control-label" for="pesoCarga">Peso da carga
                                                    (KG)</label>
                                                <div class="input-group">
                                                    <input OnlyNumber class="form-control" maxlength="5"
                                                        id="idpesoCarga" type="text" formControlName="pesoCarga" />
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-sm-12 col-md-6 col-lg-3">
                                            <div class="form-group "
                                                [ngClass]="{'has-error': displayMessage.qtdViagens}">
                                                <label class="control-label" for="qtdViagens">Quantidade de
                                                    viagens</label>
                                                <div class="input-group">
                                                    <input RntrcMask class="form-control" maxlength="5" id="qtdViagens"
                                                        type="text" formControlName="qtdViagens" />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-sm-12 col-md-6 col-lg-3">
                                            <button class="btn btn-danger" [disabled]="!viagemForm.valid"
                                                id="adicionarViagem" type="submit">Adicionar</button>
                                        </div>
                                    </div>
                                </form>
                                <br />
                                <div class="control-group">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th scope="col">Ação</th>
                                                <th style="min-width: 135px;" scope="col">Cidade origem</th>
                                                <th style="min-width: 140px;" scope="col">Cidade destino</th>
                                                <th scope="col">Natureza da carga</th>
                                                <th style="min-width: 130px;" scope="col">Peso da carga</th>
                                                <th style="min-width: 160px;" scope="col">Quant. de viagens</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr *ngFor='let viagem of viagemList'>
                                                <td>
                                                    <button type="submit" class="btn btn-danger btn-sm"
                                                        (click)='removerViagem(viagem)'>
                                                        <span aria-hidden="true">Excluir</span>
                                                    </button>
                                                </td>
                                                <td>{{viagem.cidadeOrigem.nome}} - {{viagem.cidadeOrigem.uf}} </td>
                                                <td>{{viagem.cidadeDestino.nome}} - {{viagem.cidadeDestino.uf}}</td>
                                                <td>{{viagem.naturezaCarga.descricao}}</td>
                                                <td>{{viagem.pesoCarga}}</td>
                                                <td>{{viagem.qtdViagens}}</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </fieldset>
                        </div>
                    </div>
                    <br>
                    <button class="btn btn-danger" id="voltar" (click)="voltar()">Voltar</button>
                    <button class="btn btn-success" id="encerrar" type="submit">Encerrar</button>
                </div>
            </form>
        </div>
    </div>
</div>

using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using SistemaInfo.BBC.Application.External.Conductor.Interface;
using SistemaInfo.BBC.Application.Interface.UsuarioFrota;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.UsuarioFrota;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Web.Attributes;
using SistemaInfo.BBC.Web.Controllers.Base;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;

namespace SistemaInfo.BBC.Web.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [Route("UsuarioFrota")]
    public class UsuarioFrotaController : WebControllerBase<IUsuarioFrotaAppService>
    {
      

        /// <summary>
        /// 
        /// </summary>
        /// <param name="engine"></param>
        /// <param name="appService"></param>
        /// <param name="cartaoAppService"></param>
        public UsuarioFrotaController(IAppEngine engine, IUsuarioFrotaAppService appService)
            : base(engine, appService)
        {
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("ConsultarGrid")]
        [Menu(new[] { EMenus.Veiculo, EMenus.UsuarioFrota})]
        public JsonResult ConsultarGrid([FromBody] BaseGridRequest request)
        {
            try
            {
                var consultarGridPortador = AppService.ConsultarGrid(request.Take, request.Page,
                    request.Order, request.Filters);

                return ResponseBase.ResponderSucesso(consultarGridPortador);
            }
            catch (Exception e)
            {
                return ResponseBase.ResponderErro(e);
            }
        }
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="lModel"></param>
        /// <returns></returns>
        [HttpPost("Salvar")]
        [Menu(new[] { EMenus.UsuarioFrota })]
        public JsonResult Salvar([FromBody] UsuarioFrotaRequest lModel)
        {
            try
            {
                var lSavePortador = AppService.Save(lModel).Result;
                
                return ResponseBase.BigJson(lSavePortador);
            }
            catch (Exception e)
            {
                return ResponseBase.ResponderErro("Não foi possível realizar a operação. Mensagem: " + e.Message);
            }
        }
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="lPortadorStatus"></param>
        /// <returns></returns>
        [HttpPost("AlterarStatus")]
        [Menu(new[] { EMenus.UsuarioFrota })]
        public async Task<JsonResult> AlterarStatus([FromBody] UsuarioFrotaStatusRequest lPortadorStatus)
        {
            var lAlterarStatus = await AppService.AlterarStatus(lPortadorStatus);
            return lAlterarStatus.sucesso
                ? ResponseBase.ResponderSucesso(lAlterarStatus)
                : ResponseBase.ResponderErro(lAlterarStatus.mensagem);
        }
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="lPortadorStatus"></param>
        /// <returns></returns>
        [HttpPost("Bloquear")]
        [Menu(new[] { EMenus.UsuarioFrota })]
        public async Task<JsonResult> Bloquear([FromBody] UsuarioFrotaStatusRequest lPortadorStatus)
        {
            var lAlterarStatus = await AppService.Bloquear(lPortadorStatus);
            return lAlterarStatus.sucesso
                ? ResponseBase.ResponderSucesso(lAlterarStatus)
                : ResponseBase.ResponderErro(lAlterarStatus.mensagem);
        }

        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="lPortadorStatus"></param>
        /// <returns></returns>
        [HttpPost("Cancelar")]
        [Menu(new[] { EMenus.UsuarioFrota })]
        public async Task<JsonResult> Cancelar([FromBody] UsuarioFrotaCancelarRequest lPortadorStatus)
        {
            var lAlterarStatus = await AppService.Cancelar(lPortadorStatus);
            return lAlterarStatus.sucesso
                ? ResponseBase.ResponderSucesso(lAlterarStatus)
                : ResponseBase.ResponderErro(lAlterarStatus.mensagem);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="idPortador"></param>
        /// <returns></returns>
        [HttpGet("BuscarPorId")]
        [Menu(new[] { EMenus.UsuarioFrota })]
        public async Task<JsonResult> BuscarPorId(int idPortador)
        {
            try
            {
                return ResponseBase.ResponderSucesso(await AppService.ConsultarPorId(idPortador));
            }
            catch (Exception e)
            {
                return ResponseBase.ResponderErro("Não foi possível buscar o usuário frota." + e.Message);
            }
        }
        
    }
}
﻿using System.Collections.Generic;
using System.Threading.Tasks;
using BBC.Test.Tests.Usuario.Fixture;
using Moq;
using SistemaInfo.BBC.Application.Services.Usuario;
using SistemaInfo.BBC.Domain.Models.Parametros.Repository;
using SistemaInfo.BBC.Domain.Models.Usuario.Repository;
using SistemaInfo.BBC.Infra.Data.Repository.Usuario;
using SistemaInfo.Framework.Utils;
using Xunit;

namespace BBC.Test.Tests.Usuario
{
    [Collection(nameof(UsuarioCollection))]
    public class UsuarioAppServiceSaveTest
    {
        private readonly UsuarioFixture _fixture;
        private readonly UsuarioAppService _appService;
        private readonly Mock<IUsuarioReadRepository> _readRepository;
        private readonly Mock<IParametrosReadRepository> _readParametroRepository;

        public UsuarioAppServiceSaveTest(UsuarioFixture fixture)
        {
            _fixture = fixture;
            _appService = fixture.Mocker.CreateInstance<UsuarioAppService>();
            _readRepository = fixture.Mocker.GetMock<IUsuarioReadRepository>();
            _readParametroRepository = fixture.Mocker.GetMock<IParametrosReadRepository>();
        }

        [Fact(DisplayName = "Save de usuário com request nula")]
        [Trait("UsuarioAppService", "Save")]
        public async void Save_RequestNula_RetornaFalha()
        {
            //Action
            var lResponse = await _appService.Save(null);

            //Assert
            Assert.False(lResponse.sucesso);
            Assert.Equal("Não foi possível salvar o usuário.", lResponse.mensagem);
            Assert.Null(lResponse.data);
        }

        [Fact(DisplayName = "Save de usuário válido")]
        [Trait("UsuarioAppService", "Save")]
        public async void Save_RequestValida_RetornaSucesso()
        {
            //Arrange
            var lUsuarioRequest =  _fixture.GerarUsuarioSaveValido();

            //Action
            var lResponse = await _appService.Save(lUsuarioRequest);

            //Assert
            Assert.True(lResponse.sucesso);
            Assert.Equal("Usuário salvo com sucesso.",lResponse.mensagem);
        }
        
        [Fact(DisplayName = "Save de usuario com cpf invalido")]
        [Trait("UsuarioReadRepository", "Save")]
        public async void Save_RequestValida_RetornaErro()
        {
            //Arrange
            var lUsuarioRequest =  _fixture.GerarUsuarioSaveCpfInvalido();

            //Action
            var lResponse = await _appService.Save(lUsuarioRequest);

            //Assert
            Assert.Equal("CPF com tamanho inválido", lResponse.mensagem);
        }
        
        [Fact(DisplayName = "Verifica se usuário é adm atraves do retorno de empresas")]
        [Trait("UsuarioReadRepository", "Save")]
        public async void Checked_GetEmpresasAcessoUsuario_Adm()
        {
            //Arrange
            var empresas =  _fixture.GerarUsuarioAdmRequest();
            
            _fixture.Mocker.GetMock<IUsuarioReadRepository>()
                .Setup(u => u.GetEmpresasAcessoUsuario(It.IsAny<SistemaInfo.BBC.Domain.Models.Usuario.Usuario>()))
                .Returns(Task.FromResult<List<int>>(null));
            
            //Action
            var lResponse =  _readRepository.Object.GetEmpresasAcessoUsuario(empresas);

            //Assert
            Assert.Equal(null, lResponse.Result);
        }
        
        [Fact(DisplayName = "Verifica se usuário tem apenas EmpresaId")]
        [Trait("UsuarioReadRepository", "Save")]
        public async Task Checked_GetEmpresasAcessoUsuario_EmpresaId()
        {
            // Arrange
            var usuario = _fixture.GerarUsuarioEmpresaIdRequest();
            var expectedList = new List<int> { 11 }; // Lista esperada
    
            _fixture.Mocker.GetMock<IUsuarioReadRepository>()
                .Setup(u => u.GetEmpresasAcessoUsuario(usuario))
                .ReturnsAsync(expectedList); // Retorna a lista esperada

            // Act
            var lResponse = await _readRepository.Object.GetEmpresasAcessoUsuario(usuario);

            // Assert
            Assert.NotNull(lResponse); // Garante que a resposta não seja nula
            Assert.Single(lResponse); // Garante que tem apenas 1 item
            Assert.Equal(expectedList, lResponse); // Compara listas corretamente
        }
        
        [Fact(DisplayName = "Verifica se usuário tem Empresas Usuario")]
        [Trait("UsuarioReadRepository", "Save")]
        public async void Checked_GetEmpresasAcessoUsuario_EmpresasUsuario()
        {
            //Arrange
            var usuario = _fixture.GerarUsuarioEmpresaUsuario();
            var expectedList = _fixture.GerarResponseEmpresasUsuarioAcesso();
    
            _fixture.Mocker.GetMock<IUsuarioReadRepository>()
                .Setup(u => u.GetEmpresasAcessoUsuario(usuario))
                .ReturnsAsync(expectedList); // Retorna a lista esperada

            // Act
            var lResponse = await _readRepository.Object.GetEmpresasAcessoUsuario(usuario);

            var list = new List<int> { 11, 607, 10, 612, 610 };
            
            // Assert
            Assert.NotNull(lResponse);
            Assert.True(lResponse.Count > 1);
            Assert.Equal(list, lResponse);
        }
    }
}
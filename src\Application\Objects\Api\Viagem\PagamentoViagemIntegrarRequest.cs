using System.ComponentModel.DataAnnotations;
using System.Linq;
using Newtonsoft.Json;
using SistemaInfo.BBC.Application.Utils;
using SistemaInfo.BBC.Domain.Enum;

namespace SistemaInfo.BBC.Application.Objects.Api.Viagem
{
    public class PagamentoViagemIntegrarRequest
    {
        public int? ViagemExternoId { get; set; } 
        public Tipo? Tipo { get; set; } 
        public FormaPagamentoEvento? FormaPagamento { get; set; } 
        public string CpfCnpjContratado { get; set; }
        public string NomeContratado { get; set; }
        public string CpfMotorista { get; set; }
        public string NomeMotorista { get; set; } 
        public string RecebedorAutorizado { get; set; }
        [Required(ErrorMessage = "O campo Valor é obrigatório.")]
        public decimal Valor { get; set; }
        /// <summary>
        /// Era usado para impedir pagamento caso Não fosse um banco BBC, mas ficou sem uso após implementação de Pix para outros bancos
        /// </summary>
        [JsonIgnore]
        public TipoBanco? TipoBanco { get; set; } 
        public int? PagamentoExternoId { get; set; } 
        public string Agencia { get; set; } 
        public string Conta { get; set; }
        public string CodigoBanco { get; set; }
        public ETipoContaDock? TipoConta { get; set; }
        
        private string _chavePix;
        public string ChavePix
        {
            get => _chavePix;
            set => _chavePix = value.RemoveCaracteresEpeciaisCpfCnpjChavePix();
        }
        public string FilialId { get; set; }
        public int? IbgeOrigem { get; set; }
        public int? IbgeDestino { get; set; }
        public string HashValidacao { get; set; }
        public string WebhookUrl { get; set; }
        
        public string Ciot { get; set; }
        public string VerificadorCiot { get; set; }
        public string CodigoNaturezaCarga { get; set; }
        public decimal? PesoCarga { get; set; }
    }
}
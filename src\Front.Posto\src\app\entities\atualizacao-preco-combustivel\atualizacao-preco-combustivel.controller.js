﻿(function () {
    'use strict';

    angular
        .module('jslposto')
        .controller('AtualizacaoPrecoCombustivelController', AtualizacaoPrecoCombustivelController);
    AtualizacaoPrecoCombustivelController.inject = ['$scope', 'BaseService', 'toastr', '$timeout', '$state', '$uibModalStack', '$log', '$window', '$rootScope', 'DefaultsService', '$uibModal'];

    function AtualizacaoPrecoCombustivelController(
        $scope,
        BaseService,
        toastr,
        $timeout,
        $state,
        $uibModalStack,
        $log,
        $window,
        $rootScope,
        DefaultsService,
        $uibModal) {

        var vm = this;
        vm.headerItems = [{
            name: 'Consulta solicitações'
        }, {
            name: 'Solicitações'
        }];

        vm.cancelando = false;
        vm.consultaCombustivel = DefaultsService.consultaCombustivelModal();

        vm.dateOptions = {
            timePicker: false,
            applyClass: 'btn-primary',
            locale: {
                applyLabel: "Aplicar",
                fromLabel: "De",
                format: "DD/MM/YYYY",
                toLabel: "Até",
                cancelLabel: 'Cancelar',
                customRangeLabel: 'Período',
                monthNames: ['Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho', 'Julho', 'Agosto', 'Setembro',
                    'Outubro', 'Novembro', 'Dezembro'],
                daysOfWeek: ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb']
            },
            ranges: {
                'Último dia': [moment().add(-1, 'days'), moment()],
                'Últimos 7 dias': [moment().add(-7, 'days'), moment()],
                'Último mês': [moment().subtract(1, 'months').startOf('month'), moment().subtract(1, 'months').endOf('month')]
            }
        };

        vm.date = {
            startDate: moment().add(-1, 'days'),
            endDate: moment()
        };

        vm.gridOptions = {
            data: [],
            onRegisterApi: BaseService.dataGrid.defaultOnRegisterApi,
            dataSource: {
                autoBind: true,
                url: "AtualizacaoPrecoCombustivel/HistoricoSolicitacoesPendentes",
                params: function () {
                    var start = vm.date.startDate.startOf('day').utcOffset(0, false)
                    var end = vm.date.endDate.endOf('day').utcOffset(0, true)
                    return {
                        idPosto: $rootScope.usuarioLogado.idPosto,
                        dtInicial: start,
                        dtFinal: end
                    }
                },
            },
            enablePaginationControls: true,
            rowTemplate: '<div  ng-click="grid.appScope.fnOne(row)" ng-repeat="col in colContainer.renderedColumns track by col.colDef.name" ' +
                'ng-class="{\'text-info\': row.entity.STATUS == \'Baixado\', \'text-danger\': row.entity.STATUS == \'Não Baixado\' }" class="ui-grid-cell" ui-grid-cell></div>',
            columnDefs: [
                {
                    name: 'Ações',
                    width: 80,
                    cellTemplate:
                        '<div ng-if="!row.groupHeader" class="ui-grid-cell-contents" title="TOOLTIP">\
                            <button ng-if="row.entity.statusAprovacao == 2" tooltip-placement="right" uib-tooltip="Editar" \
                                    type="button" ng-click="grid.appScope.vm.abrirModalNovaSolicitacao(row.entity)"\
                                    class="btn btn-xs btn-info"\
                                    ng-disabled="grid.appScope.vm.cancelando">\
                                <i class="fa fa-edit"></i>\
                            </button>\
                            <button type="button" tooltip-placement="right" uib-tooltip="Cancelar"\
                                    ng-click="grid.appScope.vm.cancelarSolicitacao(row.entity.id)" \
                                    ng-if="row.entity.statusAprovacao == 2"\
                                    ng-disabled="grid.appScope.vm.cancelando"\
                                    class="btn btn-xs btn-danger"> \
                                <i class="fa fa-times"></i>\
                            </button>\
                        </div>'
                },
                {
                    name: 'Combustivel',
                    displayName: 'Combustível',
                    field: 'nomeCombustivel',
                    width: '*',
                    minWidth: 190,
                    enableGrouping: false,
                    enableFiltering: false
                },
                {
                    name: 'ValorBomba',
                    field: 'valorBomba',
                    displayName: 'Valor Bomba',
                    enableGrouping: false,
                    cellTemplate: '<div class="ui-grid-cell-contents">\
                                    <input type="text" ng-model="row.entity.valorBomba" readonly\
                                            class="no-borders" style="background: none;" ui-money-mask="3" />\
                               </div>',
                    enableFiltering: false
                },
                {
                    name: 'ValorBombaSolicitado',
                    field: 'valorBombaSolicitado',
                    displayName: 'Valor bomba solicitado',
                    enableGrouping: false,
                    cellTemplate: '<div class="ui-grid-cell-contents">\
                                    <input type="text" ng-model="row.entity.valorBombaSolicitado" readonly\
                                            class="no-borders" style="background: none;" ui-money-mask="3" />\
                               </div>',
                    enableFiltering: false
                },
                {
                    name: 'ValorAtual',
                    field: 'valorBBC',
                    displayName: 'Valor BBC',
                    enableGrouping: false,
                    cellTemplate: '<div class="ui-grid-cell-contents">\
                                    <input type="text" ng-model="row.entity.valorBBC" readonly\
                                            class="no-borders" style="background: none;" ui-money-mask="3" />\
                               </div>',
                    enableFiltering: false
                },
                {
                    name: 'ValorBBCSolicitado',
                    field: 'valorBBCSolicitado',
                    displayName: 'Valor BBC solicitado',
                    enableGrouping: false,
                    cellTemplate: '<div class="ui-grid-cell-contents">\
                                    <input type="text" ng-model="row.entity.valorBBCSolicitado" readonly\
                                            class="no-borders" style="background: none;" ui-money-mask="3" />\
                               </div>',
                    enableFiltering: false
                },
                {
                    name: 'DataSolicitacao',
                    displayName: 'Data solicitação',
                    field: 'dataCadastro',
                    width: '*',
                    minWidth: 190,
                    enableGrouping: false,
                    enableFiltering: false

                },
                {
                    name: 'Status',
                    displayName: 'Status',
                    field: 'statusAprovacaoDescricao',
                    width: '*',
                    minWidth: 230,
                    enableGrouping: false,
                    enableFiltering: false
                },
                {
                    name: 'Motivo',
                    displayName: 'Motivo',
                    field: 'motivoExterno',
                    width: '*',
                    minWidth: 150,
                    enableGrouping: false,
                    enableFiltering: false
                }]
        };

        vm.abrirModalNovaSolicitacao = function (solicitacao) {
            $uibModal.open({
                animation: true,
                ariaLabelledBy: 'modal-title',
                ariaDescribedBy: 'modal-body',
                templateUrl: 'app/entities/atualizacao-preco-combustivel/modal-nova-solicitacao.html',
                controller: 'ModalNovaSolicitacaoController',
                controllerAs: 'vm',
                backdrop: 'static',
                size: 'lg',
                resolve: {
                    solicitacao: function () {
                        return solicitacao;
                    }
                }
            }).result.then(function (response) {
                vm.gridOptions.dataSource.refresh()
            });
        }

        vm.cancelarSolicitacao = function (solicitacaoId) {
            vm.cancelando = true;
            BaseService.post('AtualizacaoPrecoCombustivel', 'CancelarSolicitacao', { solicitacaoId: solicitacaoId })
                .then(function (response) {
                    vm.cancelando = false
                    if (!response.sucesso) {
                        toastr.error(response.mensagem)
                        return
                    }
                    toastr.success(response.mensagem)
                    vm.gridOptions.dataSource.refresh()
                });
        }
    }
})();
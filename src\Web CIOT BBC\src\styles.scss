/* You can add global styles to this file, and also import other style files */

//@import "~@progress/kendo-theme-default/scss/all";
@import "~ng2-toastr/bundles/ng2-toastr.min.css";
@import "~ngx-bootstrap/datepicker/bs-datepicker.css";
.content {
    padding-left: 50px;
    padding-right: 50px;
    padding-bottom: 50px;
}

.form-group.required .control-label::before {
    content: "* ";
    color: #00622c;
}

.disabled {
    cursor: not-allowed;
}

.btn-danger {
    background-color: #00622c !important;
    border-color: #00622c !important;
}

.container {
    padding-top: 15px;
    padding-bottom: 15px;
    width: auto;
}

.wrapper {
    background: #fcfcfc;
    box-shadow: 0 0 8px 3px rgba(0, 0, 0, 0.1), 0 0 4px 1px rgba(0, 0, 0, 0.15);
}

.nav {
    width: 97%;
    padding-top: 10px;
}

.panel {
    width: 95%;
}

.footer {
    height: 75px;
}

.ng-autocomplete-dropdown {
    position: relative;
    // margin-top: 25px;
    // height: 200px;
}

.ng-autocomplete-dropdown .ng-autocomplete-inputs {
    position: relative;
}

.ng-autocomplete-dropdown .ng-autocomplete-inputs input {
    width: 100%;
    padding: 6px 20px;
    font-family: Arial;
    font-weight: normal;
    outline: none !important;
    font-size: 15px;
    height: 56px;
    border: 1px solid #e0e0e0;
}

.ng-autocomplete-dropdown .ng-autocomplete-placeholder {
    position: absolute;
    margin: 3px;
    background-color: #fff;
    padding: 17px 18px;
    font-family: Arial;
    font-weight: normal;
    font-size: 15px;
    width: calc(100% - 4px);
}

.ng-autocomplete-dropdown .ng-dropdown {
    display: none;
    border: 1px solid #e0e0e0;
    z-index: 99999;
    max-height: 280px;
    overflow-x: hidden;
    position: absolute;
    width: 100%;
}

.ng-autocomplete-dropdown .ng-dropdown.open {
    display: block;
}

.ng-autocomplete-dropdown .ng-dropdown .dropdown-item {
    width: 100%;
    cursor: pointer;
    padding: 18px 20px;
    font-family: Arial;
    font-weight: normal;
    font-size: 15px;
    height: 56px;
    background-color: #ffffff;
}

.ng-autocomplete-dropdown .ng-dropdown .dropdown-item:nth-child(odd) {
    background-color: #efefef;
}

.ng-autocomplete-dropdown .ng-dropdown .dropdown-item.active {
    background-color: #0099cc;
    color: #fff !important;
}

.ng-autocomplete-dropdown .ng-dropdown .dropdown-item .dropdown-item-highlight {
    font-weight: bold;
}

.ng-autocomplete-dropdown-icon {
    display: block;
    width: 56px;
    text-align: center;
    position: absolute;
    top: 0;
    bottom: 0;
    right: 0;
    border-left: 1px solid #e0e0e0;
    cursor: pointer;
    z-index: 999;
    font-size: 12px;
    color: #758694;
    padding: 21px 0;
}

.ng-autocomplete-dropdown-icon:after {
    content: '';
    display: block;
    width: 0;
    height: 0;
    border-left: 7px solid transparent;
    border-right: 7px solid transparent;
    border-top: 7px solid #000;
    position: absolute;
    right: 21px;
    z-index: 999;
    top: 24px;
}

.ng-autocomplete-dropdown-icon.open:after {
    transform: rotate(180deg);
}

.ng-autocomplete-input.ng-pristine.ng-valid.ng-touched {
    padding-right: 61px;
    text-overflow: ellipsis;
}

.nav-tabs .nav-link {
    background-color: #21af61;
    color: #ffffff;
    border: 1px solid #ffffff;
}

.nav-tabs .nav-item.show .nav-link,
.nav-tabs .nav-link.active {
    background-color: #00622e;
    color: #ffffff;
    border-color: #dee2e6 #dee2e6 #fff;
}

.a {
    color: #ffffff
}

.hide {
    display: none;
}

.theme-red .bs-datepicker-head {
    background-color: #00622e;
}

.theme-red .bs-datepicker-body table td span.selected {
    background-color: #00622e;
}
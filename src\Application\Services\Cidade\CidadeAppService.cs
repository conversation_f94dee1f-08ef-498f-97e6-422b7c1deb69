

using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using AutoMapper.QueryableExtensions;
using SistemaInfo.BBC.Application.Helpers;
using SistemaInfo.BBC.Application.Interface.Cidade;
using SistemaInfo.BBC.Application.Objects.Web.Cidade;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.BBC.Domain.Models.Cidade.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;

namespace SistemaInfo.BBC.Application.Services.Cidade
{
    public class CidadeAppService : AppService<Domain.Models.Cidade.Cidade, ICidadeReadRepository, ICidadeWriteRepository>, ICidadeAppService
    {
        public CidadeAppService(IAppEngine engine, ICidadeReadRepository readRepository, ICidadeWriteRepository writeRepository) : base(engine, readRepository, writeRepository)
        {
        }

        public ConsultarGridCidadeResponse ConsultarGridCidade(int take, int page, OrderFilters orderFilters,
            List<QueryFilters> filters)
        {
            var lCidade = Repository.Query.GetAll();

            var lCount = lCidade.Count();

            lCidade = lCidade.AplicarFiltrosDinamicos<Domain.Models.Cidade.Cidade>(filters);
            lCidade = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                ? lCidade.OrderByDescending(o => o.Id)
                : lCidade.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            var retorno = lCidade.Skip((page - 1) * take)
                .Take(take)
                .ProjectTo<ConsultarGridCidade>(Engine.Mapper.ConfigurationProvider).ToList();

            return new ConsultarGridCidadeResponse
            {
                items = retorno,
                totalItems = lCount
            };
        }

        public async Task<Domain.Models.Cidade.Cidade> ConsultarPorIbge(int ibge)
        {
            try
            {
                new LogHelper().LogOperationStart("ConsultarPorIbge");
                return await Repository.Query.GetCidadeByIbgeAsync(ibge);
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar ConsultarPorIbge");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("ConsultarPorIbge");
            }
        }
    }
}

﻿using Microsoft.EntityFrameworkCore.Migrations;
using System;
using System.Collections.Generic;

namespace SistemaInfo.BBC.Infra.Data.Migrations
{
    public partial class SPRINT_55_ADD_STATUS_ANTECIPACAO_PROPRIETARIO : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "StatusAntecipacaoParcelaProprietario",
                schema: "BBC",
                table: "PagamentoEvento",
                type: "int",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "StatusAntecipacaoParcelaProprietario",
                schema: "BBC",
                table: "PagamentoEvento");
        }
    }
}

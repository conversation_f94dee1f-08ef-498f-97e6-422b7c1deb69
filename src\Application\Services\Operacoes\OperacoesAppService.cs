﻿using System;
using System.Threading.Tasks;
using DinkToPdf;
using DinkToPdf.Contracts;
using SistemaInfo.BBC.Application.Helpers;
using SistemaInfo.BBC.Application.Helpers.Ciot;
using SistemaInfo.BBC.Application.Interface.Operacoes;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Domain.Contracts.Operacoes;
using SistemaInfo.BBC.Domain.External.CIOT.DTO;
using SistemaInfo.BBC.Domain.Models.Empresa.Repository;
using SistemaInfo.BBC.Domain.Models.GrupoEmpresa.Repository;
using SistemaInfo.BBC.Infra.Bus.Interface.Ciot;
using SistemaInfo.BBC.Domain.Models.GrupoEmpresa.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using SistemaInfo.Framework.DomainDrivenDesign.Infra.DataTransferObjects;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.Services.Operacoes;

public class OperacoesAppService : AppService, IOperacoesAppService
{
    private readonly IEmpresaReadRepository _empresaReadRepository;
    private readonly IGrupoEmpresaReadRepository _grupoEmpresaReadRepository;
    private readonly IOperacoesPublisher _operacoesPublisher;
    //Usar eventos para mudar contexto e enviar informação para o nucleo de mensageria do sistema
    //evitar referencia direta e manter a parte do sistema monolitica intacta.
    public OperacoesAppService(IAppEngine engine, IEmpresaReadRepository empresaReadRepository, IGrupoEmpresaReadRepository grupoEmpresaReadRepository, IOperacoesPublisher operacoesPublisher) : base(engine)
    {
        _empresaReadRepository = empresaReadRepository;
        _grupoEmpresaReadRepository = grupoEmpresaReadRepository;
        _operacoesPublisher = operacoesPublisher;
    }

    public async Task<DeclararOperacaoTransporteResp> DeclararOperacaoTransporte(
        DeclararOperacaoTransporteReq declararOperacaoTransporteReq, bool isCiotPublico = false)
    {
        var requestMs = Mapper.Map<DeclararOperacaoTransporteReqMessage>(declararOperacaoTransporteReq);
        requestMs.CpfCnpjCliente = isCiotPublico ? "00000000000001" : await ResolveCnpjCliente(Engine.User, null);
        var retornoMs =  await Engine.CommandBus.SendCommandAsync<DeclararOperacaoTransporteRespMessage>(requestMs);
        return Mapper.Map<DeclararOperacaoTransporteResp>(retornoMs);
    }

    public async Task<EncerrarOperacaoTransporteResp> EncerrarOperacaoTransporte(
        EncerrarOperacaoTransporteReq encerrarOperacaoTransporteReq, bool isCiotPublico = false)
    {
        var requestMs = Mapper.Map<EncerrarOperacaoTransporteReqMessage>(encerrarOperacaoTransporteReq);
        requestMs.CpfCnpjCliente = isCiotPublico ? "00000000000001" : await ResolveCnpjCliente(Engine.User, encerrarOperacaoTransporteReq.CpfCnpjClienteAdmOrCompanyGroup);
        var retornoMs =  await Engine.CommandBus.SendCommandAsync<EncerrarOperacaoTransporteRespMessage>(requestMs);
        return Mapper.Map<EncerrarOperacaoTransporteResp>(retornoMs);
    }
    
    public async Task<EncerrarOperacaoTransporteResp> EncerrarOperacaoTransporteBbcService(
        EncerrarOperacaoTransporteReq encerrarOperacaoTransporteReq)
    {
        var retornoMs =  await Engine.CommandBus.SendCommandAsync<EncerrarOperacaoTransporteRespMessage>(Mapper.Map<EncerrarOperacaoTransporteBbcReqMessage>(encerrarOperacaoTransporteReq));
        return Mapper.Map<EncerrarOperacaoTransporteResp>(retornoMs);
    }
    
    public async Task<ConsultarSituacaoCiotResp> ConsultarSituacaoCiot(ConsultarSituacaoCiotReq consultarSituacaoCiotReq, bool isCiotPublico = false)
    {
        try
        {
            new LogHelper().LogOperationStart("ConsultarSituacaoCiot");
            var requestMs = Mapper.Map<ConsultarSituacaoCiotReqMessage>(consultarSituacaoCiotReq);
            requestMs.CpfCnpjCliente = isCiotPublico ? "00000000000001" : await ResolveCnpjCliente(Engine.User, null);
            var retornoMs =  await Engine.CommandBus.SendCommandAsync<ConsultarSituacaoCiotRespMessage>(requestMs);
            return Mapper.Map<ConsultarSituacaoCiotResp>(retornoMs);
        }
        catch (Exception ex)
        {
            new LogHelper().Error(ex, "Erro ao executar ConsultarSituacaoCiot");
            throw;
        }
        finally
        {
            new LogHelper().LogOperationEnd("ConsultarSituacaoCiot");
        }
    }

    public async Task<CancelarOperacaoTransporteResp> CancelarOperacaoTransporte(
        CancelarOperacaoTransporteReq cancelarOperacaoTransporteReq, bool isCiotPublico = false)
    {
        var requestMs = Mapper.Map<CancelarOperacaoTransporteReqMessage>(cancelarOperacaoTransporteReq);
        requestMs.CpfCnpjCliente = isCiotPublico ? "00000000000001" : await ResolveCnpjCliente(Engine.User, cancelarOperacaoTransporteReq.CpfCnpjClienteAdmOrCompanyGroup); 
        var retornoMs =  await Engine.CommandBus.SendCommandAsync<CancelarOperacaoTransporteRespMessage>(requestMs);
        return Mapper.Map<CancelarOperacaoTransporteResp>(retornoMs);
    }

    public async Task<RetificarOperacaoTransporteResp> RetificarOperacaoTransporte(
        RetificarOperacaoTransporteReq retificarOperacaoTransporteReq, bool isCiotPublico = false)
    {
        var requestMs = Mapper.Map<RetificarOperacaoTransporteReqMessage>(retificarOperacaoTransporteReq);
        requestMs.CpfCnpjCliente = isCiotPublico ? "00000000000001" : await ResolveCnpjCliente(Engine.User, retificarOperacaoTransporteReq.CpfCnpjClienteAdmOrCompanyGroup); 
        var retornoMs =  await Engine.CommandBus.SendCommandAsync<RetificarOperacaoTransporteRespMessage>(requestMs);
        return Mapper.Map<RetificarOperacaoTransporteResp>(retornoMs);
    }

    public async Task<ConsultarSituacaoTransportadorResp> ConsultarSituacaoTransportador(ConsultarSituacaoTransportadorReq req, bool isCiotPublico = false)
    {
        try
        {
            new LogHelper().LogOperationStart("ConsultarSituacaoTransportador");
            var requestMs = Mapper.Map<ConsultarSituacaoTransportadorReqMessage>(req);
            //se não tiver empresaId é o login do controle, usado para consultas apenas
            requestMs.CpfCnpjCliente = isCiotPublico || Engine.User.EmpresaId == 0 ? "00000000000001" : await ResolveCnpjCliente(Engine.User, null);
            

            var retornoMs =  await Engine.CommandBus.SendCommandAsync<ConsultarSituacaoTransportadorRespMessage>(requestMs);
            if (!retornoMs.Sucesso)
            {
                new LogHelper().Error(retornoMs.Erro.Mensagem);
            }
            return Mapper.Map<ConsultarSituacaoTransportadorResp>(retornoMs);
        }
        catch (Exception ex)
        {
            new LogHelper().Error(ex, "Erro ao executar ConsultarSituacaoTransportador");
            throw;
        }
        finally
        {
            new LogHelper().LogOperationEnd("ConsultarSituacaoTransportador");
        }
    }

    public async Task<ConsultarFrotaTransportadorResp> ConsultarFrotaTransportador(ConsultarFrotaTransportadorReq req)
    {
        try
        {
            new LogHelper().LogOperationStart("ConsultarFrotaTransportador");
            var requestMs = Mapper.Map<ConsultarFrotaTransportadorReqMessage>(req);
            requestMs.CpfCnpjCliente = await ResolveCnpjCliente(Engine.User, null);
            var retornoMs =  await Engine.CommandBus.SendCommandAsync<ConsultarFrotaTransportadorRespMessage>(requestMs);
            return Mapper.Map<ConsultarFrotaTransportadorResp>(retornoMs);
        }
        catch (Exception ex)
        {
            new LogHelper().Error(ex, "Erro ao executar ConsultarFrotaTransportador");
            throw;
        }
        finally
        {
            new LogHelper().LogOperationEnd("ConsultarFrotaTransportador");
        }
    }

    public async Task<RespBase> ConsultarOperacaoTacAgregado(ConsultarOperacaoTacAgregadoReq req)
    {
        try
        {
            new LogHelper().LogOperationStart("ConsultarOperacaoTacAgregado");
            var requestMs = Mapper.Map<ConsultarOperacaoTacAgregadoReqMessage>(req);
            requestMs.CpfCnpjCliente = await ResolveCnpjCliente(Engine.User, null);
            var retornoMs =  await Engine.CommandBus.SendCommandAsync<ConsultarOperacaoTacAgregadoRespMessage>(requestMs);
            return Mapper.Map<ConsultarOperacaoTacAgregadoResp>(retornoMs);
        }
        catch (Exception ex)
        {
            new LogHelper().Error(ex, "Erro ao executar ConsultarOperacaoTacAgregado");
            throw;
        }
        finally
        {
            new LogHelper().LogOperationEnd("ConsultarOperacaoTacAgregado");
        }
    }
    
    
    public async Task<ConsultarTacAgregadoResponse> ConsultarTacAgregado(ConsultarTacAgregadoRequest req)
    {
        var requestMs = Mapper.Map<ConsultarTacAgregadoRequestMessage>(req);
        //se não tiver empresaId é o login do controle, usado para consultas apenas
        requestMs.CpfCnpjCliente = Engine.User.EmpresaId == 0 ? "00000000000001" : await ResolveCnpjCliente(Engine.User, null);
        var retornoMs = await Engine.CommandBus.SendCommandAsync<ConsultarTacAgregadoResponseMessage>(requestMs);
        return Mapper.Map<ConsultarTacAgregadoResponse>(retornoMs);
    }
    
    private async Task<string> ResolveCnpjCliente(ISessionUser engineUser, string cpfCnpjClienteFrontWeb)
    {
        if (engineUser.AdministradoraId > 0)
        {
            var grupo = await _grupoEmpresaReadRepository.GetByIdAsync(engineUser.AdministradoraId);
            return grupo.Cnpj;
        }
        return engineUser.EmpresaId > 0 ? await _empresaReadRepository.GetCnpjAsync(engineUser.EmpresaId) : cpfCnpjClienteFrontWeb ?? "";
    }

    #region Ciot Público

    public async Task<ConsultarBancosResp> ConsultarBancos()
    { 
        var responseMessage = await _operacoesPublisher.ConsultarBancos();
        return Mapper.Map<ConsultarBancosResp>(responseMessage);
    }

    public async Task<ConsultarEstadosResp> ConsultarEstados()
    {
        var responseMessage = await _operacoesPublisher.ConsultarEstados();
        return Mapper.Map<ConsultarEstadosResp>(responseMessage);
    }
    
    public async Task<ConsultarTiposCargaResp> ConsultarTiposCarga(string txt)
    {
        var responseMessage = await _operacoesPublisher.ConsultarTiposCarga(new ConsultarTiposCargaReqMessage { Txt = txt });
        return Mapper.Map<ConsultarTiposCargaResp>(responseMessage);
    }

    public async Task<ConsultarCidadesResp> ConsultarCidades(string nome)
    {
        var responseMessage = await _operacoesPublisher.ConsultarCidades(new ConsultarCidadesReqMessage { Nome = nome });
        return Mapper.Map<ConsultarCidadesResp>(responseMessage);
    }

    public async Task<ConsultarNaturezasCargaResp> ConsultarNaturezasCarga(string descricao)
    {
        var retornoMs = await _operacoesPublisher.ConsultarNaturezasCarga(new ConsultarNaturezasCargaReqMessage { Descricao = descricao });
        return Mapper.Map<ConsultarNaturezasCargaResp>(retornoMs);
    }
    
    public async Task<ConsultarNaturezaCargaPorIdResp> ConsultarNaturezasCargaPorId(string codigo)
    {
        var responseMessage = await _operacoesPublisher.ConsultarNaturezaCargaPorId(new ConsultarNaturezaCargaPorIdReqMessage { Codigo = codigo });
        return Mapper.Map<ConsultarNaturezaCargaPorIdResp>(responseMessage);
    }

    public async Task<ConsultarDadosEncerramentoResp> ConsultarDadosEncerramento(ConsultarDadosEncerramentoReq req)
    {
        var responseMessage = await _operacoesPublisher.ConsultarDadosEncerramento(Mapper.Map<ConsultarDadosEncerramentoReqMessage>(req));
        return Mapper.Map<ConsultarDadosEncerramentoResp>(responseMessage);
    }

    public async Task<ConsultarDadosRetificacaoResp> ConsultarDadosRetificacao(ConsultarDadosRetificacaoReq req)
    {
        var responseMessage = await _operacoesPublisher.ConsultarDadosRetificacao(Mapper.Map<ConsultarDadosRetificacaoReqMessage>(req));
        return Mapper.Map<ConsultarDadosRetificacaoResp>(responseMessage);
    }

    #region Impressão

    public async Task<IDocument> GerarRelatorioGestaoCiot(string ciot, string senhaAlteracao)
    {
        try
        {
            var infosCiot = await _operacoesPublisher.ConsultarRelatorioGestaoCiot(new ConsultarRelatorioCiotReqMessage { Ciot = ciot, SenhaAlteracao = senhaAlteracao});
            
            var htmlReport = infosCiot == null
                ? $@"<div>CIOT {ciot} NÃO ENCONTRADO NA BASE DE DADOS</div>"
                : HtmlCiotPublicoReportHelper.GetHtmlImpressaoCiotPublico(infosCiot.Data);

            var doc = new HtmlToPdfDocument
            {
                GlobalSettings =
                {
                    ColorMode = ColorMode.Color,
                    Orientation = Orientation.Landscape,
                    PaperSize = PaperKind.A4Plus
                },
                Objects =
                {
                    new ObjectSettings
                    {
                        PagesCount = true,
                        HtmlContent = htmlReport,
                        WebSettings = { DefaultEncoding = "utf-8" },
                        HeaderSettings =
                            { FontSize = 9, Right = "Página: [page] / [toPage]", Line = true, Spacing = 2.812 }
                    }
                }
            };

            return doc;
        }
        catch (Exception ex)
        {
            new LogHelper().Error(ex, "Erro ao executar ImprimirCiot");
            throw;
        }
        finally
        {
            new LogHelper().LogOperationEnd("ImprimirCiot");
        }
    }

    #endregion
    

    #endregion
}
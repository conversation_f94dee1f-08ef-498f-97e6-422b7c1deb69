﻿using System.Threading.Tasks;
using SistemaInfo.BBC.Application.Interface.Operacoes;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Domain.Contracts.Operacoes;
using SistemaInfo.BBC.Domain.External.CIOT.DTO;
using SistemaInfo.BBC.Domain.Models.Empresa.Repository;
using SistemaInfo.BBC.Domain.Models.GrupoEmpresa.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using SistemaInfo.Framework.DomainDrivenDesign.Infra.DataTransferObjects;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.Services.Operacoes;

public class OperacoesAppService : AppService , IOperacoesAppService
{
    private readonly IEmpresaReadRepository _empresaReadRepository;
    private readonly IGrupoEmpresaReadRepository _grupoEmpresaReadRepository;
    //Usar eventos para mudar contexto e enviar informação para o nucleo de mensageria do sistema
    //evitar referencia direta e manter a parte do sistema monolitica intacta.
    public OperacoesAppService(IAppEngine engine, IEmpresaReadRepository empresaReadRepository, IGrupoEmpresaReadRepository grupoEmpresaReadRepository) : base(engine)
    {
        _empresaReadRepository = empresaReadRepository;
        _grupoEmpresaReadRepository = grupoEmpresaReadRepository;
    }

    public async Task<DeclararOperacaoTransporteResp> DeclararOperacaoTransporte(
        DeclararOperacaoTransporteReq declararOperacaoTransporteReq)
    {
        var requestMs = Mapper.Map<DeclararOperacaoTransporteReqMessage>(declararOperacaoTransporteReq);
        requestMs.CpfCnpjCliente = await ResolveCNPJcliente(Engine.User);
        var retornoMs =  await Engine.CommandBus.SendCommandAsync<DeclararOperacaoTransporteRespMessage>(requestMs);
        return Mapper.Map<DeclararOperacaoTransporteResp>(retornoMs);
    }

    private async Task<string> ResolveCNPJcliente(ISessionUser engineUser)
    {
        if (engineUser.AdministradoraId > 0)
        {
            var grupo = await _grupoEmpresaReadRepository.GetByIdAsync(engineUser.AdministradoraId);
            return grupo.Cnpj;
        }
        return  await _empresaReadRepository.GetCnpjAsync(engineUser.EmpresaId);
    }


    public async Task<EncerrarOperacaoTransporteResp> EncerrarOperacaoTransporte(
        EncerrarOperacaoTransporteReq encerrarOperacaoTransporteReq)
    {
        var requestMs = Mapper.Map<EncerrarOperacaoTransporteReqMessage>(encerrarOperacaoTransporteReq);
        requestMs.CpfCnpjCliente = requestMs.CpfCnpjCliente = await ResolveCNPJcliente(Engine.User);
        var retornoMs =  await Engine.CommandBus.SendCommandAsync<EncerrarOperacaoTransporteRespMessage>(requestMs);
        return Mapper.Map<EncerrarOperacaoTransporteResp>(retornoMs);
    }
    
    public async Task<EncerrarOperacaoTransporteResp> EncerrarOperacaoTransporteBbcService(
        EncerrarOperacaoTransporteReq encerrarOperacaoTransporteReq)
    {
        var retornoMs =  await Engine.CommandBus.SendCommandAsync<EncerrarOperacaoTransporteRespMessage>(Mapper.Map<EncerrarOperacaoTransporteBbcReqMessage>(encerrarOperacaoTransporteReq));
        return Mapper.Map<EncerrarOperacaoTransporteResp>(retornoMs);
    }
    public async Task<ConsultarSituacaoCiotResp> ConsultarSituacaoCiot(ConsultarSituacaoCiotReq consultarSituacaoCiotReq)
    {
        var requestMs = Mapper.Map<ConsultarSituacaoCiotReqMessage>(consultarSituacaoCiotReq);
        requestMs.CpfCnpjCliente = await ResolveCNPJcliente(Engine.User);
        var retornoMs =  await Engine.CommandBus.SendCommandAsync<ConsultarSituacaoCiotRespMessage>(requestMs);
        return Mapper.Map<ConsultarSituacaoCiotResp>(retornoMs);
    }
    public async Task<CancelarOperacaoTransporteResp> CancelarOperacaoTransporte(
        CancelarOperacaoTransporteReq cancelarOperacaoTransporteReq)
    {
        var requestMs = Mapper.Map<CancelarOperacaoTransporteReqMessage>(cancelarOperacaoTransporteReq);
        requestMs.CpfCnpjCliente = requestMs.CpfCnpjCliente = await ResolveCNPJcliente(Engine.User); //Engine.User.EmpresaId > 0 ? await _empresaReadRepository.GetCnpjAsync(Engine.User.EmpresaId) : cancelarOperacaoTransporteReq.CpfCnpjClienteAdmOrCompanyGroup;
        var retornoMs =  await Engine.CommandBus.SendCommandAsync<CancelarOperacaoTransporteRespMessage>(requestMs);
        return Mapper.Map<CancelarOperacaoTransporteResp>(retornoMs);
    }
    public async Task<RetificarOperacaoTransporteResp> RetificarOperacaoTransporte(
        RetificarOperacaoTransporteReq retificarOperacaoTransporteReq)
    {
        var requestMs = Mapper.Map<RetificarOperacaoTransporteReqMessage>(retificarOperacaoTransporteReq);
        requestMs.CpfCnpjCliente = requestMs.CpfCnpjCliente = await ResolveCNPJcliente(Engine.User); //Engine.User.EmpresaId > 0 ? await _empresaReadRepository.GetCnpjAsync(Engine.User.EmpresaId) : retificarOperacaoTransporteReq.CpfCnpjClienteAdmOrCompanyGroup;
        var retornoMs =  await Engine.CommandBus.SendCommandAsync<RetificarOperacaoTransporteRespMessage>(requestMs);
        return Mapper.Map<RetificarOperacaoTransporteResp>(retornoMs);
    }

    public async Task<ConsultarSituacaoTransportadorResp> ConsultarSituacaoTransportador(ConsultarSituacaoTransportadorReq req)
    {
        var requestMs = Mapper.Map<ConsultarSituacaoTransportadorReqMessage>(req);
        //se não tiver empresaId é o login do controle, usado para consultas apenas
        requestMs.CpfCnpjCliente = Engine.User.EmpresaId == 0 ? "00000000000001" : await ResolveCNPJcliente(Engine.User);
        var retornoMs =  await Engine.CommandBus.SendCommandAsync<ConsultarSituacaoTransportadorRespMessage>(requestMs);
        return Mapper.Map<ConsultarSituacaoTransportadorResp>(retornoMs);
    }

    public async Task<ConsultarFrotaTransportadorResp> ConsultarFrotaTransportador(ConsultarFrotaTransportadorReq req)
    {
        var requestMs = Mapper.Map<ConsultarFrotaTransportadorReqMessage>(req);
        requestMs.CpfCnpjCliente = await ResolveCNPJcliente(Engine.User);
        var retornoMs =  await Engine.CommandBus.SendCommandAsync<ConsultarFrotaTransportadorRespMessage>(requestMs);
        return Mapper.Map<ConsultarFrotaTransportadorResp>(retornoMs);
    }

    public async Task<RespBase> ConsultarOperacaoTacAgregado(ConsultarOperacaoTacAgregadoReq req)
    {
        var requestMs = Mapper.Map<ConsultarOperacaoTacAgregadoReqMessage>(req);
        requestMs.CpfCnpjCliente = await ResolveCNPJcliente(Engine.User);
        var retornoMs =  await Engine.CommandBus.SendCommandAsync<ConsultarOperacaoTacAgregadoRespMessage>(requestMs);
        return Mapper.Map<ConsultarOperacaoTacAgregadoResp>(retornoMs);
    }
    
    
    public async Task<ConsultarTacAgregadoResponse> ConsultarTacAgregado(ConsultarTacAgregadoRequest req)
    {
        var requestMs = Mapper.Map<ConsultarTacAgregadoRequestMessage>(req);
        //se não tiver empresaId é o login do controle, usado para consultas apenas
        requestMs.CpfCnpjCliente = Engine.User.EmpresaId == 0 ? "00000000000001" : await ResolveCNPJcliente(Engine.User);
        var retornoMs = await Engine.CommandBus.SendCommandAsync<ConsultarTacAgregadoResponseMessage>(requestMs);
        return Mapper.Map<ConsultarTacAgregadoResponse>(retornoMs);
    }
}
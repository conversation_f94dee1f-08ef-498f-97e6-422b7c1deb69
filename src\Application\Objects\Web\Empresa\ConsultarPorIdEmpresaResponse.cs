using System;
using System.Collections.Generic;
using SistemaInfo.BBC.Application.Objects.Web.Ciot;
using SistemaInfo.BBC.Application.Objects.Web.EmpresaCfop;

namespace SistemaInfo.BBC.Application.Objects.Web.Empresa
{
    public class ConsultarPorIdEmpresaResponse
    {
        public int Id { get; set; }
        public string Cnpj { get; set; }
        public string RNTRC { get; set; }
        public string RazaoSocial { get; set; }
        public string NomeFantasia { get; set; }
        public string Celular { get; set; }
        public string Telefone { get; set; }
        public string Email { get; set; }
        public string Cep { get; set; }
        public int EstadoId { get; set; }
        public int CidadeId { get; set; }
        public string Endereco { get; set; }
        public string Bairro { get; set; }
        public string EnderecoNumero { get; set; }
        public string Complemento { get; set; }
        public string NomeCnpjPortador { get; set; }
        public int? IdPortador { get; set; }
        public int? UsuarioCadastro { get; set; }
        public DateTime DataCadastro { get; set; }
        public int Ativo { get; set; }
        public int? UsuarioBloqueioId { get; set; }
        public DateTime DataBloqueio { get; set; }
        public int? UsuarioDesbloqueioId { get; set; }
        public DateTime DataDesbloqueio { get; set; }
        public int? StatusCadastro { get; set; }
        public int? UsuarioValidacaoId { get; set; }
        public DateTime? DataValidacao { get; set; }
        public string ParecerInterno { get; set; }
        public string ParecerExterno { get; set; }
        public int? TipoEmissaoCiot { get; set; }
        public bool LiberaBloqueioSPD { get; set; }
        public bool CobrancaTarifa { get; set; }
        public int? TempoAbastecimento { get; set; }
        public decimal? ValorTolerancia { get; set; }
        public int? ControlaOdometro { get; set; }
        public int? ControlaAutonomia { get; set; }
        public decimal? TaxaAbastecimento { get; set; }
        public decimal? Cashback { get; set; }
        public int RegistraCiot { get; set; }
        public int? TipoEmpresaId { get; set; }
        public decimal? ImpostoIRRF { get; set; }
        public decimal? ImpostoCSLL { get; set; }
        public decimal? ImpostoCOFINS { get; set; }
        public decimal? ImpostoPIS { get; set; }
        public string Link { get; set; }
        public string SenhaLink { get; set; }
        public string LinkSAP { get; set; }
        public string UsuarioSAP { get; set; }
        public string SenhaSAP { get; set; }
        public string TipoEmpresaNome { get; set; }
        public decimal? PercentualAutonomiaInferior { get; set; }
        public decimal? PercentualAutonomiaSuperior { get; set; }
        public int? ControlaContingencia { get; set; }
        public int? Prazo { get; set; }
        public DateTime? DataAlteracaoModelo { get; set; }        
        public int? DebitoProtocolo { get; set; }
        public int? DebitoPrazo { get; set; }
        public List<PortadorRepLegalEmpResponse> RepLegaisList { get; set; }
        public List<EmpresaCfopResp> EmpresaCfop { get; set; }
        public List<CiotClienteIpResponseAdaptado> CiotClienteIps { get; set; }
        public int? ContaAbastecimento { get; set; }
        public int? ContaValePedagio { get; set; }
        public int? ContaFrete { get; set; }
        public int? GrupoEmpresaId { get; set; }
        public string GrupoEmpresaNome { get; set; }
        public int UtilizaTarifaEmpresa { get; set; }
        
        public int? QtdMensalSemTaxaPix { get; set; }
        public decimal? ValorTarifaPix { get; set; } 
        public decimal? ValorTarifaBbc { get; set; }
        public bool? RecebedorAutorizado { get; set; }
        public bool PermitirPagamentoValePedagio { get; set; }
        public bool CobrarTarifaBbcValePedagio { get; set; }
        public decimal? PorcentagemTarifaServiceValePedagio { get; set; }
        public int UtilizaTarifaEmpresaPagamentoPedagio { get; set; }
        public string NotificacaoContingenciaCiot { get; set; }
        public int StatusReprocessamentoPagamentoFrete { get; set; }
        public bool HabilitaReprocessamentoValePedagio { get; set; }
        public int HabilitaPainelSaldo { get; set; }

        public string ImagemCartao { get; set; }
        public decimal ValorAdiantamentoBbc { get; set; }
        public bool PermitirEncerramentoPainelCiot { get; set; }
        public bool UtilizaCiot { get; set; }

        //public TipoEmpresa? TipoEmpresa { get; set; }
        
        public class PortadorRepLegalEmpResponse
        {
            public int Id { get; set; }
            public string Nome { get; set; }
            public string CpfCnpj { get; set; }
        }
    }

    public class ConsultarPorIdClientSecretResponse
    {
        public int Id { get; set; }
        public string NomeFantasia { get; set; }
    }
}
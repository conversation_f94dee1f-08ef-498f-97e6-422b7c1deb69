﻿using System.ComponentModel;

namespace SistemaInfo.BBC.Domain.Enum;

public enum EMenus
{
    [Description("autorizacao-abastecimento.index")]
    AutorizacaoAbastecimento = 1,
    [Description("autorizacao-contingencia.index")]
    AutorizacaoContingencia = 2,
    [Description("painel-pagamento-abastecimento.index")]
    PainelPagamentoAbastecimento = 3,
    [Description("painel-protocolo-abastecimento.index")]
    PainelProtocoloAbastecimento = 4,
    [Description("cfop.index")]
    Cfop = 5,
    [Description("percentual-transferencia.index")]
    PercentualTransferencia = 6,
    [Description("documentos-processo-vinculado.index")]
    DocumentosProcessoVinculado = 7,
    [Description("painel-pedidos-pendentes.index")]
    PainelPedidosPendentes = 8,
    [Description("painel-abastecimento.index")]
    PainelAbastecimento = 9,
    [Description("painel-pagamento-vale-pedagio.index")]
    PainelPagamentoValePedagio = 10,
    [Description("gestao-transportador.index")]
    GestaoTransportador = 11,
    [Description("feriado.index")]
    Feriado = 12,
    [Description("atualizacao-preco-combustivel.index")]
    AtualizacaoPrecoCombustivel = 13,
    [Description("protocolo-abastecimento.index")]
    ProtocoloAbastecimento = 14,
    [Description("monitoramento-servidores-ciot.index")]
    MonitoramentoServidoresCiot = 15,
    [Description("painel-financeiro.index")]
    PainelFinanceiro = 16,
    [Description("viagens.index")]
    Viagens = 17,
    [Description("emprestimo.index")]
    Emprestimo = 18,
    [Description("painel-pagamento.index")]
    PainelPagamento = 19,
    [Description("central-notificacoes.index")]
    CentralNotificacoes = 20,
    [Description("veiculo.index")]
    Veiculo = 21,
    [Description("modelo-veiculo.index")]
    ModeloVeiculo = 22,
    [Description("fabricante.index")]
    Fabricante = 23,
    [Description("combustivel.index")]
    Combustivel = 24,
    [Description("centro-custo.index")]
    CentroCusto = 25,
    [Description("banco.index")]
    Banco = 26,
    [Description("portador.index")]
    Portador = 27,
    [Description("filial.index")]
    Filial = 28,
    [Description("empresa.index")]
    Empresa = 29,
    [Description("cliente.index")]
    Cliente = 30,
    [Description("usuario.index")]
    Usuario = 31,
    [Description("parametros.index")]
    Parametros = 32,
    [Description("bloqueio-spd.index")]
    BloqueioSpd = 33,
    [Description("grupo-usuario.index")]
    GrupoUsuario = 34,
    [Description("retencao.index")]
    Retencao = 35,
    [Description("painel-ciot.index")]
    PainelCiot = 36,
    [Description("grupo-empresa.index")]
    GrupoEmpresa = 37,
    [Description("central-mensagens.index")]
    CentralMensagens = 38,
    [Description("integrar-conta.index")]
    IntegrarConta = 39,
    [Description("auditoria-seguranca.index")]
    AuditoriaSeguranca = 40,
    [Description("client-secret.index")]
    ClientSecret = 41,
    [Description("painel-financeiro-posto.index")]
    PainelFinanceiroPosto = 42,
    [Description("manutencao-abastecimento.index")]
    ManutencaoAbastecimento = 43,
    [Description("tipo-empresa.index")]
    TipoEmpresa = 44,
    [Description("posto.index")]
    Posto = 45,
    [Description("mdrprazos.index")]
    Mdrprazos = 46,
    [Description("manutencao-cadastro-posto.index")]
    ManutencaoCadastroPosto = 47,
    [Description("lancamento-abastecimento.index")]
    LancamentoAbastecimento = 48,
    [Description("central-pendencias-movida.index")]
    CentralPendenciasMovida = 49,
    [Description("central-pendencias.index")]
    CentralPendencias = 50,
    [Description("client-secret-consulta-frete.index")]
    ClientSecretAdm = 52,
    [Description("central-pendencias-pagamentos-antecipados.index")]
    CentralPendenciasAntecipados = 53,
    
}
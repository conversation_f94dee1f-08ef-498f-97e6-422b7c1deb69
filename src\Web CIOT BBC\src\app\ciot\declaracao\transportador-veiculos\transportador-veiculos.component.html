<div class="container main-container">
    <form novalidate autocomplete="false" [formGroup]="transportadorForm">
        <div class="form-horizontal">
            <fieldset>
                <legend><PERSON><PERSON> Pessoais</legend>
                <div class="row">
                    <div class="col-sm-12 col-md-6 col-lg-3">
                        <div class="form-group required" [ngClass]="{'has-error': displayMessage.cpfCnpj }">
                            <label class="control-label" for="formaPagamento">CPF/CNPJ</label>
                            <input RntrcMask class="form-control" maxlength="14" id="cpfCnpj" type="text"
                                formControlName="cpfCnpj" OnlyNumber />
                            <span class="text-danger" *ngIf="displayMessage.cpfCnpj">
                                <p [innerHTML]="displayMessage.cpfCnpj"></p>
                            </span>
                        </div>
                    </div>
                    <div class="col-sm-12 col-md-6 col-lg-3">
                        <div class="form-group" [ngClass]="{'has-error': displayMessage.RNTRC }">
                            <label class="control-label" for="formaPagamento">RNTRC</label>
                            <input class="form-control" maxlength="9" id="RNTRC" type="text" formControlName="RNTRC"
                                RntrcMask />
                            <span class="text-danger" *ngIf="displayMessage.RNTRC">
                                <p [innerHTML]="displayMessage.RNTRC"></p>
                            </span>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-12 col-md-6 col-lg-4">
                        <div class="form-group required" [ngClass]="{'has-error': displayMessage.nome}">
                            <label class="control-label" for="nome">Nome/Razão social</label>
                            <input class="form-control" id="nome" type="text" formControlName="nome" />
                            <span class="text-danger" *ngIf="displayMessage.nome">
                                <p [innerHTML]="displayMessage.nome"></p>
                            </span>
                        </div>
                    </div>
                    <div class="col-sm-12 col-md-6 col-lg-4">
                        <label class="control-label" for="nomeFantasia">Nome fantasia:</label>
                        <input class="form-control" id="nomeFantasia" type="text" disabled />
                    </div>
                </div>
            </fieldset>
            <br />
            <fieldset>
                <legend>Endereço</legend>
                <div class="row">
                    <div class="col-sm-12 col-md-6 col-lg-3">
                        <div class="form-group required" [ngClass]="{'has-error': displayMessage.cep}">
                            <label class="control-label" for="cep">CEP</label>
                            <div class="input-group">
                                <input mask="00000-000" class="form-control" id="cep" type="text" formControlName="cep"
                                    placeholder="00000-000" />
                            </div>
                            <span class="text-danger" *ngIf="displayMessage.cep">
                                <p [innerHTML]="displayMessage.cep"></p>
                            </span>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-12 col-md-6 col-lg-3">
                        <div class="form-group required" [ngClass]="{'has-error': displayMessage.logradouro}">
                            <label class="control-label" for="logradouro">Logradouro</label>
                            <input class="form-control" maxlength="40" id="logradouro" type="text"
                                formControlName="logradouro" />
                            <span class="text-danger" *ngIf="displayMessage.logradouro">
                                <p [innerHTML]="displayMessage.logradouro"></p>
                            </span>
                        </div>
                    </div>
                    <div class="col-sm-12 col-md-6 col-lg-3">
                        <label class="control-label" for="numero">Número</label>
                        <input class="form-control" id="numero" type="text" formControlName="numero" OnlyNumber
                            maxlength="15" />
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-12 col-md-6 col-lg-3">
                        <label class="control-label" for="bairro">Bairro</label>
                        <input class="form-control" id="bairro" type="text" formControlName="bairro" />
                    </div>
                    <div class="col-sm-12 col-md-6 col-lg-3">
                        <label class="control-label" for="complemento">Complemento</label>
                        <input class="form-control" id="complemento" type="text" formControlName="complemento" />
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-12 col-md-6 col-lg-3">
                        <div class="form-group required" [ngClass]="{'has-error': displayMessage.cidade}">
                            <label class="control-label" for="cidade">Cidade</label>
                            <div class="alert alert-danger" *ngIf="noResult">Cidade não encontrada</div>
                            <input [(ngModel)]="cidadeCompleterText" [typeahead]="cidadeList"
                                (typeaheadNoResults)="typeaheadNoResults($event)" typeaheadOptionField="nome"
                                (typeaheadOnSelect)="onSelectCidade($event)" class="form-control" autocomplete="off"
                                [ngModelOptions]="{standalone: true}">
                        </div>
                    </div>
                </div>
            </fieldset>
            <br />
            <fieldset>
                <legend>Contato</legend>
                <div class="row">
                    <div class="col-sm-12 col-md-6 col-lg-3">
                        <div class="form-group required" [ngClass]="{'has-error': displayMessage.telefone}">
                            <label class="control-label" for="telefone">Telefone</label>
                            <telefone [control]="transportadorForm.get('telefone')"></telefone>
                            <span class="text-danger" *ngIf="displayMessage.telefone">
                                <p [innerHTML]="displayMessage.telefone"></p>
                            </span>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-12 col-md-6 col-lg-3">
                        <div class="form-group" [ngClass]="{'has-error': displayMessage.email}">
                            <label class="control-label" for="email">E-mail</label>
                            <div class="input-group">
                                <input class="form-control" SpecialCharacterNotAllowed id="compemaillemento"
                                    type="email" formControlName="email" />
                                <span class="text-danger" *ngIf="displayMessage.email">
                                    <p [innerHTML]="displayMessage.email"></p>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </fieldset>
            <div style="padding-right: 15px !important;">
                <div class="panel-body">
                    <fieldset class="scheduler-border">
                        <legend class="scheduler-border">Veículos</legend>
                        <form novalidate (ngSubmit)="adicionarVeiculo()" [formGroup]="veiculoForm">
                            <div class="row">
                                <div class="col-sm-12 col-md-6 col-lg-3">
                                    <div class="form-group required" [ngClass]="{'has-error': displayMessage.Placa}">
                                        <label class="control-label" for="formaPagamento">Placa:</label>
                                        <input maxlength="7" PlacaAutomovel class="form-control" id="placa" type="text"
                                            formControlName="placa" />
                                        <span class="text-danger" *ngIf="displayMessage.placa">
                                            <p [innerHTML]="displayMessage.placa"></p>
                                        </span>
                                    </div>
                                </div>
                                <div class="col-sm-12 col-md-6 col-lg-3">
                                    <div class="form-group required" [ngClass]="{'has-error': displayMessage.RNTRC }">
                                        <label class="control-label" for="RNTRC">RNTRC</label>
                                        <input RntrcMask type="text" maxlength="9" class="form-control" id="rntrc"
                                            formControlName="rntrc" />
                                        <span class="text-danger" *ngIf="displayMessage.rntrc">
                                            <p [innerHTML]="displayMessage.rntrc"></p>
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-sm-12 col-md-6 col-lg-3">
                                    <button class="btn btn-danger" [disabled]="!veiculoForm.valid" id="adicionarVeiculo"
                                        type="submit">Adicionar</button>
                                </div>
                            </div>
                        </form>
                        <br />
                        <div class="control-group">
                            <table class="table table-striped" style="width:91%">
                                <thead>
                                    <tr>
                                        <th scope="col">Ação</th>
                                        <th scope="col">Placa</th>
                                        <th scope="col">RNTRC</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr *ngFor='let veiculo of veiculoList'>
                                        <td>
                                            <button type="submit" class="btn btn-danger btn-sm"
                                                (click)='removerVeiculo(veiculo)'>
                                                <span aria-hidden="true">Excluir</span>
                                            </button>
                                        </td>
                                        <td>{{veiculo.placa | placaAutomovel}}</td>
                                        <td>{{veiculo.rntrc}}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </fieldset>
                </div>
            </div>
            <br>
        </div>
    </form>
</div>
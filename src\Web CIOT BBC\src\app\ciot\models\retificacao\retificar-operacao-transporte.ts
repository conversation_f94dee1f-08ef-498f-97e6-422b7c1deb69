import { <PERSON><PERSON><PERSON><PERSON> } from "../veiculo";
import { Excecao } from "../common/excecao";

export class RetificarOperacaoTransporteReq {
    ciot: string;
    veiculos: Veiculo[];
    codigoNaturezaCarga: string;
    pesoCarga: number;
    dataInicioViagem: Date;
    dataFimViagem: Date;
    codigoMunicipioOrigem: number;
    codigoMunicipioDestino: number;
    senhaAlteracao: string;
    quantidadeTarifas: number;
    valorTarifas: number;
}

export class RetificarOperacaoTransporteResponse {
    ciot: string;
    dataRetificacao: Date;
    sucesso: boolean;
    excecao: {
        tipo: number;
        codigo: string;
        mensagem: string;
    };
}


using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using AutoMapper.QueryableExtensions;
using NLog;
using SistemaInfo.BBC.Application.Helpers;
using SistemaInfo.BBC.Application.Interface.Banco;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.Banco;
using SistemaInfo.BBC.Application.Objects.Web.Fabricante;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.BBC.Domain.Models.Banco.Commands;
using SistemaInfo.BBC.Domain.Models.Banco.Repository;
using SistemaInfo.BBC.Domain.Models.MDRPrazos.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Models.Bus;

namespace SistemaInfo.BBC.Application.Services.Banco
{
    public class BancoAppService : AppService<Domain.Models.Banco.Banco, IBancoReadRepository, IBancoWriteRepository>,
        IBancoAppService
    {
        private readonly IMDRPrazosReadRepository _mdrPrazosReadRepository;

        public BancoAppService(
            IAppEngine engine,
            IBancoReadRepository readRepository,
            IBancoWriteRepository writeRepository,
            IMDRPrazosReadRepository mdrPrazosReadRepository)
            : base(engine, readRepository, writeRepository)
        {
            _mdrPrazosReadRepository = mdrPrazosReadRepository;
        }

        public ConsultarGridBancoResponse ConsultarGridBanco(int take, int page, OrderFilters orderFilters,
            List<QueryFilters> filters)
        {
            var lBanco = Repository.Query.GetAll();

            var lCount = lBanco.Count();

            lBanco = lBanco.AplicarFiltrosDinamicos(filters);
            lBanco = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                ? lBanco.OrderByDescending(o => o.Id)
                : lBanco.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            var retorno = lBanco.Skip((page - 1) * take)
                .Take(take)
                .ProjectTo<ConsultarGridBanco>(Engine.Mapper.ConfigurationProvider).ToList();

            return new ConsultarGridBancoResponse
            {
                items = retorno,
                totalItems = lCount
            };
        }

        public ConsultarGridBancoResponse ConsultarGridBancoComMDRCombo(int take, int page, OrderFilters orderFilters,
            List<QueryFilters> filters)
        {
            var lBancoIdsComMDRPrazos = _mdrPrazosReadRepository
                .Where(m => m.Ativo == 1)
                .Select(m => m.BancoId)
                .ToList();

            var lBanco = Repository.Query
                .Where(b => lBancoIdsComMDRPrazos.Contains(b.Id));

            var lCount = lBanco.Count();

            lBanco = lBanco.AplicarFiltrosDinamicos(filters);
            lBanco = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                ? lBanco.OrderByDescending(o => o.Id)
                : lBanco.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            var retorno = lBanco.Skip((page - 1) * take)
                .Take(take)
                .ProjectTo<ConsultarGridBanco>(Engine.Mapper.ConfigurationProvider).ToList();

            return new ConsultarGridBancoResponse
            {
                items = retorno,
                totalItems = lCount
            };
        }

        public BancoResponse ConsultarPorId(string idBanco)
        {
            try
            {
                new LogHelper().LogOperationStart("ConsultarPorId");
                var lRetorno = Repository.Query.ConsultaPorId(idBanco);

                return Mapper.Map<BancoResponse>(lRetorno);
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar ConsultarPorId");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("ConsultarPorId");
            }
        }

        public async Task<RespPadrao> Save(BancoRequest lBancoReq, bool integracao)
        {
            try
            {
                var verificador = Repository.Query.FirstOrDefault(x => x.Id == lBancoReq.Id);

                if (verificador != null ? verificador.Id == lBancoReq.Id && lBancoReq.Editar == 0 : false)
                {
                    return new RespPadrao
                    {
                        sucesso = false,
                        mensagem = "Registro " + verificador.Id + " já cadastrado para outro banco!"
                    };
                }

                var command = Mapper.Map<BancoSalvarComRetornoCommand>(lBancoReq);
                var retorno = await Engine.CommandBus.SendCommandAsync<Domain.Models.Banco.Banco>(command);

                return new RespPadrao
                {
                    sucesso = true,
                    mensagem = "Registro " + retorno.Id + " salvo com sucesso!"
                };
            }
            catch (Exception e)
            {
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = e.Message
                };
            }
        }

        public async Task<RespPadrao> AlterarStatus(BancoStatusRequest request)
        {
            try
            {
                var command = Mapper.Map<BancoAlterarStatusCommand>(request);
                var retorno = await Engine.CommandBus.SendCommandAsync<Domain.Models.Banco.Banco>(command);
                return new RespPadrao(true, $"Banco {(retorno.Ativo == 1 ? "ativado" : "inativado")} com sucesso.");
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return new RespPadrao(false, "Ocorreu um erro ao alterar o status do banco: " + e.Message);
            }
        }
    }
}

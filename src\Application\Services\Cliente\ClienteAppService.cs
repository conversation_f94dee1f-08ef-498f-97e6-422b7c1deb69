using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using AutoMapper.QueryableExtensions;
using Microsoft.EntityFrameworkCore;
using SistemaInfo.BBC.Application.Helpers;
using SistemaInfo.BBC.Application.Interface.Cidade;
using SistemaInfo.BBC.Application.Interface.Cliente;
using SistemaInfo.BBC.Application.Objects.Api.Cliente;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.Cliente;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.BBC.Domain.Models.Cliente.Commands;
using SistemaInfo.BBC.Domain.Models.Cliente.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.Services.Cliente
{
    public class ClienteAppService :
        AppService<Domain.Models.Cliente.Cliente, IClienteReadRepository, IClienteWriteRepository>,
        IClienteAppService
    {
        private readonly ICidadeAppService _cidadeAppService;

        public ClienteAppService(IAppEngine engine, IClienteReadRepository readRepository,
            IClienteWriteRepository writeRepository, ICidadeAppService cidadeAppService) : base(engine, readRepository,
            writeRepository)
        {
            _cidadeAppService = cidadeAppService;
        }

        public ConsultarGridClienteResponse ConsultarGridCliente(int take, int page, OrderFilters orderFilters,
            List<QueryFilters> filters)
        {
            try
            {
                new LogHelper().LogOperationStart("ConsultarGridCliente");
                foreach (var item in filters)
                {
                    if (item.Campo == "cpfCnpj")
                        item.Valor = item.Valor.Replace(".", "").Replace("/", "").Replace("-", "");

                    if (item.Campo == "celular")
                        item.Valor = item.Valor.Replace("(", "").Replace(")", "").Replace("=", "").Replace(" ", "");
                }

                var idEmpresa = Engine.User.EmpresaId;

                if (orderFilters.Campo == "cnpj")
                {
                    orderFilters.Campo = "cpfCnpj";
                }

                var lCliente = Repository.Query.GetAll();

                if (idEmpresa > 0)
                    lCliente = lCliente.Where(c => c.EmpresaId == idEmpresa);

                var lCount = lCliente.Count();

                lCliente = lCliente.AplicarFiltrosDinamicos(filters);
                lCliente = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                    ? lCliente.OrderByDescending(o => o.Id)
                    : lCliente.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

                var retorno = lCliente.Skip((page - 1) * take)
                    .Take(take)
                    .ProjectTo<ConsultarGridCliente>(Engine.Mapper.ConfigurationProvider).ToList();

                foreach (var item in retorno)
                    item.status = item.Ativo == StatusCliente.Ativo ? 1 : 0;

                return new ConsultarGridClienteResponse
                {
                    items = retorno,
                    totalItems = lCount
                };
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar ConsultarGridCliente");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("ConsultarGridCliente");
            }
        }

        public ConsultarGridClienteResponse ConsultarComboCliente(int take, int page, OrderFilters orderFilters,
            List<QueryFilters> filters)
        {
            try
            {
                new LogHelper().LogOperationStart("ConsultarComboCliente");
                var idEmpresa = Engine.User.EmpresaId;

                var lCliente = Repository.Query.GetAll().Where(c => c.Ativo == StatusCliente.Ativo);

                if (idEmpresa > 0)
                {
                    lCliente = lCliente.Where(c => c.EmpresaId == idEmpresa && c.Ativo == StatusCliente.Ativo);
                }

                var lCount = lCliente.Count();

                lCliente = lCliente.AplicarFiltrosDinamicos(filters);
                lCliente = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                    ? lCliente.OrderByDescending(o => o.Id)
                    : lCliente.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

                var retorno = lCliente.Skip((page - 1) * take)
                    .Take(take)
                    .ProjectTo<ConsultarGridCliente>().ToList();

                foreach (var item in retorno)
                {
                    item.status = item.Ativo == StatusCliente.Ativo ? 1 : 0;
                }

                return new ConsultarGridClienteResponse
                {
                    items = retorno,
                    totalItems = lCount
                };
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar ConsultarComboCliente");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("ConsultarComboCliente");
            }
        }

        public async Task<RespPadrao> Save(ClienteRequest lClienteReq)
        {
            try
            {
                var lCliente = Mapper.Map<ClienteSalvarComRetornoCommand>(lClienteReq);

                lCliente.CpfCnpj.OnlyNumbers();

                if (lClienteReq.Id.IsNullOrWhiteSpace() || lClienteReq.Id == "0")
                {
                    var lCpfCnpjCadastrado = Repository.Query.FirstOrDefault(o =>
                        o.CpfCnpj == lCliente.CpfCnpj && o.EmpresaId == lCliente.EmpresaId);

                    if (lCpfCnpjCadastrado != null)
                        throw new Exception($"CPF/CNPJ {lCliente.CpfCnpj} já cadastrado para essa empresa!");
                }

                var cliente = Mapper.Map<Domain.Models.Cliente.Cliente>(lCliente);
                cliente.ValidarCriacao();

                var result = await Engine.CommandBus.SendCommandAsync<Domain.Models.Cliente.Cliente>(lCliente);

                return new RespPadrao()
                {
                    id = result.Id,
                    sucesso = true,
                    mensagem = ""
                };
            }
            catch (Exception e)
            {
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = e.Message
                };
            }
        }

        public ClienteResponse ConsultarPorId(int idCliente)
        {
            try
            {
                new LogHelper().LogOperationStart("ConsultarPorId");
                return Mapper.Map<ClienteResponse>(Repository.Query
                    .Include(x => x.Empresa)
                    .Include(x => x.Cidade)
                    .Include(x => x.Cidade.Estado)
                    .FirstOrDefault(a => a.Id == idCliente));
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar ConsultarPorId");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("ConsultarPorId");
            }
        }

        public async Task AlterarStatus(ClienteStatusRequest lClienteStatus)
        {
            try
            {
                new LogHelper().LogOperationStart("AlterarStatus");
                await Engine.CommandBus.SendCommandAsync(Mapper.Map<ClienteAlterarStatusCommand>(lClienteStatus));
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar AlterarStatus");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("AlterarStatus");
            }
        }

        public async Task<ClienteConsultarApiResponse> Consultar(ClienteConsultarApiRequest request)
        {
            try
            {
                new LogHelper().LogOperationStart("Consultar");
                var cons = Repository.Query.GetAll().Where(c => c.EmpresaId == User.EmpresaId);

                if (!string.IsNullOrEmpty(request.CpfCnpj))
                    cons = cons.Where(c => c.CpfCnpj == request.CpfCnpj);

                var lista = await cons.ProjectTo<ClienteApiResponse>().ToListAsync();
                var count = lista.Count;

                return new ClienteConsultarApiResponse()
                {
                    TotalItems = count,
                    Page = request.Page,
                    Items = lista.Skip((request.Page - 1) * request.Take).Take(request.Take).ToList()
                };
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar Consultar");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("Consultar");
            }
        }

        public async Task<RespPadraoApi> Integrar(ClienteIntegrarApiRequest request)
        {
            try
            {
                new LogHelper().LogOperationStart("Integrar");
                request.CpfCnpj.OnlyNumbers();

                var lCpfCnpjCadastrado =
                    Repository.Query.FirstOrDefault(o => o.CpfCnpj == request.CpfCnpj && o.EmpresaId == User.EmpresaId);

                if (lCpfCnpjCadastrado != null)
                {
                    return new RespPadraoApi
                    {
                        sucesso = false,
                        mensagem = "Cliente já cadastrado"
                    };
                }

                var cidade = _cidadeAppService.ConsultarPorIbge(request.IbgeCidade).Result;

                var clienteRequest = Mapper.Map<ClienteRequest>(request);

                if (cidade != null)
                {
                    clienteRequest.Id = string.IsNullOrEmpty(clienteRequest.Id) ? "0" : clienteRequest.Id;
                    clienteRequest.CidadeId = cidade.Id;
                    clienteRequest.EstadoId = cidade.EstadoId;
                }

                var response = await Save(clienteRequest);
                return new RespPadraoApi(response);
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar Integrar");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("Integrar");
            }
        }
    }
}
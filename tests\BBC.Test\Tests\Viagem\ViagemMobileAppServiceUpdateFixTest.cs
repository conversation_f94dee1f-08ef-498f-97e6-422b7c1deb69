using System;
using System.Linq;
using System.Threading.Tasks;
using BBC.Test.Tests.Viagem.Fixture;
using Moq;
using SistemaInfo.BBC.Application.Objects.Mobile.Viagem.Request;
using SistemaInfo.BBC.Application.Services.Viagem;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Domain.Models.PagamentoEvento.Repository;
using SistemaInfo.BBC.Domain.Models.Transacao.Repository;
using Xunit;

namespace BBC.Test.Tests.Viagem
{
    /// <summary>
    /// Teste para verificar se a correção do método Update funciona corretamente
    /// </summary>
    [Collection(nameof(ViagemCollection))]
    public class ViagemMobileAppServiceUpdateFixTest
    {
        private readonly ViagemFixture _fixture;
        private readonly ViagemMobileAppService _appService;
        private readonly Mock<IPagamentoEventoReadRepository> _pagamentoEventoReadRepository;
        private readonly Mock<IPagamentoEventoWriteRepository> _pagamentoEventoWriteRepository;
        private readonly Mock<ITransacaoReadRepository> _transacaoReadRepository;

        public ViagemMobileAppServiceUpdateFixTest(ViagemFixture fixture)
        {
            _fixture = fixture;
            _pagamentoEventoReadRepository = fixture.Mocker.GetMock<IPagamentoEventoReadRepository>();
            _pagamentoEventoWriteRepository = fixture.Mocker.GetMock<IPagamentoEventoWriteRepository>();
            _transacaoReadRepository = fixture.Mocker.GetMock<ITransacaoReadRepository>();
            _appService = fixture.Mocker.CreateInstance<ViagemMobileAppService>();
        }

        [Fact(DisplayName = "AlterarStatusAntecipacao - Verificar se Update é chamado corretamente")]
        [Trait(nameof(ViagemMobileAppService), "UpdateFix")]
        public async Task AlterarStatusAntecipacao_VerificarChamadaUpdate_DeveUsarUpdateSincrono()
        {
            // Arrange
            var request = _fixture.GerarAtualizarStatusAntecipacaoRequest(
                status: StatusAntecipacaoRequest.AguardandoProcessamento);
            
            var pagamentoEventoConsulta = _fixture.GerarPagamentoEventoComStatusAntecipacao(
                StatusAntecipacaoParcelaProprietario.AguardandoProcessamento);
            
            var pagamentoEventoUpdate = _fixture.GerarPagamentoEventoComStatusAntecipacao(
                StatusAntecipacaoParcelaProprietario.AguardandoProcessamento);
            
            pagamentoEventoConsulta.Viagem.Id = request.ViagemId;
            pagamentoEventoConsulta.Id = request.PagamentoId;
            pagamentoEventoUpdate.Id = request.PagamentoId;

            // Setup mocks para a primeira consulta (com AsNoTracking)
            _pagamentoEventoReadRepository
                .Setup(x => x.AsNoTracking())
                .Returns(CreateMockDbSet(new[] { pagamentoEventoConsulta }).Object);

            // Setup mock para a segunda consulta (para update - com tracking)
            _pagamentoEventoReadRepository
                .Setup(x => x.FirstOrDefaultAsync(It.IsAny<System.Linq.Expressions.Expression<System.Func<SistemaInfo.BBC.Domain.Models.PagamentoEvento.PagamentoEvento, bool>>>()))
                .ReturnsAsync(pagamentoEventoUpdate);

            // Setup mock para transações (vazio para não ter transação de retenção)
            _transacaoReadRepository
                .Setup(x => x.AsNoTracking())
                .Returns(CreateMockDbSet(new SistemaInfo.BBC.Domain.Models.Transacao.Transacao[0]).Object);

            // Setup mocks para o write repository
            _pagamentoEventoWriteRepository
                .Setup(x => x.Update(It.IsAny<SistemaInfo.BBC.Domain.Models.PagamentoEvento.PagamentoEvento>()));

            _pagamentoEventoWriteRepository
                .Setup(x => x.SaveChangesAsync())
                .Returns(Task.FromResult(0));

            // Act
            var result = await _appService.AlterarStatusAntecipacao(request);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(request.PagamentoId, result.Id);
            Assert.Equal(StatusAntecipacaoParcelaProprietario.Disponivel, result.StatusAntecipacaoParcelaProprietario);
            
            // Verificar que Update (síncrono) foi chamado, não UpdateAsync
            _pagamentoEventoWriteRepository.Verify(x => x.Update(It.IsAny<SistemaInfo.BBC.Domain.Models.PagamentoEvento.PagamentoEvento>()), Times.Once);
            _pagamentoEventoWriteRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
            
            // Verificar que UpdateAsync NÃO foi chamado
            _pagamentoEventoWriteRepository.Verify(x => x.UpdateAsync(It.IsAny<SistemaInfo.BBC.Domain.Models.PagamentoEvento.PagamentoEvento>()), Times.Never);
        }

        [Fact(DisplayName = "AlterarStatusAntecipacao - Verificar se propriedades são atualizadas")]
        [Trait(nameof(ViagemMobileAppService), "UpdateFix")]
        public async Task AlterarStatusAntecipacao_VerificarPropriedadesAtualizadas_DeveAlterarStatusEData()
        {
            // Arrange
            var request = _fixture.GerarAtualizarStatusAntecipacaoRequest(
                status: StatusAntecipacaoRequest.Aprovado);
            
            var pagamentoEventoConsulta = _fixture.GerarPagamentoEventoComStatusAntecipacao(
                StatusAntecipacaoParcelaProprietario.AguardandoProcessamento);
            
            var pagamentoEventoUpdate = _fixture.GerarPagamentoEventoComStatusAntecipacao(
                StatusAntecipacaoParcelaProprietario.AguardandoProcessamento);
            
            pagamentoEventoConsulta.Viagem.Id = request.ViagemId;
            pagamentoEventoConsulta.Id = request.PagamentoId;
            pagamentoEventoUpdate.Id = request.PagamentoId;

            var dataAnterior = pagamentoEventoUpdate.DataAlteracao;

            // Setup mocks
            _pagamentoEventoReadRepository
                .Setup(x => x.AsNoTracking())
                .Returns(CreateMockDbSet(new[] { pagamentoEventoConsulta }).Object);

            _pagamentoEventoReadRepository
                .Setup(x => x.FirstOrDefaultAsync(It.IsAny<System.Linq.Expressions.Expression<System.Func<SistemaInfo.BBC.Domain.Models.PagamentoEvento.PagamentoEvento, bool>>>()))
                .ReturnsAsync(pagamentoEventoUpdate);

            _transacaoReadRepository
                .Setup(x => x.AsNoTracking())
                .Returns(CreateMockDbSet(new SistemaInfo.BBC.Domain.Models.Transacao.Transacao[0]).Object);

            _pagamentoEventoWriteRepository
                .Setup(x => x.Update(It.IsAny<SistemaInfo.BBC.Domain.Models.PagamentoEvento.PagamentoEvento>()));

            _pagamentoEventoWriteRepository
                .Setup(x => x.SaveChangesAsync())
                .Returns(Task.FromResult(0));

            // Act
            var result = await _appService.AlterarStatusAntecipacao(request);

            // Assert
            Assert.NotNull(result);
            
            // Verificar que as propriedades da entidade foram alteradas
            Assert.Equal(StatusAntecipacaoParcelaProprietario.Aprovado, pagamentoEventoUpdate.StatusAntecipacaoParcelaProprietario);
            Assert.True(pagamentoEventoUpdate.DataAlteracao > dataAnterior);
            
            // Verificar que o resultado mapeado está correto
            Assert.Equal(StatusAntecipacaoParcelaProprietario.Aprovado, result.StatusAntecipacaoParcelaProprietario);
        }

        private Mock<System.Linq.IQueryable<T>> CreateMockDbSet<T>(T[] data) where T : class
        {
            var queryable = data.AsQueryable();
            var mockSet = new Mock<System.Linq.IQueryable<T>>();
            mockSet.As<System.Linq.IQueryable<T>>().Setup(m => m.Provider).Returns(queryable.Provider);
            mockSet.As<System.Linq.IQueryable<T>>().Setup(m => m.Expression).Returns(queryable.Expression);
            mockSet.As<System.Linq.IQueryable<T>>().Setup(m => m.ElementType).Returns(queryable.ElementType);
            mockSet.As<System.Linq.IQueryable<T>>().Setup(m => m.GetEnumerator()).Returns(queryable.GetEnumerator());
            return mockSet;
        }
    }
}

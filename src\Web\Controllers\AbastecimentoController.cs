using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using SistemaInfo.BBC.Application.Interface.Abastecimento;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.Abastecimento;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Web.Attributes;
using SistemaInfo.BBC.Web.Controllers.Base;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;

namespace SistemaInfo.BBC.Web.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [Route("Abastecimento")]
    public class AbastecimentoController : WebControllerBase<IAbastecimentoAppService>
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="engine"></param>
        /// <param name="appService"></param>
        public AbastecimentoController(IAppEngine engine, IAbastecimentoAppService appService) : base(engine, appService)
        {
        }
        
        /// <summary>
        /// Consulta grid de abastecimentos
        /// </summary>
        /// <returns>Dados do posto.</returns>
        /// <remarks>
        /// Última alteração: 26/11/2024 - Correção de vulnerabilidade (SIE 990).
        /// Controle: Rede BBC - item 13.
        /// Alteração: Remoção de parametro IdPosto da consulta.
        /// </remarks>
        [HttpPost("ConsultarGridAbastecimento")]
        [Menu(new [] { EMenus.LancamentoAbastecimento })]
        public async Task<JsonResult> ConsultarGridAbastecimento([FromBody]DtoConsultaGridAbastecimento request)
        {
            
            var consultarGridAbastecimento = await AppService.ConsultarGridAbastecimento(Engine.User.AdministradoraId, request.Take, 
                request.Page, request.Order, request.Filters, request.DataInicial, request.DataFinal);
            return ResponseBase.ResponderSucesso(consultarGridAbastecimento);
        }
        
        /// <summary>
        /// Consulta grid de abastecimentos
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("ConsultarGridPainelAbastecimento")]
        public JsonResult ConsultarGridAbastecimento([FromBody]DtoConsultaGridPainelAbastecimento request)
        {
            var consultarGridAbastecimento = AppService.ConsultarGridPainelAbastecimento(request);
            return ResponseBase.ResponderSucesso(consultarGridAbastecimento);
        }
        
        /// <summary>
        /// Consulta relação de abastecimentos para lote em painel financeiro no posto
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [Menu(new[] { EMenus.PainelFinanceiro, EMenus.PainelFinanceiroPosto })]
        [HttpPost("ConsultarGridLoteAbastecimentos")]
        public JsonResult ConsultarGridLoteAbastecimentos([FromBody]DtoConsultaGridFinanceiroLoteAbastecimento request)
        {
            var consultarGridLoteAbastecimento = AppService.ConsultarGridLoteAbastecimentos(Engine.User.AdministradoraId, request.PagamentoAbastecimento, request.Take, request.Page, request.Order, request.Filters);
            return consultarGridLoteAbastecimento == null ? ResponseBase.ResponderErro("Você não tem permissão para acessar essas informações") : ResponseBase.ResponderSucesso(consultarGridLoteAbastecimento);
        }
        
        /// <summary>
        /// Consulta relação de abastecimentos para lote em painel financeiro no controle
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [Menu(new []{EMenus.PainelFinanceiro})]
        [HttpPost("ConsultarGridLoteAbastecimentosControle")]
        public JsonResult ConsultarGridLoteAbastecimentosControle([FromBody]DtoConsultaGridFinanceiroLoteAbastecimento request)
        {
            var consultarGridLoteAbastecimento = AppService.ConsultarGridLoteAbastecimentos(Engine.User, request.PagamentoAbastecimento, request.Take, request.Page, request.Order, request.Filters);
            return consultarGridLoteAbastecimento == null ? ResponseBase.ResponderErro("Você não tem permissão para acessar essas informações") : ResponseBase.ResponderSucesso(consultarGridLoteAbastecimento);
        }
        
        /// <summary>
        /// Consulta relação de abastecimentos para lote em painel de pagamento abastecimento
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [Menu(new []{EMenus.PainelFinanceiro})]
        [HttpPost("ConsultarGridLoteAbastecimento")]
        public JsonResult ConsultarGridLoteAbastecimento([FromBody]DtoConsultaGridLoteAbastecimento request)
        {
            var consultarGridLoteAbastecimento = AppService.ConsultarGridLoteAbastecimento( request.LotePagamento, request.Take, request.Page, request.Order, request.Filters);
            return ResponseBase.ResponderSucesso(consultarGridLoteAbastecimento);
        }
        
        /// <summary>
        /// Consulta relação de abastecimento por protocolo
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [Menu(new [] { EMenus.ProtocoloAbastecimento, EMenus.PainelProtocoloAbastecimento })]
        [HttpPost("ConsultarGridProtocoloAbastecimento")]
        public JsonResult ConsultarGridProtocoloAbastecimento([FromBody]DtoConsultaGridProtocoloAbastecimento request)
        {
            var consultarGridProtocoloAbastecimento = AppService.ConsultarGridProtocoloAbastecimento( request.Protocolo, request.Take, request.Page, request.Order, request.Filters);
            return ResponseBase.ResponderSucesso(consultarGridProtocoloAbastecimento);
        }

        /// <summary>
        /// Metodo de salvar
        /// </summary>
        /// <param name="lAbastecimentoReq"></param>
        /// <returns></returns>
        [HttpPost("Salvar")]
        [Menu(new[] { EMenus.LancamentoAbastecimento })]
        public JsonResult SaveAbastecimento([FromBody]AbastecimentoRequest lAbastecimentoReq)
        {
            try
            {
                var lSaveAbastecimento = AppService.Save(lAbastecimentoReq).Result;
                
                return ResponseBase.BigJson(lSaveAbastecimento);
            }
            catch (Exception e)
            {
                return ResponseBase.ResponderErro("Não foi possível realizar a operação. Mensagem: " + e.Message);
            }
        }
        
        /// <summary>
        /// Consulta por ID do abastecimento
        /// </summary>
        /// <param name="idAbastecimento"></param>
        /// <returns></returns>
        [HttpGet("ConsultarPorId")]
        public JsonResult ConsultarPorId(int idAbastecimento)
        {
            try
            {
                var consultarAbastecimento = AppService.ConsultarPorId(idAbastecimento, "");
                return ResponseBase.ResponderSucesso(consultarAbastecimento);
            }
            catch (Exception e)
            {
                return ResponseBase.ResponderErro("Abastecimento não encontrado! Mensagem: " + e.Message);
            }
        }
        
        /// <summary>
        /// Consulta de funcionario
        /// </summary>
        /// <param name="portadorCpfCnpj"></param>
        /// <returns></returns>
        [HttpGet("ConsultaFuncionario")]
        [Menu(new[] { EMenus.LancamentoAbastecimento })]
        public async Task<JsonResult> ConsultarFuncionario(string portadorCpfCnpj)
        {
            var retorno = await AppService.ConsultarFuncionario(portadorCpfCnpj);
            return ResponseBase.Responder(retorno.sucesso, retorno.mensagem, retorno.data);
        }
    }
}
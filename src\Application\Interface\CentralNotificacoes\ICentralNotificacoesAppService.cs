using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.CentralNotificacoes;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Models.Notificacao.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;

namespace SistemaInfo.BBC.Application.Interface.CentralNotificacoes
{
    public interface ICentralNotificacoesAppService : IAppService<Domain.Models.Notificacao.Notificacao, INotificacaoReadRepository, INotificacaoWriteRepository>
    {
        Task<ConsultarGridCentralNotificacoesResponse> ConsultarGridCentralNotificacoes(int EmpresaId, String dataInicial, String dataFinal, int Perfil, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
        Task<ConsultarGridCentralNotificacoesValePedagioResponse> ConsultarGridCentralNotificacoesValePedagio(ConsultarGridCentralNotificacoesValePedagioRequest request);
        CentralNotificacoesResponse ConsultarPorId(int idCentralNotificacoes);
        Task<RespPadrao> Save(CentralNotificacoesRequest request);
    }
}
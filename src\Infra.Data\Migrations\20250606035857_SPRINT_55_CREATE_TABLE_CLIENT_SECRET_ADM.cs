﻿using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using System;
using System.Collections.Generic;

namespace SistemaInfo.BBC.Infra.Data.Migrations
{
    public partial class SPRINT_55_CREATE_TABLE_CLIENT_SECRET_ADM : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "ClientSecretAdm",
                schema: "BBC",
                columns: table => new
                {
                    Id = table.Column<int>(type: "serial", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.SerialColumn),
                    Ativo = table.Column<int>(type: "int", nullable: false),
                    ClientSecret = table.Column<string>(type: "varchar(300)", nullable: false),
                    DataAlteracao = table.Column<DateTime>(type: "timestamp", nullable: true),
                    DataCadastro = table.Column<DateTime>(type: "timestamp", nullable: false),
                    Descricao = table.Column<string>(type: "varchar(200)", nullable: true),
                    Login = table.Column<string>(type: "varchar(100)", nullable: false),
                    Senha = table.Column<string>(type: "varchar(300)", nullable: false),
                    UsuarioAlteracaoId = table.Column<int>(type: "int", nullable: true),
                    UsuarioCadastroId = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ClientSecretAdm", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ClientSecretAdm_Usuario_UsuarioAlteracaoId",
                        column: x => x.UsuarioAlteracaoId,
                        principalSchema: "BBC",
                        principalTable: "Usuario",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_ClientSecretAdm_Usuario_UsuarioCadastroId",
                        column: x => x.UsuarioCadastroId,
                        principalSchema: "BBC",
                        principalTable: "Usuario",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "IX_ClientSecretAdm_UsuarioAlteracaoId",
                schema: "BBC",
                table: "ClientSecretAdm",
                column: "UsuarioAlteracaoId");

            migrationBuilder.CreateIndex(
                name: "IX_ClientSecretAdm_UsuarioCadastroId",
                schema: "BBC",
                table: "ClientSecretAdm",
                column: "UsuarioCadastroId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "ClientSecretAdm",
                schema: "BBC");
        }
    }
}

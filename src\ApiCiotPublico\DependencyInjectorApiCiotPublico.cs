﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using SistemaInfo.BBC.Infra.Data.Context;
using SistemaInfo.Framework.CQRS.Message;
using SistemaInfo.TIP.ApiCiot.Filters.SistemaInfo.Auth.Bus.Common.Infra.Security;

namespace SistemaInfo.BBC.ApiCiotPublico
{
    /// <summary>
    ///
    /// </summary>
    public class DependencyInjectorApiCiotPublico
    {
        /// <summary>
        /// Registrar serviços necessários para funcionamento da aplicação
        /// </summary>
        /// <param name="services"></param>
        public static void RegisterServices(IServiceCollection services)
        {
            AddAppInfrastructure(services);
            AddAppServices(services);
        }

        /// <summary>
        /// Recuros ténicos para funcionamento da aplicação, framework's de baixo nível da aplicação
        /// </summary>
        /// <param name="services"></param>
        private static void AddAppInfrastructure(IServiceCollection services)
        {
            services.AddScoped<ConfigMigrator>();
            services.TryAddSingleton<IHttpContextAccessor, HttpContextAccessor>();
           // services.AddScoped<IAuthorizationHandler, JwtAuthorizationHandler>();            
            services.AddSingleton<IPublishMessageBusConfigurations>(provider => null);
            
            services.AddSingleton<IPublishMessageBusConfigurations>(provider => null);
        }

        /// <summary>
        /// Serviços de consultas para obter informações
        /// </summary>
        /// <param name="services"></param>
        private static void AddAppServices(IServiceCollection services)
        {
        }
    }
}
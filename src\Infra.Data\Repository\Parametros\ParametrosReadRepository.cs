using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using NLog;
using SistemaInfo.BBC.Domain.Models.Parametros.Repository;
using SistemaInfo.BBC.Infra.Data.Context;
using SistemaInfo.Framework.DomainDrivenDesign.Infra.Repository;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Infra.Data.Repository.Parametros
{
    public abstract class ParametrosBaseReadRepository<TContext, TParametrosEntity> : ReadOnlyRepository<TParametrosEntity, TContext>, IParametrosBaseReadRepository<TParametrosEntity>
        where TContext : DbContext
        where TParametrosEntity : Domain.Models.Parametros.Parametros
    {
        public ParametrosBaseReadRepository(TContext context) : base(context)
        {
        }
    }
    public class ParametrosReadRepository : ParametrosBaseReadRepository<ConfigContext, Domain.Models.Parametros.Parametros>, IParametrosReadRepository
    {
        private readonly IParametrosWriteRepository _parametrosWriteRepository;

        public ParametrosReadRepository(ConfigContext context, IParametrosWriteRepository parametrosWriteRepository) 
            : base(context)
        {
            _parametrosWriteRepository = parametrosWriteRepository;
        }

        public async Task<int> GetPeriodoMaximoInatividadeSenhaProvisoria()
        {
            var lParametro = await GetParametrosAsync(-1,
                Domain.Models.Parametros.Parametros.TipoDoParametro.PeriodoMaximoInatividadeSenhaProvisoria,
                Domain.Models.Parametros.Parametros.TipoDoValor.Number);
            
            return lParametro.Valor.ToIntSafe();
        }
        
        public async Task<int> GetPeriodoMaximoInatividadePortador()
        {
            var lParametro = await GetParametrosAsync(-1,
                Domain.Models.Parametros.Parametros.TipoDoParametro.PeriodoMaximoInatividadePortador,
                Domain.Models.Parametros.Parametros.TipoDoValor.Number);
            
            return lParametro.Valor.ToIntSafe();
        }

        public async Task<int> GetTempoInatividadeUsuarioAsync()
        {
            var lParametro = await FirstOrDefaultAsync(x =>
                x.TipoParametros == Domain.Models.Parametros.Parametros.TipoDoParametro.TempoMaximoUsuarioInativo);
            if (lParametro != null)
            {
                return lParametro.Valor.ToIntSafe();
            }
            return 0;
        }

        public async Task<decimal> GetMargemErroXml()
        {
            var lParametro = await FirstOrDefaultAsync(x =>
                x.TipoParametros == Domain.Models.Parametros.Parametros.TipoDoParametro.MargemErroArredondamentoXmlProtocolo);
            return lParametro != null ? lParametro.Valor.Replace(".",",").ToDecimalSafe() : 0;
        }
        
        public async Task<decimal> GetMargemTotalAbastecimentoXml()
        {
            var lParametro = await FirstOrDefaultAsync(x =>
                x.TipoParametros == Domain.Models.Parametros.Parametros.TipoDoParametro.MargemErroTotalAbastecimentoXmlProtocolo);
            return lParametro != null ? lParametro.Valor.Replace(".",",").ToDecimalSafe() : 0;
        }
        
        public async Task<int> GetTotalTentativaErroSenhaPortadorFrota()
        {
            var lParametro = await FirstOrDefaultAsync(x =>
                x.TipoParametros == Domain.Models.Parametros.Parametros.TipoDoParametro.QuantidadeErroSenhaPortadorFrota);
            if (lParametro != null)
            {
                return lParametro.Valor.ToIntSafe();
            }
            return 5; // Valor padrao solicitado na Análise - BBC - Cadastro Usuário App Frota.doc 
        }
        
        
        public async Task<decimal> GetMargemLitragemXml()
        {
            var lParametro = await FirstOrDefaultAsync(x =>
                x.TipoParametros == Domain.Models.Parametros.Parametros.TipoDoParametro.MargemArredondamentoCasasDecimaisLitragemXml);
            return lParametro != null ? lParametro.Valor.Replace(".",",").ToDecimalSafe() : 0;
        }
        
        

        public async Task<Domain.Models.Parametros.Parametros> GetParametrosAsync(int id, Domain.Models.Parametros.Parametros.TipoDoParametro tipoDoParametro, Domain.Models.Parametros.Parametros.TipoDoValor tipoDoValor)
        {
            var log = LogManager.GetCurrentClassLogger();
            try
            {

                var lParametro = await Context.Parametros
                .FirstOrDefaultAsync(p =>
                    p.ReferenciaId == id && p.TipoParametros == tipoDoParametro);

                if (lParametro != null)
                {
                    return lParametro;
                }

                var lValorDefault = tipoDoValor == Domain.Models.Parametros.Parametros.TipoDoValor.Number ? "0" : "";
                return _parametrosWriteRepository.SaveParametro(id, tipoDoParametro, lValorDefault, null, tipoDoValor);

            }
            catch (Exception e)
            {
                log.Fatal(e);
                throw;
            }
        }
        
        
        public async Task<Domain.Models.Parametros.Parametros> GetParametroVoidAsync(int id, Domain.Models.Parametros.Parametros.TipoDoParametro tipoDoParametro, Domain.Models.Parametros.Parametros.TipoDoValor tipoDoValor)
        {
            var log = LogManager.GetCurrentClassLogger();
            try
            {

                var lParametro = await Context.Parametros
                    .FirstOrDefaultAsync(p =>
                        p.ReferenciaId == id && p.TipoParametros == tipoDoParametro);

                if (lParametro != null)
                {
                    return lParametro;
                }
                
                return _parametrosWriteRepository.SaveParametro(id, tipoDoParametro, null, null, tipoDoValor);
            }
            catch (Exception e)
            {
                log.Fatal(e);
                throw;
            }
        }

        public async Task<List<Domain.Models.Parametros.Parametros>> GetParametrosListAsync(int id, Domain.Models.Parametros.Parametros.TipoDoParametro tipoDoParametro, Domain.Models.Parametros.Parametros.TipoDoValor tipoDoValor)
        {
            var lParametro = await Context.Parametros
                .Where(p => p.ReferenciaId == id && p.TipoParametros == tipoDoParametro)
                .ToListAsync();

            return lParametro;
        }

        public async Task<Domain.Models.Parametros.Parametros> GetByTipoDoParametroAsync(
            Domain.Models.Parametros.Parametros.TipoDoParametro tipoDoParametro)
        {
            return await FirstOrDefaultAsync(c => c.TipoParametros == tipoDoParametro);
        }
        
        public async Task<Domain.Models.Parametros.Parametros> GetParametroLimitePagamentoPedagio()
        {
            return await FirstOrDefaultAsync(p =>
                p.TipoParametros ==
                Domain.Models.Parametros.Parametros.TipoDoParametro.ValorMaximoPagamentoValePedagio);
        }

        public async Task<Domain.Models.Parametros.Parametros> GetPrazoMaximoCancelamentoPagamento()
        {
            return await GetParametrosAsync(-3,
                Domain.Models.Parametros.Parametros.TipoDoParametro.PrazoMaximaParaCancelamentoPagamentoPedagio,
                Domain.Models.Parametros.Parametros.TipoDoValor.Number);
        }
    }
}
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Newtonsoft.Json;
using NLog;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Domain.Models.Veiculo.Exeptions;
using SistemaInfo.BBC.Domain.Models.Veiculo.Repository;
using SistemaInfo.Framework.CQRS;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using SistemaInfo.Framework.DomainDrivenDesign.Infra.CQRS;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Domain.Models.Veiculo.Commands.CommandHandlers
{
    public class VeiculoCommandHandler : CommandHandler<Veiculo, IVeiculoReadRepository, IVeiculoWriteRepository>,
        IHandler<VeiculoSalvarCommand>,
        IHandler<VeiculoAlterarStatusCommand>,
        IHandler<VeiculoSalvarComRetornoCommand, Veiculo>
    {
        public VeiculoCommandHandler(IAppEngine engine, IVeiculoReadRepository readRepository, IVeiculoWriteRepository writeRepository) : base(engine, readRepository, writeRepository)
        {
        }

        public async Task HandlerAsync(VeiculoSalvarCommand request)
        {    
            var lLog = LogManager.GetCurrentClassLogger();
            try
            {
                var lVeiculo = PrepararVeiculoSave(request); 
                if (request.Id == 0)
                {
                    Repository.Command.Add(lVeiculo);
                }else if (request.Id > 0)
                {
                    var veiculo =  Repository.Query.GetById(request.Id);
                    
                    Mapper.Map(request, veiculo);
                    veiculo.Status = StatusVeiculo.Ativo;
                    veiculo.DataCadastro = DateTime.Now;
                    veiculo.UsuarioCadastroId = User.Id;
                    veiculo.EmpresaId = request.EmpresaId;
                    Repository.Command.Update(veiculo);
                }
                await SaveChangesAsync();
            }
            catch (Exception e)
            {
                lLog.Error(e,"Erro ao salvar o Veículo, erro: ");
                throw new VeiculoException("Erro ao salvar!");
            }
        }

        public async Task<Veiculo> HandlerAsync(VeiculoSalvarComRetornoCommand request)
        {
            var lLog = LogManager.GetCurrentClassLogger();
            try
            {
                var lVeiculo = PrepararVeiculoSave(request); 
                
                lVeiculo.VeiculoCombustiveis = new List<VeiculoCombustivel.VeiculoCombustivel>();
                
                lLog.Info(JsonConvert.SerializeObject(lVeiculo));

                if (lVeiculo.Id == 0)
                {
                    Repository.Command.Add(lVeiculo);
                    
                    
                    if (request.VeiculoCombustiveis != null)
                    {
                        foreach (var lCombustivel in request.VeiculoCombustiveis)
                        {
                            var veiculoCombustiveis = new VeiculoCombustivel.VeiculoCombustivel();

                            veiculoCombustiveis.CombustivelId = lCombustivel.CombustivelId;
                            veiculoCombustiveis.VeiculoId = lCombustivel.VeiculoId == 0 ? lVeiculo.Id : lCombustivel.VeiculoId;
                            veiculoCombustiveis.Autonomia = lCombustivel.Autonomia;
                            veiculoCombustiveis.Capacidade = lCombustivel.Capacidade;
                            veiculoCombustiveis.DataCadastro = DateTime.Now;

                            lVeiculo.VeiculoCombustiveis.Add(veiculoCombustiveis);
                        } 
                    }
                    
                    
                }else if (lVeiculo.Id > 0)
                {
                    var veiculo = Repository.Query.GetById(request.Id);
                
                    var combustivel =  request.VeiculoCombustiveis
                        .Select(c => new VeiculoCombustivel.VeiculoCombustivel
                        {
                            Id = c.Id,
                            CombustivelId = c.CombustivelId,
                            VeiculoId = c.VeiculoId,
                            Autonomia = c.Autonomia,
                            Capacidade = c.Capacidade,
                            UsuarioCadastroId = c.UsuarioCadastroId,
                            UsuarioAlteracaoId = c.UsuarioAlteracaoId,
                            DataAlteracao = c.DataAlteracao,
                            DataCadastro = c.DataCadastro
                        })
                        .ToList();
                    
                    Mapper.Map(request, veiculo);
                    veiculo.Status = StatusVeiculo.Ativo;
                    veiculo.DataCadastro = DateTime.Now;
                    veiculo.UsuarioCadastroId = User.Id;
                    veiculo.EmpresaId = request.EmpresaId;
                    var combustiveisAntigos = veiculo.VeiculoCombustiveis
                        .Select(c => new VeiculoCombustivel.VeiculoCombustivel
                        {
                            Id = c.Id,
                            CombustivelId = c.CombustivelId,
                            VeiculoId = c.VeiculoId,
                            Autonomia = c.Autonomia,
                            Capacidade = c.Capacidade,
                            UsuarioCadastroId = c.UsuarioCadastroId,
                            UsuarioAlteracaoId = c.UsuarioAlteracaoId,
                            DataAlteracao = c.DataAlteracao,
                            DataCadastro = c.DataCadastro
                        })
                        .ToList();
                    
                    veiculo.VeiculoCombustiveis.Clear();
                    Repository.Command.Update(veiculo);
                    Repository.Command.RemoveVeiculoCombustiveis(request.Id);
                    if (combustivel.Any())
                    {
                        foreach (var combustivelRequest in combustivel)
                        {
                            // Verifica se já existia antes, pra manter DataCadastro original
                            var combustivelAntigo = combustiveisAntigos
                                .FirstOrDefault(c => c.CombustivelId == combustivelRequest.CombustivelId);

                            var novoCombustivel = new VeiculoCombustivel.VeiculoCombustivel
                            {
                                CombustivelId = combustivelRequest.CombustivelId,
                                Capacidade = combustivelRequest.Capacidade,
                                Autonomia = combustivelRequest.Autonomia,
                                UsuarioCadastroId = User.Id,
                                VeiculoId = veiculo.Id,
                                DataCadastro = combustivelAntigo?.DataCadastro ?? DateTime.Now
                            };

                            veiculo.VeiculoCombustiveis.Add(novoCombustivel);
                            Repository.Command.AddVeiculoCombustiveis(novoCombustivel);
                        }
                    }
                }
                await SaveChangesAsync();
                return lVeiculo;
            }
            catch (Exception e)
            {
                lLog.Error(e,"Erro ao salvar o Veículo, erro: ");
                throw new VeiculoException("Erro ao salvar!");
            }
        }

        private Veiculo PrepararVeiculoSave(VeiculoSalvarCommand request)
        {
            var lVeiculo = Mapper.Map<Veiculo>(request);
            
            lVeiculo.Status = StatusVeiculo.Ativo;
            lVeiculo.DataCadastro = DateTime.Now;
            lVeiculo.UsuarioCadastroId = User.Id;
            lVeiculo.EmpresaId = request.EmpresaId;

            return lVeiculo;
        }
        
        public async Task HandlerAsync(VeiculoAlterarStatusCommand request)
        {
            var lVeiculo =  Repository.Query.GetById(request.Id);

            if (lVeiculo.Status == StatusVeiculo.Ativo)
            {
                lVeiculo.Status = StatusVeiculo.Bloqueado;
                lVeiculo.DataBloqueio = DateTime.Now;
                lVeiculo.UsuarioBloqueioId = User.Id;
            }
            else
            {
                lVeiculo.Status = StatusVeiculo.Ativo;
                lVeiculo.DataDesbloqueio = DateTime.Now;
                lVeiculo.UsuarioDesbloqueioId = User.Id;
            }
            
            await SaveChangesAsync();
        }
    }
}
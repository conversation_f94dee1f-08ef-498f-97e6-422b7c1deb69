using System.Collections.Generic;
using SistemaInfo.BBC.Domain.External.CIOT.DTO;

namespace SistemaInfo.BBC.Domain.Contracts.Operacoes
{
    
    public class ConsultarNaturezaCargaPorIdReqMessage
    {
        public string Codigo { get; set; }
    }
    
    public class ConsultarNaturezaCargaPorIdRespMessage
    {
        public ConsultarNaturezaCargaPorIdRespMessage(bool sucesso, string excecao)
        {
            Sucesso = sucesso;
            Erro = new Excecao()
            {
                Mensagem = excecao
            };
        }
        public ConsultarNaturezaCargaPorIdRespMessage() { }
        
        public bool Sucesso { get; set; }
        public Excecao Erro { get; set; }
        public string Codigo { get; set; }
        public string Descricao { get; set; }
    }
}
using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using NLog;
using SistemaInfo.BBC.Application.Interface.Mensagem;
using SistemaInfo.BBC.Application.Interface.PagamentoEvento;
using SistemaInfo.BBC.Application.Interface.Transacao;
using SistemaInfo.BBC.Application.Interface.Viagem;
using SistemaInfo.BBC.Application.Objects.Api.Transacao;
using SistemaInfo.BBC.Application.Objects.Api.Viagem;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Mobile.Viagem.Request;
using SistemaInfo.BBC.Application.Objects.Mobile.Viagem.Response;
using SistemaInfo.BBC.Application.Objects.Web.Mensagem;
using SistemaInfo.BBC.Application.Utils;
using SistemaInfo.BBC.Domain.CloudTranslationService;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Domain.External.Conductor.DTO.Transferencia;
using SistemaInfo.BBC.Domain.External.Conductor.Interface;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.BBC.Domain.Models.Empresa.Repository;
using SistemaInfo.BBC.Domain.Models.Notificacao;
using SistemaInfo.BBC.Domain.Models.Notificacao.Commands;
using SistemaInfo.BBC.Domain.Models.PagamentoEvento.Repository;
using SistemaInfo.BBC.Domain.Models.Parametros.Repository;
using SistemaInfo.BBC.Domain.Models.Transacao.Repository;
using SistemaInfo.BBC.Domain.Models.Viagem.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.Services.Viagem
{
    public class ViagemMobileAppService :
        AppService<Domain.Models.Viagem.Viagem, IViagemReadRepository, IViagemWriteRepository>,
        IViagemMobileAppService
    {
        
        private readonly ICartaoRepository _cartaoRepository;
        private readonly IPagamentoEventoReadRepository _pagamentoEventoReadRepository;
        private readonly IPagamentoEventoWriteRepository _pagamentoEventoWriteRepository;
        private readonly ITransferenciaRepository _transferenciaRepository;
        private readonly ITransacaoReadRepository _transacaoReadRepository;
        private readonly IEmpresaReadRepository _empresaReadRepository;
        private readonly ITransacaoRegisterAppService _transacaoRegisterAppService;
        private readonly IParametrosReadRepository _parametrosReadRepository;
        private readonly ITransacaoWriteRepository _transacaoWriteRepository;
        private readonly ICloudTranslationService _cloudTranslationService;
        private readonly IPagamentoEventoAppSerivce _pagamentoEventoAppService;
        
        
        private readonly IMensagemAppService _mensagemAppService;
        
        public ViagemMobileAppService(IAppEngine engine,
            ICartaoRepository cartaoRepository,
            IViagemReadRepository readRepository,
            IViagemWriteRepository writeRepository,
            IPagamentoEventoReadRepository pagamentoEventoReadRepository,
            ITransferenciaRepository transferenciaRepository,
            IPagamentoEventoWriteRepository pagamentoEventoWriteRepository,
            ITransacaoReadRepository transacaoReadRepository,
            IEmpresaReadRepository empresaReadRepository,
            IParametrosReadRepository parametrosReadRepository,
            ITransacaoRegisterAppService transacaoRegisterAppService,
            ITransacaoWriteRepository transacaoWriteRepository,
            ICloudTranslationService cloudTranslationService,
            IMensagemAppService mensagemAppService,
            IPagamentoEventoAppSerivce pagamentoEventoAppService)
            : base(engine, readRepository, writeRepository)
        {
            _cartaoRepository = cartaoRepository;
            _pagamentoEventoReadRepository = pagamentoEventoReadRepository;
            _transferenciaRepository = transferenciaRepository;
            _pagamentoEventoWriteRepository = pagamentoEventoWriteRepository;
            _transacaoReadRepository = transacaoReadRepository;
            _empresaReadRepository = empresaReadRepository;
            _transacaoRegisterAppService = transacaoRegisterAppService;
            _parametrosReadRepository = parametrosReadRepository;
            _transacaoWriteRepository = transacaoWriteRepository;
            _cloudTranslationService = cloudTranslationService;
            _mensagemAppService = mensagemAppService;
            _pagamentoEventoAppService = pagamentoEventoAppService;
        }
        
        public async Task<ConsultarViagensResponse> ConsultarViagemMobile(ConsultarViagensRequest request)
        {
            #region 1. Validar filtros de entrada
            var validacao = request.validarFiltros();
            if (validacao is not null)
                throw new Exception(validacao);
            #endregion
           
            #region 2. Query base com Includes necessários
            var query = Repository.Query.AsNoTracking()
                .Include(v => v.Empresa)
                .Include(v => v.PortadorProprietario)
                .Include(v => v.PortadorMotorista)
                .Include(v => v.CidadeOrigem)
                .Include(v => v.CidadeDestino)
                .Include(v => v.PagamentoEvento)
                .AsQueryable();
            #endregion
            
            #region 3. Filtros de Viagem
            if (request.ViagemId.HasValue && request.ViagemExternoId.HasValue)
            {
                query = query.Where(v =>
                    v.Id == request.ViagemId && v.ViagemExternoId == request.ViagemExternoId);
            }
            else
            {
                if (request.ViagemId > 0)
                    query = query.Where(v => v.Id == request.ViagemId);
            
                if (request.ViagemExternoId > 0)
                    query = query.Where(v => v.ViagemExternoId == request.ViagemExternoId);
            }
            #endregion
             
            #region 4. Filtro por CPF/CNPJ
            if (!string.IsNullOrWhiteSpace(request.CpfCnpj))
                query = query.Where(v => v.PortadorProprietario.CpfCnpj == request.CpfCnpj || v.PortadorMotorista.CpfCnpj == request.CpfCnpj);
            #endregion
            
            #region  5. Filtro de datas
            if (request.DataInicial.HasValue)
                query = query.Where(v => v.DataCadastro >= request.DataInicial.Value.Date);

            if (request.DataFinal.HasValue)
                query = query.Where(v => v.DataCadastro < request.DataFinal.Value.Date.AddDays(1));
            #endregion 

            #region  6. Ordenação
            query = request.Ordem.ToUpper() == "ASC"
                ? query.OrderBy(v => v.DataCadastro)
                : query.OrderByDescending(v => v.DataCadastro);
            #endregion 
            
            #region 7. Paginação e Mapeamento
            var total = await query.CountAsync();
            
            var viagens = await query
                .Skip(request.Page * request.Limit)
                .Take(request.Limit)
                .Select(d => Mapper.Map<ViagemResponseItem>(d))
                .ToListAsync();
            #endregion
            
            #region 8. Resposta final
            return new ConsultarViagensResponse
            {
                Page = request.Page,
                Limit = request.Limit,
                TotalPages = (int) Math.Ceiling((double) total / request.Limit),
                TotalItems = total,
                Result = viagens
            };
            #endregion
        }

        public async Task<ConsultarPagamentoResponse> ConsultarPagamentoMobile(ConsultarPagamentosRequest request)
        {
            #region 1. Validar filtros de entrada
            var validacao = request.validarFiltros();
            if (validacao is not null)
                throw new Exception(validacao);
            #endregion
            
            #region 2. Query base com Includes necessários
            var query = _pagamentoEventoReadRepository.AsNoTracking()
                .Include(v => v.Empresa)
                .Include(v => v.Viagem).ThenInclude(v => v.PortadorProprietario)
                .Include(v => v.Viagem).ThenInclude(v => v.PortadorMotorista)
                .Include(v => v.UsuarioCadastro)
                .Include(v => v.Transacao)
                .AsQueryable();
            #endregion
            
            #region 3. Filtros de Viagem
            if (request.ViagemId.HasValue && request.ViagemExternoId.HasValue)
            {
                query = query.Where(v =>
                    v.Viagem.Id == request.ViagemId && v.Viagem.ViagemExternoId == request.ViagemExternoId);
            }
            else
            {
                if (request.ViagemId > 0)
                    query = query.Where(v => v.Viagem.Id == request.ViagemId);
            
                if (request.ViagemExternoId > 0)
                    query = query.Where(v => v.Viagem.ViagemExternoId == request.ViagemExternoId);
            }
            #endregion 
            
            #region  3.0 Filtro Tipo Pagamento 
            
            query = query.Where(v => v.Tipo == Tipo.Saldo); // da para fazer direto, adicionei dessa forma caso queira implementar por tipo na request.

            #endregion 
            
            #region 3.1 Filtros de Pagamento
            if (request.PagamentoId.HasValue && request.PagamentoExternoId.HasValue)
            {
                query = query.Where(v =>
                    v.Id == request.PagamentoId && v.PagamentoExternoId == request.PagamentoExternoId);
            }
            else
            {
                if (request.PagamentoId > 0)
                    query = query.Where(v => v.Id == request.PagamentoId);
            
                if (request.PagamentoExternoId > 0)
                    query = query.Where(v => v.PagamentoExternoId == request.PagamentoExternoId);
            }
            #endregion


            #region 3.2 Filtro IdConta
            
            if (request.IdConta.HasValue)
            {
                var contaId = request.IdConta.Value;

                query = query.Where(v =>
                        v.Transacao
                            .Any(t => t.Origem  == contaId
                                      || t.Destino == contaId)
                );
            }
            
            #endregion
            
            #region 4. Filtro por CPF/CNPJ
            
            if (!string.IsNullOrWhiteSpace(request.CpfCnpj))
            {
                query = query.Where(v =>
                    v.Viagem.PortadorProprietario.CpfCnpj == request.CpfCnpj ||
                    v.Viagem.PortadorMotorista.CpfCnpj == request.CpfCnpj);
            }
            
            #endregion 
            
            #region  5. Filtro de datas
            if (request.DataInicial.HasValue)
                query = query.Where(v => v.DataCadastro >= request.DataInicial.Value.Date);

            if (request.DataFinal.HasValue)
                query = query.Where(v => v.DataCadastro < request.DataFinal.Value.Date.AddDays(1));
            #endregion 
            
            #region  6. GrupoStatus

            query = FiltrarPorGrupoStatus(query, request.GrupoStatus);
            
            #endregion
            
            #region  6.1 Ordenação
            query = request.Ordem.ToUpper() == "ASC"
                ? query.OrderBy(v => v.DataCadastro)
                : query.OrderByDescending(v => v.DataCadastro);
            #endregion 
            
            #region 7. Paginação

            var total = await query.CountAsync();
            var pagamentos = await query
                .Skip(request.Page * request.Limit)
                .Take(request.Limit)
                .ToListAsync();

            #endregion 
            
            #region 8. Mapeamento
            
            var mapped = pagamentos
                .Select(p => Mapper.Map<PagamentoMobileResponse>(p))
                .ToList();

            var parametroDias = await _parametrosReadRepository.GetByTipoDoParametroAsync(
                Domain.Models.Parametros.Parametros.TipoDoParametro.DiasParaDisponibilizacaoAntecipacaoRecebivel);

            if (parametroDias != null)
            {
                var diasNecessarios = parametroDias.Valor.ToIntSafe();
                var agora = DateTime.Now;
                foreach (var pagamento in mapped)
                {
                    if (!pagamento.PodeAntecipar()) continue;
                    var dataLiberacao = pagamento.DataCadastro.AddDays(diasNecessarios);
                    if (agora >= dataLiberacao) continue;
                    var diasRestantes = (dataLiberacao - agora).Days + 1;
                    var textoDias = diasRestantes == 1 ? "dia" : "dias";
                    pagamento.Mensagem = $"Antecipação de recebível indisponível. Em {diasRestantes} {textoDias} será possível antecipar.";
                    pagamento.StatusAntecipacaoParcelaProprietario = StatusAntecipacaoParcelaProprietario.NaoDisponivel;
                }
            }
            #endregion 
            
            #region 9. Resposta final
            return new ConsultarPagamentoResponse
            {
                Page = request.Page,
                Limit = request.Limit,
                TotalPages = (int)Math.Ceiling((double)total / request.Limit),
                TotalItems = total,
                Result = mapped
            };
            #endregion 
        }

        public async Task<PagamentoMobileResponse> AlterarStatusAntecipacao(AtualizarStatusAntecipacaoRequest request)
        {
            var log = LogManager.GetCurrentClassLogger();
            log.Info("Inicio AlterarStatusAntecipacao Request: " + JsonConvert.SerializeObject(request));

            #region 1. Validar filtros de entrada
            var validacao = request.validarFiltros();
            if (validacao is not null)
                throw new Exception(validacao);
            #endregion

            #region 2. Buscar o pagamento evento

            var pagamentoEvento = await _pagamentoEventoAppService.BuscarPagamentoEvento(request.PagamentoId, request.ViagemId);

            if (pagamentoEvento == null)
                return null;
            #endregion

            #region 3. Validar regras de negócio

            if (pagamentoEvento.StatusAntecipacaoParcelaProprietario.ToIntSafe() == request.StatusAntecipacao.ToIntSafe())
            {
                throw new Exception("Não é possível alterar o status para o mesmo status atual.");
            }

            if (pagamentoEvento.StatusAntecipacaoParcelaProprietario is StatusAntecipacaoParcelaProprietario.Aprovado)
            {
                throw new Exception("Status Pagamento já aprovado, não é possível altera-lo.");
            }
            if (pagamentoEvento.StatusAntecipacaoParcelaProprietario is StatusAntecipacaoParcelaProprietario.Erro &&
                request.StatusAntecipacao != StatusAntecipacaoRequest.AguardandoProcessamento)
            {
                throw new Exception($"Status Pagamento está em Erro e não pode ser alterado para {request.StatusAntecipacao.DescriptionAttr()}!");
            }
            if (pagamentoEvento.StatusAntecipacaoParcelaProprietario is StatusAntecipacaoParcelaProprietario.Disponivel &&
                request.StatusAntecipacao == StatusAntecipacaoRequest.Aprovado)
            {
                throw new Exception($"Status Pagamento está em Disponivél e não pode ser alterado para {request.StatusAntecipacao.DescriptionAttr()}!");
            }
            #endregion
        
            #region 4. Converter enum do request para enum do domain

            var novoStatus = request.StatusAntecipacao.ParaStatusParcelaProprietario();
           
            #endregion

            #region 5. Tratar regras específicas para status Erro
            if (request.StatusAntecipacao == StatusAntecipacaoRequest.Erro)
            {
                #region 5.1 Cancela Transação de Retenção
                var transacaoRetencao = await CarregaTransacaoRetencao(pagamentoEvento.Id);
                var cancelaTransacaoRetencao = await CancelarTransacaoRetencao(transacaoRetencao, pagamentoEvento);
                #endregion
                
                #region 5.2 Realiza transacao proprietário
                if (cancelaTransacaoRetencao)
                {
                    var resposta = await GerarPagamentoEventoViagemP2P(transacaoRetencao, pagamentoEvento);
                    if (!resposta.Sucesso)
                    {
                        throw new Exception(resposta.Mensagem);
                    }
                }
                #endregion
            }
            #endregion
        
            #region 6. Atualizar o status do pagamento evento
            
            var pagamentoEventoAtualizado = await AtualizaStatusPagamentoEvento(request.Motivo, novoStatus, pagamentoEvento);
            
            log.Info($"Fim AlterarStatusAntecipacao Request: {JsonConvert.SerializeObject(request)}");
            return pagamentoEventoAtualizado;
            
            #endregion
        }

        
        private async Task<Domain.Models.Transacao.Transacao> CarregaTransacaoRetencao(int pagamentoEventoId)
        {
            var transacaoRetencao = await _transacaoReadRepository.AsNoTracking()
                .FirstOrDefaultAsync(t => t.IdPagamentoEvento == pagamentoEventoId &&
                                          t.FormaPagamento == FormaPagamentoEvento.RetencaoAntecipacao &&
                                          t.Status == StatusPagamento.Fechado);
            return transacaoRetencao;
        }
        
        private async Task<bool> CancelarTransacaoRetencao(Domain.Models.Transacao.Transacao transacaoRetencao, Domain.Models.PagamentoEvento.PagamentoEvento pagamentoEvento)
        {
            if (transacaoRetencao == null) return false;
            var contaBbc = transacaoRetencao.Destino ?? 0;
                 
            var retornoConsultaSaldo = await VerificarSaldoContaOrigem(contaBbc,
                Math.Round(transacaoRetencao.Valor, 2, MidpointRounding.AwayFromZero));

            if (!retornoConsultaSaldo.sucesso)
            {
                throw new Exception(
                    $"Estorno da transação cód {transacaoRetencao.Id} com conta destino {transacaoRetencao.Origem} " +
                    $"e conta origem {transacaoRetencao.Destino} no valor de {transacaoRetencao.Valor.FormatMonetario()} " +
                    $"com pagamento evento cód {transacaoRetencao.IdPagamentoEvento} não iniciada: {retornoConsultaSaldo.mensagem}");
            }
                  
            CancelamentoEventoViagemResponse retornoCancelamento = null;
            try
            {
                retornoCancelamento = await CancelarPagamentoP2P(transacaoRetencao, pagamentoEvento);
                if (retornoCancelamento.Sucesso)
                {
                    if (pagamentoEvento.FormaPagamento == FormaPagamentoEvento.Pix)
                    {
                    }
                }
            }
            catch (Exception)
            {
                pagamentoEvento.JsonRetornoCancelamento = retornoCancelamento?.ToJson();
                await Repository.Command.SaveChangesAsync();
                      
            }
            return true;
        }
        
        private async Task<CancelamentoEventoViagemResponse> CancelarPagamentoP2P(Domain.Models.Transacao.Transacao transacao, Domain.Models.PagamentoEvento.PagamentoEvento pagamentoEvento)
        {
            var mensagemNotificacao = $"Estorno do pagamento realizado com sucesso.";
            var transferencia = new Transferencia()
            {
                amount = Math.Round(transacao.Valor, 2, MidpointRounding.AwayFromZero),
                destinationAccount = transacao.Origem,
                originalAccount = transacao.Destino ?? 0,
                description = JsonConvert.SerializeObject(new
                {
                    description = "Número de Referência: " + $"{pagamentoEvento.Viagem.ViagemExternoId}/{pagamentoEvento.Tipo.GetSigla()}",
                    protocol = $"{pagamentoEvento.Viagem.ViagemExternoId}/{pagamentoEvento.Tipo.GetSigla()}"
                })
            };
            
            // lLog.Info("Json de cancelamento: " + JsonConvert.SerializeObject(transferencia));
            transacao.JsonEnvioDockCancelamento = JsonConvert.SerializeObject(transferencia);
            var retornoTransferencia = await _transferenciaRepository.RealizaTransferenciaEntreContas(transferencia);
            transacao.JsonRespostaDockCancelamento = retornoTransferencia.RetornoJson;
            transacao.ResponseCodeDockCancelamento = retornoTransferencia.Code.ToInt();
            
            if (retornoTransferencia.Sucesso)
            {
                transacao.Status = StatusPagamento.Cancelado;
                transacao.DataCancelamento = DateTime.Now;
            }
            else
            {
                mensagemNotificacao =
                    $"Estorno da transação cód {transacao.Id} com conta destino {transacao.Origem} e conta origem {transacao.
                        Destino} no valor de {transacao.Valor.FormatMonetario()} com pagamento evento cód {transacao.
                        IdPagamentoEvento} não realizada: {retornoTransferencia.message}";
                await RegistrarNotificacaoPagamento(mensagemNotificacao, pagamentoEvento.Id);
                throw new Exception(mensagemNotificacao);
            }
            
            var statusCode = await QualificarPagamentoFreteP2P(retornoTransferencia, 
                $"{pagamentoEvento.Viagem.ViagemExternoId}/{pagamentoEvento.Tipo.GetSigla()}");

            transacao.Qualificado = statusCode == 204 ? 1 : 0;
            
            _transacaoWriteRepository.Update(transacao);
            await _transacaoWriteRepository.SaveChangesAsync();
            return CriarCancelamentoEventoViagem(pagamentoEvento, mensagemNotificacao, transacao);
        }

        private CancelamentoEventoViagemResponse CriarCancelamentoEventoViagem(Domain.Models.PagamentoEvento.PagamentoEvento pagamentoEvento, string mensagemNotificacao,Domain.Models.Transacao.Transacao transacao)
        {
            var result = new CancelamentoEventoViagemTransacaoResponse()
            {
                Sucesso = transacao.Status == StatusPagamento.Cancelado,
                Mensagem = transacao.Status == StatusPagamento.Cancelado
                    ? "Sucesso ao cancelar a transação do pagamento!"
                    : "Erro ao processar estorno da transação.",
                Valor = transacao.Valor,
                ContaOrigem = transacao.Origem,
                ContaDestino = transacao.Destino,
                Agencia = transacao.Agencia,
                Conta = transacao.Conta,
                CodigoBanco = transacao.CodigoBanco,
                Status = transacao.Status.ToString(),
                StatusEnum = transacao.Status,
                Id = transacao.Id
            };
            
            return new CancelamentoEventoViagemResponse()
            {
                Sucesso = true,
                Mensagem = mensagemNotificacao,
                PagamentoEvento = new CancelamentoEventoViagemPagamentoResponse()
                {
                    Id = pagamentoEvento.Id,
                    Tipo = Tipo.Cancelamento.ToString(),
                    Status = pagamentoEvento.Status.ToString()
                },
                Transacoes = [result]
            };
        }
        
        private async Task RegistrarNotificacaoPagamento(string notificacaoPagamento, int idPagamentoEvento)
        {
            var commandNotificacao = new NotificacaoSalvarComRetornoCommand()
            {
                Descricao = notificacaoPagamento,
                PagamentoEventoId = idPagamentoEvento
            };

            await Engine.CommandBus
                .SendCommandAsync<Notificacao>(commandNotificacao);
        }
        
        private async Task<PagamentoMobileResponse> AtualizaStatusPagamentoEvento(string motivo, StatusAntecipacaoParcelaProprietario novoStatus, Domain.Models.PagamentoEvento.PagamentoEvento pagamentoEvento)
        {

            var pagamentoEventoParaAtualizar = await _pagamentoEventoAppService.BuscarPagamentoEventoCompleto(pagamentoEvento.Id);
            
            if (pagamentoEventoParaAtualizar == null)
                throw new Exception("Não foi possível alterar o status do pagamento envento, tente novamente mais tarde");

            pagamentoEventoParaAtualizar.StatusAntecipacaoParcelaProprietario = novoStatus;
            pagamentoEventoParaAtualizar.DataAlteracao = DateTime.Now;
            pagamentoEventoParaAtualizar.AntecipacaoMotivo  = motivo;
            if (novoStatus == StatusAntecipacaoParcelaProprietario.AguardandoProcessamento)
            {
                pagamentoEventoParaAtualizar.DataCadastroAntecipacao = DateTime.Now;
            } else if (novoStatus is StatusAntecipacaoParcelaProprietario.Erro or StatusAntecipacaoParcelaProprietario.Aprovado)
            {
                pagamentoEventoParaAtualizar.DataAlteracaoAntecipacao = DateTime.Now;
            }

            _pagamentoEventoWriteRepository.Update(pagamentoEventoParaAtualizar);
            await _pagamentoEventoWriteRepository.SaveChangesAsync();
            
            var pagamentoEventoAtualizado = Mapper.Map<PagamentoMobileResponse>(pagamentoEventoParaAtualizar);
            return pagamentoEventoAtualizado;
        }
    
        private async Task<RespPadrao> VerificarSaldoContaOrigem(int idContaOrigem, decimal valorTransacao)
        {
            try
            {
                var lSaldoResponse = await _cartaoRepository.ConsultarSaldo(idContaOrigem.ToString());

                if (lSaldoResponse.saldoDisponivelGlobal > 0)
                {
                    if (lSaldoResponse.saldoDisponivelGlobal >= valorTransacao)
                    {
                        return new RespPadrao
                        {
                            sucesso = true,
                            mensagem = "Conta ID: " + idContaOrigem + " Saldo disponivel para pagamento"
                        };
                    }

                    return new RespPadrao
                    {
                        sucesso = false,
                        mensagem = "Conta ID: " + idContaOrigem + " Insuficiência de fundos"
                    };
                }

                return new RespPadrao
                {
                    sucesso = false,
                    mensagem = "Erro ao avaliar saldo de conta " + idContaOrigem
                };
            }
            catch (Exception e)
            {
                return new RespPadrao
                {
                    sucesso = false,
                    mensagem = e.Message
                };
            }
        }
        
        private IQueryable<Domain.Models.PagamentoEvento.PagamentoEvento> FiltrarPorGrupoStatus(IQueryable<Domain.Models.PagamentoEvento.PagamentoEvento> query, string grupoStatus)
        {
            if (string.IsNullOrEmpty(grupoStatus)) return query;
            
            if (grupoStatus.Equals("Futuros", StringComparison.OrdinalIgnoreCase))
                return query.Where(v => v.Status == StatusPagamento.Aberto);

            return grupoStatus.Equals("Recentes", StringComparison.OrdinalIgnoreCase) ? query.Where(v => v.Status != StatusPagamento.Aberto) : query;
        }
        
        private async Task<RespPadrao> LocalizarContaAliasBank(int contaDestinoId)
        {
            return await ViagemUtils.LocalizarContaAliasBank(contaDestinoId, _cartaoRepository);
        }
        
        private async Task<RespostaViagemPagamento> TransferenciaPagamentoEventoP2PDock(Domain.Models.Transacao.Transacao transacaoRetencao,
            Domain.Models.PagamentoEvento.PagamentoEvento eventoPagamento,
            int contaOrigemId, int contaDestinoId)
        {
            var log = LogManager.GetCurrentClassLogger();
            TransferenciaEntreContaResp lRetornoTransferencia;

            var lAlteraTransacao = new TransacaoAlterarStatusRequest();
            string lMensagem;

            try
            {
                log.Info("Inicio TransferenciaPagamentoEventoP2PDock");
                var lTransferencia = new Transferencia
                {
                    amount = Math.Round(transacaoRetencao.Valor, 2),
                    destinationAccount = contaDestinoId.ToInt(),
                    originalAccount = contaOrigemId.ToInt(),
                    description = JsonConvert.SerializeObject(new
                    {
                        branchNumber = eventoPagamento.Viagem.FilialId,
                        nationalRegistration = eventoPagamento.Viagem.PortadorProprietario.CpfCnpj,
                        description = "Número de Referência: " +
                                      $"{eventoPagamento.Viagem.ViagemExternoId}/{eventoPagamento.Tipo.GetSigla()}",
                        protocol = $"{eventoPagamento.Viagem.ViagemExternoId}/{eventoPagamento.Tipo.GetSigla()}"
                    })
                };

                var lTransacaoReq = new TransacaoRequest()
                {
                    IdPagamentoEvento = eventoPagamento.Id,
                    Valor = lTransferencia.amount,
                    Destino = lTransferencia.destinationAccount,
                    Origem = lTransferencia.originalAccount,
                    Descricao = "Transferencia P2P Sendo Criada",
                    Tipo = transacaoRetencao.Tipo,
                    FormaPagamento = FormaPagamentoEvento.Deposito,
                    Status = StatusPagamento.Processando,
                    DataCadastro = DateTime.Now,
                    Description = lTransferencia.description,
                    JsonEnvioDock = JsonConvert.SerializeObject(lTransferencia)
                };

                var lTransacaoId = await _transacaoRegisterAppService.RegistrarTransacao(lTransacaoReq);

                log.Info("Json de TransferenciaPagamentoEventoP2P: " + JsonConvert.SerializeObject(lTransferencia));
                lAlteraTransacao.Id = lTransacaoId.id.ToIntSafe();
                lRetornoTransferencia = await _transferenciaRepository.RealizaTransferenciaEntreContas(lTransferencia);

                var statusCode = await QualificarPagamentoFreteP2P(lRetornoTransferencia,
                    $"{eventoPagamento.Viagem.ViagemExternoId}/{eventoPagamento.Tipo.GetSigla()}");

                lAlteraTransacao.Qualificado = statusCode == 204 ? 1 : 0;
                lAlteraTransacao.JsonRespostaDock = lRetornoTransferencia.RetornoJson;
                lAlteraTransacao.ResponseCodeDock = lRetornoTransferencia.Code.ToInt();
                lAlteraTransacao.DataRetornoDock = DateTime.Now;

            }
            catch (Exception e)
            {
                lMensagem = "Pagamento para proprietário não efetuado. " + e.Message;

                log.Info(lMensagem);

                lAlteraTransacao.Descricao = lMensagem;
                lAlteraTransacao.Status = StatusPagamento.Erro;
                await _transacaoRegisterAppService.AtualizarTransacao(lAlteraTransacao);

                return new RespostaViagemPagamento()
                {
                    Sucesso = false,
                    Mensagem = lMensagem,
                    TipoOperacao = TipoOperacao.Erro
                };
            }
            finally
            {
                log.Info("Fim TransferenciaPagamentoEventoP2PDock");
            }

            if (lRetornoTransferencia.Sucesso)
            {
                var lEventoTipo = eventoPagamento.Tipo != null
                    ? eventoPagamento.Tipo.Value.ToStringSafe()
                    : "Sem tipo definido";

                lMensagem = $"Pagamento Id {eventoPagamento.Id}, {lEventoTipo}, de valor: R$ {eventoPagamento.Valor} realizado.";

                lAlteraTransacao.Descricao = "Transferencia P2P criada com sucesso";
                lAlteraTransacao.Status = StatusPagamento.Fechado;
                lAlteraTransacao.DataBaixa = DateTime.Now;

                await _transacaoRegisterAppService.AtualizarTransacao(lAlteraTransacao);
                return new RespostaViagemPagamento()
                {
                    Sucesso = true,
                    Mensagem = lMensagem,
                    TipoOperacao = TipoOperacao.Notificacao
                };
            }

            lAlteraTransacao.Descricao = "Pagamento não efetuado. Motivo: " + lRetornoTransferencia.message;
            lAlteraTransacao.Status = StatusPagamento.Erro;
            await _transacaoRegisterAppService.AtualizarTransacao(lAlteraTransacao);
            log.Info("Pagamento não efetuado. Motivo: " + lRetornoTransferencia.message);

            #region Mensagem de retorno da integração da viagem
            
            lMensagem = await TraduzirMensagemDock(lRetornoTransferencia.message);
            
            #endregion
            
            return new RespostaViagemPagamento()
            {
                Sucesso = false,
                Mensagem = lMensagem,
                TipoOperacao = TipoOperacao.Notificacao
            };
        }

        private async Task<string> TraduzirMensagemDock(string aRetornoTransferenciaMensagem)
        {
            var mensagemDockTraduzida = await _cloudTranslationService.Traduzir(aRetornoTransferenciaMensagem);
            var mensagemRetorno = await RetornarMensagemDockIntegracaoViagem(mensagemDockTraduzida, aRetornoTransferenciaMensagem);
            return  $"Pagamento para o proprietário não efetuado. Erro DOCK: {mensagemRetorno}";
        }
        
        private async Task<int> QualificarPagamentoFreteP2P(TransferenciaEntreContaResp objectJson, string receipt)
        {
            return await ViagemUtils.QualificarPagamentoFreteP2P(objectJson, receipt, _parametrosReadRepository);
        }
        
        private async Task<string> RetornarMensagemDockIntegracaoViagem(string mensagemTraduzida, 
            string retornoTransferenciaMensagem)
        {
            
            var mensagemRetornada = await _mensagemAppService.RegistraMensagem(new MensagemRequest
            {
                CodigoAplicacao = ECodigoAplicacao.Dock,
                TextoMensagemOriginal = retornoTransferenciaMensagem,
                TextoMensagemPadrao = mensagemTraduzida,
                Ativo = 1,
                CodigoMensagem = null,
                DataInicioMensagem = null,
                DataFimMensagem = null,
                DescricaoMensagem = null,
                ImagemMensagem = null
            });
            
            var mensagemRetornoDockPagamento = "";

            if (mensagemRetornada.novaMensagem) 
                return mensagemRetornada.data.TextoMensagemPadrao ?? mensagemTraduzida;

            if (!mensagemRetornada.novaMensagem)
            {
                if (mensagemRetornada.data.Ativo == 1 && mensagemRetornada.data.MensagemTratada == 1)
                {
                    mensagemRetornoDockPagamento = mensagemRetornada.data.TextoMensagem ?? mensagemTraduzida; 
                }
                else if (mensagemRetornada.data.Ativo == 1 && mensagemRetornada.data.MensagemTratada == 0)
                {
                    mensagemRetornoDockPagamento = mensagemRetornada.data.TextoMensagemPadrao ?? mensagemTraduzida;
                }
                else
                {
                    mensagemRetornoDockPagamento = mensagemTraduzida;
                }
                    
            }
            
            return mensagemRetornoDockPagamento;
        }
        
        private async Task<RespostaViagemPagamento> GerarPagamentoEventoViagemP2P(Domain.Models.Transacao.Transacao transacaoRetencao,
            Domain.Models.PagamentoEvento.PagamentoEvento eventoPagamento)
        {
            var log = LogManager.GetCurrentClassLogger();

            #region Coleta de Contas BBC

            var empresa = await _empresaReadRepository.GetByIdAsync(eventoPagamento.EmpresaId.ToInt());

            //CONTA empresa

            #region Coleta conta Empresa

            int idContaOrigem;
            if (empresa.ContaFrete == null)
            {
                var contasOrigem = await _cartaoRepository.ConsultarContas(null, null, empresa.Cnpj);

                if (contasOrigem == null)
                {
                    return new RespostaViagemPagamento()
                    {
                        Sucesso = false,
                        Mensagem = "Nenhuma conta da empresa foi encontrada.",
                        TipoOperacao = TipoOperacao.Erro,
                        StatusPagamento = StatusPagamento.NaoExecutado
                    };
                }
                var contaOrigemAtiva = contasOrigem.content?
                    .FirstOrDefault(x => ViagemUtils.StatusConta().Contains(x.idStatusConta));

                if (contaOrigemAtiva == null)
                {
                    return new RespostaViagemPagamento
                    {
                        Sucesso = false,
                        Mensagem = "Nenhuma conta ativa da empresa foi encontrada.",
                        TipoOperacao = TipoOperacao.Erro,
                        StatusPagamento = StatusPagamento.NaoExecutado
                    };
                }

                idContaOrigem = contaOrigemAtiva.id;
            }
            else
            {
                idContaOrigem = empresa.ContaFrete ?? 0;
            }

            log.Info("Conta origem da empresa: " + idContaOrigem);

            #region Validacoes conta origem

            //Verificador saldo
            var retornoConsultaSaldo =
                await VerificarSaldoContaOrigem(idContaOrigem, eventoPagamento.Valor.ToDecimal());

            if (!retornoConsultaSaldo.sucesso)
            {
                return new RespostaViagemPagamento
                {
                    Sucesso = false,
                    Mensagem = retornoConsultaSaldo.mensagem,
                    TipoOperacao = TipoOperacao.Notificacao,
                    StatusPagamento = StatusPagamento.Erro
                };
            }

            #endregion

            #endregion

            //CONTA proprietario

            #region Coleta conta Proprietario

            if (string.IsNullOrWhiteSpace(eventoPagamento.Viagem.PortadorProprietario.CpfCnpj))
            {
                return new RespostaViagemPagamento()
                {
                    Sucesso = false,
                    Mensagem = "Proprietário (portador contratado) sem cpf/cnpj definido.",
                    TipoOperacao = TipoOperacao.Notificacao,
                    StatusPagamento = StatusPagamento.Erro
                };
            }

            var contaProprietarioDestino = _cartaoRepository
                .ConsultarContas(null, null, eventoPagamento.Viagem.PortadorProprietario.CpfCnpj)?.Result;

            if (contaProprietarioDestino == null)
            {
                return new RespostaViagemPagamento()
                {
                    Sucesso = false,
                    Mensagem = "Nenhuma conta do proprietário (portador contratado) foi encontrada.",
                    TipoOperacao = TipoOperacao.Notificacao,
                    StatusPagamento = StatusPagamento.Erro
                };
            }

            var idContaProprietarioDestino = contaProprietarioDestino.content
                ?.FirstOrDefault(x => ViagemUtils.StatusConta().Contains(x.idStatusConta))?.id ?? 0;

            if (idContaProprietarioDestino == 0)
            {
                log.Info(
                    $"Nenhuma conta ativa do proprietário (portador contratado) foi encontrada. Pagamento evento Id {eventoPagamento.Id}");

                return new RespostaViagemPagamento()
                {
                    Sucesso = false,
                    Mensagem = "Nenhuma conta ativa do proprietário (portador contratado) foi encontrada.",
                    TipoOperacao = TipoOperacao.Notificacao,
                    StatusPagamento = StatusPagamento.Erro
                };
            }

            log.Info("Conta destino do proprietário (portador contratado): " + idContaProprietarioDestino);

            if (eventoPagamento.Viagem.PortadorProprietario.CpfCnpj.Length > 11)
            {
                var individuoPortadorContratado = await _cartaoRepository
                    .ConusltaContaPessoaJuridica(eventoPagamento.Viagem.PortadorProprietario.CpfCnpj);

                var bloqueioSpds = individuoPortadorContratado.results?.FirstOrDefault()?.statusSPD;
                if (bloqueioSpds != null)
                {
                    foreach (var lBloqueioSpd in bloqueioSpds)
                    {
                        if (lBloqueioSpd.statusId == 1 && lBloqueioSpd.createDate < DateTime.Now.AddDays(-15))
                        {
                            return new RespostaViagemPagamento()
                            {
                                Sucesso = false,
                                Mensagem = "Conta de destino do proprietário (portador contratado) bloqueada.",
                                TipoOperacao = TipoOperacao.Notificacao,
                                StatusPagamento = StatusPagamento.Erro
                            };
                        }

                        if (lBloqueioSpd.statusId != 4 && lBloqueioSpd.statusId != 10 &&
                            lBloqueioSpd.statusId != 11 && lBloqueioSpd.statusId != 12 &&
                            lBloqueioSpd.statusId != 18 && lBloqueioSpd.statusId != 19 &&
                            lBloqueioSpd.statusId != 20 && lBloqueioSpd.statusId != 1)
                        {
                            return new RespostaViagemPagamento()
                            {
                                Sucesso = false,
                                Mensagem = "Conta de destino do proprietário (portador contratado) bloqueada.",
                                TipoOperacao = TipoOperacao.Notificacao,
                                StatusPagamento = StatusPagamento.Erro
                            };
                        }
                    }
                }
            }
            else
            {
                var idPessoaPortadorContratado = contaProprietarioDestino.content
                    ?.FirstOrDefault(x => ViagemUtils.StatusConta().Contains(x.idStatusConta))?.idPessoa.ToDecimalSafe() ?? 0;

                var validacaoStatusSpdContaProprietario =
                    await _cartaoRepository.ConsultarPessoa(idPessoaPortadorContratado);

                var bloqueioSpds = validacaoStatusSpdContaProprietario?.statusSPD;
                if (bloqueioSpds != null)
                {
                    foreach (var lBloqueioSpd in bloqueioSpds)
                    {
                        if (lBloqueioSpd.statusId != 4 && lBloqueioSpd.statusId != 10 &&
                            lBloqueioSpd.statusId != 11 && lBloqueioSpd.statusId != 12 &&
                            lBloqueioSpd.statusId != 18 && lBloqueioSpd.statusId != 19 &&
                            lBloqueioSpd.statusId != 20)
                        {
                            return new RespostaViagemPagamento()
                            {
                                Sucesso = false,
                                Mensagem = "Conta de destino do proprietário (portador contratado) bloqueada.",
                                TipoOperacao = TipoOperacao.Notificacao,
                                StatusPagamento = StatusPagamento.Erro
                            };
                        }
                    }
                }
            }
            
            #endregion
            
            #region Localizar conta AliasBank

            //do proprietario
            var retornoContaAliasBank = await LocalizarContaAliasBank(idContaProprietarioDestino);

            if (!retornoContaAliasBank.sucesso)
            {
                log.Info("Erro ao validar conta AliasBank do proprietário!");

                return new RespostaViagemPagamento()
                {
                    Sucesso = false,
                    Mensagem =
                        $"Erro ao validar conta do proprietário (portador contratado). {retornoContaAliasBank.mensagem}",
                    TipoOperacao = TipoOperacao.Pendencia,
                    StatusPagamento = StatusPagamento.Erro
                };
            }
            
            #endregion

            #endregion;

            log.Info($"Inicio IntegrarPagamentoViagem Empresa: {Engine.User.EmpresaId}, " +
                     $"Conta origem: {idContaOrigem}, " +
                     $"Conta proprietario destino: {idContaProprietarioDestino}, " +
                     $"Valor do pagamento: {eventoPagamento.Valor}"
            );

            log.Info("Valida Pagamento Duplicado Dock");

            eventoPagamento.Valor = Math.Round(eventoPagamento.Valor, 2);
            
            var lRespostaViagemPagamento = await TransferenciaPagamentoEventoP2PDock(transacaoRetencao, eventoPagamento, idContaOrigem,
                idContaProprietarioDestino); //TODO VER TIPO

            if (!lRespostaViagemPagamento.Sucesso)
            {
                return new RespostaViagemPagamento()
                {
                    Sucesso = false,
                    Mensagem = lRespostaViagemPagamento.Mensagem,
                    TipoOperacao = TipoOperacao.Erro,
                    StatusPagamento = StatusPagamento.Pendente
                };
            }
            return lRespostaViagemPagamento;
        }
    }
}
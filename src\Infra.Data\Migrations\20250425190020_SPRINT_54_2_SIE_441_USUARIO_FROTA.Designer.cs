﻿// <auto-generated />
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage;
using Microsoft.EntityFrameworkCore.Storage.Internal;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Domain.Models.DeclaracaoCiot;
using SistemaInfo.BBC.Domain.Models.Documento;
using SistemaInfo.BBC.Domain.Models.Emprestimo;
using SistemaInfo.BBC.Domain.Models.Parametros;
using SistemaInfo.BBC.Domain.Models.ParametrosHistorico;
using SistemaInfo.BBC.Domain.Models.Retencao;
using SistemaInfo.BBC.Domain.Models.ServidorCiot;
using SistemaInfo.BBC.Infra.Data.Context;
using System;

namespace SistemaInfo.BBC.Infra.Data.Migrations
{
    [DbContext(typeof(ConfigContext))]
    [Migration("20250425190020_SPRINT_54_2_SIE_441_USUARIO_FROTA")]
    partial class SPRINT_54_2_SIE_441_USUARIO_FROTA
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasDefaultSchema("BBC")
                .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.SerialColumn)
                .HasAnnotation("ProductVersion", "2.0.2-rtm-10011");

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.Abastecimento.Abastecimento", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("Id")
                        .HasColumnType("serial");

                    b.Property<int?>("AutorizacaoAbastecimentoId")
                        .HasColumnName("AutorizacaoAbastecimentoId")
                        .HasColumnType("int");

                    b.Property<string>("CnpjAFaturar")
                        .HasColumnName("CnpjAFaturar")
                        .HasColumnType("varchar(200)");

                    b.Property<int>("CombustivelId")
                        .HasColumnName("CombustivelId")
                        .HasColumnType("int");

                    b.Property<int?>("ContadorIntegracaoMovida")
                        .HasColumnName("ContadorIntegracaoMovida")
                        .HasColumnType("int");

                    b.Property<DateTime?>("DataAlteracao")
                        .HasColumnName("DataAlteracao")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("DataCadastro")
                        .HasColumnName("DataCadastro")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DataIntegracaoMovida")
                        .HasColumnName("DataIntegracaoMovida")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DataPrazoPagamentoRetencao")
                        .HasColumnName("DataPrazoPagamentoRetencao")
                        .HasColumnType("timestamp");

                    b.Property<int?>("EmpresaId")
                        .HasColumnName("EmpresaId")
                        .HasColumnType("int");

                    b.Property<int?>("FilialId")
                        .HasColumnName("FilialId")
                        .HasColumnType("int");

                    b.Property<int?>("IntegracaoMovida")
                        .HasColumnName("IntegracaoMovida")
                        .HasColumnType("int");

                    b.Property<string>("JsonEnvioMovida")
                        .HasColumnName("JsonEnvioMovida")
                        .HasColumnType("text");

                    b.Property<decimal>("Litragem")
                        .HasColumnName("Litragem")
                        .HasColumnType("decimal");

                    b.Property<int?>("LoteRetencaoId")
                        .HasColumnName("LoteRetencaoId")
                        .HasColumnType("int");

                    b.Property<string>("Motivo")
                        .HasColumnName("Motivo")
                        .HasColumnType("varchar(200)");

                    b.Property<int?>("NumeroItemXmlNota")
                        .HasColumnName("NumeroItemXmlNota")
                        .HasColumnType("int");

                    b.Property<int?>("Odometro")
                        .HasColumnName("Odometro")
                        .HasColumnType("int");

                    b.Property<int?>("OdometroAnterior")
                        .HasColumnName("OdometroAnterior")
                        .HasColumnType("int");

                    b.Property<int>("PortadorId")
                        .HasColumnName("PortadorId")
                        .HasColumnType("int");

                    b.Property<int>("PostoId")
                        .HasColumnName("PostoId")
                        .HasColumnType("int");

                    b.Property<int?>("ProtocoloAbastecimentoId")
                        .HasColumnName("ProtocoloAbastecimentoId")
                        .HasColumnType("int");

                    b.Property<string>("RetornoMovida")
                        .HasColumnName("RetornoMovida")
                        .HasColumnType("text");

                    b.Property<int>("Status")
                        .HasColumnName("Status")
                        .HasColumnType("int");

                    b.Property<int>("TipoDebito")
                        .HasColumnName("TipoDebito")
                        .HasColumnType("int");

                    b.Property<int?>("UsuarioAlteracaoId")
                        .HasColumnName("UsuarioAlteracaoId")
                        .HasColumnType("int");

                    b.Property<int?>("UsuarioCadastroId")
                        .HasColumnName("UsuarioCadastroId")
                        .HasColumnType("int");

                    b.Property<decimal>("ValorAbastecimento")
                        .HasColumnName("ValorAbastecimento")
                        .HasColumnType("decimal");

                    b.Property<decimal>("ValorUnitario")
                        .HasColumnName("ValorUnitario")
                        .HasColumnType("decimal");

                    b.Property<int>("VeiculoId")
                        .HasColumnName("VeiculoId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("AutorizacaoAbastecimentoId");

                    b.HasIndex("CombustivelId");

                    b.HasIndex("EmpresaId");

                    b.HasIndex("FilialId");

                    b.HasIndex("LoteRetencaoId");

                    b.HasIndex("PortadorId");

                    b.HasIndex("PostoId");

                    b.HasIndex("ProtocoloAbastecimentoId");

                    b.HasIndex("UsuarioAlteracaoId");

                    b.HasIndex("UsuarioCadastroId");

                    b.HasIndex("VeiculoId");

                    b.ToTable("Abastecimento");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.AtualizacaoPrecoCombustivel.AtualizacaoPrecoCombustivel", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("Id")
                        .HasColumnType("serial");

                    b.Property<int?>("CombustivelId")
                        .HasColumnName("CombustivelId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("DataAlteracao")
                        .HasColumnName("DataAlteracao")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DataCadastro")
                        .HasColumnName("DataCadastro")
                        .HasColumnType("timestamp");

                    b.Property<string>("MotivoExterno")
                        .HasColumnName("MotivoExterno")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("MotivoInterno")
                        .HasColumnName("MotivoInterno")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("MotivoSolicitacao")
                        .HasColumnName("MotivoSolicitacao")
                        .HasColumnType("varchar(200)");

                    b.Property<int?>("PostoId")
                        .HasColumnName("PostoId")
                        .HasColumnType("int");

                    b.Property<int?>("StatusAprovacao")
                        .HasColumnName("StatusAprovacao")
                        .HasColumnType("int");

                    b.Property<int?>("UsuarioAlteracaoId")
                        .HasColumnName("UsuarioAlteracaoId")
                        .HasColumnType("int");

                    b.Property<decimal?>("ValorBBC")
                        .HasColumnName("ValorBBC")
                        .HasColumnType("decimal");

                    b.Property<decimal?>("ValorBBCSolicitado")
                        .HasColumnName("ValorBBCSolicitado")
                        .HasColumnType("decimal");

                    b.Property<decimal?>("ValorBomba")
                        .HasColumnName("ValorBomba")
                        .HasColumnType("decimal");

                    b.Property<decimal?>("ValorBombaSolicitado")
                        .HasColumnName("ValorBombaSolicitado")
                        .HasColumnType("decimal");

                    b.HasKey("Id");

                    b.HasIndex("CombustivelId");

                    b.HasIndex("PostoId");

                    b.HasIndex("UsuarioAlteracaoId");

                    b.ToTable("AtualizacaoPrecoCombustivel");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.AuditoriaSeguranca.AuditoriaSeguranca", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("Id")
                        .HasColumnType("serial");

                    b.Property<DateTime?>("DataAcesso")
                        .HasColumnName("DataAcesso")
                        .HasColumnType("timestamp");

                    b.Property<string>("Menu")
                        .IsRequired()
                        .HasColumnName("Menu")
                        .HasColumnType("varchar(100)");

                    b.Property<int>("UsuarioId")
                        .HasColumnName("UsuarioId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("UsuarioId");

                    b.ToTable("AuditoriaSeguranca");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.AuthSession.AuthSession", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("Id")
                        .HasColumnType("serial");

                    b.Property<DateTime>("DataCadastro")
                        .HasColumnName("DataCadastro")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("DataUltimaReq")
                        .HasColumnName("DataUltimaReq")
                        .HasColumnType("timestamp");

                    b.Property<int?>("IdPosto")
                        .HasColumnName("IdPosto")
                        .HasColumnType("int");

                    b.Property<int>("IdUsuario")
                        .HasColumnName("IdUsuario")
                        .HasColumnType("int");

                    b.Property<int?>("PortadorId")
                        .HasColumnName("PortadorId")
                        .HasColumnType("int");

                    b.Property<string>("Token")
                        .IsRequired()
                        .HasColumnName("Token")
                        .HasColumnType("varchar(300)");

                    b.Property<int?>("UsuarioId1");

                    b.HasKey("Id");

                    b.HasIndex("IdPosto");

                    b.HasIndex("IdUsuario");

                    b.HasIndex("PortadorId");

                    b.HasIndex("UsuarioId1");

                    b.ToTable("AuthSession");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.AuthSessionApi.AuthSessionApi", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("Id")
                        .HasColumnType("serial");

                    b.Property<DateTime>("DataCadastro")
                        .HasColumnName("DataCadastro")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("DataUltimaReq")
                        .HasColumnName("DataUltimaReq")
                        .HasColumnType("timestamp");

                    b.Property<int>("EmpresaId")
                        .HasColumnName("EmpresaId")
                        .HasColumnType("int");

                    b.Property<int?>("GrupoEmpresaId")
                        .HasColumnName("GrupoEmpresaId")
                        .HasColumnType("int");

                    b.Property<string>("Token")
                        .IsRequired()
                        .HasColumnName("Token")
                        .HasColumnType("varchar(300)");

                    b.HasKey("Id");

                    b.HasIndex("EmpresaId");

                    b.HasIndex("GrupoEmpresaId");

                    b.ToTable("AuthSessionApi");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.AutorizacaoAbastecimento.AutorizacaoAbastecimento", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("Id")
                        .HasColumnType("serial");

                    b.Property<int>("CombustivelId")
                        .HasColumnName("CombustivelId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("DataAtualizacao")
                        .HasColumnName("DataAtualizacao")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("DataCadastro")
                        .HasColumnName("DataCadastro")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DataCancelamento")
                        .HasColumnName("DataCancelamento")
                        .HasColumnType("timestamp");

                    b.Property<int?>("EmpresaId")
                        .HasColumnName("EmpresaId")
                        .HasColumnType("int");

                    b.Property<int?>("FilialId")
                        .HasColumnName("FilialId")
                        .HasColumnType("int");

                    b.Property<int?>("IdentificadorOrigem")
                        .HasColumnName("IdentificadorOrigem")
                        .HasColumnType("int");

                    b.Property<decimal>("Litragem")
                        .HasColumnName("Litragem")
                        .HasColumnType("decimal");

                    b.Property<decimal>("LitragemUtilizada")
                        .HasColumnName("LitragemUtilizada")
                        .HasColumnType("decimal");

                    b.Property<int?>("Metodo")
                        .HasColumnName("Metodo")
                        .HasColumnType("int");

                    b.Property<int>("Status")
                        .HasColumnName("Status")
                        .HasColumnType("int");

                    b.Property<string>("Uf")
                        .HasColumnName("Uf")
                        .HasColumnType("varchar(2)");

                    b.Property<int>("UsuarioCadastroId")
                        .HasColumnName("UsuarioCadastroId")
                        .HasColumnType("int");

                    b.Property<int?>("UsuarioCancelamentoId")
                        .HasColumnName("UsuarioCancelamentoId")
                        .HasColumnType("int");

                    b.Property<int>("VeiculoId")
                        .HasColumnName("VeiculoId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CombustivelId");

                    b.HasIndex("EmpresaId");

                    b.HasIndex("FilialId");

                    b.HasIndex("UsuarioCadastroId");

                    b.HasIndex("VeiculoId");

                    b.ToTable("AutorizacaoAbastecimento");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.Banco.Banco", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnName("Id")
                        .HasColumnType("varchar(5)");

                    b.Property<int>("Ativo")
                        .HasColumnName("Ativo")
                        .HasColumnType("int");

                    b.Property<DateTime?>("DataBloqueio")
                        .HasColumnName("DataBloqueio")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DataCadastro")
                        .IsRequired()
                        .HasColumnName("DataCadastro")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DataDesbloqueio")
                        .HasColumnName("DataDesbloqueio")
                        .HasColumnType("timestamp");

                    b.Property<int?>("IsBbc")
                        .HasColumnName("IsBbc")
                        .HasColumnType("int");

                    b.Property<string>("Nome")
                        .IsRequired()
                        .HasColumnType("varchar(120)");

                    b.Property<int?>("UsuarioBloqueioId")
                        .HasColumnName("UsuarioBloqueioId")
                        .HasColumnType("int");

                    b.Property<int?>("UsuarioCadastroId")
                        .HasColumnName("UsuarioCadastroId")
                        .HasColumnType("int");

                    b.Property<int?>("UsuarioDesbloqueioId")
                        .HasColumnName("UsuarioDesbloqueioId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("UsuarioBloqueioId");

                    b.HasIndex("UsuarioCadastroId");

                    b.HasIndex("UsuarioDesbloqueioId");

                    b.ToTable("Banco");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.BloqueioSpd.BloqueioSpd", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("Id")
                        .HasColumnType("serial");

                    b.Property<int>("Ativo")
                        .HasColumnName("Ativo")
                        .HasColumnType("int");

                    b.Property<int>("Codigo")
                        .HasColumnName("Codigo")
                        .HasColumnType("int");

                    b.Property<string>("Descricao")
                        .IsRequired()
                        .HasColumnName("Descricao")
                        .HasColumnType("varchar(100)");

                    b.Property<int>("EmpresaId")
                        .HasColumnName("EmpresaId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("EmpresaId");

                    b.ToTable("LiberacaoBloqueioSPD");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.CentroCusto.CentroCusto", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("Id")
                        .HasColumnType("serial");

                    b.Property<int>("Ativo")
                        .HasColumnName("Ativo")
                        .HasColumnType("int");

                    b.Property<string>("CodigoCentroCusto")
                        .HasColumnName("CodigoCentroCusto")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("CodigoExterno")
                        .HasColumnName("CodigoExterno")
                        .HasColumnType("varchar(100)");

                    b.Property<DateTime?>("DataBloqueio")
                        .HasColumnName("DataBloqueio")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("DataCadastro")
                        .HasColumnName("DataCadastro")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DataDesbloqueio")
                        .HasColumnName("DataDesbloqueio")
                        .HasColumnType("timestamp");

                    b.Property<string>("Descricao")
                        .IsRequired()
                        .HasColumnName("Descrição")
                        .HasColumnType("varchar(200)");

                    b.Property<int?>("EmpresaId")
                        .HasColumnName("EmpresaId");

                    b.Property<int?>("FilialId")
                        .HasColumnName("FilialId");

                    b.Property<int?>("UsuarioBloqueioId")
                        .HasColumnName("UsuarioBloqueioId")
                        .HasColumnType("int");

                    b.Property<int?>("UsuarioCadastroId")
                        .HasColumnName("UsuarioCadastroId")
                        .HasColumnType("int");

                    b.Property<int?>("UsuarioDesbloqueioId")
                        .HasColumnName("UsuarioDesbloqueioId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("EmpresaId");

                    b.HasIndex("FilialId");

                    b.HasIndex("UsuarioBloqueioId");

                    b.HasIndex("UsuarioCadastroId");

                    b.HasIndex("UsuarioDesbloqueioId");

                    b.ToTable("CentroCusto");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.CFOP.CFOP", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("Id")
                        .HasColumnType("serial");

                    b.Property<int>("Cfop")
                        .HasColumnName("CFOP")
                        .HasColumnType("int");

                    b.Property<string>("Descricao")
                        .IsRequired()
                        .HasColumnName("Descricao")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("CFOP");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.Cidade.Cidade", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("Id")
                        .HasColumnType("serial");

                    b.Property<int>("EstadoId")
                        .HasColumnName("EstadoId");

                    b.Property<int?>("Ibge")
                        .IsRequired()
                        .HasColumnName("Ibge");

                    b.Property<string>("Nome")
                        .IsRequired()
                        .HasColumnName("Nome")
                        .HasColumnType("varchar(40)")
                        .HasMaxLength(60);

                    b.Property<string>("Pais")
                        .IsRequired()
                        .HasColumnName("Pais")
                        .HasColumnType("varchar(40)");

                    b.Property<string>("Uf")
                        .IsRequired()
                        .HasColumnName("Uf")
                        .HasColumnType("varchar(40)");

                    b.HasKey("Id");

                    b.HasIndex("EstadoId");

                    b.ToTable("Cidade");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.CiotVeiculo.CiotVeiculo", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("Id")
                        .HasColumnType("serial");

                    b.Property<int>("DeclaracaoCiotId")
                        .HasColumnName("DeclaracaoCiotId")
                        .HasColumnType("int");

                    b.Property<int>("VeiculoId")
                        .HasColumnName("VeiculoId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("DeclaracaoCiotId");

                    b.HasIndex("VeiculoId");

                    b.ToTable("CiotVeiculo");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.CiotViagem.CiotViagem", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("Id")
                        .HasColumnType("serial");

                    b.Property<int?>("CidadeDestinoId")
                        .HasColumnName("CidadeDestinoId")
                        .HasColumnType("int");

                    b.Property<int?>("CidadeOrigemId")
                        .HasColumnName("CidadeOrigemId")
                        .HasColumnType("int");

                    b.Property<int?>("ConsignatarioId")
                        .HasColumnName("ConsignatarioId")
                        .HasColumnType("int");

                    b.Property<int>("DeclaracaoCiotId")
                        .HasColumnName("DeclaracaoCiotId")
                        .HasColumnType("int");

                    b.Property<int?>("DestinatarioId")
                        .HasColumnName("DestinatarioId")
                        .HasColumnType("int");

                    b.Property<int?>("NaturezaCargaId")
                        .HasColumnName("NaturezaCargaId")
                        .HasColumnType("int");

                    b.Property<decimal?>("Peso")
                        .HasColumnName("Peso")
                        .HasColumnType("numeric(7,2)");

                    b.Property<int?>("RemetenteId")
                        .HasColumnName("RemetenteId")
                        .HasColumnType("int");

                    b.Property<decimal?>("ValorCombustivel")
                        .HasColumnName("ValorCombustivel")
                        .HasColumnType("numeric(16,2)");

                    b.Property<decimal?>("ValorDespesas")
                        .HasColumnName("ValorDespesas")
                        .HasColumnType("numeric(16,2)");

                    b.Property<decimal?>("ValorFrete")
                        .HasColumnName("ValorFrete")
                        .HasColumnType("numeric(16,2)");

                    b.Property<decimal?>("ValorImposto")
                        .HasColumnName("ValorImposto")
                        .HasColumnType("numeric(16,2)");

                    b.Property<decimal?>("ValorPedagio")
                        .HasColumnName("ValorPedagio")
                        .HasColumnType("numeric(16,2)");

                    b.HasKey("Id");

                    b.HasIndex("CidadeDestinoId");

                    b.HasIndex("CidadeOrigemId");

                    b.HasIndex("ConsignatarioId");

                    b.HasIndex("DeclaracaoCiotId");

                    b.HasIndex("DestinatarioId");

                    b.HasIndex("NaturezaCargaId");

                    b.HasIndex("RemetenteId");

                    b.ToTable("CiotViagem");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.Cliente.Cliente", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("Id")
                        .HasColumnType("serial");

                    b.Property<int>("Ativo")
                        .HasColumnName("Ativo")
                        .HasColumnType("int");

                    b.Property<string>("Bairro")
                        .HasColumnName("Bairro")
                        .HasColumnType("varchar(100)");

                    b.Property<string>("Celular")
                        .HasColumnName("Celular")
                        .HasColumnType("varchar(15)");

                    b.Property<string>("Cep")
                        .HasColumnName("Cep")
                        .HasColumnType("varchar(15)");

                    b.Property<int?>("CidadeId")
                        .HasColumnName("CidadeId")
                        .HasColumnType("int");

                    b.Property<string>("Complemento")
                        .HasColumnName("Complemento")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("CpfCnpj")
                        .IsRequired()
                        .HasColumnName("CpfCnpj")
                        .HasColumnType("varchar(14)");

                    b.Property<DateTime?>("DataBloqueio")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("DataCadastro")
                        .HasColumnName("DataCadastro")
                        .HasColumnType("date");

                    b.Property<DateTime?>("DataDesbloqueio")
                        .HasColumnType("timestamp");

                    b.Property<string>("Email")
                        .HasColumnName("Email")
                        .HasColumnType("varchar(200)");

                    b.Property<int>("EmpresaId")
                        .HasColumnName("EmpresaId")
                        .HasColumnType("int");

                    b.Property<string>("Endereco")
                        .HasColumnName("Endereco")
                        .HasColumnType("varchar(200)");

                    b.Property<int?>("EnderecoNumero")
                        .HasColumnName("EnderecoNumero")
                        .HasColumnType("int");

                    b.Property<string>("NomeFantasia")
                        .IsRequired()
                        .HasColumnName("NomeFantasia")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("RazaoSocial")
                        .IsRequired()
                        .HasColumnName("RazaoSocial")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("Telefone")
                        .HasColumnName("Telefone")
                        .HasColumnType("varchar(15)");

                    b.Property<int?>("UsuarioBloqueioId");

                    b.Property<int>("UsuarioCadastroId")
                        .HasColumnName("UsuarioCadastroId")
                        .HasColumnType("int");

                    b.Property<int?>("UsuarioDesbloqueioId");

                    b.HasKey("Id");

                    b.HasIndex("CidadeId");

                    b.HasIndex("EmpresaId");

                    b.HasIndex("UsuarioBloqueioId");

                    b.HasIndex("UsuarioCadastroId");

                    b.HasIndex("UsuarioDesbloqueioId");

                    b.ToTable("Cliente");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.ClientSecret.ClientSecret", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("Id")
                        .HasColumnType("serial");

                    b.Property<int>("Ativo")
                        .HasColumnName("Ativo")
                        .HasColumnType("int");

                    b.Property<DateTime?>("DataAlteracao")
                        .HasColumnName("DataAlteracao")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("DataCadastro")
                        .HasColumnName("DataCadastro")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DataDesativacao")
                        .HasColumnName("DataDesativacao")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DataExpiracao")
                        .HasColumnName("DataExpiracao")
                        .HasColumnType("timestamp");

                    b.Property<string>("Descricao")
                        .HasColumnName("Descricao")
                        .HasColumnType("varchar(200)");

                    b.Property<int?>("GrupoEmpresaId")
                        .HasColumnName("GrupoEmpresaId")
                        .HasColumnType("int");

                    b.Property<int?>("IdEmpresa")
                        .HasColumnName("EmpresaId")
                        .HasColumnType("int");

                    b.Property<string>("SecretKey")
                        .HasColumnName("SecretKey")
                        .HasColumnType("varchar(300)");

                    b.Property<string>("Senha")
                        .HasColumnName("Senha")
                        .HasColumnType("varchar(300)");

                    b.Property<int?>("UsuarioAlteracaoId");

                    b.Property<int>("UsuarioCadastroId");

                    b.HasKey("Id");

                    b.HasIndex("GrupoEmpresaId");

                    b.HasIndex("IdEmpresa");

                    b.HasIndex("UsuarioAlteracaoId");

                    b.HasIndex("UsuarioCadastroId");

                    b.ToTable("ClientSecret");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.Combustivel.Combustivel", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("Id")
                        .HasColumnType("serial");

                    b.Property<int>("Ativo")
                        .HasColumnName("Ativo")
                        .HasColumnType("int");

                    b.Property<string>("CodigoExterno")
                        .HasColumnName("CodigoExterno")
                        .HasColumnType("varchar(100)");

                    b.Property<DateTime>("DataBloqueio")
                        .HasColumnName("DataBloqueio")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("DataCadastro")
                        .HasColumnName("DataCadastro")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("DataDesbloqueio")
                        .HasColumnName("DataDesbloqueio")
                        .HasColumnType("timestamp");

                    b.Property<string>("Nome")
                        .IsRequired()
                        .HasColumnName("Nome")
                        .HasColumnType("varchar(100)");

                    b.Property<string>("UnidadeMedida")
                        .HasColumnName("UnidadeMedida")
                        .HasColumnType("varchar(2)");

                    b.Property<int?>("UsuarioBloqueioId")
                        .HasColumnName("UsuarioBloqueioId")
                        .HasColumnType("int");

                    b.Property<int>("UsuarioCadastroId")
                        .HasColumnName("UsuarioCadastroId")
                        .HasColumnType("int");

                    b.Property<int?>("UsuarioDesbloqueioId")
                        .HasColumnName("UsuarioDesbloqueioId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("UsuarioBloqueioId");

                    b.HasIndex("UsuarioCadastroId");

                    b.HasIndex("UsuarioDesbloqueioId");

                    b.ToTable("Combustivel");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.ContasConductor.ContaConductor", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("Id");

                    b.Property<string>("Agencia")
                        .HasColumnName("Agencia")
                        .HasColumnType("varchar(5)");

                    b.Property<string>("Conta")
                        .IsRequired()
                        .HasColumnName("Conta")
                        .HasColumnType("varchar(20)");

                    b.Property<string>("CpfCnpj")
                        .IsRequired()
                        .HasColumnName("CpfCnpj")
                        .HasColumnType("varchar(14)");

                    b.Property<string>("DigitoVerificadorAgencia")
                        .HasColumnName("DigitoVerificadorAgencia")
                        .HasColumnType("varchar(5)");

                    b.Property<string>("DigitoVerificadorConta")
                        .HasColumnName("DigitoVerificadorConta")
                        .HasColumnType("varchar(5)");

                    b.Property<string>("IdBanco")
                        .HasColumnName("IdBanco")
                        .HasColumnType("varchar(5)");

                    b.Property<string>("IdCartao")
                        .HasColumnName("IdCartao")
                        .HasColumnType("varchar(20)");

                    b.Property<string>("IdConta")
                        .HasColumnName("IdConta")
                        .HasColumnType("varchar(20)");

                    b.Property<string>("Nome")
                        .HasColumnName("Nome")
                        .HasColumnType("varchar(100)");

                    b.HasKey("Id");

                    b.HasIndex("IdBanco");

                    b.ToTable("ContaConductor");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.DeclaracaoCiot.DeclaracaoCiot", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("Id")
                        .HasColumnType("serial");

                    b.Property<string>("Agencia")
                        .HasColumnName("Agencia")
                        .HasColumnType("varchar(10)");

                    b.Property<string>("AvisoTransportador")
                        .HasColumnName("AvisoTransportador")
                        .HasColumnType("varchar(500)");

                    b.Property<string>("Banco")
                        .HasColumnName("Banco")
                        .HasColumnType("varchar(10)");

                    b.Property<string>("Ciot")
                        .IsRequired()
                        .HasColumnName("Ciot")
                        .HasColumnType("varchar(20)");

                    b.Property<string>("Conta")
                        .HasColumnName("Conta")
                        .HasColumnType("varchar(100)");

                    b.Property<DateTime>("DataCadastro")
                        .HasColumnName("DataCadastro")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DataEncerramento")
                        .HasColumnName("DataEncerramento")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("DataFim")
                        .HasColumnName("DataFim")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("DataInicio")
                        .HasColumnName("DataInicio")
                        .HasColumnType("timestamp");

                    b.Property<bool>("EmContigencia")
                        .HasColumnName("EmContigencia")
                        .HasColumnType("boolean");

                    b.Property<int>("EmpresaId")
                        .HasColumnName("EmpresaId")
                        .HasColumnType("int");

                    b.Property<int?>("FilialId")
                        .HasColumnName("FilialId")
                        .HasColumnType("int");

                    b.Property<int>("FormaPagamento")
                        .HasColumnName("FormaPagamento")
                        .HasColumnType("int");

                    b.Property<int>("PortadorMotId")
                        .HasColumnName("PortadorMotId")
                        .HasColumnType("int");

                    b.Property<int>("PortadorPropId")
                        .HasColumnName("PortadorPropId")
                        .HasColumnType("int");

                    b.Property<int?>("QuantidadeTarifas")
                        .HasColumnName("QuantidadeTarifas")
                        .HasColumnType("int");

                    b.Property<string>("RequisicaoCiot")
                        .HasColumnName("RequisicaoCiot")
                        .HasColumnType("xml");

                    b.Property<string>("RetornoCiot")
                        .HasColumnName("RetornoCiot")
                        .HasColumnType("xml");

                    b.Property<string>("SenhaAlteracao")
                        .HasColumnName("SenhaAlteracao")
                        .HasColumnType("varchar(20)");

                    b.Property<int>("Status")
                        .HasColumnName("Status")
                        .HasColumnType("int");

                    b.Property<int>("Tipo")
                        .HasColumnName("Tipo")
                        .HasColumnType("int");

                    b.Property<string>("TipoConta")
                        .HasColumnName("TipoConta")
                        .HasColumnType("varchar(10)");

                    b.Property<int>("TipoEmissao")
                        .HasColumnName("TipoEmissao")
                        .HasColumnType("int");

                    b.Property<int>("UsuarioCadastroId")
                        .HasColumnName("UsuarioCadastroId")
                        .HasColumnType("int");

                    b.Property<decimal?>("ValorCombustivel")
                        .HasColumnName("ValorCombustivel")
                        .HasColumnType("numeric(16,2)");

                    b.Property<decimal?>("ValorDespesas")
                        .HasColumnName("ValorDespesas")
                        .HasColumnType("numeric(16,2)");

                    b.Property<decimal>("ValorFrete")
                        .HasColumnName("ValorFrete")
                        .HasColumnType("numeric(16,2)");

                    b.Property<decimal>("ValorImposto")
                        .HasColumnName("ValorImposto")
                        .HasColumnType("numeric(16,2)");

                    b.Property<decimal?>("ValorPedagio")
                        .HasColumnName("ValorPedagio")
                        .HasColumnType("numeric(16,2)");

                    b.Property<decimal?>("ValorTarifas")
                        .HasColumnName("ValorTarifas")
                        .HasColumnType("numeric(16,2)");

                    b.Property<string>("Verificador")
                        .HasColumnName("Verificador")
                        .HasColumnType("varchar(20)");

                    b.Property<string>("VerificadorConta")
                        .HasColumnName("VerificadorConta")
                        .HasColumnType("varchar(10)");

                    b.HasKey("Id");

                    b.HasIndex("EmpresaId");

                    b.HasIndex("FilialId");

                    b.HasIndex("PortadorMotId");

                    b.HasIndex("PortadorPropId");

                    b.HasIndex("UsuarioCadastroId");

                    b.ToTable("DeclaracaoCiot");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.Documento.Documento", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("Id")
                        .HasColumnType("serial");

                    b.Property<DateTime>("DataCadastro")
                        .HasColumnName("DataCadastro")
                        .HasColumnType("date");

                    b.Property<string>("Descricao")
                        .HasColumnName("Descricao")
                        .HasColumnType("varchar(100)");

                    b.Property<int?>("DocumentosProcessoVinculadoId")
                        .HasColumnName("DocumentosProcessoVinculadoId")
                        .HasColumnType("int");

                    b.Property<int?>("EmpresaId")
                        .HasColumnName("EmpresaId");

                    b.Property<bool>("EnviadoBBC")
                        .HasColumnName("EnviadoBBC")
                        .HasColumnType("boolean");

                    b.Property<string>("Foto")
                        .HasColumnName("Foto")
                        .HasColumnType("text");

                    b.Property<string>("Motivo")
                        .HasColumnName("Motivo")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("Nome")
                        .HasColumnName("Nome")
                        .HasColumnType("varchar(100)");

                    b.Property<int?>("PortadorId")
                        .HasColumnName("PortadorId");

                    b.Property<int?>("PostoId")
                        .HasColumnName("PostoId");

                    b.Property<int?>("Status")
                        .HasColumnName("Status")
                        .HasColumnType("int");

                    b.Property<int>("Tipo")
                        .HasColumnName("Tipo")
                        .HasColumnType("int");

                    b.Property<int?>("UsuarioCadastroId")
                        .HasColumnName("UsuarioCadastroId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("DocumentosProcessoVinculadoId");

                    b.HasIndex("EmpresaId");

                    b.HasIndex("PortadorId");

                    b.HasIndex("PostoId");

                    b.HasIndex("UsuarioCadastroId");

                    b.ToTable("Documento");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.DocumentosProcessoVinculado.DocumentosProcessoVinculado", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("Id")
                        .HasColumnType("serial");

                    b.Property<int>("Ativo")
                        .HasColumnName("Ativo")
                        .HasColumnType("int");

                    b.Property<DateTime?>("DataAlteracao")
                        .HasColumnName("DataAlteracao")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("DataCadastro")
                        .HasColumnName("DataCadastro")
                        .HasColumnType("timestamp");

                    b.Property<string>("Documento")
                        .IsRequired()
                        .HasColumnName("Documento")
                        .HasColumnType("varchar(100)");

                    b.Property<int>("Obrigatorio")
                        .HasColumnName("Obrigatorio")
                        .HasColumnType("int");

                    b.Property<int>("ProcessoVinculadoId")
                        .HasColumnName("ProcessoVinculadoId")
                        .HasColumnType("int");

                    b.Property<int>("Tipo")
                        .HasColumnName("Tipo")
                        .HasColumnType("int");

                    b.Property<int?>("UsuarioAlteracaoId")
                        .HasColumnName("UsuarioAlteracaoId")
                        .HasColumnType("int");

                    b.Property<int?>("UsuarioCadastroId")
                        .HasColumnName("UsuarioCadastroId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("UsuarioAlteracaoId");

                    b.HasIndex("UsuarioCadastroId");

                    b.ToTable("DocumentosProcessoVinculado");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.Empresa.Empresa", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("Id")
                        .HasColumnType("serial");

                    b.Property<int>("Ativo")
                        .HasColumnName("Ativo")
                        .HasColumnType("int");

                    b.Property<string>("Bairro")
                        .IsRequired()
                        .HasColumnName("Bairro")
                        .HasColumnType("varchar(100)");

                    b.Property<decimal?>("Cashback")
                        .HasColumnName("Cashback")
                        .HasColumnType("decimal");

                    b.Property<string>("Celular")
                        .HasColumnName("Celular")
                        .HasColumnType("varchar(15)");

                    b.Property<string>("Cep")
                        .HasColumnName("Cep")
                        .HasColumnType("varchar(15)");

                    b.Property<int?>("CidadeId")
                        .HasColumnName("CidadeId");

                    b.Property<string>("Cnpj")
                        .IsRequired()
                        .HasColumnName("Cnpj")
                        .HasColumnType("varchar(14)");

                    b.Property<int>("CobrancaTarifa")
                        .HasColumnName("CobrancaTarifa")
                        .HasColumnType("int");

                    b.Property<int?>("CobrarTarifaBbcValePedagio")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("CobrarTarifaBbcValePedagio")
                        .HasColumnType("int")
                        .HasDefaultValue(0);

                    b.Property<string>("Complemento")
                        .HasColumnName("Complemento")
                        .HasColumnType("varchar(200)");

                    b.Property<int?>("ContaAbastecimento")
                        .HasColumnName("ContaAbastecimento")
                        .HasColumnType("int");

                    b.Property<int?>("ContaFrete")
                        .HasColumnName("ContaFrete")
                        .HasColumnType("int");

                    b.Property<int?>("ContaValePedagio")
                        .HasColumnName("ContaValePedagio")
                        .HasColumnType("int");

                    b.Property<int?>("ControlaAutonomia")
                        .IsRequired()
                        .HasColumnName("ControlaAutonomia")
                        .HasColumnType("int");

                    b.Property<int>("ControlaContingencia")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("ControlaContingencia")
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.Property<int?>("ControlaOdometro")
                        .IsRequired()
                        .HasColumnName("ControlaOdometro")
                        .HasColumnType("int");

                    b.Property<DateTime?>("DataAberturaEmpresa")
                        .HasColumnName("DataAberturaEmpresa")
                        .HasColumnType("date");

                    b.Property<DateTime?>("DataAlteracaoContaAbastecimento")
                        .HasColumnName("DataAlteracaoContaAbastecimento")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DataAlteracaoContaFrete")
                        .HasColumnName("DataAlteracaoContaFrete")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DataAlteracaoContaValePedagio")
                        .HasColumnName("DataAlteracaoContaValePedagio")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DataAlteracaoModelo")
                        .HasColumnName("DataAlteracaoModelo")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DataBloqueio")
                        .HasColumnName("DataBloqueio")
                        .HasColumnType("date");

                    b.Property<DateTime>("DataCadastro")
                        .HasColumnName("DataCadastro")
                        .HasColumnType("date");

                    b.Property<DateTime?>("DataDesbloqueio")
                        .HasColumnName("DataDesbloqueio")
                        .HasColumnType("date");

                    b.Property<DateTime?>("DataValidacao")
                        .HasColumnName("DataValidacao")
                        .HasColumnType("date");

                    b.Property<int?>("DebitoPrazo")
                        .HasColumnName("DebitoPrazo")
                        .HasColumnType("int");

                    b.Property<int?>("DebitoProtocolo")
                        .HasColumnName("DebitoProtocolo")
                        .HasColumnType("int");

                    b.Property<string>("Email")
                        .HasColumnName("Email")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("Endereco")
                        .HasColumnName("Endereco")
                        .HasColumnType("varchar(200)");

                    b.Property<int?>("EnderecoNumero")
                        .HasColumnName("EnderecoNumber")
                        .HasColumnType("int");

                    b.Property<string>("FormaConstituicao")
                        .HasColumnName("FormaConstituicao")
                        .HasColumnType("varchar(200)");

                    b.Property<int?>("GrupoEmpresaId");

                    b.Property<int>("HabilitaPainelSaldo")
                        .HasColumnName("HabilitaPainelSaldo")
                        .HasColumnType("int");

                    b.Property<int?>("HabilitaReprocessamentoValePedagio")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnName("HabilitaReprocessamentoValePedagio")
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.Property<byte[]>("ImagemCartao")
                        .HasColumnName("ImagemCartao")
                        .HasColumnType("bytea");

                    b.Property<decimal?>("ImpostoCOFINS")
                        .HasColumnName("ImpostoCOFINS")
                        .HasColumnType("decimal");

                    b.Property<decimal?>("ImpostoCSLL")
                        .HasColumnName("ImpostoCSLL")
                        .HasColumnType("decimal");

                    b.Property<decimal?>("ImpostoIRRF")
                        .HasColumnName("ImpostoIRRF")
                        .HasColumnType("decimal");

                    b.Property<decimal?>("ImpostoPIS")
                        .HasColumnName("ImpostoPIS")
                        .HasColumnType("decimal");

                    b.Property<string>("InscricaoEstadual")
                        .HasColumnName("InscricaoEstadual")
                        .HasColumnType("varchar(20)");

                    b.Property<int>("LiberaBloqueioSPD")
                        .HasColumnName("LiberaBloqueioSPD")
                        .HasColumnType("int");

                    b.Property<string>("Link")
                        .HasColumnName("Link")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("LinkSAP")
                        .HasColumnName("LinkSAP")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("NomeFantasia")
                        .IsRequired()
                        .HasColumnName("NomeFantasia")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("NotificacaoContingenciaCiot")
                        .HasColumnName("NotificacaoContingenciaCiot")
                        .HasColumnType("varchar(1500)");

                    b.Property<string>("ParecerExterno")
                        .HasColumnName("ParecerExterno")
                        .HasColumnType("varchar(500)");

                    b.Property<string>("ParecerInterno")
                        .HasColumnName("ParecerInterno")
                        .HasColumnType("varchar(500)");

                    b.Property<decimal?>("PercentualAutonomiaInferior")
                        .HasColumnName("PercentualAutonomiaInferior")
                        .HasColumnType("decimal");

                    b.Property<decimal?>("PercentualAutonomiaSuperior")
                        .HasColumnName("PercentualAutonomiaSuperior")
                        .HasColumnType("decimal");

                    b.Property<int?>("PermitirEncerramentoPainelCiot")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnName("PermitirEncerramentoPainelCiot")
                        .HasColumnType("int")
                        .HasDefaultValue(0);

                    b.Property<int?>("PermitirPagamentoValePedagio")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("PermitirPagamentoValePedagio")
                        .HasColumnType("int")
                        .HasDefaultValue(0);

                    b.Property<decimal?>("PorcentagemTarifaServiceValePedagio")
                        .HasColumnName("PorcentagemTarifaServiceValePedagio")
                        .HasColumnType("decimal");

                    b.Property<int?>("Prazo")
                        .HasColumnName("Prazo")
                        .HasColumnType("int");

                    b.Property<int?>("QtdMensalSemTaxaPix")
                        .HasColumnName("QtdMensalSemTaxaPix")
                        .HasColumnType("int");

                    b.Property<string>("RNTRC")
                        .HasColumnName("RNTRC")
                        .HasColumnType("varchar(15)");

                    b.Property<string>("RazaoSocial")
                        .IsRequired()
                        .HasColumnName("RazaoSocial")
                        .HasColumnType("varchar(200)");

                    b.Property<bool?>("RecebedorAutorizado")
                        .HasColumnName("RecebedorAutorizado")
                        .HasColumnType("boolean");

                    b.Property<int?>("RegistraCiot")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("RegistraCiot")
                        .HasColumnType("int")
                        .HasDefaultValue(0);

                    b.Property<string>("SenhaApi")
                        .HasColumnName("SenhaApi")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("SenhaLink")
                        .HasColumnName("SenhaLink")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("SenhaSAP")
                        .HasColumnName("SenhaSAP")
                        .HasColumnType("varchar(200)");

                    b.Property<int>("StatusCadastro")
                        .HasColumnName("StatusCadastro")
                        .HasColumnType("int");

                    b.Property<int?>("StatusReprocessamentoPagamentoFrete")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("StatusReprocessamentoPagamentoFrete")
                        .HasColumnType("int")
                        .HasDefaultValue(0);

                    b.Property<decimal?>("TaxaAbastecimento")
                        .HasColumnName("TaxaAbastecimento")
                        .HasColumnType("decimal");

                    b.Property<string>("Telefone")
                        .HasColumnName("Telefone")
                        .HasColumnType("varchar(15)");

                    b.Property<int?>("TempoAbastecimento")
                        .IsRequired()
                        .HasColumnName("TempoAbastecimento")
                        .HasColumnType("int");

                    b.Property<int?>("TipoEmpresaId")
                        .HasColumnName("TipoEmpresaId");

                    b.Property<int?>("UsuarioAlteracaoContaAbastecimentoId")
                        .HasColumnName("UsuarioAlteracaoContaAbastecimentoId")
                        .HasColumnType("int");

                    b.Property<int?>("UsuarioAlteracaoContaFreteId")
                        .HasColumnName("UsuarioAlteracaoContaFreteId")
                        .HasColumnType("int");

                    b.Property<int?>("UsuarioAlteracaoContaValePedagioId")
                        .HasColumnName("UsuarioAlteracaoContaValePedagioId")
                        .HasColumnType("int");

                    b.Property<int?>("UsuarioBloqueioId")
                        .HasColumnName("UsuarioBloqueioId")
                        .HasColumnType("int");

                    b.Property<int?>("UsuarioCadastroId")
                        .HasColumnName("UsuarioCadastroId")
                        .HasColumnType("int");

                    b.Property<int?>("UsuarioDesbloqueioId")
                        .HasColumnName("UsuarioDesbloqueioId")
                        .HasColumnType("int");

                    b.Property<string>("UsuarioSAP")
                        .HasColumnName("UsuarioSAP")
                        .HasColumnType("varchar(200)");

                    b.Property<int?>("UsuarioValidacaoId")
                        .HasColumnName("UsuarioValidacaoId")
                        .HasColumnType("int");

                    b.Property<int?>("UtilizaCiot")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnName("UtilizaCiot")
                        .HasColumnType("int")
                        .HasDefaultValue(0);

                    b.Property<int>("UtilizaTarifaEmpresa")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("UtilizaTarifaEmpresa")
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.Property<int?>("UtilizaTarifaEmpresaPagamentoPedagio")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("UtilizaTarifaEmpresaPagamentoPedagio")
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.Property<decimal>("ValorAdiantamentoBbc")
                        .HasColumnName("ValorAdiantamentoBBC")
                        .HasColumnType("decimal");

                    b.Property<decimal?>("ValorTarifaBbc")
                        .HasColumnName("ValorTarifaBbc")
                        .HasColumnType("decimal");

                    b.Property<decimal?>("ValorTarifaPix")
                        .HasColumnName("ValorTarifaPix")
                        .HasColumnType("decimal");

                    b.Property<decimal?>("ValorTolerancia")
                        .IsRequired()
                        .HasColumnName("ValorTolerancia")
                        .HasColumnType("decimal");

                    b.HasKey("Id");

                    b.HasIndex("CidadeId");

                    b.HasIndex("GrupoEmpresaId");

                    b.HasIndex("TipoEmpresaId");

                    b.HasIndex("UsuarioAlteracaoContaAbastecimentoId");

                    b.HasIndex("UsuarioBloqueioId");

                    b.HasIndex("UsuarioCadastroId");

                    b.HasIndex("UsuarioDesbloqueioId");

                    b.ToTable("Empresa");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.EmpresaCfop.EmpresaCfop", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("Id")
                        .HasColumnType("serial");

                    b.Property<int>("CfopId")
                        .HasColumnName("CFOPId")
                        .HasColumnType("int");

                    b.Property<int>("EmpresaId")
                        .HasColumnName("EmpresaId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CfopId");

                    b.HasIndex("EmpresaId");

                    b.ToTable("EmpresaCfop");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.Emprestimo.Emprestimo", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("Id")
                        .HasColumnType("serial");

                    b.Property<string>("Agencia")
                        .HasColumnName("Agencia")
                        .HasColumnType("varchar(10)");

                    b.Property<string>("Conta")
                        .HasColumnName("Conta")
                        .HasColumnType("varchar(100)");

                    b.Property<string>("CpfCnpjPortador")
                        .HasColumnName("CpfCnpjPortador")
                        .HasColumnType("varchar(14)");

                    b.Property<DateTime?>("DataEmprestimo")
                        .IsRequired()
                        .HasColumnName("DataEmprestimo")
                        .HasColumnType("date");

                    b.Property<string>("IdState")
                        .HasColumnName("IdState")
                        .HasColumnType("varchar(100)");

                    b.Property<int?>("PortadorId")
                        .HasColumnName("PortadorId")
                        .HasColumnType("int");

                    b.Property<int>("Status")
                        .HasColumnName("Status")
                        .HasColumnType("int");

                    b.Property<decimal>("TaxaRetencao")
                        .HasColumnName("TaxaRetencao")
                        .HasColumnType("numeric(16,2)");

                    b.Property<decimal>("ValorAquisicao")
                        .HasColumnName("ValorAquisicao")
                        .HasColumnType("numeric(16,2)");

                    b.Property<decimal?>("ValorPago")
                        .HasColumnName("ValorPago")
                        .HasColumnType("numeric(16,2)");

                    b.HasKey("Id");

                    b.HasIndex("PortadorId");

                    b.ToTable("Emprestimo");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.Estado.Estado", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("Id")
                        .HasColumnType("serial");

                    b.Property<string>("Nome")
                        .IsRequired()
                        .HasColumnName("Nome")
                        .HasColumnType("varchar(40)")
                        .HasMaxLength(60);

                    b.Property<string>("Pais")
                        .IsRequired()
                        .HasColumnName("Pais")
                        .HasColumnType("varchar(40)");

                    b.Property<string>("Uf")
                        .IsRequired()
                        .HasColumnName("Uf")
                        .HasColumnType("varchar(40)");

                    b.HasKey("Id");

                    b.ToTable("Estado");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.Fabricante.Fabricante", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("Id")
                        .HasColumnType("serial");

                    b.Property<int>("Ativo")
                        .HasColumnName("Ativo")
                        .HasColumnType("int");

                    b.Property<DateTime>("DataBloqueio")
                        .HasColumnName("DataBloqueio")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("DataCadastro")
                        .HasColumnName("DataCadastro")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("DataDesbloqueio")
                        .HasColumnName("DataDesbloqueio")
                        .HasColumnType("timestamp");

                    b.Property<string>("Nome")
                        .IsRequired()
                        .HasColumnName("Nome")
                        .HasColumnType("varchar(200)");

                    b.Property<int?>("UsuarioBloqueioId")
                        .HasColumnName("UsuarioBloqueioId")
                        .HasColumnType("int");

                    b.Property<int>("UsuarioCadastroId")
                        .HasColumnName("UsuarioCadastroId")
                        .HasColumnType("int");

                    b.Property<int?>("UsuarioDesbloqueioId")
                        .HasColumnName("UsuarioDesbloqueioId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("UsuarioBloqueioId");

                    b.HasIndex("UsuarioCadastroId");

                    b.HasIndex("UsuarioDesbloqueioId");

                    b.ToTable("Fabricante");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.Filial.Filial", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("Id")
                        .HasColumnType("serial");

                    b.Property<int>("Ativo")
                        .HasColumnName("Ativo")
                        .HasColumnType("int");

                    b.Property<string>("Bairro")
                        .IsRequired()
                        .HasColumnName("Bairro")
                        .HasColumnType("varchar(100)");

                    b.Property<string>("Celular")
                        .HasColumnName("Celular")
                        .HasColumnType("varchar(15)");

                    b.Property<string>("Cep")
                        .HasColumnName("Cep")
                        .HasColumnType("varchar(15)");

                    b.Property<int?>("CidadeId")
                        .HasColumnName("CidadeId")
                        .HasColumnType("int");

                    b.Property<string>("Cnpj")
                        .IsRequired()
                        .HasColumnName("Cnpj")
                        .HasColumnType("varchar(14)");

                    b.Property<string>("Complemento")
                        .HasColumnName("Complemento")
                        .HasColumnType("varchar(200)");

                    b.Property<DateTime?>("DataBloqueio")
                        .HasColumnName("DataBloqueio")
                        .HasColumnType("date");

                    b.Property<DateTime>("DataCadastro")
                        .HasColumnName("DataCadastro")
                        .HasColumnType("date");

                    b.Property<DateTime?>("DataDesbloqueio")
                        .HasColumnName("DataDesbloqueio")
                        .HasColumnType("date");

                    b.Property<string>("Email")
                        .HasColumnName("Email")
                        .HasColumnType("varchar(200)");

                    b.Property<int>("EmpresaId")
                        .HasColumnName("EmpresaId")
                        .HasColumnType("int");

                    b.Property<string>("Endereco")
                        .HasColumnName("Endereco")
                        .HasColumnType("varchar(200)");

                    b.Property<int?>("EnderecoNumero")
                        .HasColumnName("EnderecoNumero")
                        .HasColumnType("int");

                    b.Property<int>("Faturar")
                        .HasColumnName("Faturar")
                        .HasColumnType("int");

                    b.Property<string>("HorarioLimiteAbastecimentoFim")
                        .HasColumnType("varchar(40)");

                    b.Property<string>("HorarioLimiteAbastecimentoInicio")
                        .HasColumnType("varchar(40)");

                    b.Property<string>("NomeFantasia")
                        .IsRequired()
                        .HasColumnName("NomeFantasia")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("Operacao")
                        .HasColumnName("Operacao")
                        .HasColumnType("varchar(100)");

                    b.Property<string>("RazaoSocial")
                        .IsRequired()
                        .HasColumnName("RazaoSocial")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("Telefone")
                        .HasColumnName("Telefone")
                        .HasColumnType("varchar(15)");

                    b.Property<int?>("UsuarioBloqueioId")
                        .HasColumnName("UsuarioBloqueioId")
                        .HasColumnType("int");

                    b.Property<int>("UsuarioCadastroId")
                        .HasColumnName("UsuarioCadastroId")
                        .HasColumnType("int");

                    b.Property<int?>("UsuarioDesbloqueioId");

                    b.HasKey("Id");

                    b.HasIndex("CidadeId");

                    b.HasIndex("EmpresaId");

                    b.HasIndex("UsuarioBloqueioId");

                    b.HasIndex("UsuarioCadastroId");

                    b.HasIndex("UsuarioDesbloqueioId");

                    b.ToTable("Filial");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.GrupoEmpresa.GrupoEmpresa", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("Id")
                        .HasColumnType("serial");

                    b.Property<int>("Ativo")
                        .HasColumnName("Ativo")
                        .HasColumnType("int");

                    b.Property<string>("Cnpj")
                        .IsRequired()
                        .HasColumnName("Cnpj")
                        .HasColumnType("varchar(14)");

                    b.Property<int>("CobrancaTarifa")
                        .HasColumnName("CobrancaTarifa")
                        .HasColumnType("int");

                    b.Property<int>("CobrarTarifaBbcValePedagio")
                        .HasColumnName("CobrarTarifaBbcValePedagio")
                        .HasColumnType("int");

                    b.Property<DateTime?>("DataAlteracao")
                        .HasColumnName("DataAlteracao")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DataBloqueio")
                        .HasColumnName("DataBloqueio")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("DataCadastro")
                        .HasColumnName("DataCadastro")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DataDesbloqueio")
                        .HasColumnType("timestamp");

                    b.Property<string>("EmailsNotificacaoContingenciaCiot")
                        .HasColumnName("EmailsNotificacaoContingenciaCiot")
                        .HasColumnType("varchar(1500)");

                    b.Property<int?>("HabilitaReprocessamentoValePedagio")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnName("HabilitaReprocessamentoValePedagio")
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.Property<decimal?>("PercentualTarifaBbc")
                        .HasColumnName("PercentualTarifaBbc")
                        .HasColumnType("decimal");

                    b.Property<decimal?>("PercentualTarifaValePedagio")
                        .HasColumnName("PercentualTarifaValePedagio")
                        .HasColumnType("decimal");

                    b.Property<int?>("QtdMensalSemTaxaPix")
                        .HasColumnName("QtdMensalSemTaxaPix")
                        .HasColumnType("int");

                    b.Property<string>("RazaoSocial")
                        .IsRequired()
                        .HasColumnName("RazaoSocial")
                        .HasColumnType("varchar(200)");

                    b.Property<int>("StatusReprocessamentoPagamentoFrete")
                        .HasColumnName("StatusReprocessamentoPagamentoFrete")
                        .HasColumnType("int");

                    b.Property<int?>("UsuarioAlteracaoId")
                        .HasColumnName("UsuarioAlteracaoId")
                        .HasColumnType("int");

                    b.Property<int?>("UsuarioBloqueioId")
                        .HasColumnName("UsuarioBloqueioId")
                        .HasColumnType("int");

                    b.Property<int>("UsuarioCadastroId")
                        .HasColumnName("UsuarioCadastroId")
                        .HasColumnType("int");

                    b.Property<int?>("UsuarioDesbloqueioId")
                        .HasColumnName("UsuarioDesbloqueioId")
                        .HasColumnType("int");

                    b.Property<decimal?>("ValorTarifaPix")
                        .HasColumnName("ValorTarifaPix")
                        .HasColumnType("decimal");

                    b.HasKey("Id");

                    b.HasIndex("UsuarioAlteracaoId");

                    b.HasIndex("UsuarioBloqueioId");

                    b.HasIndex("UsuarioCadastroId");

                    b.HasIndex("UsuarioDesbloqueioId");

                    b.ToTable("GrupoEmpresa");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.GrupoUsuario.GrupoUsuario", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("Id")
                        .HasColumnType("serial");

                    b.Property<string>("Descricao")
                        .IsRequired()
                        .HasColumnName("Descricao")
                        .HasColumnType("varchar(40)")
                        .HasMaxLength(100);

                    b.Property<int?>("EmpresaId")
                        .HasColumnName("EmpresaId")
                        .HasColumnType("int");

                    b.Property<int?>("PostoId")
                        .HasColumnName("PostoId")
                        .HasColumnType("int");

                    b.Property<int>("Sistema")
                        .HasColumnName("Sistema")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("EmpresaId");

                    b.HasIndex("PostoId");

                    b.ToTable("GrupoUsuario");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.GrupoUsuarioMenu.GrupoUsuarioMenu", b =>
                {
                    b.Property<int>("GrupoUsuarioId")
                        .HasColumnName("GrupoUsuarioId");

                    b.Property<int>("MenuId")
                        .HasColumnName("MenuId");

                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("Id")
                        .HasColumnType("serial");

                    b.HasKey("GrupoUsuarioId", "MenuId");

                    b.HasIndex("MenuId");

                    b.ToTable("GrupoUsuarioMenu");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.LotePagamento.LotePagamento", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("Id")
                        .HasColumnType("serial");

                    b.Property<DateTime>("DataCadastro")
                        .HasColumnName("DataCadastro")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("DataPrevisaoPagamento")
                        .HasColumnName("DataPrevisaoPagamento")
                        .HasColumnType("timestamp");

                    b.Property<int>("EmpresaId")
                        .HasColumnName("EmpresaId")
                        .HasColumnType("int");

                    b.Property<string>("ObservacaoJson")
                        .HasColumnName("ObservacaoJson")
                        .HasColumnType("text");

                    b.Property<int?>("PagamentoReceitaEmpresaId")
                        .HasColumnName("PagamentoReceitaEmpresaId")
                        .HasColumnType("int");

                    b.Property<int?>("PagamentoReceitaMdrId")
                        .HasColumnName("PagamentoReceitaMdrId")
                        .HasColumnType("int");

                    b.Property<int?>("PostoId")
                        .HasColumnName("PostoId")
                        .HasColumnType("int");

                    b.Property<int>("TipoLote")
                        .HasColumnName("TipoLote")
                        .HasColumnType("int");

                    b.Property<decimal>("ValorPagamento")
                        .HasColumnName("ValorPagamento")
                        .HasColumnType("decimal");

                    b.HasKey("Id");

                    b.HasIndex("EmpresaId");

                    b.HasIndex("PostoId");

                    b.ToTable("LotePagamento");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.MDRPrazos.MDRPrazos", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("Id")
                        .HasColumnType("serial");

                    b.Property<int>("Ativo")
                        .HasColumnName("Ativo")
                        .HasColumnType("int");

                    b.Property<string>("BancoId")
                        .IsRequired()
                        .HasColumnName("BancoId")
                        .HasColumnType("varchar(5)");

                    b.Property<DateTime?>("DataBloqueio")
                        .HasColumnName("DataBloqueio")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("DataCadastro")
                        .HasColumnName("DataCadastro")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DataDesbloqueio")
                        .HasColumnName("DataDesbloqueio")
                        .HasColumnType("timestamp");

                    b.Property<string>("Descricao")
                        .HasColumnName("Descricao")
                        .HasColumnType("varchar(200)");

                    b.Property<decimal?>("MDR")
                        .HasColumnName("MDR")
                        .HasColumnType("decimal");

                    b.Property<int?>("Prazo")
                        .HasColumnName("Prazo")
                        .HasColumnType("int");

                    b.Property<int?>("UsuarioBloqueioId")
                        .HasColumnName("UsuarioBloqueioId")
                        .HasColumnType("int");

                    b.Property<int?>("UsuarioCadastroId")
                        .HasColumnName("UsuarioCadastroId")
                        .HasColumnType("int");

                    b.Property<int?>("UsuarioDesbloqueioId")
                        .HasColumnName("UsuarioDesbloqueioId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("BancoId");

                    b.HasIndex("UsuarioBloqueioId");

                    b.HasIndex("UsuarioCadastroId");

                    b.HasIndex("UsuarioDesbloqueioId");

                    b.ToTable("MDRPrazos");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.Mensagem.Mensagem", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("Id")
                        .HasColumnType("serial");

                    b.Property<int>("Ativo")
                        .HasColumnName("Ativo")
                        .HasColumnType("int");

                    b.Property<int>("CodigoAplicacao")
                        .HasColumnName("CodigoAplicacao")
                        .HasColumnType("int");

                    b.Property<int>("CodigoMensagem")
                        .HasColumnName("CodigoMensagem")
                        .HasColumnType("int");

                    b.Property<DateTime>("DataBloqueio")
                        .HasColumnName("DataBloqueio")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("DataCadastro")
                        .HasColumnName("DataCadastro")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("DataDesbloqueio")
                        .HasColumnName("DataDesbloqueio")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DataFimMensagem")
                        .HasColumnName("DataFimMensagem")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DataInicioMensagem")
                        .HasColumnName("DataInicioMensagem")
                        .HasColumnType("timestamp");

                    b.Property<string>("DescricaoMensagem")
                        .HasColumnName("DescricaoMensagem")
                        .HasColumnType("varchar(1000)");

                    b.Property<string>("ImagemMensagem")
                        .HasColumnName("ImagemMensagem")
                        .HasColumnType("xml");

                    b.Property<int?>("MensagemTratada")
                        .HasColumnName("MensagemTratada")
                        .HasColumnType("int");

                    b.Property<string>("TextoMensagem")
                        .HasColumnName("TextoMensagem")
                        .HasColumnType("varchar(1000)");

                    b.Property<string>("TextoMensagemOriginal")
                        .HasColumnName("TextoMensagemOriginal")
                        .HasColumnType("varchar(5000)");

                    b.Property<string>("TextoMensagemPadrao")
                        .HasColumnName("TextoMensagemPadrao")
                        .HasColumnType("varchar(1000)");

                    b.Property<int>("TipoMensagem")
                        .HasColumnName("TipoMensagem")
                        .HasColumnType("int");

                    b.Property<int?>("UsuarioBloqueioId")
                        .HasColumnName("UsuarioBloqueioId")
                        .HasColumnType("int");

                    b.Property<int?>("UsuarioDesbloqueioId")
                        .HasColumnName("UsuarioDesbloqueioId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("UsuarioBloqueioId");

                    b.HasIndex("UsuarioDesbloqueioId");

                    b.ToTable("Mensagem");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.Menu.Menu", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("Id")
                        .HasColumnType("serial");

                    b.Property<string>("Descricao")
                        .HasColumnType("varchar(500)");

                    b.Property<int?>("IsMenuPai");

                    b.Property<int>("IsMostraApenasAdmin");

                    b.Property<string>("Link")
                        .HasColumnType("varchar(40)");

                    b.Property<string>("LinkImagemMenu")
                        .HasColumnType("varchar(40)");

                    b.Property<int?>("MenuPaiId");

                    b.Property<int>("Sequencia");

                    b.HasKey("Id");

                    b.HasIndex("MenuPaiId");

                    b.ToTable("Menu");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.Modelo.Modelo", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("Id")
                        .HasColumnType("serial");

                    b.Property<int?>("Ano");

                    b.Property<int>("Ativo");

                    b.Property<DateTime>("DataBloqueio")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("DataCadastro")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("DataDesbloqueio")
                        .HasColumnType("timestamp");

                    b.Property<int?>("FabricanteId");

                    b.Property<decimal>("MediaMinima")
                        .HasColumnType("decimal(10, 2)");

                    b.Property<decimal>("MediaSugerida")
                        .HasColumnType("decimal(10, 2)");

                    b.Property<string>("Nome")
                        .HasColumnType("varchar(40)");

                    b.Property<int?>("UsuarioBloqueioId");

                    b.Property<int>("UsuarioCadastroId");

                    b.Property<int?>("UsuarioDesbloqueioId");

                    b.HasKey("Id");

                    b.HasIndex("FabricanteId");

                    b.HasIndex("UsuarioBloqueioId");

                    b.HasIndex("UsuarioCadastroId");

                    b.HasIndex("UsuarioDesbloqueioId");

                    b.ToTable("Modelo");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.Modulo.Modulo", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("Id")
                        .HasColumnType("serial");

                    b.Property<string>("Descricao")
                        .IsRequired()
                        .HasColumnName("Descricao")
                        .HasColumnType("varchar(100)");

                    b.Property<string>("Icone")
                        .IsRequired()
                        .HasColumnName("Icone")
                        .HasColumnType("varchar(100)");

                    b.Property<int>("IsMostraApenasAdmin")
                        .HasColumnName("IsMostraApenasAdmin")
                        .HasColumnType("int");

                    b.Property<int>("Sequencia")
                        .HasColumnName("Sequencia")
                        .HasColumnType("int");

                    b.Property<int>("Sistema")
                        .HasColumnName("Sistema")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("Modulo");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.ModuloMenu.ModuloMenu", b =>
                {
                    b.Property<int>("ModuloId")
                        .HasColumnName("ModuloId");

                    b.Property<int>("MenuId")
                        .HasColumnName("MenuId");

                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("Id")
                        .HasColumnType("serial");

                    b.HasKey("ModuloId", "MenuId");

                    b.HasIndex("MenuId");

                    b.ToTable("ModuloMenu");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.NaturezaCarga.NaturezaCarga", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("Id")
                        .HasColumnType("serial");

                    b.Property<string>("Codigo")
                        .IsRequired()
                        .HasColumnName("Codigo")
                        .HasColumnType("varchar(5)");

                    b.Property<string>("Descricao")
                        .IsRequired()
                        .HasColumnName("Descricao")
                        .HasColumnType("varchar(2000)");

                    b.HasKey("Id");

                    b.ToTable("NaturezaCarga");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.Notificacao.Notificacao", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("Id")
                        .HasColumnType("serial");

                    b.Property<DateTime?>("DataAlteracao")
                        .HasColumnName("DataAlteracao")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("DataCadastro")
                        .HasColumnName("DataCadastro")
                        .HasColumnType("timestamp");

                    b.Property<string>("Descricao")
                        .IsRequired()
                        .HasColumnName("Descricao")
                        .HasColumnType("varchar(200)");

                    b.Property<int?>("PagamentoEventoId")
                        .HasColumnName("PagamentoEventoId")
                        .HasColumnType("int");

                    b.Property<int?>("Status")
                        .HasColumnName("Status")
                        .HasColumnType("int");

                    b.Property<int?>("Tipo")
                        .HasColumnName("Tipo")
                        .HasColumnType("int");

                    b.Property<int?>("UsuarioAlteracaoId")
                        .HasColumnName("UsuarioAlteracaoId")
                        .HasColumnType("int");

                    b.Property<int?>("UsuarioCadastroId")
                        .HasColumnName("UsuarioCadastroId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("PagamentoEventoId");

                    b.HasIndex("UsuarioAlteracaoId");

                    b.HasIndex("UsuarioCadastroId");

                    b.ToTable("Notificacao");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.PagamentoAbastecimento.PagamentoAbastecimento", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("Id")
                        .HasColumnType("serial");

                    b.Property<int?>("AbastecimentoId")
                        .HasColumnName("AbastecimentoId")
                        .HasColumnType("int");

                    b.Property<decimal?>("CashbackTransacao")
                        .HasColumnName("CashbackTransacao")
                        .HasColumnType("numeric(16,2)");

                    b.Property<int>("ContaDestino")
                        .HasColumnName("ContaDestino")
                        .HasColumnType("int");

                    b.Property<int>("ContaOrigem")
                        .HasColumnName("ContaOrigem")
                        .HasColumnType("int");

                    b.Property<int?>("ContadorTentativas")
                        .HasColumnName("ContadorTentativas")
                        .HasColumnType("int");

                    b.Property<DateTime?>("DataAlteracao")
                        .HasColumnName("DataAlteracao")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DataBaixa")
                        .HasColumnName("DataBaixa")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("DataCadastro")
                        .HasColumnName("DataCadastro")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DataCancelamento")
                        .HasColumnName("DataCancelamento")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DataPrevisaoPagamento")
                        .HasColumnName("DataPrevisaoPagamento")
                        .HasColumnType("timestamp");

                    b.Property<decimal>("DescontoMDR")
                        .HasColumnName("DescontoMDR")
                        .HasColumnType("numeric(16,3)");

                    b.Property<decimal?>("ImpostoCOFINSTransacao")
                        .HasColumnName("ImpostoCOFINSTransacao")
                        .HasColumnType("numeric(16,2)");

                    b.Property<decimal?>("ImpostoCSLLTransacao")
                        .HasColumnName("ImpostoCSLLTransacao")
                        .HasColumnType("numeric(16,2)");

                    b.Property<decimal?>("ImpostoIRRFTransacao")
                        .HasColumnName("ImpostoIRRFTransacao")
                        .HasColumnType("numeric(16,2)");

                    b.Property<decimal?>("ImpostoPISTransacao")
                        .HasColumnName("ImpostoPISTransacao")
                        .HasColumnType("numeric(16,2)");

                    b.Property<int?>("LotePagamentoId")
                        .HasColumnName("LotePagamentoId")
                        .HasColumnType("int");

                    b.Property<int?>("LoteReceitaId")
                        .HasColumnName("LoteReceitaId")
                        .HasColumnType("int");

                    b.Property<string>("Observacao")
                        .HasColumnName("Observacao")
                        .HasColumnType("text");

                    b.Property<int?>("PagamentoReceitaId")
                        .HasColumnName("PagamentoReceitaId")
                        .HasColumnType("int");

                    b.Property<string>("PedidoSAP")
                        .HasColumnName("PedidoSAP")
                        .HasColumnType("text");

                    b.Property<int>("Status")
                        .HasColumnName("Status")
                        .HasColumnType("int");

                    b.Property<decimal>("TaxaAbastecimento")
                        .HasColumnName("TaxaAbastecimento")
                        .HasColumnType("numeric(16,3)");

                    b.Property<int>("TipoOperacao")
                        .HasColumnName("TipoOperacao")
                        .HasColumnType("int");

                    b.Property<int?>("UsuarioAlteracaoId")
                        .HasColumnName("UsuarioAlteracaoId")
                        .HasColumnType("int");

                    b.Property<int?>("UsuarioCadastroId")
                        .HasColumnName("UsuarioCadastroId")
                        .HasColumnType("int");

                    b.Property<int?>("UsuarioCancelamentoId")
                        .HasColumnName("UsuarioCancelamentoId")
                        .HasColumnType("int");

                    b.Property<decimal>("ValorAbastecimento")
                        .HasColumnName("ValorAbastecimento")
                        .HasColumnType("numeric(16,3)");

                    b.Property<decimal>("ValorAbastecimentoAcrescimoTaxa")
                        .HasColumnName("ValorAbastecimentoAcrescimoTaxa")
                        .HasColumnType("numeric(16,3)");

                    b.Property<decimal>("ValorAbastecimentoDesconto")
                        .HasColumnName("ValorAbastecimentoDesconto")
                        .HasColumnType("numeric(16,3)");

                    b.HasKey("Id");

                    b.HasIndex("AbastecimentoId");

                    b.HasIndex("LotePagamentoId");

                    b.HasIndex("PagamentoReceitaId");

                    b.HasIndex("UsuarioAlteracaoId");

                    b.HasIndex("UsuarioCadastroId");

                    b.HasIndex("UsuarioCancelamentoId");

                    b.ToTable("PagamentoAbastecimento");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.PagamentoEvento.PagamentoEvento", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("Id")
                        .HasColumnType("serial");

                    b.Property<string>("Agencia")
                        .HasColumnName("Agencia")
                        .HasColumnType("varchar(150)");

                    b.Property<string>("ChavePix")
                        .HasColumnName("ChavePix")
                        .HasColumnType("varchar(150)");

                    b.Property<int?>("CobrancaTarifa")
                        .HasColumnName("CobrancaTarifa")
                        .HasColumnType("int");

                    b.Property<string>("CodigoBanco")
                        .HasColumnName("CodigoBanco")
                        .HasColumnType("varchar(150)");

                    b.Property<string>("CodigoTransacao")
                        .HasColumnName("CodigoTransacao")
                        .HasColumnType("varchar(100)");

                    b.Property<string>("Conta")
                        .HasColumnName("Conta")
                        .HasColumnType("varchar(150)");

                    b.Property<int?>("ContadorReenvio")
                        .HasColumnName("ContadorReenvio")
                        .HasColumnType("int");

                    b.Property<int?>("ContadorVerificacaoStatusPix")
                        .HasColumnName("ContadorVerificacaoStatusPix")
                        .HasColumnType("int");

                    b.Property<DateTime?>("DataAlteracao")
                        .HasColumnName("DataAlteracao")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DataBaixa")
                        .HasColumnName("DataBaixa")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("DataCadastro")
                        .HasColumnName("DataCadastro")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DataCadastroCancelamento")
                        .HasColumnName("DataCadastroCancelamento")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DataCancelamento")
                        .HasColumnName("DataCancelamento")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DataRetorno")
                        .HasColumnName("DataRetorno")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DataRetornoCancelamento")
                        .HasColumnName("DataRetornoCancelamento")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DataSolicitacaoCancelamento")
                        .HasColumnName("DataSolicitacaoCancelamento")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DataTerceiraVerificacaoStatusPix")
                        .HasColumnName("DataTerceiraVerificacaoStatusPix")
                        .HasColumnType("timestamp");

                    b.Property<string>("Descricao")
                        .HasColumnName("Descricao")
                        .HasColumnType("varchar(200)");

                    b.Property<int?>("EmpresaId")
                        .HasColumnName("EmpresaId")
                        .HasColumnType("int");

                    b.Property<int?>("FormaPagamento")
                        .HasColumnName("FormaPagamento")
                        .HasColumnType("int");

                    b.Property<string>("JsonEnvio")
                        .HasColumnName("JsonEnvio")
                        .HasColumnType("json");

                    b.Property<string>("JsonEnvioCancelamento")
                        .HasColumnName("JsonEnvioCancelamento")
                        .HasColumnType("json");

                    b.Property<string>("JsonRetorno")
                        .HasColumnName("JsonRetorno")
                        .HasColumnType("json");

                    b.Property<string>("JsonRetornoCancelamento")
                        .HasColumnName("JsonRetornoCancelamento")
                        .HasColumnType("json");

                    b.Property<string>("MotivoPendencia")
                        .HasColumnName("MotivoPendencia")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("Ocorrencia")
                        .HasColumnName("Ocorrencia")
                        .HasColumnType("varchar(500)");

                    b.Property<int?>("PagamentoExternoId")
                        .HasColumnName("PagamentoExternoId")
                        .HasColumnType("int");

                    b.Property<string>("RecebedorAutorizado")
                        .HasColumnName("RecebedorAutorizado")
                        .HasColumnType("varchar(150)");

                    b.Property<int?>("Status")
                        .HasColumnName("Status")
                        .HasColumnType("int");

                    b.Property<decimal?>("TarifaBbc")
                        .HasColumnName("TarifaBbc")
                        .HasColumnType("decimal");

                    b.Property<decimal?>("TarifaPix")
                        .HasColumnName("TarifaPix")
                        .HasColumnType("decimal");

                    b.Property<int?>("Tipo")
                        .HasColumnName("Tipo")
                        .HasColumnType("int");

                    b.Property<int?>("TipoConta")
                        .HasColumnName("TipoConta")
                        .HasColumnType("int");

                    b.Property<int?>("UsuarioAlteracaoId")
                        .HasColumnName("UsuarioAlteracaoId")
                        .HasColumnType("int");

                    b.Property<int?>("UsuarioCacelamentoId")
                        .HasColumnName("UsuarioCacelamentoId")
                        .HasColumnType("int");

                    b.Property<int>("UsuarioCadastroId")
                        .HasColumnName("UsuarioCadastroId")
                        .HasColumnType("int");

                    b.Property<decimal>("Valor")
                        .HasColumnName("Valor")
                        .HasColumnType("decimal");

                    b.Property<decimal?>("ValorCancelamento")
                        .HasColumnName("ValorCancelamento")
                        .HasColumnType("decimal");

                    b.Property<decimal?>("ValorTarifaBbc")
                        .HasColumnName("ValorTarifaBbc")
                        .HasColumnType("decimal");

                    b.Property<decimal?>("ValorTarifaPix")
                        .HasColumnName("ValorTarifaPix")
                        .HasColumnType("decimal");

                    b.Property<decimal?>("ValorTransferenciaMotorista")
                        .HasColumnName("ValorTransferenciaMotorista")
                        .HasColumnType("decimal");

                    b.Property<int>("ViagemId")
                        .HasColumnName("ViagemId")
                        .HasColumnType("int");

                    b.Property<string>("WebhookUrl")
                        .HasColumnName("WebhookUrl")
                        .HasColumnType("varchar(400)");

                    b.HasKey("Id");

                    b.HasIndex("EmpresaId");

                    b.HasIndex("UsuarioAlteracaoId");

                    b.HasIndex("UsuarioCadastroId");

                    b.HasIndex("ViagemId");

                    b.ToTable("PagamentoEvento");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.PagamentoEventoHistorico.PagamentoEventoHistorico", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("Id")
                        .HasColumnType("serial");

                    b.Property<string>("Agencia")
                        .HasColumnName("Agencia")
                        .HasColumnType("varchar(150)");

                    b.Property<string>("ChavePix")
                        .HasColumnName("ChavePix")
                        .HasColumnType("varchar(150)");

                    b.Property<int?>("CobrancaTarifa")
                        .HasColumnName("CobrancaTarifa")
                        .HasColumnType("int");

                    b.Property<string>("CodigoBanco")
                        .HasColumnName("CodigoBanco")
                        .HasColumnType("varchar(150)");

                    b.Property<string>("CodigoTransacao")
                        .HasColumnName("CodigoTransacao")
                        .HasColumnType("varchar(100)");

                    b.Property<string>("Conta")
                        .HasColumnName("Conta")
                        .HasColumnType("varchar(150)");

                    b.Property<int?>("ContadorReenvio")
                        .HasColumnName("ContadorReenvio")
                        .HasColumnType("int");

                    b.Property<int?>("ContadorVerificacaoStatusPix")
                        .HasColumnName("ContadorVerificacaoStatusPix")
                        .HasColumnType("int");

                    b.Property<DateTime?>("DataAlteracao")
                        .HasColumnName("DataAlteracao")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DataBaixa")
                        .HasColumnName("DataBaixa")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("DataCadastro")
                        .HasColumnName("DataCadastro")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DataCadastroCancelamento")
                        .HasColumnName("DataCadastroCancelamento")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DataCancelamento")
                        .HasColumnName("DataCancelamento")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DataRetorno")
                        .HasColumnName("DataRetorno")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DataRetornoCancelamento")
                        .HasColumnName("DataRetornoCancelamento")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DataSolicitacaoCancelamento")
                        .HasColumnName("DataSolicitacaoCancelamento")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DataTerceiraVerificacaoStatusPix")
                        .HasColumnName("DataTerceiraVerificacaoStatusPix")
                        .HasColumnType("timestamp");

                    b.Property<int?>("EmpresaId")
                        .HasColumnName("EmpresaId")
                        .HasColumnType("int");

                    b.Property<int?>("FormaPagamento")
                        .HasColumnName("FormaPagamento")
                        .HasColumnType("int");

                    b.Property<string>("JsonEnvio")
                        .HasColumnName("JsonEnvio")
                        .HasColumnType("json");

                    b.Property<string>("JsonEnvioCancelamento")
                        .HasColumnName("JsonEnvioCancelamento")
                        .HasColumnType("json");

                    b.Property<string>("JsonRetorno")
                        .HasColumnName("JsonRetorno")
                        .HasColumnType("json");

                    b.Property<string>("JsonRetornoCancelamento")
                        .HasColumnName("JsonRetornoCancelamento")
                        .HasColumnType("json");

                    b.Property<string>("MotivoPendencia")
                        .HasColumnName("MotivoPendencia")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("Ocorrencia")
                        .HasColumnName("Ocorrencia")
                        .HasColumnType("varchar(500)");

                    b.Property<int?>("PagamentoEventoId")
                        .HasColumnName("PagamentoEventoId");

                    b.Property<int?>("PagamentoExternoId")
                        .HasColumnName("PagamentoExternoId")
                        .HasColumnType("int");

                    b.Property<string>("RecebedorAutorizado")
                        .HasColumnName("RecebedorAutorizado")
                        .HasColumnType("varchar(150)");

                    b.Property<int?>("Status")
                        .HasColumnName("Status")
                        .HasColumnType("int");

                    b.Property<decimal?>("TarifaBbc")
                        .HasColumnName("TarifaBbc")
                        .HasColumnType("decimal");

                    b.Property<decimal?>("TarifaPix")
                        .HasColumnName("TarifaPix")
                        .HasColumnType("decimal");

                    b.Property<int?>("Tipo")
                        .HasColumnName("Tipo")
                        .HasColumnType("int");

                    b.Property<int?>("TipoConta")
                        .HasColumnName("TipoConta")
                        .HasColumnType("int");

                    b.Property<int?>("UsuarioAlteracaoId")
                        .HasColumnName("UsuarioAlteracaoId")
                        .HasColumnType("int");

                    b.Property<int?>("UsuarioCacelamentoId")
                        .HasColumnName("UsuarioCacelamentoId")
                        .HasColumnType("int");

                    b.Property<int>("UsuarioCadastroId")
                        .HasColumnName("UsuarioCadastroId")
                        .HasColumnType("int");

                    b.Property<decimal>("Valor")
                        .HasColumnName("Valor")
                        .HasColumnType("decimal");

                    b.Property<decimal?>("ValorCancelamento")
                        .HasColumnName("ValorCancelamento")
                        .HasColumnType("decimal");

                    b.Property<decimal?>("ValorTarifaBbc")
                        .HasColumnName("ValorTarifaBbc")
                        .HasColumnType("decimal");

                    b.Property<decimal?>("ValorTarifaPix")
                        .HasColumnName("ValorTarifaPix")
                        .HasColumnType("decimal");

                    b.Property<decimal?>("ValorTransferenciaMotorista")
                        .HasColumnName("ValorTransferenciaMotorista")
                        .HasColumnType("decimal");

                    b.Property<int>("ViagemId")
                        .HasColumnName("ViagemId")
                        .HasColumnType("int");

                    b.Property<string>("WebhookUrl")
                        .HasColumnName("WebhookUrl")
                        .HasColumnType("varchar(400)");

                    b.HasKey("Id");

                    b.HasIndex("PagamentoEventoId");

                    b.ToTable("PagamentoEventoHistorico");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.Pagamentos.Pagamentos", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("Id")
                        .HasColumnType("serial");

                    b.Property<string>("Agencia")
                        .HasColumnName("Agencia")
                        .HasColumnType("varchar(10)");

                    b.Property<string>("BancoId")
                        .HasColumnName("BancoId")
                        .HasColumnType("varchar(5)");

                    b.Property<bool?>("CargaCancelamento")
                        .HasColumnName("CargaCancelamento")
                        .HasColumnType("boolean");

                    b.Property<int?>("CiotId")
                        .HasColumnName("CiotId")
                        .HasColumnType("int");

                    b.Property<string>("Conta")
                        .HasColumnName("Conta")
                        .HasColumnType("varchar(100)");

                    b.Property<string>("ContaDestino")
                        .HasColumnType("varchar(40)");

                    b.Property<string>("ContaOrigem")
                        .HasColumnType("varchar(40)");

                    b.Property<string>("CpfContaTransferencia")
                        .HasColumnName("CpfContaTransferencia")
                        .HasColumnType("varchar(100)");

                    b.Property<DateTime?>("DataBaixa")
                        .HasColumnName("DataBaixa")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DataBloqueio")
                        .HasColumnName("DataBloqueio")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("DataCadastro")
                        .HasColumnName("DataCadastro")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DataCancelamento")
                        .HasColumnName("DataCancelamento")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DataDesblqueio")
                        .HasColumnName("DataDesbloqueio")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DataPrevisaoPagamento")
                        .HasColumnName("DataPrevisaoPagamento")
                        .HasColumnType("timestamp");

                    b.Property<string>("Descricao")
                        .HasColumnName("Descricao")
                        .HasColumnType("varchar(500)");

                    b.Property<int>("EmpresaId")
                        .HasColumnName("EmpresaId")
                        .HasColumnType("int");

                    b.Property<int?>("EtapaErroIntegracao")
                        .HasColumnName("EtapaErroIntegracao")
                        .HasColumnType("int");

                    b.Property<int>("FormaPagamento")
                        .HasColumnName("FormaPagamento")
                        .HasColumnType("int");

                    b.Property<string>("IdContaTransferencia")
                        .HasColumnName("IdContaTransferencia")
                        .HasColumnType("varchar(100)");

                    b.Property<int?>("IdPagamentoExterno")
                        .HasColumnName("IdPagamentoExterno")
                        .HasColumnType("int");

                    b.Property<string>("MensagemIntegracao")
                        .HasColumnName("MensagemIntegracao")
                        .HasColumnType("varchar(200)");

                    b.Property<int>("OrigemPagamento")
                        .HasColumnName("OrigemPagamento")
                        .HasColumnType("int");

                    b.Property<decimal>("PercentualTransferencia")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("PercentualTransferencia")
                        .HasColumnType("numeric(16,2)")
                        .HasDefaultValue(100m);

                    b.Property<int>("PortadorId")
                        .HasColumnName("PortadorId")
                        .HasColumnType("int");

                    b.Property<int>("Status")
                        .HasColumnName("Status")
                        .HasColumnType("int");

                    b.Property<int>("Tipo")
                        .HasColumnName("Tipo")
                        .HasColumnType("int");

                    b.Property<string>("TipoConta")
                        .HasColumnName("TipoConta")
                        .HasColumnType("varchar(10)");

                    b.Property<bool?>("TransferenciaCancelamento")
                        .HasColumnName("TransferenciaCancelamento")
                        .HasColumnType("boolean");

                    b.Property<int?>("UsuarioBaixaId")
                        .HasColumnName("UsuarioBaixaId")
                        .HasColumnType("int");

                    b.Property<int?>("UsuarioBloqueioId")
                        .HasColumnName("UsuarioBloqueioId")
                        .HasColumnType("int");

                    b.Property<int>("UsuarioCadastroId")
                        .HasColumnName("UsuarioCadastroId")
                        .HasColumnType("int");

                    b.Property<int?>("UsuarioCancelamentoId")
                        .HasColumnName("UsuarioCancelamentoId")
                        .HasColumnType("int");

                    b.Property<int?>("UsuarioDesbloqueioId")
                        .HasColumnName("UsuarioDesbloqueioId")
                        .HasColumnType("int");

                    b.Property<decimal>("Valor")
                        .HasColumnName("Valor")
                        .HasColumnType("numeric(16,2)");

                    b.Property<string>("VerificadorConta")
                        .HasColumnName("VerificadorConta")
                        .HasColumnType("varchar(10)");

                    b.HasKey("Id");

                    b.HasIndex("BancoId");

                    b.HasIndex("CiotId");

                    b.HasIndex("EmpresaId");

                    b.HasIndex("IdPagamentoExterno")
                        .IsUnique();

                    b.HasIndex("PortadorId");

                    b.HasIndex("UsuarioBloqueioId");

                    b.HasIndex("UsuarioCadastroId");

                    b.HasIndex("UsuarioDesbloqueioId");

                    b.ToTable("Pagamentos");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.PagamentosHistorico.PagamentosHistorico", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("Id")
                        .HasColumnType("serial");

                    b.Property<string>("Agencia")
                        .HasColumnName("Agencia")
                        .HasColumnType("varchar(10)");

                    b.Property<string>("BancoId")
                        .HasColumnName("BancoId")
                        .HasColumnType("varchar(5)");

                    b.Property<bool?>("CargaCancelamento")
                        .HasColumnName("CargaCancelamento")
                        .HasColumnType("boolean");

                    b.Property<int?>("CiotId")
                        .HasColumnName("CiotId")
                        .HasColumnType("int");

                    b.Property<string>("Conta")
                        .HasColumnName("Conta")
                        .HasColumnType("varchar(100)");

                    b.Property<string>("ContaDestino")
                        .HasColumnType("varchar(40)");

                    b.Property<string>("ContaOrigem")
                        .HasColumnType("varchar(40)");

                    b.Property<string>("CpfContaTransferencia")
                        .HasColumnName("CpfContaTransferencia")
                        .HasColumnType("varchar(100)");

                    b.Property<DateTime?>("DataBaixa")
                        .HasColumnName("DataBaixa")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DataBloqueio")
                        .HasColumnName("DataBloqueio")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("DataCadastro")
                        .HasColumnName("DataCadastro")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DataCancelamento")
                        .HasColumnName("DataCancelamento")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DataDesblqueio")
                        .HasColumnName("DataDesbloqueio")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DataPrevisaoPagamento")
                        .HasColumnName("DataPrevisaoPagamento")
                        .HasColumnType("timestamp");

                    b.Property<string>("Descricao")
                        .HasColumnName("Descricao")
                        .HasColumnType("varchar(500)");

                    b.Property<int>("EmpresaId")
                        .HasColumnName("EmpresaId")
                        .HasColumnType("int");

                    b.Property<int?>("EtapaErroIntegracao")
                        .HasColumnName("EtapaErroIntegracao")
                        .HasColumnType("int");

                    b.Property<int>("FormaPagamento")
                        .HasColumnName("FormaPagamento")
                        .HasColumnType("int");

                    b.Property<string>("IdContaTransferencia")
                        .HasColumnName("IdContaTransferencia")
                        .HasColumnType("varchar(100)");

                    b.Property<int?>("IdPagamentoExterno")
                        .HasColumnName("IdPagamentoExterno")
                        .HasColumnType("int");

                    b.Property<string>("MensagemIntegracao")
                        .HasColumnName("MensagemIntegracao")
                        .HasColumnType("varchar(200)");

                    b.Property<int?>("OrigemPagamento")
                        .HasColumnName("OrigemPagamento")
                        .HasColumnType("int");

                    b.Property<int?>("PagamentoId")
                        .HasColumnName("PagamentoId");

                    b.Property<decimal>("PercentualTransferencia")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("PercentualTransferencia")
                        .HasColumnType("numeric(16,2)")
                        .HasDefaultValue(100m);

                    b.Property<int>("PortadorId")
                        .HasColumnName("PortadorId")
                        .HasColumnType("int");

                    b.Property<int>("Status")
                        .HasColumnName("Status")
                        .HasColumnType("int");

                    b.Property<int>("Tipo")
                        .HasColumnName("Tipo")
                        .HasColumnType("int");

                    b.Property<string>("TipoConta")
                        .HasColumnName("TipoConta")
                        .HasColumnType("varchar(10)");

                    b.Property<bool?>("TransferenciaCancelamento")
                        .HasColumnName("TransferenciaCancelamento")
                        .HasColumnType("boolean");

                    b.Property<int?>("UsuarioBaixaId")
                        .HasColumnName("UsuarioBaixaId")
                        .HasColumnType("int");

                    b.Property<int?>("UsuarioBloqueioId")
                        .HasColumnName("UsuarioBloqueioId")
                        .HasColumnType("int");

                    b.Property<int>("UsuarioCadastroId")
                        .HasColumnName("UsuarioCadastroId")
                        .HasColumnType("int");

                    b.Property<int?>("UsuarioCancelamentoId")
                        .HasColumnName("UsuarioCancelamentoId")
                        .HasColumnType("int");

                    b.Property<int?>("UsuarioDesbloqueioId")
                        .HasColumnName("UsuarioDesbloqueioId")
                        .HasColumnType("int");

                    b.Property<decimal>("Valor")
                        .HasColumnName("Valor")
                        .HasColumnType("numeric(16,2)");

                    b.Property<string>("VerificadorConta")
                        .HasColumnName("VerificadorConta")
                        .HasColumnType("varchar(10)");

                    b.HasKey("Id");

                    b.HasIndex("BancoId");

                    b.HasIndex("CiotId");

                    b.HasIndex("EmpresaId");

                    b.HasIndex("IdPagamentoExterno")
                        .IsUnique();

                    b.HasIndex("PagamentoId");

                    b.HasIndex("PortadorId");

                    b.HasIndex("UsuarioBloqueioId");

                    b.HasIndex("UsuarioCadastroId");

                    b.HasIndex("UsuarioDesbloqueioId");

                    b.ToTable("PagamentosHistorico");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.ParametroConfiguracaoSla.ParametroConfiguracaoSla", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("Id")
                        .HasColumnType("serial");

                    b.Property<string>("EmailsFarolAmarelo")
                        .IsRequired()
                        .HasColumnName("EmailsFarolAmarelo")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("EmailsFarolVerde")
                        .IsRequired()
                        .HasColumnName("EmailsFarolVerde")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("EmailsFarolVermelho")
                        .IsRequired()
                        .HasColumnName("EmailsFarolVermelho")
                        .HasColumnType("varchar(200)");

                    b.Property<int>("FarolAmareloHoras")
                        .HasColumnName("FarolAmareloHoras")
                        .HasColumnType("int");

                    b.Property<int>("FarolVerdeHoras")
                        .HasColumnName("FarolVerdeHoras")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("ParametroConfiguracaoSla");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.Parametros.Parametros", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("Id")
                        .HasColumnType("serial");

                    b.Property<DateTime?>("DataAlteracao")
                        .HasColumnName("DataAlteracao")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DataCadastro")
                        .HasColumnName("DataCadastro")
                        .HasColumnType("timestamp");

                    b.Property<string>("InfoAdicional")
                        .HasColumnName("InfoAdicional")
                        .HasColumnType("varchar(100)");

                    b.Property<int>("ReferenciaId")
                        .HasColumnName("ReferenciaId")
                        .HasColumnType("int");

                    b.Property<int>("TipoParametros")
                        .HasColumnName("TipoParametros")
                        .HasColumnType("int");

                    b.Property<int>("TipoValor")
                        .HasColumnName("TipoValor")
                        .HasColumnType("int");

                    b.Property<int?>("UsuarioAlteracaoId")
                        .HasColumnName("UsuarioAlteracaoId")
                        .HasColumnType("int");

                    b.Property<int?>("UsuarioCadastroId")
                        .HasColumnName("UsuarioCadastroId")
                        .HasColumnType("int");

                    b.Property<string>("Valor")
                        .IsRequired()
                        .HasColumnName("Valor")
                        .HasColumnType("varchar(1500)");

                    b.Property<string>("ValorCriptografado")
                        .HasColumnName("ValorCriptografado")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("UsuarioAlteracaoId");

                    b.HasIndex("UsuarioCadastroId");

                    b.ToTable("Parametros");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.ParametrosHistorico.ParametrosHistorico", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("Id");

                    b.Property<DateTime?>("DataAlteracao")
                        .HasColumnName("DataAlteracao")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DataCadastro")
                        .HasColumnName("DataCadastro")
                        .HasColumnType("timestamp");

                    b.Property<string>("InfoAdicional")
                        .HasColumnName("InfoAdicional")
                        .HasColumnType("varchar(100)");

                    b.Property<int>("ParametroId")
                        .HasColumnName("ParametroId");

                    b.Property<int>("ReferenciaId")
                        .HasColumnName("ReferenciaId")
                        .HasColumnType("int");

                    b.Property<int>("TipoParametros")
                        .HasColumnName("TipoParametros")
                        .HasColumnType("int");

                    b.Property<int>("TipoValor")
                        .HasColumnName("TipoValor")
                        .HasColumnType("int");

                    b.Property<int?>("UsuarioAlteracaoId")
                        .HasColumnName("UsuarioAlteracaoId")
                        .HasColumnType("int");

                    b.Property<int?>("UsuarioCadastroId")
                        .HasColumnName("UsuarioCadastroId")
                        .HasColumnType("int");

                    b.Property<string>("Valor")
                        .IsRequired()
                        .HasColumnName("Valor")
                        .HasColumnType("varchar(1500)");

                    b.Property<string>("ValorCriptografado")
                        .HasColumnName("ValorCriptografado")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("ParametroId");

                    b.HasIndex("UsuarioAlteracaoId");

                    b.HasIndex("UsuarioCadastroId");

                    b.ToTable("ParametrosHistorico");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.PercentualTransferencia.PercentualTransferencia", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("Id")
                        .HasColumnType("serial");

                    b.Property<decimal?>("Adiantamento")
                        .HasColumnType("decimal(10, 2)");

                    b.Property<int>("Ativo");

                    b.Property<DateTime?>("DataAlteracao")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DataCadastro")
                        .HasColumnType("timestamp");

                    b.Property<int>("ParaTodosMotoristas");

                    b.Property<int>("ProprietarioId");

                    b.Property<decimal?>("Saldo")
                        .HasColumnType("decimal(10, 2)");

                    b.Property<int?>("UsuarioAlteracaoId");

                    b.Property<int?>("UsuarioCadastroId");

                    b.HasKey("Id");

                    b.HasIndex("ProprietarioId")
                        .IsUnique();

                    b.HasIndex("UsuarioCadastroId");

                    b.ToTable("PercentualTransferencia");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.PercentualTransferenciaHistorico.PercentualTransferenciaHistorico", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("Id")
                        .HasColumnType("serial");

                    b.Property<decimal>("Adiantamento")
                        .HasColumnType("decimal(10, 2)");

                    b.Property<int>("Ativo");

                    b.Property<DateTime?>("DataAlteracao")
                        .HasColumnType("timestamp");

                    b.Property<int>("ParaTodosMotoristas");

                    b.Property<int>("PercentualTransferenciaId");

                    b.Property<int>("ProprietarioId");

                    b.Property<decimal>("Saldo")
                        .HasColumnType("decimal(10, 2)");

                    b.Property<int?>("UsuarioAlteracaoId");

                    b.HasKey("Id");

                    b.HasIndex("PercentualTransferenciaId");

                    b.HasIndex("UsuarioAlteracaoId");

                    b.ToTable("PercentualTransferenciaHistorico");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.PercentualTransferenciaPortador.PercentualTransferenciaPortador", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("Id")
                        .HasColumnType("serial");

                    b.Property<decimal>("Adiantamento")
                        .HasColumnType("decimal(10, 2)");

                    b.Property<int>("Ativo");

                    b.Property<DateTime?>("DataAlteracao")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DataCadastro")
                        .HasColumnType("timestamp");

                    b.Property<int>("PercentualTransferenciaId");

                    b.Property<int>("PortadorId");

                    b.Property<decimal>("Saldo")
                        .HasColumnType("decimal(10, 2)");

                    b.Property<int?>("UsuarioAlteracaoId");

                    b.Property<int?>("UsuarioCadastroId");

                    b.HasKey("Id");

                    b.HasIndex("PercentualTransferenciaId");

                    b.HasIndex("PortadorId");

                    b.HasIndex("UsuarioAlteracaoId");

                    b.HasIndex("UsuarioCadastroId");

                    b.ToTable("PercentualTransferenciaPortador");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.PercentualTransferenciaPortadorHistorico.PercentualTransferenciaPortadorHistorico", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("Id")
                        .HasColumnType("serial");

                    b.Property<decimal>("Adiantamento")
                        .HasColumnType("decimal(10, 2)");

                    b.Property<int>("Ativo");

                    b.Property<DateTime?>("DataAlteracao")
                        .HasColumnType("timestamp");

                    b.Property<int>("PercentualTransferenciaPortadorId");

                    b.Property<int>("PortadorId");

                    b.Property<decimal>("Saldo")
                        .HasColumnType("decimal(10, 2)");

                    b.Property<int?>("UsuarioAlteracaoId");

                    b.HasKey("Id");

                    b.ToTable("PercentualTransferenciaPortadorHistorico");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.Portador.Portador", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("Id")
                        .HasColumnType("serial");

                    b.Property<int?>("Atividade")
                        .HasColumnName("Atividade")
                        .HasColumnType("int");

                    b.Property<int>("Ativo")
                        .HasColumnName("Ativo")
                        .HasColumnType("int");

                    b.Property<string>("Bairro")
                        .IsRequired()
                        .HasColumnName("Bairro")
                        .HasColumnType("varchar(100)");

                    b.Property<string>("CNH")
                        .HasColumnName("CNH")
                        .HasColumnType("varchar(200)");

                    b.Property<int?>("Carreta2Id")
                        .HasColumnName("Carreta2Id")
                        .HasColumnType("int");

                    b.Property<int?>("Carreta3Id")
                        .HasColumnName("Carreta3Id")
                        .HasColumnType("int");

                    b.Property<int?>("CarretaId")
                        .HasColumnName("CarretaId")
                        .HasColumnType("int");

                    b.Property<string>("Celular")
                        .HasColumnName("Celular")
                        .HasColumnType("varchar(15)");

                    b.Property<string>("Cep")
                        .HasColumnName("Cep")
                        .HasColumnType("varchar(15)");

                    b.Property<int?>("CidadeId")
                        .HasColumnName("CidadeId")
                        .HasColumnType("int");

                    b.Property<int>("CiotTacAgregado")
                        .HasColumnName("CiotTacAgregado")
                        .HasColumnType("int");

                    b.Property<string>("Cnae")
                        .HasColumnName("Cnae")
                        .HasColumnType("varchar(10)");

                    b.Property<string>("Complemento")
                        .HasColumnName("Complemento")
                        .HasColumnType("varchar(200)");

                    b.Property<int?>("ControlaAbastecimentoCentroCusto")
                        .HasColumnName("ControlaAbastecimentoCentroCusto")
                        .HasColumnType("int");

                    b.Property<string>("CpfCnpj")
                        .IsRequired()
                        .HasColumnName("CpfCnpj")
                        .HasColumnType("varchar(14)");

                    b.Property<DateTime?>("DataAberturaEmpresa")
                        .HasColumnName("DataAberturaEmpresa")
                        .HasColumnType("date");

                    b.Property<DateTime?>("DataBloqueio")
                        .HasColumnName("DataBloqueio")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("DataBloqueioMobile")
                        .HasColumnName("DataBloqueioMobile")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("DataCadastro")
                        .HasColumnName("DataCadastro")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DataCancelamento")
                        .HasColumnName("DataCancelamento")
                        .HasColumnType("date");

                    b.Property<DateTime?>("DataCriacaoSenha")
                        .HasColumnName("DataCriacaoSenha")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DataDesbloqueio")
                        .HasColumnName("DataDesbloqueio")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("DataDesbloqueioMobile")
                        .HasColumnName("DataDesbloqueioMobile")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DataEmissaoCNH")
                        .HasColumnName("DataEmissaoCNH")
                        .HasColumnType("date");

                    b.Property<DateTime?>("DataNascimento")
                        .HasColumnName("DataNascimento")
                        .HasColumnType("date");

                    b.Property<DateTime?>("DataUltimoAcesso")
                        .HasColumnName("DataUltimoAcesso")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DataVencimentoCNH")
                        .HasColumnName("DataVencimentoCNH")
                        .HasColumnType("date");

                    b.Property<string>("Email")
                        .HasColumnName("Email")
                        .HasColumnType("varchar(200)");

                    b.Property<DateTime?>("EmissaoIdentidade")
                        .HasColumnName("EmissaoIdentidade")
                        .HasColumnType("date");

                    b.Property<int?>("EmpresaIdFrota")
                        .HasColumnName("EmpresaIdFrota")
                        .HasColumnType("int");

                    b.Property<string>("Endereco")
                        .HasColumnName("Endereco")
                        .HasColumnType("varchar(200)");

                    b.Property<int?>("EnderecoNumero")
                        .HasColumnName("EnderecoNumero")
                        .HasColumnType("int");

                    b.Property<int?>("EstadoId")
                        .HasColumnName("EstadoId")
                        .HasColumnType("int");

                    b.Property<string>("FormaConstituicao")
                        .HasColumnName("FormaConstituicao")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("InscricaoEstadual")
                        .HasColumnName("InscricaoEstadual")
                        .HasColumnType("varchar(20)");

                    b.Property<string>("MotivoCancelamento")
                        .HasColumnName("MotivoCancelamento")
                        .HasColumnType("varchar(500)");

                    b.Property<string>("NaturezaJuridica")
                        .HasColumnName("NaturezaJuridica")
                        .HasColumnType("varchar(4)");

                    b.Property<string>("Nome")
                        .IsRequired()
                        .HasColumnName("Nome")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("NomeMae")
                        .HasColumnName("NomeMae")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("NomePai")
                        .HasColumnName("NomePai")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("NumeroCNH")
                        .HasColumnName("NumeroCNH")
                        .HasColumnType("varchar(11)");

                    b.Property<string>("NumeroIdentidade")
                        .HasColumnName("NumeroIdentidade")
                        .HasColumnType("varchar(20)");

                    b.Property<string>("OrgaoEmissor")
                        .HasColumnName("OrgaoEmissor")
                        .HasColumnType("varchar(20)");

                    b.Property<string>("Placa")
                        .HasColumnName("Placa")
                        .HasColumnType("varchar(7)");

                    b.Property<int>("QuantidadeErroSenha")
                        .HasColumnName("QuantidadeErroSenha")
                        .HasColumnType("int");

                    b.Property<string>("RNTRC")
                        .HasColumnName("RNTRC")
                        .HasColumnType("varchar(15)");

                    b.Property<string>("RazaoSocial")
                        .HasColumnName("RazaoSocial")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("SenhaApi")
                        .HasColumnName("SenhaApi")
                        .HasColumnType("varchar(200)");

                    b.Property<int?>("SenhaProvisoria")
                        .HasColumnName("SenhaProvisoria")
                        .HasColumnType("int");

                    b.Property<int?>("Sexo")
                        .HasColumnName("Sexo")
                        .HasColumnType("int");

                    b.Property<int>("Status")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("Status")
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.Property<string>("Telefone")
                        .HasColumnName("Telefone")
                        .HasColumnType("varchar(15)");

                    b.Property<int>("TipoPessoa")
                        .HasColumnName("TipoPessoa")
                        .HasColumnType("int");

                    b.Property<string>("UfEmissao")
                        .HasColumnName("UfEmissao")
                        .HasColumnType("varchar(20)");

                    b.Property<int?>("UsuarioBloqueioId")
                        .HasColumnName("UsuarioBloqueioId")
                        .HasColumnType("int");

                    b.Property<int?>("UsuarioCadastroId")
                        .HasColumnName("UsuarioCadastroId")
                        .HasColumnType("int");

                    b.Property<int?>("UsuarioCancelamentoId")
                        .HasColumnName("UsuarioCancelamentoId")
                        .HasColumnType("int");

                    b.Property<int?>("UsuarioDesbloqueioId")
                        .HasColumnName("UsuarioDesbloqueioId")
                        .HasColumnType("int");

                    b.Property<int?>("UsuarioDesbloqueioMobileId")
                        .HasColumnName("UsuarioDesbloqueioMobileId")
                        .HasColumnType("int");

                    b.Property<int>("Visibilidade")
                        .HasColumnName("Visibilidade")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("Carreta2Id");

                    b.HasIndex("Carreta3Id");

                    b.HasIndex("CarretaId");

                    b.HasIndex("CidadeId");

                    b.HasIndex("EmpresaIdFrota");

                    b.HasIndex("EstadoId");

                    b.HasIndex("UsuarioBloqueioId");

                    b.HasIndex("UsuarioCadastroId");

                    b.HasIndex("UsuarioCancelamentoId");

                    b.HasIndex("UsuarioDesbloqueioId");

                    b.HasIndex("UsuarioDesbloqueioMobileId");

                    b.ToTable("Portador");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.PortadorCentroCusto.PortadorCentroCusto", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("Id")
                        .HasColumnType("serial");

                    b.Property<int>("CentroCustoId");

                    b.Property<int>("PortadorId");

                    b.HasKey("Id");

                    b.HasIndex("CentroCustoId");

                    b.HasIndex("PortadorId");

                    b.ToTable("PortadorCentroCusto");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.PortadorEmpresa.PortadorEmpresa", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("Id")
                        .HasColumnType("serial");

                    b.Property<int>("EmpresaId");

                    b.Property<int>("PortadorId");

                    b.HasKey("Id");

                    b.HasIndex("EmpresaId");

                    b.HasIndex("PortadorId");

                    b.ToTable("PortadorEmpresa");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.PortadorRepresentanteLegal.PortadorRepresentanteLegal", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("Id")
                        .HasColumnType("serial");

                    b.Property<int>("PortadorId")
                        .HasColumnName("PortadorId")
                        .HasColumnType("int");

                    b.Property<int>("PortadorRepresentanteId")
                        .HasColumnName("PortadorRepresentanteId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("PortadorId");

                    b.HasIndex("PortadorRepresentanteId");

                    b.ToTable("PortadorRepresentanteLegal");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.Posto.Posto", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("Id")
                        .HasColumnType("serial");

                    b.Property<int?>("Aberto24h")
                        .HasColumnName("Aberto24h")
                        .HasColumnType("int");

                    b.Property<string>("Agencia")
                        .HasColumnName("Agencia")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("AgenciaDV")
                        .HasColumnName("AgenciaDV")
                        .HasColumnType("varchar(50)");

                    b.Property<int>("Ativo")
                        .HasColumnName("Ativo")
                        .HasColumnType("int");

                    b.Property<string>("Bairro")
                        .IsRequired()
                        .HasColumnName("Bairro")
                        .HasColumnType("varchar(100)");

                    b.Property<string>("BancoId")
                        .HasColumnName("BancoId")
                        .HasColumnType("varchar(5)");

                    b.Property<string>("Bandeira")
                        .HasColumnName("Bandeira")
                        .HasColumnType("varchar(100)");

                    b.Property<int?>("Bloqueado")
                        .HasColumnName("Bloqueado")
                        .HasColumnType("int");

                    b.Property<string>("Cep")
                        .HasColumnName("Cep")
                        .HasColumnType("varchar(15)");

                    b.Property<int?>("CidadeId")
                        .HasColumnName("CidadeId")
                        .HasColumnType("int");

                    b.Property<string>("Cnpj")
                        .IsRequired()
                        .HasColumnName("Cnpj")
                        .HasColumnType("varchar(14)");

                    b.Property<string>("CnpjFaturamento")
                        .HasColumnName("CnpjFaturamento")
                        .HasColumnType("varchar(14)");

                    b.Property<string>("ContaCorrente")
                        .HasColumnName("ContaCorrente")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ContaCorrenteDV")
                        .HasColumnName("ContaCorrenteDV")
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("DataBloqueio")
                        .HasColumnName("DataBloqueio")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("DataCadastro")
                        .HasColumnName("DataCadastro")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DataDesbloqueio")
                        .HasColumnName("DataDesbloqueio")
                        .HasColumnType("timestamp");

                    b.Property<string>("EmailPosto")
                        .IsRequired()
                        .HasColumnName("EmailPosto")
                        .HasColumnType("varchar(100)");

                    b.Property<string>("Endereco")
                        .HasColumnName("Endereco")
                        .HasColumnType("varchar(200)");

                    b.Property<int?>("EnderecoNumero")
                        .HasColumnName("EnderecoNumero")
                        .HasColumnType("int");

                    b.Property<int?>("EstadoId")
                        .HasColumnName("EstadoId")
                        .HasColumnType("int");

                    b.Property<int>("FarolSla")
                        .HasColumnName("FarolSla")
                        .HasColumnType("int");

                    b.Property<int?>("FilialId")
                        .HasColumnName("FilialId")
                        .HasColumnType("int");

                    b.Property<string>("Informacoes")
                        .HasColumnName("Informacoes")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("InscricaoEstadual")
                        .HasColumnName("InscricaoEstadual")
                        .HasColumnType("varchar(15)");

                    b.Property<string>("Latitude")
                        .HasColumnName("Latitude")
                        .HasColumnType("varchar(20)");

                    b.Property<int?>("LoginBloqueado")
                        .HasColumnName("LoginBloqueado")
                        .HasColumnType("int");

                    b.Property<string>("Longitude")
                        .HasColumnName("Longitude")
                        .HasColumnType("varchar(20)");

                    b.Property<int?>("MDRId")
                        .HasColumnName("MDRId")
                        .HasColumnType("int");

                    b.Property<string>("MotivoBloqueio")
                        .HasColumnName("MotivoBloqueio")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("NomeFantasia")
                        .IsRequired()
                        .HasColumnName("NomeFantasia")
                        .HasColumnType("varchar(200)");

                    b.Property<int?>("PossuiAreaDescanso")
                        .HasColumnName("PossuiAreaDescanso")
                        .HasColumnType("int");

                    b.Property<int?>("PossuiBorracharia")
                        .HasColumnName("PossuiBorracharia")
                        .HasColumnType("int");

                    b.Property<int?>("PossuiComputador")
                        .HasColumnName("PossuiComputador")
                        .HasColumnType("int");

                    b.Property<int?>("PossuiRestaurante")
                        .HasColumnName("PossuiRestaurante")
                        .HasColumnType("int");

                    b.Property<int?>("PossuiVeiculoApoio")
                        .HasColumnName("PossuiVeiculoApoio")
                        .HasColumnType("int");

                    b.Property<int?>("PossuiVestiario")
                        .HasColumnName("PossuiVestiario")
                        .HasColumnType("int");

                    b.Property<int?>("PrazoPagamento")
                        .HasColumnName("PrazoPagamento")
                        .HasColumnType("int");

                    b.Property<int>("QtdeErroSenha")
                        .HasColumnName("QtdeErroSenha")
                        .HasColumnType("int");

                    b.Property<string>("RazaoSocial")
                        .IsRequired()
                        .HasColumnName("RazaoSocial")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("Responsavel")
                        .HasColumnName("Responsavel")
                        .HasColumnType("varchar(100)");

                    b.Property<string>("Senha")
                        .IsRequired()
                        .HasColumnName("Senha")
                        .HasColumnType("varchar(100)");

                    b.Property<int>("StatusCadastro")
                        .HasColumnName("StatusCadastro")
                        .HasColumnType("int");

                    b.Property<int?>("UsuarioBloqueioId")
                        .HasColumnName("UsuarioBloqueioId")
                        .HasColumnType("int");

                    b.Property<int?>("UsuarioCadastroId")
                        .HasColumnName("UsuarioCadastroId")
                        .HasColumnType("int");

                    b.Property<int?>("UsuarioCredenciamentoSlaId")
                        .HasColumnName("UsuarioCredenciamentoSlaId")
                        .HasColumnType("int");

                    b.Property<int?>("UsuarioDesbloqueioId");

                    b.HasKey("Id");

                    b.HasIndex("BancoId");

                    b.HasIndex("CidadeId");

                    b.HasIndex("FilialId");

                    b.HasIndex("MDRId");

                    b.HasIndex("UsuarioBloqueioId");

                    b.HasIndex("UsuarioCadastroId");

                    b.HasIndex("UsuarioCredenciamentoSlaId");

                    b.HasIndex("UsuarioDesbloqueioId");

                    b.ToTable("Posto");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.PostoCombustivel.PostoCombustivel", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("Id")
                        .HasColumnType("serial");

                    b.Property<int>("CombustivelId")
                        .HasColumnName("CombustivelId")
                        .HasColumnType("int");

                    b.Property<DateTime>("DataAlteracao")
                        .HasColumnName("DataAlteracao")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("DataCadastro")
                        .HasColumnName("DataCadastro")
                        .HasColumnType("timestamp");

                    b.Property<string>("Nfe")
                        .HasColumnName("Nfe")
                        .HasColumnType("varchar(100)");

                    b.Property<int>("PostoId")
                        .HasColumnName("PostoId")
                        .HasColumnType("int");

                    b.Property<int?>("UsuarioAlteracaoId")
                        .HasColumnName("UsuarioAlteracaoId")
                        .HasColumnType("int");

                    b.Property<int?>("UsuarioCadastroId")
                        .HasColumnName("UsuarioCadastroId")
                        .HasColumnType("int");

                    b.Property<decimal?>("ValorCombustivelBBC")
                        .HasColumnName("ValorCombustivelBBC")
                        .HasColumnType("decimal");

                    b.Property<decimal?>("ValorCombustivelBomba")
                        .HasColumnName("ValorCombustivelBomba")
                        .HasColumnType("decimal");

                    b.HasKey("Id");

                    b.HasIndex("CombustivelId");

                    b.HasIndex("PostoId");

                    b.HasIndex("UsuarioAlteracaoId");

                    b.ToTable("PostoCombustivel");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.PostoCombustivelProduto.PostoCombustivelProduto", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("Id")
                        .HasColumnType("serial");

                    b.Property<string>("CodigoProduto")
                        .HasColumnType("varchar(40)");

                    b.Property<int>("CombustivelId");

                    b.Property<int>("PostoId");

                    b.HasKey("Id");

                    b.HasIndex("CombustivelId");

                    b.HasIndex("PostoId");

                    b.ToTable("PostoCombustivelProduto");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.PostoContato.PostoContato", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("Id")
                        .HasColumnType("serial");

                    b.Property<string>("Celular")
                        .HasColumnName("Celular")
                        .HasColumnType("varchar(15)");

                    b.Property<string>("Email")
                        .HasColumnName("Email")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("Nome")
                        .IsRequired()
                        .HasColumnName("Nome")
                        .HasColumnType("varchar(100)");

                    b.Property<int>("PostoId")
                        .HasColumnName("PostoId")
                        .HasColumnType("int");

                    b.Property<string>("Telefone")
                        .HasColumnName("Telefone")
                        .HasColumnType("varchar(15)");

                    b.Property<string>("Telefone2")
                        .HasColumnName("Telefone2")
                        .HasColumnType("varchar(15)");

                    b.Property<int>("Tipo")
                        .HasColumnName("Tipo")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("PostoId");

                    b.ToTable("PostoContato");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.ProtocoloAbastecimento.ProtocoloAbastecimento", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("Id")
                        .HasColumnType("serial");

                    b.Property<string>("Cnpj")
                        .IsRequired()
                        .HasColumnName("Cnpj")
                        .HasColumnType("varchar(200)");

                    b.Property<DateTime?>("DataAprovacao")
                        .HasColumnName("DataAprovacao")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("DataCadastro")
                        .HasColumnName("DataCadastro")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DataReprovacao")
                        .HasColumnName("DataReprovacao")
                        .HasColumnType("timestamp");

                    b.Property<int?>("DiasMDR")
                        .HasColumnName("DiasMDR")
                        .HasColumnType("int");

                    b.Property<int?>("EmpresaId")
                        .HasColumnName("EmpresaId")
                        .HasColumnType("int");

                    b.Property<int?>("IntegracaoSap")
                        .HasColumnName("IntegracaoSap")
                        .HasColumnType("int");

                    b.Property<string>("JsonEnvioSap")
                        .HasColumnName("JsonEnvioSap")
                        .HasColumnType("text");

                    b.Property<string>("JsonRetornoSap")
                        .HasColumnName("JsonRetornoSap")
                        .HasColumnType("text");

                    b.Property<int?>("LotePagamentoId");

                    b.Property<string>("NotaFiscal")
                        .HasColumnName("NotaFiscal")
                        .HasColumnType("varchar(100)");

                    b.Property<string>("NotaXml")
                        .IsRequired()
                        .HasColumnName("NotaXml")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("NumeroPedidoSap")
                        .HasColumnName("NumeroPedidoSap")
                        .HasColumnType("varchar(40)");

                    b.Property<string>("Pdf")
                        .IsRequired()
                        .HasColumnName("Pdf")
                        .HasColumnType("text");

                    b.Property<decimal?>("PercentualMDR")
                        .HasColumnName("PrazoMDR")
                        .HasColumnType("int");

                    b.Property<int?>("PostoId")
                        .HasColumnName("PostoId")
                        .HasColumnType("int");

                    b.Property<decimal>("QtdLitros")
                        .HasColumnName("QuantidadeLitros")
                        .HasColumnType("decimal");

                    b.Property<decimal>("QtdLitrosXml")
                        .HasColumnName("QuantidadeLitrosXml")
                        .HasColumnType("decimal");

                    b.Property<int>("Status")
                        .HasColumnName("Status")
                        .HasColumnType("int");

                    b.Property<decimal>("ValorAbastecido")
                        .HasColumnName("ValorAbastecido")
                        .HasColumnType("decimal");

                    b.Property<decimal>("ValorXml")
                        .HasColumnName("ValorXml")
                        .HasColumnType("decimal");

                    b.Property<string>("Xml")
                        .IsRequired()
                        .HasColumnName("Xml")
                        .HasColumnType("xml");

                    b.HasKey("Id");

                    b.HasIndex("EmpresaId");

                    b.HasIndex("LotePagamentoId");

                    b.HasIndex("PostoId");

                    b.ToTable("ProtocoloAbastecimento");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.Retencao.Retencao", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("Id")
                        .HasColumnType("serial");

                    b.Property<DateTime?>("DataCadastro")
                        .IsRequired()
                        .HasColumnName("DataCadastro")
                        .HasColumnType("date");

                    b.Property<DateTime?>("DataIntegracao")
                        .HasColumnName("DataIntegracao")
                        .HasColumnType("date");

                    b.Property<int?>("EmprestimoId")
                        .HasColumnName("EmprestimoId")
                        .HasColumnType("int");

                    b.Property<string>("MensagemIntegracao")
                        .HasColumnName("MensagemIntegracao")
                        .HasColumnType("varchar(200)");

                    b.Property<int?>("PagamentoId")
                        .HasColumnName("PagamentoId")
                        .HasColumnType("int");

                    b.Property<int>("Status")
                        .HasColumnName("Status")
                        .HasColumnType("int");

                    b.Property<decimal>("Valor")
                        .HasColumnName("Valor")
                        .HasColumnType("numeric(16,2)");

                    b.HasKey("Id");

                    b.HasIndex("EmprestimoId");

                    b.HasIndex("PagamentoId");

                    b.ToTable("Retencao");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.ServidorCiot.ServidorCiot", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("Id")
                        .HasColumnType("serial");

                    b.Property<int>("Ativo")
                        .HasColumnName("Ativo")
                        .HasColumnType("int");

                    b.Property<DateTime?>("DataAlteracao")
                        .HasColumnName("DataAlteracao")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("DataCadastrada")
                        .HasColumnName("DataCadastro")
                        .HasColumnType("timestamp");

                    b.Property<string>("Link")
                        .IsRequired()
                        .HasColumnName("Link")
                        .HasColumnType("varchar(500)");

                    b.Property<string>("Nome")
                        .IsRequired()
                        .HasColumnName("Nome")
                        .HasColumnType("varchar(500)");

                    b.Property<int>("ServidorId")
                        .HasColumnName("ServidorId")
                        .HasColumnType("int");

                    b.Property<int>("Status")
                        .HasColumnName("Status")
                        .HasColumnType("int");

                    b.Property<int>("StatusComunicacaoAntt")
                        .HasColumnName("StatusComunicacaoAntt")
                        .HasColumnType("int");

                    b.Property<int>("TipoServidor")
                        .HasColumnName("TipoServidor")
                        .HasColumnType("int");

                    b.Property<int?>("UsuarioAlteracaoId");

                    b.Property<int?>("UsuarioCadastroId");

                    b.HasKey("Id");

                    b.HasIndex("UsuarioAlteracaoId");

                    b.HasIndex("UsuarioCadastroId");

                    b.ToTable("ServidorCiotHistorico");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.TipoEmpresa.TipoEmpresa", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("Id")
                        .HasColumnType("serial");

                    b.Property<int>("Ativo")
                        .HasColumnName("Ativo")
                        .HasColumnType("int");

                    b.Property<DateTime>("DataBloqueio")
                        .HasColumnName("DataBloqueio")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("DataCadastro")
                        .HasColumnName("DataCadastro")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("DataDesbloqueio")
                        .HasColumnName("DataDesbloqueio")
                        .HasColumnType("timestamp");

                    b.Property<string>("Nome")
                        .IsRequired()
                        .HasColumnName("Nome")
                        .HasColumnType("varchar(200)");

                    b.Property<int?>("UsuarioBloqueioId")
                        .HasColumnName("UsuarioBloqueioId")
                        .HasColumnType("int");

                    b.Property<int>("UsuarioCadastroId")
                        .HasColumnName("UsuarioCadastroId")
                        .HasColumnType("int");

                    b.Property<int?>("UsuarioDesbloqueioId")
                        .HasColumnName("UsuarioDesbloqueioId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("UsuarioBloqueioId");

                    b.HasIndex("UsuarioCadastroId");

                    b.HasIndex("UsuarioDesbloqueioId");

                    b.ToTable("TipoEmpresa");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.Transacao.Transacao", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("Id")
                        .HasColumnType("serial");

                    b.Property<string>("Agencia")
                        .HasColumnName("Agencia")
                        .HasColumnType("varchar(150)");

                    b.Property<string>("CodigoBanco")
                        .HasColumnName("CodigoBanco")
                        .HasColumnType("varchar(150)");

                    b.Property<string>("Conta")
                        .HasColumnName("Conta")
                        .HasColumnType("varchar(150)");

                    b.Property<DateTime?>("DataAlteracao")
                        .HasColumnName("DataAlteracao")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DataBaixa")
                        .HasColumnName("DataBaixa")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("DataCadastro")
                        .HasColumnName("DataCadastro")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DataCancelamento")
                        .HasColumnName("DataCancelamento")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DataRetornoDock")
                        .HasColumnName("DataRetornoDock")
                        .HasColumnType("timestamp");

                    b.Property<string>("Descricao")
                        .HasColumnName("DescricaoMotivo")
                        .HasColumnType("varchar(100)");

                    b.Property<string>("Description")
                        .HasColumnName("DescriptionPagamentoDock")
                        .HasColumnType("varchar(1000)");

                    b.Property<int?>("Destino")
                        .HasColumnName("Destino")
                        .HasColumnType("int");

                    b.Property<int>("FormaPagamento")
                        .HasColumnName("FormaPagamento")
                        .HasColumnType("int");

                    b.Property<string>("IdEndToEnd")
                        .HasColumnName("IdEndToEnd")
                        .HasColumnType("varchar(100)");

                    b.Property<int>("IdPagamentoEvento");

                    b.Property<string>("JsonEnvioDock")
                        .HasColumnName("JsonEnvioDock")
                        .HasColumnType("json");

                    b.Property<string>("JsonEnvioDockCancelamento")
                        .HasColumnName("JsonEnvioDockCancelamento")
                        .HasColumnType("json");

                    b.Property<string>("JsonRespostaDock")
                        .HasColumnName("JsonRespostaDock")
                        .HasColumnType("json");

                    b.Property<string>("JsonRespostaDockCancelamento")
                        .HasColumnName("JsonRespostaDockCancelamento")
                        .HasColumnType("json");

                    b.Property<int>("Origem")
                        .HasColumnName("Origem")
                        .HasColumnType("int");

                    b.Property<int?>("Qualificado")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("Qualificado")
                        .HasColumnType("int")
                        .HasDefaultValue(0);

                    b.Property<int>("ResponseCodeDock")
                        .HasColumnName("ResponseCodeDock")
                        .HasColumnType("int");

                    b.Property<int?>("ResponseCodeDockCancelamento")
                        .HasColumnName("ResponseCodeDockCancelamento")
                        .HasColumnType("int");

                    b.Property<int>("Status")
                        .HasColumnName("Status")
                        .HasColumnType("int");

                    b.Property<int?>("Tipo")
                        .HasColumnName("Tipo")
                        .HasColumnType("int");

                    b.Property<int?>("TipoConta")
                        .HasColumnName("TipoConta")
                        .HasColumnType("int");

                    b.Property<decimal>("Valor")
                        .HasColumnName("Valor")
                        .HasColumnType("numeric(16,2)");

                    b.HasKey("Id");

                    b.HasIndex("IdPagamentoEvento");

                    b.ToTable("Transacao");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("Id")
                        .HasColumnType("serial");

                    b.Property<int>("Ativo")
                        .HasColumnName("Ativo")
                        .HasColumnType("int");

                    b.Property<string>("Celular")
                        .HasColumnName("Celular")
                        .HasColumnType("varchar(15)");

                    b.Property<string>("Cpf")
                        .IsRequired()
                        .HasColumnName("Cpf")
                        .HasColumnType("varchar(11)");

                    b.Property<DateTime?>("DataBloqueio")
                        .HasColumnName("DataBloqueio")
                        .HasColumnType("date");

                    b.Property<DateTime>("DataCadastro")
                        .HasColumnName("DataCadastro")
                        .HasColumnType("date");

                    b.Property<DateTime?>("DataCriacaoSenha")
                        .HasColumnName("DataCriacaoSenha")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DataDesbloqueio")
                        .HasColumnName("DataDesbloqueio")
                        .HasColumnType("date");

                    b.Property<string>("Email")
                        .HasColumnName("Email")
                        .HasColumnType("varchar(200)");

                    b.Property<int?>("EmpresaId")
                        .HasColumnName("EmpresaId")
                        .HasColumnType("int");

                    b.Property<string>("Foto")
                        .HasColumnName("Foto")
                        .HasColumnType("xml");

                    b.Property<int?>("GrupoEmpresaId")
                        .HasColumnName("GrupoEmpresaId")
                        .HasColumnType("int");

                    b.Property<int>("GrupoUsuarioId")
                        .HasColumnName("GrupoUsuarioId")
                        .HasColumnType("int");

                    b.Property<string>("Login")
                        .HasColumnName("Login")
                        .HasColumnType("varchar(100)");

                    b.Property<string>("Nome")
                        .IsRequired()
                        .HasColumnName("Nome")
                        .HasColumnType("varchar(200)");

                    b.Property<int?>("PostoId")
                        .HasColumnName("PostoId")
                        .HasColumnType("int");

                    b.Property<int>("QtdeErroSenha")
                        .HasColumnName("QtdeErroSenha")
                        .HasColumnType("int");

                    b.Property<string>("Senha")
                        .IsRequired()
                        .HasColumnName("Senha")
                        .HasColumnType("varchar(100)");

                    b.Property<int?>("SenhaProvisoria")
                        .HasColumnName("SenhaProvisoria")
                        .HasColumnType("int");

                    b.Property<string>("Telefone")
                        .HasColumnName("Telefone")
                        .HasColumnType("varchar(15)");

                    b.Property<int?>("UsuarioAlteracaoId")
                        .HasColumnName("UsuarioAlteracaoId")
                        .HasColumnType("int");

                    b.Property<int?>("UsuarioBloqueioId")
                        .HasColumnName("UsuarioBloqueioId")
                        .HasColumnType("int");

                    b.Property<int?>("UsuarioCadastro")
                        .HasColumnName("UsuarioCadastro")
                        .HasColumnType("int");

                    b.Property<int?>("UsuarioCancelado")
                        .HasColumnName("UsuarioCancelado")
                        .HasColumnType("int");

                    b.Property<int?>("UsuarioDesbloqueioId")
                        .HasColumnName("UsuarioDesbloqueioId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("EmpresaId");

                    b.HasIndex("GrupoEmpresaId");

                    b.HasIndex("GrupoUsuarioId");

                    b.HasIndex("PostoId");

                    b.ToTable("Usuario");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.UsuarioCentroCusto.UsuarioCentroCusto", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("Id")
                        .HasColumnType("serial");

                    b.Property<int>("CentroCustoId");

                    b.Property<int>("UsuarioId");

                    b.HasKey("Id");

                    b.HasIndex("CentroCustoId");

                    b.HasIndex("UsuarioId");

                    b.ToTable("UsuarioCentroCusto");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.UsuarioFilial.UsuarioFilial", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("Id")
                        .HasColumnType("serial");

                    b.Property<int>("FilialId");

                    b.Property<int>("UsuarioId");

                    b.HasKey("Id");

                    b.HasIndex("FilialId");

                    b.HasIndex("UsuarioId");

                    b.ToTable("UsuarioFilial");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.UsuarioHistorico.UsuarioHistorico", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("Id")
                        .HasColumnType("serial");

                    b.Property<string>("Campo")
                        .HasColumnName("Campo")
                        .HasColumnType("varchar(200)");

                    b.Property<DateTime?>("DataAlteracao")
                        .HasColumnName("DataAlteracao")
                        .HasColumnType("timestamp");

                    b.Property<int?>("UsuarioAlteracaoId")
                        .HasColumnName("UsuarioAlteracaoId")
                        .HasColumnType("int");

                    b.Property<int?>("UsuarioId")
                        .HasColumnName("UsuarioId")
                        .HasColumnType("int");

                    b.Property<string>("Valor")
                        .HasColumnName("Valor")
                        .HasColumnType("varchar(200)");

                    b.HasKey("Id");

                    b.HasIndex("UsuarioId");

                    b.ToTable("UsuarioHistorico");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.Veiculo.Veiculo", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("Id")
                        .HasColumnType("serial");

                    b.Property<int>("Ano")
                        .HasColumnName("Ano")
                        .HasColumnType("int");

                    b.Property<int?>("CarretaId")
                        .HasColumnName("CarretaId")
                        .HasColumnType("int");

                    b.Property<int?>("CentroCustoId")
                        .HasColumnName("CentroCustoId")
                        .HasColumnType("int");

                    b.Property<string>("Chassi")
                        .HasColumnName("Chassi")
                        .HasColumnType("varchar(18)");

                    b.Property<int?>("CidadeId")
                        .HasColumnName("CidadeId")
                        .HasColumnType("int");

                    b.Property<int?>("CombustivelId");

                    b.Property<int?>("CombustivelPreferencialId")
                        .HasColumnName("CombustivelPreferencialId")
                        .HasColumnType("int");

                    b.Property<int?>("ControlaAutonomia")
                        .HasColumnName("ControlaAutonomia")
                        .HasColumnType("int");

                    b.Property<string>("Cor")
                        .HasColumnName("Cor")
                        .HasColumnType("varchar(100)");

                    b.Property<DateTime>("DataBloqueio")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("DataCadastro")
                        .HasColumnName("DataCadastro")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("DataDesbloqueio")
                        .HasColumnType("timestamp");

                    b.Property<int?>("EmpresaId")
                        .HasColumnName("EmpresaId")
                        .HasColumnType("int");

                    b.Property<int?>("EstadoId")
                        .HasColumnName("EstadoId")
                        .HasColumnType("int");

                    b.Property<int?>("FabricanteId")
                        .HasColumnName("FabricanteId")
                        .HasColumnType("int");

                    b.Property<int?>("FilialId")
                        .HasColumnName("FilialId")
                        .HasColumnType("int");

                    b.Property<int?>("ModeloId")
                        .HasColumnName("ModeloId")
                        .HasColumnType("int");

                    b.Property<string>("NumeroFrota")
                        .HasColumnName("Frota")
                        .HasColumnType("varchar(100)");

                    b.Property<int?>("Odometro")
                        .HasColumnName("Odometro")
                        .HasColumnType("int");

                    b.Property<string>("Placa")
                        .IsRequired()
                        .HasColumnName("Placa")
                        .HasColumnType("varchar(8)");

                    b.Property<int>("PortadorProprietarioId")
                        .HasColumnName("PortadorProprietarioId")
                        .HasColumnType("int");

                    b.Property<int?>("QuantidadeEixos")
                        .HasColumnName("QuantidadeEixos")
                        .HasColumnType("int");

                    b.Property<string>("Renavam")
                        .HasColumnName("Renavam")
                        .HasColumnType("varchar(11)");

                    b.Property<int>("Status")
                        .HasColumnName("Status")
                        .HasColumnType("int");

                    b.Property<int?>("TipoAbastecimento")
                        .HasColumnName("TipoAbastecimento")
                        .HasColumnType("int");

                    b.Property<int>("TipoVeiculo")
                        .HasColumnName("TipoVeiculo")
                        .HasColumnType("int");

                    b.Property<int>("UsuarioBloqueioId");

                    b.Property<int>("UsuarioCadastroId")
                        .HasColumnName("UsuarioCadastroId")
                        .HasColumnType("int");

                    b.Property<int>("UsuarioDesbloqueioId");

                    b.HasKey("Id");

                    b.HasIndex("CentroCustoId");

                    b.HasIndex("CidadeId");

                    b.HasIndex("CombustivelId");

                    b.HasIndex("CombustivelPreferencialId");

                    b.HasIndex("EmpresaId");

                    b.HasIndex("EstadoId");

                    b.HasIndex("FabricanteId");

                    b.HasIndex("FilialId");

                    b.HasIndex("ModeloId");

                    b.HasIndex("PortadorProprietarioId");

                    b.HasIndex("UsuarioCadastroId");

                    b.ToTable("Veiculo");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.VeiculoCombustivel.VeiculoCombustivel", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("Id")
                        .HasColumnType("serial");

                    b.Property<decimal?>("Autonomia")
                        .HasColumnName("Autonomia")
                        .HasColumnType("decimal");

                    b.Property<decimal?>("Capacidade")
                        .IsRequired()
                        .HasColumnName("Capacidade")
                        .HasColumnType("decimal");

                    b.Property<int>("CombustivelId")
                        .HasColumnName("CombustivelId")
                        .HasColumnType("int");

                    b.Property<DateTime>("DataAlteracao")
                        .HasColumnName("DataAlteracao")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("DataCadastro")
                        .HasColumnName("DataCadastro")
                        .HasColumnType("timestamp");

                    b.Property<int?>("UsuarioAlteracaoId")
                        .HasColumnName("UsuarioAlteracaoId")
                        .HasColumnType("int");

                    b.Property<int?>("UsuarioCadastroId")
                        .HasColumnName("UsuarioCadastroId")
                        .HasColumnType("int");

                    b.Property<int>("VeiculoId")
                        .HasColumnName("VeiculoId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CombustivelId");

                    b.HasIndex("UsuarioAlteracaoId");

                    b.HasIndex("UsuarioCadastroId");

                    b.HasIndex("VeiculoId");

                    b.ToTable("VeiculoCombustivel");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.VeiculoEmpresa.VeiculoEmpresa", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("Id")
                        .HasColumnType("serial");

                    b.Property<int>("EmpresaId")
                        .HasColumnName("EmpresaId")
                        .HasColumnType("int");

                    b.Property<int>("VeiculoId")
                        .HasColumnName("VeiculoId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("EmpresaId");

                    b.HasIndex("VeiculoId");

                    b.ToTable("VeiculoEmpresa");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.Viagem.Viagem", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("Id")
                        .HasColumnType("serial");

                    b.Property<string>("Agencia")
                        .HasColumnName("Agencia")
                        .HasColumnType("varchar(150)");

                    b.Property<int?>("CidadeDestinoId")
                        .HasColumnName("CidadeIdDestino")
                        .HasColumnType("int");

                    b.Property<int?>("CidadeOrigemId")
                        .HasColumnName("CidadeIdOrigem")
                        .HasColumnType("int");

                    b.Property<string>("Ciot")
                        .HasColumnName("Ciot")
                        .HasColumnType("varchar(36)");

                    b.Property<int?>("CiotViagemId")
                        .HasColumnName("CiotViagemId")
                        .HasColumnType("int");

                    b.Property<string>("CodigoNaturezaCarga")
                        .HasColumnName("CodigoNaturezaCarga")
                        .HasColumnType("varchar(15)");

                    b.Property<string>("Conta")
                        .HasColumnName("Conta")
                        .HasColumnType("varchar(150)");

                    b.Property<DateTime?>("DataAlteracao")
                        .HasColumnName("DataAlteracao")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DataBaixa")
                        .HasColumnName("DataBaixa")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("DataCadastro")
                        .HasColumnName("DataCadastro")
                        .HasColumnType("timestamp");

                    b.Property<int>("EmpresaId")
                        .HasColumnName("EmpresaId")
                        .HasColumnType("int");

                    b.Property<string>("FilialId")
                        .HasColumnName("FilialId")
                        .HasColumnType("varchar(100)");

                    b.Property<string>("NomeMotorista")
                        .HasColumnName("NomeMotorista")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("NomeProprietario")
                        .HasColumnName("NomeProprietario")
                        .HasColumnType("varchar(200)");

                    b.Property<int?>("PagamentoExternoId")
                        .HasColumnName("PagamentoExternoId")
                        .HasColumnType("int");

                    b.Property<decimal?>("PesoCarga")
                        .HasColumnName("PesoCarga")
                        .HasColumnType("numeric(13,2)");

                    b.Property<int?>("PortadorMotoristaId")
                        .IsRequired()
                        .HasColumnName("PortadorMotoristaId")
                        .HasColumnType("int");

                    b.Property<int>("PortadorProprietarioId")
                        .HasColumnName("PortadorProprietarioId")
                        .HasColumnType("int");

                    b.Property<int?>("Status")
                        .HasColumnName("Status")
                        .HasColumnType("int");

                    b.Property<int?>("TipoBanco")
                        .HasColumnName("TipoBanco")
                        .HasColumnType("int");

                    b.Property<int?>("TipoConta")
                        .HasColumnName("TipoConta")
                        .HasColumnType("int");

                    b.Property<int?>("UsuarioAlteracaoId")
                        .HasColumnName("UsuarioAlteracaoId")
                        .HasColumnType("int");

                    b.Property<int?>("UsuarioCadastroId")
                        .HasColumnName("UsuarioCadastroId")
                        .HasColumnType("int");

                    b.Property<string>("VerificadorCiot")
                        .HasColumnName("VerificadorCiot")
                        .HasColumnType("varchar(12)");

                    b.Property<int?>("ViagemExternoId")
                        .HasColumnName("ViagemExternoId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CidadeDestinoId");

                    b.HasIndex("CidadeOrigemId");

                    b.HasIndex("CiotViagemId");

                    b.HasIndex("EmpresaId");

                    b.HasIndex("PortadorMotoristaId");

                    b.HasIndex("PortadorProprietarioId");

                    b.HasIndex("UsuarioAlteracaoId");

                    b.HasIndex("UsuarioCadastroId");

                    b.ToTable("Viagem");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.Abastecimento.Abastecimento", b =>
                {
                    b.HasOne("SistemaInfo.BBC.Domain.Models.AutorizacaoAbastecimento.AutorizacaoAbastecimento", "AutorizacaoAbastecimento")
                        .WithMany()
                        .HasForeignKey("AutorizacaoAbastecimentoId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Combustivel.Combustivel", "Combustivel")
                        .WithMany()
                        .HasForeignKey("CombustivelId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Empresa.Empresa", "Empresa")
                        .WithMany()
                        .HasForeignKey("EmpresaId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Filial.Filial", "Filial")
                        .WithMany()
                        .HasForeignKey("FilialId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.LotePagamento.LotePagamento", "LotePagamento")
                        .WithMany()
                        .HasForeignKey("LoteRetencaoId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Portador.Portador", "Portador")
                        .WithMany()
                        .HasForeignKey("PortadorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Posto.Posto", "Posto")
                        .WithMany()
                        .HasForeignKey("PostoId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("SistemaInfo.BBC.Domain.Models.ProtocoloAbastecimento.ProtocoloAbastecimento", "ProtocoloAbastecimento")
                        .WithMany("Abastecimentos")
                        .HasForeignKey("ProtocoloAbastecimentoId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioAlteracao")
                        .WithMany()
                        .HasForeignKey("UsuarioAlteracaoId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioCadastro")
                        .WithMany()
                        .HasForeignKey("UsuarioCadastroId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Veiculo.Veiculo", "Veiculo")
                        .WithMany()
                        .HasForeignKey("VeiculoId")
                        .OnDelete(DeleteBehavior.Restrict);
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.AtualizacaoPrecoCombustivel.AtualizacaoPrecoCombustivel", b =>
                {
                    b.HasOne("SistemaInfo.BBC.Domain.Models.Combustivel.Combustivel", "Combustivel")
                        .WithMany()
                        .HasForeignKey("CombustivelId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Posto.Posto", "Posto")
                        .WithMany()
                        .HasForeignKey("PostoId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "Usuario")
                        .WithMany()
                        .HasForeignKey("UsuarioAlteracaoId");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.AuditoriaSeguranca.AuditoriaSeguranca", b =>
                {
                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "Usuario")
                        .WithMany()
                        .HasForeignKey("UsuarioId")
                        .OnDelete(DeleteBehavior.Restrict);
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.AuthSession.AuthSession", b =>
                {
                    b.HasOne("SistemaInfo.BBC.Domain.Models.Posto.Posto", "Posto")
                        .WithMany()
                        .HasForeignKey("IdPosto");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "Usuario")
                        .WithMany()
                        .HasForeignKey("IdUsuario")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Portador.Portador", "Portador")
                        .WithMany()
                        .HasForeignKey("PortadorId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario")
                        .WithMany("AuthsSession")
                        .HasForeignKey("UsuarioId1");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.AuthSessionApi.AuthSessionApi", b =>
                {
                    b.HasOne("SistemaInfo.BBC.Domain.Models.Empresa.Empresa", "Empresa")
                        .WithMany()
                        .HasForeignKey("EmpresaId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("SistemaInfo.BBC.Domain.Models.GrupoEmpresa.GrupoEmpresa", "GrupoEmpresa")
                        .WithMany()
                        .HasForeignKey("GrupoEmpresaId");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.AutorizacaoAbastecimento.AutorizacaoAbastecimento", b =>
                {
                    b.HasOne("SistemaInfo.BBC.Domain.Models.Combustivel.Combustivel", "Combustivel")
                        .WithMany()
                        .HasForeignKey("CombustivelId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Empresa.Empresa", "Empresa")
                        .WithMany()
                        .HasForeignKey("EmpresaId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Filial.Filial", "Filial")
                        .WithMany()
                        .HasForeignKey("FilialId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioCadastro")
                        .WithMany()
                        .HasForeignKey("UsuarioCadastroId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Veiculo.Veiculo", "Veiculo")
                        .WithMany()
                        .HasForeignKey("VeiculoId")
                        .OnDelete(DeleteBehavior.Restrict);
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.Banco.Banco", b =>
                {
                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioBloqueio")
                        .WithMany()
                        .HasForeignKey("UsuarioBloqueioId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioCadastro")
                        .WithMany()
                        .HasForeignKey("UsuarioCadastroId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioDesbloqueio")
                        .WithMany()
                        .HasForeignKey("UsuarioDesbloqueioId");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.BloqueioSpd.BloqueioSpd", b =>
                {
                    b.HasOne("SistemaInfo.BBC.Domain.Models.Empresa.Empresa", "Empresa")
                        .WithMany()
                        .HasForeignKey("EmpresaId")
                        .OnDelete(DeleteBehavior.Restrict);
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.CentroCusto.CentroCusto", b =>
                {
                    b.HasOne("SistemaInfo.BBC.Domain.Models.Empresa.Empresa", "Empresa")
                        .WithMany("CentroCustos")
                        .HasForeignKey("EmpresaId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Filial.Filial", "Filial")
                        .WithMany("CentroCustos")
                        .HasForeignKey("FilialId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioBloqueio")
                        .WithMany()
                        .HasForeignKey("UsuarioBloqueioId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioCadastro")
                        .WithMany()
                        .HasForeignKey("UsuarioCadastroId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioDesbloqueio")
                        .WithMany()
                        .HasForeignKey("UsuarioDesbloqueioId");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.Cidade.Cidade", b =>
                {
                    b.HasOne("SistemaInfo.BBC.Domain.Models.Estado.Estado", "Estado")
                        .WithMany("Cidades")
                        .HasForeignKey("EstadoId")
                        .OnDelete(DeleteBehavior.Restrict);
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.CiotVeiculo.CiotVeiculo", b =>
                {
                    b.HasOne("SistemaInfo.BBC.Domain.Models.DeclaracaoCiot.DeclaracaoCiot", "DeclaracaoCiot")
                        .WithMany("CiotVeiculo")
                        .HasForeignKey("DeclaracaoCiotId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Veiculo.Veiculo", "Veiculo")
                        .WithMany()
                        .HasForeignKey("VeiculoId")
                        .OnDelete(DeleteBehavior.Restrict);
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.CiotViagem.CiotViagem", b =>
                {
                    b.HasOne("SistemaInfo.BBC.Domain.Models.Cidade.Cidade", "CidadeDestino")
                        .WithMany()
                        .HasForeignKey("CidadeDestinoId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Cidade.Cidade", "CidadeOrigem")
                        .WithMany()
                        .HasForeignKey("CidadeOrigemId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Cliente.Cliente", "ClienteConsignatario")
                        .WithMany()
                        .HasForeignKey("ConsignatarioId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.DeclaracaoCiot.DeclaracaoCiot", "DeclaracaoCiot")
                        .WithMany("CiotViagem")
                        .HasForeignKey("DeclaracaoCiotId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Cliente.Cliente", "ClienteDestinatario")
                        .WithMany()
                        .HasForeignKey("DestinatarioId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.NaturezaCarga.NaturezaCarga", "NaturezaCarga")
                        .WithMany()
                        .HasForeignKey("NaturezaCargaId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Cliente.Cliente", "ClienteRemetente")
                        .WithMany()
                        .HasForeignKey("RemetenteId");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.Cliente.Cliente", b =>
                {
                    b.HasOne("SistemaInfo.BBC.Domain.Models.Cidade.Cidade", "Cidade")
                        .WithMany()
                        .HasForeignKey("CidadeId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Empresa.Empresa", "Empresa")
                        .WithMany()
                        .HasForeignKey("EmpresaId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioBloqueio")
                        .WithMany()
                        .HasForeignKey("UsuarioBloqueioId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "Usuario")
                        .WithMany()
                        .HasForeignKey("UsuarioCadastroId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioDesbloqueio")
                        .WithMany()
                        .HasForeignKey("UsuarioDesbloqueioId");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.ClientSecret.ClientSecret", b =>
                {
                    b.HasOne("SistemaInfo.BBC.Domain.Models.GrupoEmpresa.GrupoEmpresa", "GrupoEmpresa")
                        .WithMany("ClientSecret")
                        .HasForeignKey("GrupoEmpresaId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Empresa.Empresa", "Empresa")
                        .WithMany("ClientSecret")
                        .HasForeignKey("IdEmpresa");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioAlteracao")
                        .WithMany()
                        .HasForeignKey("UsuarioAlteracaoId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "Usuario")
                        .WithMany()
                        .HasForeignKey("UsuarioCadastroId")
                        .OnDelete(DeleteBehavior.Restrict);
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.Combustivel.Combustivel", b =>
                {
                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioBloqueio")
                        .WithMany()
                        .HasForeignKey("UsuarioBloqueioId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioCadastro")
                        .WithMany()
                        .HasForeignKey("UsuarioCadastroId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioDesbloqueio")
                        .WithMany()
                        .HasForeignKey("UsuarioDesbloqueioId");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.ContasConductor.ContaConductor", b =>
                {
                    b.HasOne("SistemaInfo.BBC.Domain.Models.Banco.Banco", "Banco")
                        .WithMany()
                        .HasForeignKey("IdBanco");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.DeclaracaoCiot.DeclaracaoCiot", b =>
                {
                    b.HasOne("SistemaInfo.BBC.Domain.Models.Empresa.Empresa", "Empresa")
                        .WithMany()
                        .HasForeignKey("EmpresaId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Filial.Filial", "Filial")
                        .WithMany()
                        .HasForeignKey("FilialId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Portador.Portador", "PortadorMot")
                        .WithMany()
                        .HasForeignKey("PortadorMotId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Portador.Portador", "PortadorProp")
                        .WithMany()
                        .HasForeignKey("PortadorPropId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "Usuario")
                        .WithMany()
                        .HasForeignKey("UsuarioCadastroId")
                        .OnDelete(DeleteBehavior.Restrict);
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.Documento.Documento", b =>
                {
                    b.HasOne("SistemaInfo.BBC.Domain.Models.DocumentosProcessoVinculado.DocumentosProcessoVinculado", "DocumentosProcessoVinculado")
                        .WithMany()
                        .HasForeignKey("DocumentosProcessoVinculadoId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Empresa.Empresa", "Empresa")
                        .WithMany("Documentos")
                        .HasForeignKey("EmpresaId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Portador.Portador", "Portador")
                        .WithMany()
                        .HasForeignKey("PortadorId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Posto.Posto", "Posto")
                        .WithMany("Documentos")
                        .HasForeignKey("PostoId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "Usuario")
                        .WithMany()
                        .HasForeignKey("UsuarioCadastroId");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.DocumentosProcessoVinculado.DocumentosProcessoVinculado", b =>
                {
                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioAlteracao")
                        .WithMany()
                        .HasForeignKey("UsuarioAlteracaoId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioCadastro")
                        .WithMany()
                        .HasForeignKey("UsuarioCadastroId");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.Empresa.Empresa", b =>
                {
                    b.HasOne("SistemaInfo.BBC.Domain.Models.Cidade.Cidade", "Cidade")
                        .WithMany()
                        .HasForeignKey("CidadeId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.GrupoEmpresa.GrupoEmpresa", "GrupoEmpresa")
                        .WithMany("Empresa")
                        .HasForeignKey("GrupoEmpresaId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.TipoEmpresa.TipoEmpresa", "TipoEmpresa")
                        .WithMany()
                        .HasForeignKey("TipoEmpresaId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioAlteracaoContaAbastecimento")
                        .WithMany()
                        .HasForeignKey("UsuarioAlteracaoContaAbastecimentoId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioBloqueio")
                        .WithMany()
                        .HasForeignKey("UsuarioBloqueioId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "Usuario")
                        .WithMany()
                        .HasForeignKey("UsuarioCadastroId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioDesbloqueio")
                        .WithMany()
                        .HasForeignKey("UsuarioDesbloqueioId");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.EmpresaCfop.EmpresaCfop", b =>
                {
                    b.HasOne("SistemaInfo.BBC.Domain.Models.CFOP.CFOP", "CFOP")
                        .WithMany("EmpresaCfop")
                        .HasForeignKey("CfopId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Empresa.Empresa", "Empresa")
                        .WithMany("EmpresaCfop")
                        .HasForeignKey("EmpresaId")
                        .OnDelete(DeleteBehavior.Restrict);
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.Emprestimo.Emprestimo", b =>
                {
                    b.HasOne("SistemaInfo.BBC.Domain.Models.Portador.Portador", "Portador")
                        .WithMany()
                        .HasForeignKey("PortadorId");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.Fabricante.Fabricante", b =>
                {
                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioBloqueio")
                        .WithMany()
                        .HasForeignKey("UsuarioBloqueioId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioCadastro")
                        .WithMany()
                        .HasForeignKey("UsuarioCadastroId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioDesbloqueio")
                        .WithMany()
                        .HasForeignKey("UsuarioDesbloqueioId");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.Filial.Filial", b =>
                {
                    b.HasOne("SistemaInfo.BBC.Domain.Models.Cidade.Cidade", "Cidade")
                        .WithMany()
                        .HasForeignKey("CidadeId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Empresa.Empresa", "Empresa")
                        .WithMany("Filial")
                        .HasForeignKey("EmpresaId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioBloqueio")
                        .WithMany()
                        .HasForeignKey("UsuarioBloqueioId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioCadastro")
                        .WithMany()
                        .HasForeignKey("UsuarioCadastroId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioDesbloqueio")
                        .WithMany()
                        .HasForeignKey("UsuarioDesbloqueioId");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.GrupoEmpresa.GrupoEmpresa", b =>
                {
                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioAlteracao")
                        .WithMany()
                        .HasForeignKey("UsuarioAlteracaoId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioBloqueio")
                        .WithMany()
                        .HasForeignKey("UsuarioBloqueioId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioCadastro")
                        .WithMany()
                        .HasForeignKey("UsuarioCadastroId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioDesbloqueio")
                        .WithMany()
                        .HasForeignKey("UsuarioDesbloqueioId");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.GrupoUsuario.GrupoUsuario", b =>
                {
                    b.HasOne("SistemaInfo.BBC.Domain.Models.Empresa.Empresa", "Empresa")
                        .WithMany()
                        .HasForeignKey("EmpresaId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Posto.Posto", "Posto")
                        .WithMany()
                        .HasForeignKey("PostoId");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.GrupoUsuarioMenu.GrupoUsuarioMenu", b =>
                {
                    b.HasOne("SistemaInfo.BBC.Domain.Models.GrupoUsuario.GrupoUsuario", "GrupoUsuario")
                        .WithMany("GrupoUsuarioMenu")
                        .HasForeignKey("GrupoUsuarioId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Menu.Menu", "Menu")
                        .WithMany("GrupoUsuarioMenu")
                        .HasForeignKey("MenuId")
                        .OnDelete(DeleteBehavior.Restrict);
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.LotePagamento.LotePagamento", b =>
                {
                    b.HasOne("SistemaInfo.BBC.Domain.Models.Empresa.Empresa", "Empresa")
                        .WithMany()
                        .HasForeignKey("EmpresaId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Posto.Posto", "Posto")
                        .WithMany()
                        .HasForeignKey("PostoId");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.MDRPrazos.MDRPrazos", b =>
                {
                    b.HasOne("SistemaInfo.BBC.Domain.Models.Banco.Banco", "Banco")
                        .WithMany()
                        .HasForeignKey("BancoId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioBloqueio")
                        .WithMany()
                        .HasForeignKey("UsuarioBloqueioId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioCadastro")
                        .WithMany()
                        .HasForeignKey("UsuarioCadastroId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioDesbloqueio")
                        .WithMany()
                        .HasForeignKey("UsuarioDesbloqueioId");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.Mensagem.Mensagem", b =>
                {
                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioBloqueio")
                        .WithMany()
                        .HasForeignKey("UsuarioBloqueioId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioDesbloqueio")
                        .WithMany()
                        .HasForeignKey("UsuarioDesbloqueioId");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.Menu.Menu", b =>
                {
                    b.HasOne("SistemaInfo.BBC.Domain.Models.Menu.Menu", "MenuPai")
                        .WithMany()
                        .HasForeignKey("MenuPaiId");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.Modelo.Modelo", b =>
                {
                    b.HasOne("SistemaInfo.BBC.Domain.Models.Fabricante.Fabricante", "Fabricante")
                        .WithMany()
                        .HasForeignKey("FabricanteId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioBloqueio")
                        .WithMany()
                        .HasForeignKey("UsuarioBloqueioId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioCadastro")
                        .WithMany()
                        .HasForeignKey("UsuarioCadastroId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioDesbloqueio")
                        .WithMany()
                        .HasForeignKey("UsuarioDesbloqueioId");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.ModuloMenu.ModuloMenu", b =>
                {
                    b.HasOne("SistemaInfo.BBC.Domain.Models.Menu.Menu", "Menu")
                        .WithMany("ModuloMenu")
                        .HasForeignKey("MenuId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Modulo.Modulo", "Modulo")
                        .WithMany("ModuloMenu")
                        .HasForeignKey("ModuloId")
                        .OnDelete(DeleteBehavior.Restrict);
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.Notificacao.Notificacao", b =>
                {
                    b.HasOne("SistemaInfo.BBC.Domain.Models.PagamentoEvento.PagamentoEvento", "PagamentoEvento")
                        .WithMany("Notificacao")
                        .HasForeignKey("PagamentoEventoId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioAlteracao")
                        .WithMany()
                        .HasForeignKey("UsuarioAlteracaoId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioCadastro")
                        .WithMany()
                        .HasForeignKey("UsuarioCadastroId");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.PagamentoAbastecimento.PagamentoAbastecimento", b =>
                {
                    b.HasOne("SistemaInfo.BBC.Domain.Models.Abastecimento.Abastecimento", "Abastecimento")
                        .WithMany("PagamentoAbastecimentos")
                        .HasForeignKey("AbastecimentoId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.LotePagamento.LotePagamento", "LotePagamento")
                        .WithMany("PagamentoAbastecimento")
                        .HasForeignKey("LotePagamentoId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.PagamentoAbastecimento.PagamentoAbastecimento", "Receita")
                        .WithMany("PagamentosReceitas")
                        .HasForeignKey("PagamentoReceitaId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioAlteracao")
                        .WithMany()
                        .HasForeignKey("UsuarioAlteracaoId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioCadastro")
                        .WithMany()
                        .HasForeignKey("UsuarioCadastroId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioCancelamento")
                        .WithMany()
                        .HasForeignKey("UsuarioCancelamentoId");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.PagamentoEvento.PagamentoEvento", b =>
                {
                    b.HasOne("SistemaInfo.BBC.Domain.Models.Empresa.Empresa", "Empresa")
                        .WithMany()
                        .HasForeignKey("EmpresaId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioAlteracao")
                        .WithMany()
                        .HasForeignKey("UsuarioAlteracaoId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioCadastro")
                        .WithMany()
                        .HasForeignKey("UsuarioCadastroId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Viagem.Viagem", "Viagem")
                        .WithMany("PagamentoEvento")
                        .HasForeignKey("ViagemId")
                        .OnDelete(DeleteBehavior.Restrict);
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.PagamentoEventoHistorico.PagamentoEventoHistorico", b =>
                {
                    b.HasOne("SistemaInfo.BBC.Domain.Models.PagamentoEvento.PagamentoEvento", "PagamentoEvento")
                        .WithMany("PagamentoEventoHistorico")
                        .HasForeignKey("PagamentoEventoId");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.Pagamentos.Pagamentos", b =>
                {
                    b.HasOne("SistemaInfo.BBC.Domain.Models.Banco.Banco", "Banco")
                        .WithMany()
                        .HasForeignKey("BancoId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.DeclaracaoCiot.DeclaracaoCiot", "Ciot")
                        .WithMany()
                        .HasForeignKey("CiotId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Empresa.Empresa", "Empresa")
                        .WithMany()
                        .HasForeignKey("EmpresaId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Portador.Portador", "Portador")
                        .WithMany()
                        .HasForeignKey("PortadorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioBloqueio")
                        .WithMany()
                        .HasForeignKey("UsuarioBloqueioId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioCadastro")
                        .WithMany()
                        .HasForeignKey("UsuarioCadastroId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioDesbloqueio")
                        .WithMany()
                        .HasForeignKey("UsuarioDesbloqueioId");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.PagamentosHistorico.PagamentosHistorico", b =>
                {
                    b.HasOne("SistemaInfo.BBC.Domain.Models.Banco.Banco", "Banco")
                        .WithMany()
                        .HasForeignKey("BancoId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.DeclaracaoCiot.DeclaracaoCiot", "Ciot")
                        .WithMany()
                        .HasForeignKey("CiotId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Empresa.Empresa", "Empresa")
                        .WithMany()
                        .HasForeignKey("EmpresaId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Pagamentos.Pagamentos", "Pagamentos")
                        .WithMany()
                        .HasForeignKey("PagamentoId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Portador.Portador", "Portador")
                        .WithMany()
                        .HasForeignKey("PortadorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioBloqueio")
                        .WithMany()
                        .HasForeignKey("UsuarioBloqueioId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioCadastro")
                        .WithMany()
                        .HasForeignKey("UsuarioCadastroId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioDesbloqueio")
                        .WithMany()
                        .HasForeignKey("UsuarioDesbloqueioId");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.Parametros.Parametros", b =>
                {
                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioAlteracao")
                        .WithMany()
                        .HasForeignKey("UsuarioAlteracaoId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioCadastro")
                        .WithMany()
                        .HasForeignKey("UsuarioCadastroId");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.ParametrosHistorico.ParametrosHistorico", b =>
                {
                    b.HasOne("SistemaInfo.BBC.Domain.Models.Parametros.Parametros", "Parametros")
                        .WithMany()
                        .HasForeignKey("ParametroId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioAlteracao")
                        .WithMany()
                        .HasForeignKey("UsuarioAlteracaoId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioCadastro")
                        .WithMany()
                        .HasForeignKey("UsuarioCadastroId");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.PercentualTransferencia.PercentualTransferencia", b =>
                {
                    b.HasOne("SistemaInfo.BBC.Domain.Models.Portador.Portador", "Proprietario")
                        .WithOne("PercentualTransferencia")
                        .HasForeignKey("SistemaInfo.BBC.Domain.Models.PercentualTransferencia.PercentualTransferencia", "ProprietarioId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioCadastro")
                        .WithMany()
                        .HasForeignKey("UsuarioCadastroId");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.PercentualTransferenciaHistorico.PercentualTransferenciaHistorico", b =>
                {
                    b.HasOne("SistemaInfo.BBC.Domain.Models.PercentualTransferencia.PercentualTransferencia", "PercentualTransferencia")
                        .WithMany("PercentualTransferenciaHistoricos")
                        .HasForeignKey("PercentualTransferenciaId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioAlteracao")
                        .WithMany()
                        .HasForeignKey("UsuarioAlteracaoId");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.PercentualTransferenciaPortador.PercentualTransferenciaPortador", b =>
                {
                    b.HasOne("SistemaInfo.BBC.Domain.Models.PercentualTransferencia.PercentualTransferencia", "PercentualTransferencia")
                        .WithMany("PercentualTransferenciaPortadores")
                        .HasForeignKey("PercentualTransferenciaId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Portador.Portador", "Portador")
                        .WithMany()
                        .HasForeignKey("PortadorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioAlteracao")
                        .WithMany()
                        .HasForeignKey("UsuarioAlteracaoId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioCadastro")
                        .WithMany()
                        .HasForeignKey("UsuarioCadastroId");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.Portador.Portador", b =>
                {
                    b.HasOne("SistemaInfo.BBC.Domain.Models.Veiculo.Veiculo", "Carreta2")
                        .WithMany()
                        .HasForeignKey("Carreta2Id");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Veiculo.Veiculo", "Carreta3")
                        .WithMany()
                        .HasForeignKey("Carreta3Id");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Veiculo.Veiculo", "Carreta")
                        .WithMany()
                        .HasForeignKey("CarretaId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Cidade.Cidade", "Cidade")
                        .WithMany()
                        .HasForeignKey("CidadeId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Empresa.Empresa", "EmpresaFrota")
                        .WithMany()
                        .HasForeignKey("EmpresaIdFrota");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Estado.Estado", "Estado")
                        .WithMany()
                        .HasForeignKey("EstadoId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioBloqueio")
                        .WithMany()
                        .HasForeignKey("UsuarioBloqueioId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioCadastro")
                        .WithMany()
                        .HasForeignKey("UsuarioCadastroId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioCancelamento")
                        .WithMany()
                        .HasForeignKey("UsuarioCancelamentoId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioDesbloqueio")
                        .WithMany()
                        .HasForeignKey("UsuarioDesbloqueioId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioDesbloqueioMobile")
                        .WithMany()
                        .HasForeignKey("UsuarioDesbloqueioMobileId");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.PortadorCentroCusto.PortadorCentroCusto", b =>
                {
                    b.HasOne("SistemaInfo.BBC.Domain.Models.CentroCusto.CentroCusto", "CentroCusto")
                        .WithMany()
                        .HasForeignKey("CentroCustoId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Portador.Portador", "Portador")
                        .WithMany("PortadorCentroCusto")
                        .HasForeignKey("PortadorId")
                        .OnDelete(DeleteBehavior.Restrict);
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.PortadorEmpresa.PortadorEmpresa", b =>
                {
                    b.HasOne("SistemaInfo.BBC.Domain.Models.Empresa.Empresa", "Empresa")
                        .WithMany()
                        .HasForeignKey("EmpresaId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Portador.Portador", "Portador")
                        .WithMany()
                        .HasForeignKey("PortadorId")
                        .OnDelete(DeleteBehavior.Restrict);
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.PortadorRepresentanteLegal.PortadorRepresentanteLegal", b =>
                {
                    b.HasOne("SistemaInfo.BBC.Domain.Models.Portador.Portador", "Portador")
                        .WithMany("PortadorRepresentanteLegal")
                        .HasForeignKey("PortadorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Portador.Portador", "PortadorRepresentante")
                        .WithMany()
                        .HasForeignKey("PortadorRepresentanteId")
                        .OnDelete(DeleteBehavior.Restrict);
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.Posto.Posto", b =>
                {
                    b.HasOne("SistemaInfo.BBC.Domain.Models.Banco.Banco", "Banco")
                        .WithMany()
                        .HasForeignKey("BancoId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Cidade.Cidade", "Cidade")
                        .WithMany()
                        .HasForeignKey("CidadeId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Filial.Filial", "Filial")
                        .WithMany()
                        .HasForeignKey("FilialId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.MDRPrazos.MDRPrazos", "MdrPrazos")
                        .WithMany()
                        .HasForeignKey("MDRId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioBloqueio")
                        .WithMany()
                        .HasForeignKey("UsuarioBloqueioId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioCadastro")
                        .WithMany()
                        .HasForeignKey("UsuarioCadastroId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioCredenciamentoSla")
                        .WithMany()
                        .HasForeignKey("UsuarioCredenciamentoSlaId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioDesbloqueio")
                        .WithMany()
                        .HasForeignKey("UsuarioDesbloqueioId");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.PostoCombustivel.PostoCombustivel", b =>
                {
                    b.HasOne("SistemaInfo.BBC.Domain.Models.Combustivel.Combustivel", "Combustivel")
                        .WithMany()
                        .HasForeignKey("CombustivelId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Posto.Posto", "Posto")
                        .WithMany("PostoCombustiveis")
                        .HasForeignKey("PostoId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "Usuario")
                        .WithMany()
                        .HasForeignKey("UsuarioAlteracaoId");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.PostoCombustivelProduto.PostoCombustivelProduto", b =>
                {
                    b.HasOne("SistemaInfo.BBC.Domain.Models.Combustivel.Combustivel", "Combustivel")
                        .WithMany()
                        .HasForeignKey("CombustivelId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Posto.Posto", "Posto")
                        .WithMany("PostoCombustivelProduto")
                        .HasForeignKey("PostoId")
                        .OnDelete(DeleteBehavior.Restrict);
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.PostoContato.PostoContato", b =>
                {
                    b.HasOne("SistemaInfo.BBC.Domain.Models.Posto.Posto", "Posto")
                        .WithMany("PostoContatos")
                        .HasForeignKey("PostoId")
                        .OnDelete(DeleteBehavior.Restrict);
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.ProtocoloAbastecimento.ProtocoloAbastecimento", b =>
                {
                    b.HasOne("SistemaInfo.BBC.Domain.Models.Empresa.Empresa", "Empresa")
                        .WithMany()
                        .HasForeignKey("EmpresaId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.LotePagamento.LotePagamento", "LotePagamento")
                        .WithMany("ProtocolosAbastecimento")
                        .HasForeignKey("LotePagamentoId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Posto.Posto", "Posto")
                        .WithMany()
                        .HasForeignKey("PostoId");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.Retencao.Retencao", b =>
                {
                    b.HasOne("SistemaInfo.BBC.Domain.Models.Emprestimo.Emprestimo", "Emprestimo")
                        .WithMany("Retencoes")
                        .HasForeignKey("EmprestimoId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Pagamentos.Pagamentos", "Pagamento")
                        .WithMany()
                        .HasForeignKey("PagamentoId");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.ServidorCiot.ServidorCiot", b =>
                {
                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioAlteracao")
                        .WithMany()
                        .HasForeignKey("UsuarioAlteracaoId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioCadastro")
                        .WithMany()
                        .HasForeignKey("UsuarioCadastroId");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.TipoEmpresa.TipoEmpresa", b =>
                {
                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioBloqueio")
                        .WithMany()
                        .HasForeignKey("UsuarioBloqueioId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioCadastro")
                        .WithMany()
                        .HasForeignKey("UsuarioCadastroId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioDesbloqueio")
                        .WithMany()
                        .HasForeignKey("UsuarioDesbloqueioId");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.Transacao.Transacao", b =>
                {
                    b.HasOne("SistemaInfo.BBC.Domain.Models.PagamentoEvento.PagamentoEvento", "PagamentoEvento")
                        .WithMany("Transacao")
                        .HasForeignKey("IdPagamentoEvento")
                        .OnDelete(DeleteBehavior.Restrict);
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", b =>
                {
                    b.HasOne("SistemaInfo.BBC.Domain.Models.Empresa.Empresa", "Empresa")
                        .WithMany()
                        .HasForeignKey("EmpresaId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.GrupoEmpresa.GrupoEmpresa", "GrupoEmpresa")
                        .WithMany("Usuarios")
                        .HasForeignKey("GrupoEmpresaId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.GrupoUsuario.GrupoUsuario", "GrupoUsuario")
                        .WithMany()
                        .HasForeignKey("GrupoUsuarioId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Posto.Posto", "Posto")
                        .WithMany()
                        .HasForeignKey("PostoId");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.UsuarioCentroCusto.UsuarioCentroCusto", b =>
                {
                    b.HasOne("SistemaInfo.BBC.Domain.Models.CentroCusto.CentroCusto", "CentroCusto")
                        .WithMany()
                        .HasForeignKey("CentroCustoId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "Usuario")
                        .WithMany("UsuarioCentroCusto")
                        .HasForeignKey("UsuarioId")
                        .OnDelete(DeleteBehavior.Restrict);
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.UsuarioFilial.UsuarioFilial", b =>
                {
                    b.HasOne("SistemaInfo.BBC.Domain.Models.Filial.Filial", "Filial")
                        .WithMany("UsuarioFilial")
                        .HasForeignKey("FilialId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "Usuario")
                        .WithMany("UsuarioFilial")
                        .HasForeignKey("UsuarioId")
                        .OnDelete(DeleteBehavior.Restrict);
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.UsuarioHistorico.UsuarioHistorico", b =>
                {
                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "Usuario")
                        .WithMany()
                        .HasForeignKey("UsuarioId");
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.Veiculo.Veiculo", b =>
                {
                    b.HasOne("SistemaInfo.BBC.Domain.Models.CentroCusto.CentroCusto", "CentroCusto")
                        .WithMany()
                        .HasForeignKey("CentroCustoId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Cidade.Cidade", "Cidade")
                        .WithMany()
                        .HasForeignKey("CidadeId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Combustivel.Combustivel", "Combustivel")
                        .WithMany()
                        .HasForeignKey("CombustivelId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Combustivel.Combustivel", "CombustivelPreferencial")
                        .WithMany()
                        .HasForeignKey("CombustivelPreferencialId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Empresa.Empresa", "Empresa")
                        .WithMany()
                        .HasForeignKey("EmpresaId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Estado.Estado", "Estado")
                        .WithMany()
                        .HasForeignKey("EstadoId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Fabricante.Fabricante", "Fabricante")
                        .WithMany()
                        .HasForeignKey("FabricanteId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Filial.Filial", "Filial")
                        .WithMany()
                        .HasForeignKey("FilialId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Modelo.Modelo", "Modelo")
                        .WithMany()
                        .HasForeignKey("ModeloId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Portador.Portador", "Portador")
                        .WithMany()
                        .HasForeignKey("PortadorProprietarioId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "Usuario")
                        .WithMany()
                        .HasForeignKey("UsuarioCadastroId")
                        .OnDelete(DeleteBehavior.Restrict);
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.VeiculoCombustivel.VeiculoCombustivel", b =>
                {
                    b.HasOne("SistemaInfo.BBC.Domain.Models.Combustivel.Combustivel", "Combustivel")
                        .WithMany()
                        .HasForeignKey("CombustivelId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioAlteracao")
                        .WithMany()
                        .HasForeignKey("UsuarioAlteracaoId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioCadastro")
                        .WithMany()
                        .HasForeignKey("UsuarioCadastroId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Veiculo.Veiculo", "Veiculo")
                        .WithMany("VeiculoCombustiveis")
                        .HasForeignKey("VeiculoId")
                        .OnDelete(DeleteBehavior.Restrict);
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.VeiculoEmpresa.VeiculoEmpresa", b =>
                {
                    b.HasOne("SistemaInfo.BBC.Domain.Models.Empresa.Empresa", "Empresa")
                        .WithMany()
                        .HasForeignKey("EmpresaId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Veiculo.Veiculo", "Veiculo")
                        .WithMany()
                        .HasForeignKey("VeiculoId")
                        .OnDelete(DeleteBehavior.Restrict);
                });

            modelBuilder.Entity("SistemaInfo.BBC.Domain.Models.Viagem.Viagem", b =>
                {
                    b.HasOne("SistemaInfo.BBC.Domain.Models.Cidade.Cidade", "CidadeDestino")
                        .WithMany()
                        .HasForeignKey("CidadeDestinoId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Cidade.Cidade", "CidadeOrigem")
                        .WithMany()
                        .HasForeignKey("CidadeOrigemId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.CiotViagem.CiotViagem", "CiotViagem")
                        .WithMany()
                        .HasForeignKey("CiotViagemId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Empresa.Empresa", "Empresa")
                        .WithMany()
                        .HasForeignKey("EmpresaId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Portador.Portador", "PortadorMotorista")
                        .WithMany()
                        .HasForeignKey("PortadorMotoristaId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Portador.Portador", "PortadorProprietario")
                        .WithMany()
                        .HasForeignKey("PortadorProprietarioId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioAlteracao")
                        .WithMany()
                        .HasForeignKey("UsuarioAlteracaoId");

                    b.HasOne("SistemaInfo.BBC.Domain.Models.Usuario.Usuario", "UsuarioCadastro")
                        .WithMany()
                        .HasForeignKey("UsuarioCadastroId");
                });
#pragma warning restore 612, 618
        }
    }
}

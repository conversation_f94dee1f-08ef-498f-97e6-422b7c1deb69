using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using AutoMapper.QueryableExtensions;
using Microsoft.EntityFrameworkCore;
using SistemaInfo.BBC.Application.Helpers;
using Newtonsoft.Json;
using NLog;
using SistemaInfo.BBC.Application.Interface.GrupoUsuario;
using SistemaInfo.BBC.Application.Interface.Modulo;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.Empresa;
using SistemaInfo.BBC.Application.Objects.Web.EmpresaGrupoUsuario;
using SistemaInfo.BBC.Application.Objects.Web.GruposUsuario;
using SistemaInfo.BBC.Application.Objects.Web.GruposUsuario.Request;
using SistemaInfo.BBC.Application.Objects.Web.Menu;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.BBC.Domain.Models.Empresa.Repository;
using SistemaInfo.BBC.Domain.Models.EmpresaGrupoUsuario;
using SistemaInfo.BBC.Domain.Models.EmpresaGrupoUsuario.Commands;
using SistemaInfo.BBC.Domain.Models.EmpresaGrupoUsuario.Repository;
using SistemaInfo.BBC.Domain.Models.EmpresaUsuario.Repository;
using SistemaInfo.BBC.Domain.Models.GrupoUsuario.Commands;
using SistemaInfo.BBC.Domain.Models.GrupoUsuario.Repository;
using SistemaInfo.BBC.Domain.Models.Usuario.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.Services.GrupoUsuario
{
    public class GrupoUsuarioAppService : AppService<Domain.Models.GrupoUsuario.GrupoUsuario, IGrupoUsuarioReadRepository, IGrupoUsuarioWriteRepository>, 
        IGrupoUsuarioAppService
    {
        private readonly IModuloAppService _moduloAppService;
        private readonly IEmpresaReadRepository _empresaReadRepository;

        private readonly IEmpresaGrupoUsuarioReadRepository _empresaGrupoUsuarioReadRepository;
        private readonly IUsuarioReadRepository _usuarioReadRepository;
        private readonly IEmpresaUsuarioReadRepository _empresaUsuarioReadRepository;
        
        public GrupoUsuarioAppService(IAppEngine engine,
            IGrupoUsuarioReadRepository readRepository,
            IGrupoUsuarioWriteRepository writeRepository,
            IUsuarioReadRepository usuarioReadRepository,
            IModuloAppService moduloAppService, IEmpresaReadRepository empresaReadRepository, IEmpresaGrupoUsuarioReadRepository empresaGrupoUsuarioReadRepository, IEmpresaUsuarioReadRepository empresaUsuarioReadRepository) : base(engine, readRepository, writeRepository)
        {
            _usuarioReadRepository = usuarioReadRepository;
            _moduloAppService = moduloAppService;
            _empresaReadRepository = empresaReadRepository;
            _empresaGrupoUsuarioReadRepository = empresaGrupoUsuarioReadRepository;
            _empresaUsuarioReadRepository = empresaUsuarioReadRepository;
        }

        public async Task<int> CadastrarGrupoUsuarioParaEmpresaAsync(GrupoUsuarioCadastrarRequest request)
        {
            try
            {
                new LogHelper().LogOperationStart("CadastrarGrupoUsuarioParaEmpresaAsync");
                var grupoUsuarioCadastrado = Repository.Query.FirstOrDefault(o => o.EmpresaId == request.EmpresaId);

                if (grupoUsuarioCadastrado != null)
                    return grupoUsuarioCadastrado.Id;

                var grupoUsuarioCommand = Mapper.Map<GrupoUsuarioSaveComRetornoCommand>(request);
                grupoUsuarioCommand.ValidarCriacao();
                var resultado =
                    await Engine.CommandBus.SendCommandAsync<Domain.Models.GrupoUsuario.GrupoUsuario>(
                        grupoUsuarioCommand);

                return resultado.Id;
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar CadastrarGrupoUsuarioParaEmpresaAsync");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("CadastrarGrupoUsuarioParaEmpresaAsync");
            }
        }

        public async Task<ConsultarEmpresaComboResponse> ConsultaGridEmpresasVinculadasCombo(int requestTake, int requestPage, OrderFilters requestOrder,
            List<QueryFilters> requestFilters, List<int> listaIdsEmpresa, int? grupoUsuarioId)
        {
            var lLog = LogManager.GetCurrentClassLogger();
            try
            {
                
                var empresasSemVinculo = _empresaReadRepository.GetAll();
                
                var empresas = await _usuarioReadRepository.GetEmpresasAcessoUsuario(Engine.User.Id);
                if (!empresas.IsEmpty())
                    empresasSemVinculo = empresasSemVinculo.Where(x => empresas.Contains(x.Id));
                
                if (listaIdsEmpresa != null && listaIdsEmpresa.Any())
                {
                    empresasSemVinculo = empresasSemVinculo.Where(e => !listaIdsEmpresa.Contains(e.Id));
                }
                
                empresasSemVinculo = empresasSemVinculo.AplicarFiltrosDinamicos(requestFilters);
                
                var lCount = await empresasSemVinculo.CountAsync();
                    
                empresasSemVinculo = string.IsNullOrWhiteSpace(requestOrder?.Campo)
                    ? empresasSemVinculo.OrderByDescending(o => o.Id)
                    : empresasSemVinculo.OrderBy($"{requestOrder?.Campo} {requestOrder?.Operador.DescriptionAttr()}");

                var lRetorno = await empresasSemVinculo.Skip((requestPage - 1) * requestTake)
                    .Take(requestTake).ProjectTo<ConsultarEmpresaCombo>(Engine.Mapper.ConfigurationProvider).ToListAsync();

                return new ConsultarEmpresaComboResponse
                {
                    items = lRetorno,
                    totalItems = lCount
                };
            }
            catch (Exception e)
            {
                lLog.Error(e.Message);
                return new ConsultarEmpresaComboResponse();
            }
        }
        
        public ConsultarGrupoUsuarioMenuResponse ConsultarPorId(int idGrupoUsuario)
        {
            try
            {
                new LogHelper().LogOperationStart("ConsultarPorId");
                var lGrupoUsuario = Repository.Query.Where(a => a.Id == idGrupoUsuario)
                    .Include(x => x.GrupoUsuarioMenu)
                    .Include(x => x.GrupoUsuarioMenu)
                    .Include(x => x.Empresa)
                    .Include(x => x.Posto)
                    .FirstOrDefault();

                if (lGrupoUsuario == null) return null;

                var lMenus = Repository.Query.GetMenusGrupoUsuario(lGrupoUsuario.Id);

                var lMenusPai = Repository.Query.GetMenusPaiGrupoUsuario(lGrupoUsuario.Id);

                
                var lEmpresasVinculadas = _empresaGrupoUsuarioReadRepository
                    .Where(x => x.GrupoUsuarioId == idGrupoUsuario)
                    .Select(y => new ConsultarEmpresaCombo
                    {
                        Id = y.Empresa.Id,
                        Cnpj = y.Empresa.Cnpj.ToCNPJFormato(),
                        NomeFantasia = y.Empresa.NomeFantasia,
                        RazaoSocial = y.Empresa.RazaoSocial
                    }).ToList();
            
                var lretorno = new ConsultarGrupoUsuarioMenuResponse()
                {
                    nomeEmpresa = lGrupoUsuario.Empresa?.NomeFantasia,
                    nomePosto = lGrupoUsuario.Posto?.NomeFantasia,
                    idEmpresa = lGrupoUsuario.EmpresaId,
                    idPosto = lGrupoUsuario.PostoId,
                    descricao = lGrupoUsuario.Descricao,
                    idGrupoUsuario = lGrupoUsuario.Id,
                    sistema = lGrupoUsuario.Sistema,
                    Menus = lMenus.ProjectTo<MenuFilhoResponse>().ToList(),
                    Modulo = _moduloAppService.ConsultarModulos(lGrupoUsuario.EmpresaId, lGrupoUsuario.PostoId, lGrupoUsuario.Sistema).Result,
                    MenusPai = lMenusPai.ProjectTo<MenuEstruturaModelResponse>().ToList(),
                    empresasVinculadas = lEmpresasVinculadas
                };

                return lretorno;
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar ConsultarPorId");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("ConsultarPorId");
            }
        }

        public async Task<ConsultarGridGrupoUsuarioResponse> ConsultarGrupoUsuarioCombo(int take, 
            int page, OrderFilters orderFilters, List<QueryFilters> filters, int? grupoEmpresaId)
        {
            
            var gruposUsuario = Repository.Query.Where(x => x.Sistema == 0);
            
            filters.RemoveAt(0);
            
            var lListaEmpresasIds = filters.FirstOrDefault(x => x.Campo == "ListaEmpresasIds");
            var empresasIds = new List<int>();

            if (lListaEmpresasIds != null)
            {
                empresasIds = JsonConvert.DeserializeObject<List<int>>(lListaEmpresasIds.Valor);
            }
            
            if (Engine.User.EmpresaId > 0)
            {
                gruposUsuario = gruposUsuario.Where(x => x.EmpresaId == Engine.User.EmpresaId);
            }
            
            if (empresasIds.Any() && Engine.User.EmpresaId <= 0)
            {
                if (empresasIds.Count == 1)
                {
                    gruposUsuario = gruposUsuario.Where(x =>empresasIds.Contains(x.EmpresaId ?? 0)).AsQueryable();
                }
                else
                {
                    var gruposUsuarioEmpresa = await _empresaGrupoUsuarioReadRepository
                        .Where(x => empresasIds.Contains(x.EmpresaId))
                        .GroupBy(x => x.GrupoUsuarioId)
                        .Where(g => g.Select(e => e.EmpresaId).Distinct().Count() == empresasIds.Count)
                        .Select(g => g.Key)
                        .ToListAsync();
                    
                    gruposUsuario = gruposUsuario.Where(z => gruposUsuarioEmpresa.Contains(z.Id)).AsQueryable();
                }
            } 
            
            if (grupoEmpresaId != 0)
            {
                var idsEmpresas = await _empresaReadRepository.Where(x => x.GrupoEmpresaId == grupoEmpresaId)
                    .Select(x => x.Id).ToListAsync();
                gruposUsuario = gruposUsuario.Where(x =>
                    !x.EmpresaId.HasValue || x.EmpresaId.HasValue 
                    && idsEmpresas.Contains(x.EmpresaId ?? 0) && x.MultiEmpresas == 0);
            }

            var totalItems = await gruposUsuario.CountAsync();
            
            gruposUsuario = gruposUsuario.AplicarFiltrosDinamicos(filters);
            gruposUsuario = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                ? gruposUsuario.OrderByDescending(o => o.Id)
                : gruposUsuario.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");
            
            
            var retorno = await gruposUsuario.Skip((page - 1) * take)
                .Take(take).ProjectTo<ConsultarGrupoUsuarioGrid>(Engine.Mapper.ConfigurationProvider).ToListAsync();
            
            return new ConsultarGridGrupoUsuarioResponse()
            {
                items = retorno, 
                totalItems = totalItems
            };
        }
        
        public GrupoUsuarioResponse ConsultarPorIdEmpresa(int id)
        {
            try
            {
                new LogHelper().LogOperationStart("ConsultarPorIdEmpresa");
                return Repository.Query.Where(o => o.EmpresaId == id).ProjectTo<GrupoUsuarioResponse>()
                    .FirstOrDefault();
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar ConsultarPorIdEmpresa");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("ConsultarPorIdEmpresa");
            }
        }

        public async Task SaveGrupoUsuario(ConsultarGrupoUsuarioResponse lModel)
        {
            try
            {
                new LogHelper().LogOperationStart("SaveGrupoUsuario");
                var lGrupo = Repository.Query.GetById(lModel.idGrupoUsuario);

                if (lGrupo == null || lModel.idGrupoUsuario == 0)
                {
                    lModel.multiEmpresas = lModel.empresasVinculadas?.Count > 1 ? 1 : 0;
                    var lGrupoUsuarioInsert = Mapper.Map<GrupoUsuarioAdicionarComRetornoCommand>(lModel);
                    var retorno = await Engine.CommandBus.SendCommandAsync<Domain.Models.GrupoUsuario.GrupoUsuario>(lGrupoUsuarioInsert);
                    await ValidaSalvarEmpresaGrupoUsuario(lModel, retorno.Id);
                }
                else
                {
                    lModel.multiEmpresas = lModel.empresasVinculadas?.Count > 1 ? 1 : 0;
                    var lGrupoUsuarioUpdate = Mapper.Map<GrupoUsuarioEditarCommand>(lModel);
                    await Engine.CommandBus.SendCommandAsync(lGrupoUsuarioUpdate);
                    await ValidaSalvarEmpresaGrupoUsuario(lModel, lModel.idGrupoUsuario);

                }
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar SaveGrupoUsuario");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("SaveGrupoUsuario");
            }
        }

        private async Task ValidaSalvarEmpresaGrupoUsuario(ConsultarGrupoUsuarioResponse lModel, int? idGrupoUsuario)
        {
            
            var empresasGrupoUsuario = await ConsultarEmpresasVinculadasGrupoUsuario(idGrupoUsuario);
            
            var empresasVinculadasIds = lModel.empresasVinculadas?.Select(e => e.Id).ToList() ?? [];
            
            #region Adiciona empresaGrupoUsuario
            
            if (lModel.empresasVinculadas != null)
                foreach (var empresa in lModel.empresasVinculadas.Where(empresa => empresasGrupoUsuario.All(eu => eu.EmpresaId != empresa.Id)))
                {
                    await SalvarEmpresaGrupoUsuario(new EmpresaGrupoUsuarioRequest
                    {
                        GrupoUsuarioId = idGrupoUsuario ?? lModel.idGrupoUsuario,
                        EmpresaId = empresa.Id
                    });
                }
            
            #endregion

            #region Exclui empresaGrupoUsuario

            foreach (var empresaGrupoUsuario in empresasGrupoUsuario.Where(empresaGrupoUsuario => !empresasVinculadasIds.Contains(empresaGrupoUsuario.EmpresaId)))
            {
                await Engine.CommandBus.SendCommandAsync(new EmpresaGrupoUsuarioExcluirCommand
                {
                    GrupoUsuarioId = empresaGrupoUsuario.GrupoUsuarioId,
                    EmpresaId = empresaGrupoUsuario.EmpresaId
                });
            }

            #endregion
            
            
        }

        private async Task<List<EmpresaGrupoUsuario>> ConsultarEmpresasVinculadasGrupoUsuario(int? idGrupoUsuario)
        {
            var empresasGrupoUsuario = await _empresaGrupoUsuarioReadRepository
                .Where(eu => eu.GrupoUsuarioId == idGrupoUsuario)
                .ToListAsync();
            return empresasGrupoUsuario;
        }

        public async Task<RespPadrao> SalvarEmpresaGrupoUsuario(EmpresaGrupoUsuarioRequest lEmpresaGrupoUsuario)
        {
            try
            {
                lEmpresaGrupoUsuario.EmpresaId = Engine.User.EmpresaId == 0 ? lEmpresaGrupoUsuario.EmpresaId : Engine.User.EmpresaId;
                await Engine.CommandBus.SendCommandAsync(Mapper.Map<EmpresaGrupoUsuarioSalvarCommand>(lEmpresaGrupoUsuario));
                return new RespPadrao { sucesso = true, mensagem = "" };
            }
            catch (Exception e)
            {
                return new RespPadrao { sucesso = false, mensagem = e.Message };
            }
        }


        public ConsultarGridGrupoUsuarioResponse ConsultarGridGrupoUsuario(DtoConsultaGridGrupoUsuario request)
        {
            try
            {
                new LogHelper().LogOperationStart("ConsultarGridGrupoUsuario");
                var lGrupoUsuario = GetDataToGridAndReport(request.Order, request.Filters);

                if (request.Sistema != null)
                {
                    lGrupoUsuario = lGrupoUsuario.Where(x => x.Sistema == request.Sistema);
                }
            
                var empresas = _usuarioReadRepository.GetEmpresasAcessoUsuario(Engine.User.Id).Result;
                if (!empresas.IsEmpty())
                    lGrupoUsuario = lGrupoUsuario.Where(x => x.EmpresaId.HasValue && empresas.Contains(x.EmpresaId.ToInt()));
            
                lGrupoUsuario = lGrupoUsuario.AplicarFiltrosDinamicos(request.Filters);
                lGrupoUsuario = string.IsNullOrWhiteSpace(request.Order?.Campo)
                    ? lGrupoUsuario.OrderByDescending(o => o.Id)
                    : lGrupoUsuario.OrderBy($"{request.Order.Campo} {request.Order.Operador.DescriptionAttr()}");
                
                var retorno = lGrupoUsuario.Skip((request.Page - 1) * request.Take)
                    .Take(request.Take)
                    .ProjectTo<ConsultarGrupoUsuarioGrid>().ToList();

                return new ConsultarGridGrupoUsuarioResponse()
                {
                    items = retorno,
                    totalItems = lGrupoUsuario.Count()
                };
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar ConsultarGridGrupoUsuario");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("ConsultarGridGrupoUsuario");
            }
        }

        public ConsultarGridGrupoUsuarioResponse ConsultarGridGrupoUsuarioPosto(DtoConsultaGridGrupoUsuario request)
        {
            try
            {
                new LogHelper().LogOperationStart("ConsultarGridGrupoUsuarioPosto");
                var lGrupoUsuario = GetDataToGridAndReport(request.Order, request.Filters);

                lGrupoUsuario = lGrupoUsuario.Where(x =>
                    x.Sistema == 1 && (x.PostoId == null || x.PostoId == User.AdministradoraId));

                lGrupoUsuario = lGrupoUsuario.AplicarFiltrosDinamicos(request.Filters);
                lGrupoUsuario = string.IsNullOrWhiteSpace(request.Order?.Campo)
                    ? lGrupoUsuario.OrderByDescending(o => o.Id)
                    : lGrupoUsuario.OrderBy($"{request.Order.Campo} {request.Order.Operador.DescriptionAttr()}");

                var retorno = lGrupoUsuario.Skip((request.Page - 1) * request.Take)
                    .Take(request.Take)
                    .ProjectTo<ConsultarGrupoUsuarioGrid>().ToList();

                return new ConsultarGridGrupoUsuarioResponse()
                {
                    items = retorno,
                    totalItems = retorno.Count
                };
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar ConsultarGridGrupoUsuarioPosto");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("ConsultarGridGrupoUsuarioPosto");
            }
        }

        private IQueryable<Domain.Models.GrupoUsuario.GrupoUsuario> GetDataToGridAndReport(OrderFilters orderFilters,
            List<QueryFilters> filters)
        {
            IQueryable<Domain.Models.GrupoUsuario.GrupoUsuario> lGrupoUsuario;

            lGrupoUsuario = Repository.Query.GetAll();

            if (Engine.User.EmpresaId > 0)
            {
                lGrupoUsuario = Repository.Query.Where(g => g.EmpresaId == Engine.User.EmpresaId);
            }
            
            lGrupoUsuario = lGrupoUsuario.AplicarFiltrosDinamicos(filters);

            lGrupoUsuario = lGrupoUsuario.AplicarFiltrosDinamicos<Domain.Models.GrupoUsuario.GrupoUsuario>(filters);
            lGrupoUsuario = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                ? lGrupoUsuario.OrderByDescending(o => o.Id)
                : lGrupoUsuario.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            return lGrupoUsuario;
        }
        
    }
}
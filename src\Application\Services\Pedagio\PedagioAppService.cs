using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using AutoMapper.QueryableExtensions;
using NLog;
using SistemaInfo.BBC.Application.Helpers;
using SistemaInfo.BBC.Application.Interface.Pedagio;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.CentralNotificacoes;
using SistemaInfo.BBC.Application.Objects.Web.CentralPendencias;
using SistemaInfo.BBC.Application.Objects.Web.PagamentoValePedagio;
using SistemaInfo.BBC.Domain.Contracts.NotificacaoPedagio;
using SistemaInfo.BBC.Domain.Contracts.Pedagio;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.BBC.Domain.Models.Empresa.Repository;
using SistemaInfo.BBC.Domain.Models.GrupoEmpresa.Repository;
using SistemaInfo.BBC.Domain.Models.Parametros.Repository;
using SistemaInfo.BBC.Domain.Models.TransacaoPedagio.Commands;
using SistemaInfo.BBC.Domain.Models.Usuario.Repository;
using SistemaInfo.BBC.Infra.Bus.Interface.Pedagio;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using SistemaInfo.Framework.Utils;
using TransacaoConsultaGridMessage = SistemaInfo.BBC.Domain.Contracts.Transacao.TransacaoConsultaGridMessage;

namespace SistemaInfo.BBC.Application.Services.Pedagio;

public class PedagioAppService : AppService, IPedagioAppService
{
    private readonly IEmpresaReadRepository _empresaReadRepository;
    private readonly IParametrosReadRepository _parametrosReadRepository;
    private readonly IPedagioPublisher _pedagioPublisher;
    private readonly IUsuarioReadRepository _usuarioReadRepository;
    private readonly IGrupoEmpresaReadRepository _grupoEmpresaReadRepository;

    public PedagioAppService(IAppEngine engine, IEmpresaReadRepository empresaReadRepository,
        IPedagioPublisher pedagioPublisher, IParametrosReadRepository parametrosReadRepository,
        IUsuarioReadRepository usuarioReadRepository,
        IGrupoEmpresaReadRepository grupoEmpresaReadRepository) : base(engine)
    {
        _empresaReadRepository = empresaReadRepository;
        _pedagioPublisher = pedagioPublisher;
        _parametrosReadRepository = parametrosReadRepository;
        _usuarioReadRepository = usuarioReadRepository;
        _grupoEmpresaReadRepository = grupoEmpresaReadRepository;
    }


    public async Task<ConsultarGridPagamentoValePedagioResponse> ConsultarGridPagamentoValePedagio(
        ConsultarGridPagamentoValePedagioRequest request)
    {
        try
        {
            #region Consulta personalizada filtros da grid

            foreach (var item in request.Filters)
            {
                item.Valor = item.Campo switch
                {
                    "empresaCnpj" => item.Valor.Replace(".", "").Replace("/", "").Replace("-", ""),
                    _ => item.Valor
                };
            }

            #endregion

            //var grupoEmpresaIdUser = await _usuarioReadRepository.GetGrupoEmpresaIdById(User.EmpresaId);
            
            var message = Mapper.Map<ConsultarGridPagamentoValePedagioRequestMessage>(request);
            message.EmpresaId = _usuarioReadRepository.GetEmpresasAcessoUsuario(User.Id).Result; //new List<int>();
            //if (Engine.User.EmpresaId > 0)
            //    message.EmpresaId.Add(Engine.User.EmpresaId);
            //else if (grupoEmpresaIdUser > 0)
            //    message.EmpresaId = await _grupoEmpresaReadRepository.GetAllEmpresaIdbyId(grupoEmpresaIdUser);

            var resultMs = await _pedagioPublisher.PublicarEventoConsultaGridValePedagio(message);

            foreach (var pedagio in resultMs.Items) pedagio.NomeContratante =
                (await _empresaReadRepository.GetByCnpjAsync(pedagio.EmpresaCnpj))?.RazaoSocial;

            return Mapper.Map<ConsultarGridPagamentoValePedagioResponse>(resultMs);
        }
        catch (Exception e)
        {
            LogManager.GetCurrentClassLogger().Error(e.Message);
            throw;
        }
    }


    public async Task<RespPadrao> ConsultarPorId(int idPagamentoValePedagio)
    {
        var lLog = LogManager.GetCurrentClassLogger();
        try
        {
            if (idPagamentoValePedagio <= 0)
                throw new Exception("Id inválido.");

            var pagamentoValePedagio = await _pedagioPublisher.PublicarEventoConsultaPorId(
                new ConsultarPorIdRequestMessage()
                    { Id = idPagamentoValePedagio });

            if (pagamentoValePedagio == null)
                throw new Exception("Pagamento vale pedágio não encontrada.");

            return new RespPadrao(true, "Sucesso!",
                Mapper.Map<ConsultarPagamentoValePedagioResponse>(pagamentoValePedagio));
        }
        catch (Exception e)
        {
            lLog.Error(e);
            return new RespPadrao(false, e.Message);
        }
    }

    public async Task<RespPadrao> ConsultarNotificacaoPorId(int idCentralNotificacao)
    {
        var lLog = LogManager.GetCurrentClassLogger();
        try
        {
            if (idCentralNotificacao <= 0)
                throw new Exception("Id inválido.");

            var pagamentoValePedagio = await _pedagioPublisher.PublicarEventoConsultaNotificacaoPorId(
                new ConsultarNotificacaoPorIdRequestMessage()
                    { Id = idCentralNotificacao });

            if (pagamentoValePedagio == null)
                throw new Exception("Pagamento vale pedágio não encontrada.");

            return new RespPadrao(true, "Sucesso!",
                Mapper.Map<CentralNotificacoesValePedagioResponse>(pagamentoValePedagio));
        }
        catch (Exception e)
        {
            lLog.Error(e);
            return new RespPadrao(false, e.Message);
        }
    }

    public async Task<ConsultarGridCentralNotificacoesValePedagioResponse> ConsultarGridCentralNotificacoesValePedagio(
        ConsultarGridCentralNotificacoesValePedagioRequest request)
    {
        try
        {
            var respostaMs =
                await _pedagioPublisher.PublicarEventoConsultaGridNotificacoes(
                    Mapper.Map<ConsultarGridCentralNotificacoesRequestMessage>(request));
            return Mapper.Map<ConsultarGridCentralNotificacoesValePedagioResponse>(respostaMs);
        }
        catch (Exception e)
        {
            LogManager.GetCurrentClassLogger().Error(e);
            throw;
        }
    }

    public async Task<ConsultarGridCentralPendenciasValePedagioResponse> ConsultarGridCentralPendenciasValePedagio(
        ConsultarGridCentralPendenciasValePedagioRequest request)
    {
        try
        {
            var respostaMs =
                await _pedagioPublisher.PublicarEventoConsultaGridCentralPendencias(
                    Mapper.Map<ConsultarGridCentralPendenciasValePedagioRequestMessage>(request));
            return Mapper.Map<ConsultarGridCentralPendenciasValePedagioResponse>(respostaMs);
        }
        catch (Exception e)
        {
            LogManager.GetCurrentClassLogger().Error(e);
            throw;
        }
    }

    public async Task<ConsultarGridTransacaoPagamentoValePedagioResponse> ConsultarGridTransacaoPagamentoValePedagio(
        ConsultarGridTransacaoPagamentoValePedagioRequest request)
    {
        try
        {
            var lCommand = new TransacaoConsultarMSCommand { IdPedagio = request.PagamentoValePedagioId ?? 0 };
            var lMessage = await Engine.CommandBus.SendCommandAsync<TransacaoConsultaGridMessage>(lCommand);
            var transacaoPagamentoPedagio = lMessage.TransacaoList.AsQueryable();

            transacaoPagamentoPedagio = transacaoPagamentoPedagio.AplicarFiltrosDinamicos(request.Filters);

            var count = transacaoPagamentoPedagio.Count();

            transacaoPagamentoPedagio = string.IsNullOrWhiteSpace(request.Order?.Campo)
                ? transacaoPagamentoPedagio.OrderByDescending(o => o.Id)
                : transacaoPagamentoPedagio.OrderBy(
                    $"{request.Order?.Campo} {request.Order?.Operador.DescriptionAttr()}");

            var lRetorno = transacaoPagamentoPedagio.Skip((request.Page - 1) * request.Take).Take(request.Take)
                .ProjectTo<ConsultarGridTransacaoPagamentoValePedagioItem>().ToList();
            return new ConsultarGridTransacaoPagamentoValePedagioResponse
            {
                Items = lRetorno,
                TotalItems = count
            };
        }
        catch (Exception e)
        {
            LogManager.GetCurrentClassLogger().Error(e.Message);
            throw;
        }
    }

    public async Task<ConsultarGridPagamentoValePedagioHistoricoResponse> ConsultarGridPagamentoValePedagioHistorico(
        ConsultarGridTransacaoPagamentoValePedagioRequest request)
    {
        try
        {
            var lCommand = new PagamentoPedagioHistoricoGridMessageRequest
                { IdPedagio = request.PagamentoValePedagioId ?? 0 };
            var lMessage = await Engine.CommandBus.SendCommandAsync<PagamentoPedagioHistoricoGridMessage>(lCommand);
            var pagamentoPedagioHistorico = lMessage.PagamentoPedagioHistoricoList.AsQueryable();

            pagamentoPedagioHistorico = pagamentoPedagioHistorico.AplicarFiltrosDinamicos(request.Filters);

            var count = pagamentoPedagioHistorico.Count();

            pagamentoPedagioHistorico = string.IsNullOrWhiteSpace(request.Order?.Campo)
                ? pagamentoPedagioHistorico.OrderByDescending(o => o.Id)
                : pagamentoPedagioHistorico.OrderBy(
                    $"{request.Order?.Campo} {request.Order?.Operador.DescriptionAttr()}");

            var lRetorno = pagamentoPedagioHistorico.Skip((request.Page - 1) * request.Take).Take(request.Take)
                .ProjectTo<ConsultarGridPagamentoValePedagioHistoricoItem>().ToList();
            return new ConsultarGridPagamentoValePedagioHistoricoResponse
            {
                Items = lRetorno,
                TotalItems = count
            };
        }
        catch (Exception e)
        {
            LogManager.GetCurrentClassLogger().Error(e.Message);
            throw;
        }
    }

    public async Task<IntegrarPedagioMessageResponse> ReenviarPagamentoPedagio(int idPagamentoValePedagio)
    {
        try
        {
            return
                await _pedagioPublisher.PublicarEventoReenvioPedagio(new ReenviarPedagioMessage
                    { IdPedagio = idPagamentoValePedagio });
        }
        catch (Exception e)
        {
            LogManager.GetCurrentClassLogger().Error(e);
            return new IntegrarPedagioMessageResponse(false, e.Message);
        }
    }

    public async Task ServiceReenviarPagamentosPendentes()
    {
        try
        {
            var lIdsGp = await _empresaReadRepository.GetIdHabilitadasParaReprocessarPedagio();
            var quantidadeTentativas =
                ((await _parametrosReadRepository.GetByTipoDoParametroAsync(Domain.Models.Parametros.Parametros
                    .TipoDoParametro.ConfiguracaoTentativaReenvioValePedagio)).Valor ?? "5").ToInt();
            await _pedagioPublisher.PublicarEventoReprocessamentoDePedagio(new ReprocessarPedagioMessage()
                { IdsEmpresasPermitidas = lIdsGp, QuantidadeTentativas = quantidadeTentativas });
        }
        catch (Exception e)
        {
            LogManager.GetCurrentClassLogger().Error(e);
        }
    }

    public async Task ServiceReenviarCancelamentosPendentes()
    {
        try
        {
            new LogHelper().LogOperationStart("ServiceReenviarCancelamentosPendentes");
            await _pedagioPublisher.PublicarEventoReprocessamentoDeCancelamentoPedagio(
                new ReprocessarCancelamentoPedagioMessage());
        }
        catch (Exception ex)
        {
            new LogHelper().Error(ex, "Erro ao executar ServiceReenviarCancelamentosPendentes");
            throw;
        }
        finally
        {
            new LogHelper().LogOperationEnd("ServiceReenviarCancelamentosPendentes");
        }
    }
}
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using AutoMapper.QueryableExtensions;
using Microsoft.EntityFrameworkCore;
using NLog;
using SistemaInfo.BBC.Application.Helpers;
using SistemaInfo.BBC.Application.Interface.AtualizacaoPrecoCombustivel;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.AtualizacaoPrecoCombustivel;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.BBC.Domain.Models.AtualizacaoPrecoCombustivel.Commands;
using SistemaInfo.BBC.Domain.Models.AtualizacaoPrecoCombustivel.Repository;
using SistemaInfo.BBC.Domain.Models.Parametros.Repository;
using SistemaInfo.BBC.Domain.Models.PostoCombustivel.Commands;
using SistemaInfo.BBC.Domain.Models.PostoCombustivel.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.Services.AtualizacaoPrecoCombustivel
{
    public class AtualizacaoPrecoCombustivelAppService : AppService<Domain.Models.AtualizacaoPrecoCombustivel.AtualizacaoPrecoCombustivel,
        IAtualizacaoPrecoCombustivelReadRepository, IAtualizacaoPrecoCombustivelWriteRepository>, IAtualizacaoPrecoCombustivelAppService
    {

        private readonly IPostoCombustivelReadRepository _postoCombustivelReadRepository;
        private readonly IParametrosReadRepository _parametrosReadRepository;
        
        public AtualizacaoPrecoCombustivelAppService(
            IAppEngine engine,
            IPostoCombustivelReadRepository postoCombustivelReadRepository,
            IAtualizacaoPrecoCombustivelReadRepository readRepository,
            IAtualizacaoPrecoCombustivelWriteRepository writeRepository, 
            IParametrosReadRepository parametrosReadRepository) : base(
            engine, readRepository, writeRepository)
        {
            _postoCombustivelReadRepository = postoCombustivelReadRepository;
            _parametrosReadRepository = parametrosReadRepository;
        }

        public async Task<ConsultarGridHistoricoAtualizacaoPrecoCombustivelResponse> ConsultarGridHistoricoSolicitacoesPendentes(int postoId, 
            DateTime dtInicial, DateTime dtFinal, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters)
        {
            try
            {
                dtInicial = dtInicial.ToLocalTime().Date;
                
                var lHistoricoSolicitacoesPendentes = Repository.Query.GetAll();

                if (postoId > 0)
                    lHistoricoSolicitacoesPendentes = lHistoricoSolicitacoesPendentes.Where(x => x.PostoId == postoId);

                if (User.AdministradoraId > 0)
                {
                    if (postoId != User.AdministradoraId)
                    {
                        return new ConsultarGridHistoricoAtualizacaoPrecoCombustivelResponse
                        {
                            items = [],
                            totalItems = 0
                        };
                    }

                    lHistoricoSolicitacoesPendentes =
                        lHistoricoSolicitacoesPendentes.Where(x => x.PostoId == User.AdministradoraId);
                }

                if (dtInicial > DateTime.MinValue)
                    lHistoricoSolicitacoesPendentes =
                        lHistoricoSolicitacoesPendentes.Where(p => p.DataCadastro >= dtInicial.Date);

                if (dtFinal > DateTime.MinValue)
                {
                    var dtFim = dtFinal.Date.AddDays(1).AddTicks(-1);
                    lHistoricoSolicitacoesPendentes =
                        lHistoricoSolicitacoesPendentes.Where(p => p.DataCadastro <= dtFim);
                }

                lHistoricoSolicitacoesPendentes = lHistoricoSolicitacoesPendentes.AplicarFiltrosDinamicos(filters);

                lHistoricoSolicitacoesPendentes = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                    ? lHistoricoSolicitacoesPendentes.OrderByDescending(o => o.Id)
                    : lHistoricoSolicitacoesPendentes.OrderBy(
                        $"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

                var lCount = await lHistoricoSolicitacoesPendentes.CountAsync();

                var retorno = await lHistoricoSolicitacoesPendentes
                    .Skip((page - 1) * take)
                    .Take(take)
                    .ProjectTo<ConsultarGridHistoricoAtualizacaoPrecoCombustivel>(Engine.Mapper.ConfigurationProvider)
                    .ToListAsync();

                return new ConsultarGridHistoricoAtualizacaoPrecoCombustivelResponse
                {
                    items = retorno,
                    totalItems = lCount
                };
            }
            catch (Exception ex)
            {
                LogManager.GetCurrentClassLogger().Error("HistoricoSolicitacoesPendentes: " + ex.Message);
                throw;
            }
        }

        public List<AtualizacaoPrecoCombustivelResponse> SolicitacoesPendentes(int idPosto, DateTime startDate,
            DateTime endDate)
        {
            try
            {
                new LogHelper().LogOperationStart("SolicitacoesPendentes");
                var lQuery = Repository.Query
                    .Include(c => c.Combustivel)
                    .Include(p => p.Posto)
                    .Where(a => a.DataAlteracao >= startDate && a.DataAlteracao <= endDate && a.StatusAprovacao == 2);

                if (idPosto != 0) lQuery = lQuery.Where(a => a.PostoId == idPosto);

                return lQuery.ProjectTo<AtualizacaoPrecoCombustivelResponse>().ToList();
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar SolicitacoesPendentes");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("SolicitacoesPendentes");
            }
        }
        
        public async Task<RespPadrao> SaveSolicitacao(AtualizacaoPrecoCombustivelRequest request)
        {
            var lLog = LogManager.GetCurrentClassLogger();
            try
            {
                var lPostoId = User.Sistema == "Posto" ? User.AdministradoraId : User.Id;

                var lCombustiveisPosto = _postoCombustivelReadRepository
                    .Include(c => c.Combustivel)
                    .Where(x => x.PostoId == lPostoId);

                var lSolicitacoesPendentes = await Repository.Query
                    .Where(x => x.PostoId == lPostoId && x.StatusAprovacao == 2)
                    .Include(c => c.Combustivel)
                    .ToListAsync();

                var lAprovacaoAutomaticaLigada = await _parametrosReadRepository.FirstOrDefaultAsync(c =>
                    c.TipoParametros == Domain.Models.Parametros.Parametros.TipoDoParametro.AprovacaoAutomaticaPrecoCombustivel) != null;
                var lLimiteAcimaAprovacao = (await _parametrosReadRepository.GetParametrosAsync(-3,
                    Domain.Models.Parametros.Parametros.TipoDoParametro.AprovacaoAutomaticaPrecoCombustivelLimiteAcima,
                    Domain.Models.Parametros.Parametros.TipoDoValor.Number))?.Valor.ToDecimalSafe() ?? 0;
                var lLimiteAbaixoAprovacao = (await _parametrosReadRepository.GetParametrosAsync(-3,
                    Domain.Models.Parametros.Parametros.TipoDoParametro.AprovacaoAutomaticaPrecoCombustivelLimiteAbaixo,
                    Domain.Models.Parametros.Parametros.TipoDoValor.Number))?.Valor.ToDecimalSafe() ?? 0;
                var lLimiteAcimaAprovacaoHabilitada = (await _parametrosReadRepository.GetParametrosAsync(-3,
                    Domain.Models.Parametros.Parametros.TipoDoParametro.AprovacaoAutomaticaPrecoCombustivelLimiteAcimaHabilitado,
                    Domain.Models.Parametros.Parametros.TipoDoValor.String))?.Valor?.EqualsIgnoreCase("true") ?? false;
                var lLimiteAbaixoAprovacaoHabilitada = (await _parametrosReadRepository.GetParametrosAsync(-3,
                    Domain.Models.Parametros.Parametros.TipoDoParametro.AprovacaoAutomaticaPrecoCombustivelLimiteAbaixoHabilitado,
                    Domain.Models.Parametros.Parametros.TipoDoValor.String))?.Valor?.EqualsIgnoreCase("true") ?? false;

                var lMensagemValidacoes = string.Empty;
                var lSucessoAprovacaoAutomatica = false;
                
                foreach (var lSolicitacao in request.Solicitacoes)
                {
                    if (lSolicitacao.PostoId != lPostoId)
                    {
                        return new RespPadrao
                        {
                            sucesso = false,
                            mensagem = "Posto informado na requisição inválido."
                        };
                    }

                    var lCombustivel = await lCombustiveisPosto.FirstOrDefaultAsync(c => c.CombustivelId == lSolicitacao.CombustivelId);
                    
                    if (lSolicitacoesPendentes.Any())
                    {
                        var lSolicitacaoExistente = lSolicitacoesPendentes
                            .FirstOrDefault(c => c.CombustivelId == lSolicitacao.CombustivelId);

                        if (lSolicitacaoExistente != null)
                        {
                            lSolicitacaoExistente.MotivoSolicitacao = lSolicitacao.MotivoSolicitacao;
                            lSolicitacaoExistente.ValorBombaSolicitado = lSolicitacao.ValorBombaSolicitado;
                            lSolicitacaoExistente.ValorBBCSolicitado = lSolicitacao.ValorBBCSolicitado;
                            lSolicitacaoExistente.ValorBomba = lCombustivel?.ValorCombustivelBomba ?? lSolicitacaoExistente.ValorBomba;
                            lSolicitacaoExistente.ValorBBC = lCombustivel?.ValorCombustivelBBC ?? lSolicitacaoExistente.ValorBBC;
                            await Repository.Command.SaveChangesAsync();
                            var lValidacaoExistente = await ValidarAprovacaoAutomatica(lAprovacaoAutomaticaLigada, lSolicitacaoExistente, lCombustivel, lLimiteAcimaAprovacao,
                                lLimiteAbaixoAprovacao, lLimiteAcimaAprovacaoHabilitada, lLimiteAbaixoAprovacaoHabilitada);
                            if (!lValidacaoExistente.sucesso && lValidacaoExistente.mensagem.IsNullOrWhiteSpace()) continue;
                            if (lValidacaoExistente.sucesso)
                            {
                                lSucessoAprovacaoAutomatica = true;
                                continue;
                            }
                            if (lMensagemValidacoes.IsNullOrWhiteSpace()) 
                                lMensagemValidacoes += $"Não foi possível aprovar automaticamente as solicitações para os seguintes combustíveis pois um dos valores estava fora do limite: {lValidacaoExistente.mensagem}";
                            else 
                                lMensagemValidacoes += $", {lValidacaoExistente.mensagem}";
                            
                            continue;
                        }
                    }

                    var lCommand = Mapper.Map<AtualizacaoPrecoCombustivelSalvarComRetornoCommand>(lSolicitacao);
                    lCommand.ValorBomba = lCombustivel?.ValorCombustivelBomba ?? 0;
                    lCommand.ValorBBC = lCombustivel?.ValorCombustivelBBC ?? 0;
                    var lAtualizacao = await Engine.CommandBus.SendCommandAsync<Domain.Models.AtualizacaoPrecoCombustivel.AtualizacaoPrecoCombustivel>(lCommand);
                    var lValidacao = await ValidarAprovacaoAutomatica(lAprovacaoAutomaticaLigada, lAtualizacao, lCombustivel, lLimiteAcimaAprovacao,
                        lLimiteAbaixoAprovacao, lLimiteAcimaAprovacaoHabilitada, lLimiteAbaixoAprovacaoHabilitada);
                    if (!lValidacao.sucesso && lValidacao.mensagem.IsNullOrWhiteSpace()) continue;
                    if (lValidacao.sucesso)
                    {
                        lSucessoAprovacaoAutomatica = true;
                        continue;
                    }
                    if (lMensagemValidacoes.IsNullOrWhiteSpace()) 
                        lMensagemValidacoes += $"Não foi possível aprovar automaticamente as solicitações para os seguintes combustíveis pois um dos valores estava fora do limite: {lValidacao.mensagem}";
                    else 
                        lMensagemValidacoes += $", {lValidacao.mensagem}";
                }

                var lMensagemFinal = lSucessoAprovacaoAutomatica ? "Solicitações salvas e aprovadas automaticamente. " : "Solicitações foram salvas com sucesso. ";

                return new RespPadrao
                {
                    sucesso = true,
                    mensagem = lMensagemFinal + lMensagemValidacoes
                };
            }
            catch (Exception e)
            {
                lLog.Error(e);
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = $"Erro interno ao salvar solicitação. {e.Message}"
                };
            }
        }

        private async Task<RespPadrao> ValidarAprovacaoAutomatica(bool aprovacaoAutomaticaLigada, Domain.Models.AtualizacaoPrecoCombustivel.AtualizacaoPrecoCombustivel solicitacao, 
            Domain.Models.PostoCombustivel.PostoCombustivel combustivel, decimal limiteAcima, decimal limiteAbaixo, bool limiteAcimaHabilitado, bool limiteAbaixoHabilitado)
        {
            if (!aprovacaoAutomaticaLigada || combustivel == null)
            {
                return new RespPadrao
                {
                    sucesso = false,
                    mensagem = null
                };
            }
            
            var solicBbc = solicitacao.ValorBBCSolicitado.ToDecimalSafe();
            var solicBomba = solicitacao.ValorBombaSolicitado.ToDecimalSafe();
            
            if (!limiteAbaixoHabilitado && !limiteAcimaHabilitado)
            {
                if (solicBbc > 0) combustivel.ValorCombustivelBBC = solicitacao.ValorBBCSolicitado;
                if (solicBomba > 0) combustivel.ValorCombustivelBomba = solicitacao.ValorBombaSolicitado;
                solicitacao.StatusAprovacao = 1;
                await Repository.Command.SaveChangesAsync();
                return new RespPadrao
                {
                    sucesso = true,
                    mensagem = "Aprovação automática efetuada com sucesso."
                };
            }
            
            var bbcAprovado = false;
            var bombaAprovado = false;
            var valBbc = combustivel.ValorCombustivelBBC.ToDecimalSafe();
            var valBomba = combustivel.ValorCombustivelBomba.ToDecimalSafe();
            var diferencaBbc = Math.Abs(valBbc - solicBbc);
            var diferencaBomba = Math.Abs(valBomba - solicBomba);

            bool diminuindoBbc = solicBbc <= valBbc;
            bool diminuindoBomba = solicBomba <= valBomba;

            if (diminuindoBbc)
            {
                if (diferencaBbc <= limiteAbaixo)
                {
                    combustivel.ValorCombustivelBBC = solicitacao.ValorBBCSolicitado;
                    bbcAprovado = true;
                }
            }

            if (!diminuindoBbc)
            {
                if (diferencaBbc <= limiteAcima)
                {
                    combustivel.ValorCombustivelBBC = solicitacao.ValorBBCSolicitado;
                    bbcAprovado = true;
                }
            }

            if (diminuindoBomba)
            {
                if (diferencaBomba <= limiteAbaixo)
                {
                    combustivel.ValorCombustivelBomba = solicitacao.ValorBombaSolicitado;
                    bombaAprovado = true;
                }
            }

            if (!diminuindoBomba)
            {
                if (diferencaBomba <= limiteAcima)
                {
                    combustivel.ValorCombustivelBomba = solicitacao.ValorBombaSolicitado;
                    bombaAprovado = true;
                }  
            }

            if (bbcAprovado && bombaAprovado)
            {
                solicitacao.StatusAprovacao = 1;
                await Repository.Command.SaveChangesAsync();
                return new RespPadrao
                {
                    sucesso = true,
                    mensagem = "Aprovação automática efetuada com sucesso."
                };
            }

            return new RespPadrao
            {
                sucesso = false,
                mensagem = combustivel.Combustivel.Nome
            };
        }

        public async Task<RespPadrao> CancelarSolicitacao(AtualizacaoPrecoCombustivelCancelarRequest request)
        {
            try
            {
                var lPostoId = User.Sistema == "Posto" ? User.AdministradoraId : User.Id;

                var lSolicitacao = await Repository.Query
                    .Where(x => x.PostoId == lPostoId && x.StatusAprovacao == 2 && x.Id == request.SolicitacaoId)
                    .FirstOrDefaultAsync();

                if (lSolicitacao == null)
                {
                    return new RespPadrao
                    {
                        sucesso = false,
                        mensagem = "Solicitação não encontrada."
                    };
                }

                lSolicitacao.StatusAprovacao = 3;
                await Repository.Command.SaveChangesAsync();
                
                return new RespPadrao
                {
                    sucesso = true,
                    mensagem = "Solicitação cancelada com sucesso."
                };
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = $"Erro interno ao cancelar solicitação. {e.Message}"
                };
            }
        }
        
        public async Task<RespPadrao> SaveAprovacao(AtualizacaoPrecoCombustivelRequest request)
        {
            try
            {
                var lMensagem = string.Empty;
                foreach (var lSolicitacao in request.Solicitacoes)
                {
                    var lSolicitacaoBanco = await Repository.Query.GetByIdAsNoTrackingAsync(lSolicitacao.Id);

                    if (lSolicitacaoBanco?.StatusAprovacao != 2)
                    {
                        lMensagem = "Algumas solicitações não puderam ser alteradas pois já não estavam mais pendentes. ";
                        continue;
                    }
                    
                    if (lSolicitacao.StatusAprovacao == 1)
                    {
                        var lCommandPostoCombustivel = new PostoCombustivelSaveComRetornoCommand();
                        lCommandPostoCombustivel.CombustivelId = lSolicitacao.CombustivelId;
                        lCommandPostoCombustivel.PostoId = lSolicitacao.PostoId;
                        lCommandPostoCombustivel.ValorCombustivelBomba = lSolicitacao.ValorBombaSolicitado;
                        lCommandPostoCombustivel.ValorCombustivelBBC = lSolicitacao.ValorBBCSolicitado;
                        await Engine.CommandBus.SendCommandAsync<Domain.Models.PostoCombustivel.PostoCombustivel>(lCommandPostoCombustivel);
                    }
                    
                    lSolicitacaoBanco.StatusAprovacao = lSolicitacao.StatusAprovacao;
                    var lSolicitacaoCommand = Mapper.Map<AtualizacaoPrecoCombustivelSalvarComRetornoCommand>(lSolicitacaoBanco);

                    await Engine.CommandBus.SendCommandAsync<Domain.Models.AtualizacaoPrecoCombustivel.AtualizacaoPrecoCombustivel>(lSolicitacaoCommand);
                }

                return new RespPadrao
                {
                    sucesso = true,
                    mensagem = "Solicitações salvas com sucesso. " + lMensagem 
                };
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = $"Erro ao atualizar solicitações. {e.Message}"
                };
            }
        }
    }
}


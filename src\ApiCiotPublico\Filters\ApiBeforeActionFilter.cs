﻿using System;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Security.Authentication;
using System.Text;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.IdentityModel.Tokens;
using NLog;
using SistemaInfo.Framework.DomainDrivenDesign.Infra.CustomFilters;
using SistemaInfo.BBC.ApiCiotPublico.Security;
using SistemaInfo.BBC.Domain.Exceptions;

namespace SistemaInfo.BBC.ApiCiotPublico.Filters
{
    /// <summary>
    /// 
    /// </summary>
    public class ApiBeforeActionFilter : IActionFilter
    {
        private static readonly Type[] AllowedNullParametersTypes = {typeof(FilterOptions)};
        private static Guid logId = Guid.Empty;
        private Microsoft.Extensions.Configuration.IConfiguration _configuration;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="configuration"></param>
        public ApiBeforeActionFilter(Microsoft.Extensions.Configuration.IConfiguration configuration)
        {
            _configuration = configuration;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="context"></param>
        /// <exception cref="ArgumentException"></exception>
        public void OnActionExecuting(ActionExecutingContext context)
        {
            var nomesParametrosEsperados = context.ActionDescriptor.Parameters
                .Where(p => Nullable.GetUnderlyingType(p.ParameterType) == null &&
                            !AllowedNullParametersTypes.Contains(p.ParameterType))
                .Select(c => c.Name)
                .ToList();

            var basePath = "";
            foreach (var valuesRoute in context.ActionDescriptor.RouteValues.Reverse())
            {
                basePath += (basePath == "" ? valuesRoute.Value : "/" + valuesRoute.Value);
            }

            var nomesParametrosRecebidos = context.ActionArguments.Select(c => c.Key).ToList();

            if (!nomesParametrosEsperados.Any())
            {
                return;
            }

            var sequenceEqual = nomesParametrosEsperados.OrderBy(t => t).All(c => nomesParametrosRecebidos.Contains(c));

            if (sequenceEqual)
            {
                return;
            }

            var inexistenteName = nomesParametrosEsperados.First(c => !nomesParametrosRecebidos.Contains(c));
            var parametro = context.ActionDescriptor.Parameters.First(c => c.Name == inexistenteName);

            throw new ArgumentException(
                $"Request {parametro.ParameterType.Name} inválida. Certifique-se se o json de envio é válido de acordo com o esperado pelo método");
        }
        

        /// <summary>
        /// 
        /// </summary>
        /// <param name="context"></param>
        public void OnActionExecuted(ActionExecutedContext context)
        {
            try
            {

            }
            catch (Exception e)
            {
                var lLog = LogManager.GetCurrentClassLogger();
                lLog.Error(e, "ApiBeforeAction error: ");
            }
        }
    }
}
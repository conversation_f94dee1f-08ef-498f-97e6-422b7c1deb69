using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Text;
using System.Threading.Tasks;
using AutoMapper.QueryableExtensions;
using Microsoft.EntityFrameworkCore;
using NLog;
using SistemaInfo.BBC.Application.Helpers;
using SistemaInfo.BBC.Application.Interface.Parametros;
using SistemaInfo.BBC.Application.Objects.Api.Parametros;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.ConfiguracaoAtualizacaoAutomaticaPrecoCombustivel;
using SistemaInfo.BBC.Application.Objects.Web.ConfiguracaoMonitoramentoCiot;
using SistemaInfo.BBC.Application.Objects.Web.ConfiguracaoQualificacaoTransacao;
using SistemaInfo.BBC.Application.Objects.Web.ConfiguracaoTelaoSaldo;
using SistemaInfo.BBC.Application.Objects.Web.ConfiguracaoTokenMobile;
using SistemaInfo.BBC.Application.Objects.Web.ConfiguracaoValePedagio;
using SistemaInfo.BBC.Application.Objects.Web.Parametros;
using SistemaInfo.BBC.Domain.Contracts.Parametro;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.BBC.Domain.Models.ParametroConfiguracaoSla;
using SistemaInfo.BBC.Domain.Models.ParametroConfiguracaoSla.Commands;
using SistemaInfo.BBC.Domain.Models.ParametroConfiguracaoSla.Repository;
using SistemaInfo.BBC.Domain.Models.Parametros.Commands;
using SistemaInfo.BBC.Domain.Models.Parametros.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.Services.Parametros
{
    public class ParametrosAppService :
        AppService<Domain.Models.Parametros.Parametros, IParametrosReadRepository, IParametrosWriteRepository>,
        IParametrosAppService
    {
        private readonly IParametroConfiguracaoSlaReadRepository _parametroConfiguracaoSlaReadRepository;

        public ParametrosAppService(IAppEngine engine,
            IParametrosReadRepository readRepository,
            IParametrosWriteRepository writeRepository,
            IParametroConfiguracaoSlaReadRepository parametroConfiguracaoSlaReadRepository) : base(engine,
            readRepository, writeRepository)
        {
            _parametroConfiguracaoSlaReadRepository = parametroConfiguracaoSlaReadRepository;
        }

        public ConsultarGridParametroResponse ConsultarGridParametro(int take, int page, OrderFilters orderFilters,
            List<QueryFilters> filters)
        {
            var lParametro = Repository.Query.GetAll();

            lParametro = lParametro.AplicarFiltrosDinamicos(filters);

            lParametro = lParametro.Where(p => p.ReferenciaId == -1 || p.ReferenciaId == -2);

            var lCount = lParametro.Count();

            lParametro = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                ? lParametro.OrderByDescending(o => o.Id)
                : lParametro.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            var retorno = lParametro.Skip((page - 1) * take)
                .Take(take)
                .ProjectTo<ConsultarGridParametro>(Engine.Mapper.ConfigurationProvider).ToList();

            return new ConsultarGridParametroResponse()
            {
                items = retorno,
                totalItems = lCount
            };
        }

        public Task<Domain.Models.Parametros.Parametros> GetParametrosAsync(int id,
            Domain.Models.Parametros.Parametros.TipoDoParametro tipoDoParametro,
            Domain.Models.Parametros.Parametros.TipoDoValor tipoDoValor)
        {
            return Repository.Query.GetParametrosAsync(id, tipoDoParametro, tipoDoValor);
        }

        public async Task<int> GetTempoInatividadeUsuario()
        {
            try
            {
                new LogHelper().LogOperationStart("GetTempoInatividadeUsuario");
                return await Repository.Query.GetTempoInatividadeUsuarioAsync();
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar GetTempoInatividadeUsuario");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("GetTempoInatividadeUsuario");
            }
        }

        public async Task<RespPadrao> SincronizaParametrosComMicroServico()
        {
            try
            {
                var lParametros = Repository.Query.GetAll();

                var lMessage = new ParametroSincronizarMessage
                {
                    ParametrosList = await lParametros
                        .Where(p => p.ReferenciaId == -1 || p.ReferenciaId == -2 || p.ReferenciaId == -3).ToListAsync()
                };

                await Engine.CommandBus.SendCommandAsync(Mapper.Map<ParametroSincronizarMessage>(lMessage));
                return new RespPadrao(true, "sucesso");
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return new RespPadrao(false, e.Message);
            }
        }

        public Task<List<Domain.Models.Parametros.Parametros>> GetParametrosListAsync(int id,
            Domain.Models.Parametros.Parametros.TipoDoParametro tipoDoParametro,
            Domain.Models.Parametros.Parametros.TipoDoValor tipoDoValor)
        {
            return Repository.Query.GetParametrosListAsync(id, tipoDoParametro, tipoDoValor);
        }

        public Domain.Models.Parametros.Parametros SaveParametro(int id,
            Domain.Models.Parametros.Parametros.TipoDoParametro tipoDoParametro, string valor,
            string valorCriptografado, Domain.Models.Parametros.Parametros.TipoDoValor tipoValor)
        {
            return Repository.Command.SaveParametro(id, tipoDoParametro, valor, valorCriptografado, tipoValor);
        }

        public async Task<RespPadrao> Cadastrar(ParametrosCadastrarRequest parametrosCadastrarRequest)
        {
            try
            {
                new LogHelper().LogOperationStart("Cadastrar");
                var parametroExistente = await Repository.Query
                    .GetByTipoDoParametroAsync(parametrosCadastrarRequest.TipoParametros);

                if (parametroExistente != null && parametrosCadastrarRequest.Id == 0)
                {
                    return new RespPadrao(false, "Parâmetro já cadastrado!");
                }

                var command = Mapper.Map<ParametrosSalvarCommand>(parametrosCadastrarRequest);

                if (command.TipoValor == Domain.Models.Parametros.Parametros.TipoDoValor.Criptografia)
                {
                    if (parametroExistente != null && parametrosCadastrarRequest.Id > 0 && command.Valor == "******")
                    {
                        command.ValorCriptografado = parametroExistente.ValorCriptografado;
                    }
                    else
                    {
                        var valorBytes = System.Text.Encoding.UTF8.GetBytes(command.Valor);
                        command.Valor = "******";
                        command.ValorCriptografado = Convert.ToBase64String(valorBytes);
                    }
                }

                await Engine.CommandBus.SendCommandAsync(command);

                return new RespPadrao(true, "Parâmetro cadastrado com sucesso!");
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar Cadastrar");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("Cadastrar");
            }
        }

        public async Task<RespPadrao> SalvarConfiguracaoSla(
            ParametroConfiguracaoSlaRequest parametroConfiguracaoSlaRequest)
        {
            try
            {
                new LogHelper().LogOperationStart("SalvarConfiguracaoSla");
                var parametroConfiguracaoSla =
                    Mapper.Map<ParametroConfiguracaoSlaSalvarCommand>(parametroConfiguracaoSlaRequest);

                await Engine.CommandBus.SendCommandAsync(parametroConfiguracaoSla);

                return new RespPadrao(true, "Configuração de SLA cadastrada com sucesso!");
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar SalvarConfiguracaoSla");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("SalvarConfiguracaoSla");
            }
        }

        public async Task<TipoFarol> FarolSlaValidar(DateTime dataCadastroPosto)
        {
            try
            {
                new LogHelper().LogOperationStart("FarolSlaValidar");
                var lParametrosSla = await _parametroConfiguracaoSlaReadRepository.FirstOrDefaultAsync();

                if (dataCadastroPosto.AddHours(lParametrosSla.FarolVerdeHoras + lParametrosSla.FarolAmareloHoras) <
                    DateTime.Now)
                    return TipoFarol.FarolVermelho;

                if (dataCadastroPosto.AddHours(lParametrosSla.FarolVerdeHoras) < DateTime.Now)
                    return TipoFarol.FarolAmarelo;

                return TipoFarol.FarolVerde;
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar FarolSlaValidar");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("FarolSlaValidar");
            }
        }

        public async Task<ParametroConfiguracaoSla> ConsultarParametroConfiguracaoSla()
        {
            try
            {
                new LogHelper().LogOperationStart("ConsultarParametroConfiguracaoSla");
                return await _parametroConfiguracaoSlaReadRepository.FirstOrDefaultAsync();
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar ConsultarParametroConfiguracaoSla");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("ConsultarParametroConfiguracaoSla");
            }
        }

        public async Task<RespPadrao> ConfiguracaoAtualizacaoAutomaticaPrecoCombustivel()
        {
            try
            {
                var valorMaior = await Repository.Query.GetParametrosAsync(-3,
                    Domain.Models.Parametros.Parametros.TipoDoParametro.AprovacaoAutomaticaPrecoCombustivelLimiteAcima,
                    Domain.Models.Parametros.Parametros.TipoDoValor.Number);

                var valorMenor = await Repository.Query.GetParametrosAsync(-3,
                    Domain.Models.Parametros.Parametros.TipoDoParametro.AprovacaoAutomaticaPrecoCombustivelLimiteAbaixo,
                    Domain.Models.Parametros.Parametros.TipoDoValor.Number);

                var valorMaiorHabilitado = await Repository.Query.GetParametrosAsync(-3,
                    Domain.Models.Parametros.Parametros.TipoDoParametro
                        .AprovacaoAutomaticaPrecoCombustivelLimiteAcimaHabilitado,
                    Domain.Models.Parametros.Parametros.TipoDoValor.String);

                var valorMenorHabilitado = await Repository.Query.GetParametrosAsync(-3,
                    Domain.Models.Parametros.Parametros.TipoDoParametro
                        .AprovacaoAutomaticaPrecoCombustivelLimiteAbaixoHabilitado,
                    Domain.Models.Parametros.Parametros.TipoDoValor.String);

                var response = new ConfiguracaoAtualizacaoAutomaticaPrecoCombustivelResponse
                {
                    ValorMaior = decimal.Parse(valorMaior.Valor),
                    ValorMenor = decimal.Parse(valorMenor.Valor),
                    ValorMaiorHabilitado = bool.Parse(valorMaiorHabilitado.Valor),
                    ValorMenorHabilitado = bool.Parse(valorMenorHabilitado.Valor)
                };

                return new RespPadrao
                {
                    data = response,
                    sucesso = true,
                    mensagem = "Parâmetros consultados com sucesso."
                };
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return new RespPadrao(false, "Erro interno ao consultar os parâmetros. " + e.Message);
            }
        }

        public async Task<RespPadrao> SalvarConfiguracaoAtualizacaoAutomaticaPrecoCombustivel(
            ConfiguracaoAtualizacaoAutomaticaPrecoCombustivelRequest request)
        {
            try
            {
                var valorMaior = await Repository.Query.GetParametrosAsync(-3,
                    Domain.Models.Parametros.Parametros.TipoDoParametro.AprovacaoAutomaticaPrecoCombustivelLimiteAcima,
                    Domain.Models.Parametros.Parametros.TipoDoValor.Number);

                valorMaior.Valor = request.ValorMaior.ToStringSafe();

                var valorMenor = await Repository.Query.GetParametrosAsync(-3,
                    Domain.Models.Parametros.Parametros.TipoDoParametro.AprovacaoAutomaticaPrecoCombustivelLimiteAbaixo,
                    Domain.Models.Parametros.Parametros.TipoDoValor.Number);

                valorMenor.Valor = request.ValorMenor.ToStringSafe();

                var valorMaiorHabilitado = await Repository.Query.GetParametrosAsync(-3,
                    Domain.Models.Parametros.Parametros.TipoDoParametro
                        .AprovacaoAutomaticaPrecoCombustivelLimiteAcimaHabilitado,
                    Domain.Models.Parametros.Parametros.TipoDoValor.String);

                valorMaiorHabilitado.Valor = request.ValorMaiorHabilitado.ToString();

                var valorMenorHabilitado = await Repository.Query.GetParametrosAsync(-3,
                    Domain.Models.Parametros.Parametros.TipoDoParametro
                        .AprovacaoAutomaticaPrecoCombustivelLimiteAbaixoHabilitado,
                    Domain.Models.Parametros.Parametros.TipoDoValor.String);

                valorMenorHabilitado.Valor = request.ValorMenorHabilitado.ToString();

                await Repository.Command.SaveChangesAsync();

                return new RespPadrao(true, "Parâmetros salvos com sucesso.");
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return new RespPadrao(false, "Erro interno ao salvar os parâmetros. " + e.Message);
            }
        }

        public async Task<int> GetMaxTentativasReenvioPedagio()
        {
            try
            {
                return ((await Repository.Query.GetByTipoDoParametroAsync(Domain.Models.Parametros.Parametros
                    .TipoDoParametro.ConfiguracaoTentativaReenvioValePedagio))?.Valor ?? "5").ToIntSafe();
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return 5;
            }
        }

        public async Task<int> GetMaxTentativasReenvioFrete()
        {
            try
            {
                return ((await Repository.Query.GetByTipoDoParametroAsync(Domain.Models.Parametros.Parametros
                    .TipoDoParametro.ConfiguracaoTentativaReenvioPagamentoFrete))?.Valor ?? "5").ToIntSafe();
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return 5;
            }
        }

        public async Task<RespPadrao> ConsultaParametrosConfiguracaoMonitoramentoCiot()
        {
            try
            {
                new LogHelper().LogOperationStart("ConsultaParametrosConfiguracaoMonitoramentoCiot");

                var emailsNotificacaoInternaContingenciaForcada = await Repository.Query.GetParametrosAsync(-3,
                    Domain.Models.Parametros.Parametros.TipoDoParametro.EmailsNotificacaoInternaContingenciaForcada,
                    Domain.Models.Parametros.Parametros.TipoDoValor.String);

                var emailsNotificacaoInternaContingenciaAutomatica = await Repository.Query.GetParametrosAsync(-3,
                    Domain.Models.Parametros.Parametros.TipoDoParametro.EmailsNotificacaoInternaContingenciaAutomatica,
                    Domain.Models.Parametros.Parametros.TipoDoValor.String);

                var tempoChecagemAmbiente = await Repository.Query.GetParametrosAsync(-3,
                    Domain.Models.Parametros.Parametros.TipoDoParametro.TempoChecagemAmbiente,
                    Domain.Models.Parametros.Parametros.TipoDoValor.Number);

                var quantidadeErroCiot = await Repository.Query.GetParametrosAsync(-3,
                    Domain.Models.Parametros.Parametros.TipoDoParametro.QuantidadeErroCiot,
                    Domain.Models.Parametros.Parametros.TipoDoValor.Number);

                var tempoNotificacaoExterna = await Repository.Query.GetParametrosAsync(-3,
                    Domain.Models.Parametros.Parametros.TipoDoParametro.TempoNotificacaoExterna,
                    Domain.Models.Parametros.Parametros.TipoDoValor.Number);

                var obrigaValorFrete = await Repository.Query.GetParametrosAsync(-3,
                    Domain.Models.Parametros.Parametros.TipoDoParametro.ObrigaValorFrete,
                    Domain.Models.Parametros.Parametros.TipoDoValor.Number);

                var dataInicioObrigaValorFrete = await Repository.Query.GetParametrosAsync(-3,
                    Domain.Models.Parametros.Parametros.TipoDoParametro.DataInicioObrigacaoValorFrete,
                    Domain.Models.Parametros.Parametros.TipoDoValor.String);

                var periodoReenvioCiot = await Repository.Query.GetParametroVoidAsync(-3,
                    Domain.Models.Parametros.Parametros.TipoDoParametro.PeriodoReenvioCiot,
                    Domain.Models.Parametros.Parametros.TipoDoValor.Number);


                var response = new ConfiguracaoMonitoramentoCiotResponse
                {
                    EmailsNotificacaoInternaContingenciaForcada = emailsNotificacaoInternaContingenciaForcada.Valor,
                    EmailsNotificacaoInternaContingenciaAutomatica =
                        emailsNotificacaoInternaContingenciaAutomatica.Valor,
                    TempoChecagemAmbiente = tempoChecagemAmbiente.Valor.ToIntSafe(),
                    QuantidadeErroCiot = quantidadeErroCiot.Valor.ToIntSafe(),
                    TempoNotificacaoExterna = tempoNotificacaoExterna.Valor.ToIntSafe(),
                    ObrigaValorFrete = obrigaValorFrete.Valor.ToIntSafe(),
                    DataInicioObrigaValorFrete = dataInicioObrigaValorFrete.Valor.IsNullOrWhiteSpace()
                        ? null
                        : dataInicioObrigaValorFrete.Valor.ToDateTimeSafe(),
                    PeriodoReenvioCiot = !periodoReenvioCiot.Valor.IsEmpty()
                        ? periodoReenvioCiot.Valor.ToIntSafe()
                        : null
                };


                return new RespPadrao
                {
                    data = response,
                    sucesso = true,
                    mensagem = "Parâmetros consultados com sucesso."
                };
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar ConsultaParametrosConfiguracaoMonitoramentoCiot");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("ConsultaParametrosConfiguracaoMonitoramentoCiot");
            }
        }

        public async Task<RespPadrao> SalvarConfiguracaoMonitoramentoCiot(ConfiguracaoMonitoramentoCiotRequest request)
        {
            try
            {
                var emailsNotificacaoInternaContingenciaAutomatica = await Repository.Query.GetParametrosAsync(-3,
                    Domain.Models.Parametros.Parametros.TipoDoParametro.EmailsNotificacaoInternaContingenciaAutomatica,
                    Domain.Models.Parametros.Parametros.TipoDoValor.Number);

                emailsNotificacaoInternaContingenciaAutomatica.Valor =
                    request.EmailsNotificacaoInternaContingenciaAutomatica.ToStringSafe();

                var emailsNotificacaoInternaContingenciaForcada = await Repository.Query.GetParametrosAsync(-3,
                    Domain.Models.Parametros.Parametros.TipoDoParametro.EmailsNotificacaoInternaContingenciaForcada,
                    Domain.Models.Parametros.Parametros.TipoDoValor.Number);

                emailsNotificacaoInternaContingenciaForcada.Valor =
                    request.EmailsNotificacaoInternaContingenciaForcada.ToStringSafe();

                var tempoChecagemAmbiente = await Repository.Query.GetParametrosAsync(-3,
                    Domain.Models.Parametros.Parametros.TipoDoParametro.TempoChecagemAmbiente,
                    Domain.Models.Parametros.Parametros.TipoDoValor.String);

                tempoChecagemAmbiente.Valor = request.TempoChecagemAmbiente.ToString();

                var quantidadeErroCiot = await Repository.Query.GetParametrosAsync(-3,
                    Domain.Models.Parametros.Parametros.TipoDoParametro.QuantidadeErroCiot,
                    Domain.Models.Parametros.Parametros.TipoDoValor.String);

                quantidadeErroCiot.Valor = request.QuantidadeErroCiot.ToString();

                var tempoNotificacaoExterna = await Repository.Query.GetParametrosAsync(-3,
                    Domain.Models.Parametros.Parametros.TipoDoParametro.TempoNotificacaoExterna,
                    Domain.Models.Parametros.Parametros.TipoDoValor.String);

                tempoNotificacaoExterna.Valor = request.TempoNotificacaoExterna.ToString();

                var obrigaValorFrete = await Repository.Query.GetParametrosAsync(-3,
                    Domain.Models.Parametros.Parametros.TipoDoParametro.ObrigaValorFrete,
                    Domain.Models.Parametros.Parametros.TipoDoValor.Number);

                obrigaValorFrete.Valor = request.ObrigaValorFrete.ToString();

                var dataInicioValorFrete = await Repository.Query.GetParametrosAsync(-3,
                    Domain.Models.Parametros.Parametros.TipoDoParametro.DataInicioObrigacaoValorFrete,
                    Domain.Models.Parametros.Parametros.TipoDoValor.String);

                dataInicioValorFrete.Valor = request.DataInicioObrigaValorFrete == null
                    ? ""
                    : request.DataInicioObrigaValorFrete.ToDateTime().ToString("d");


                var periodoReenvioCiot = await Repository.Query.GetParametroVoidAsync(-3,
                    Domain.Models.Parametros.Parametros.TipoDoParametro.PeriodoReenvioCiot,
                    Domain.Models.Parametros.Parametros.TipoDoValor.Number);

                periodoReenvioCiot.Valor = request.PeriodoReenvioCiot.ToString();

                var lRetornoMs = await SincronizarComCiotMs(request);
                if (!lRetornoMs.sucesso)
                    return new RespPadrao(false,
                        "Ocorreu um erro ao sincronizar o parametro com o microserviço. " + lRetornoMs.mensagem);
                await Repository.Command.SaveChangesAsync();

                return new RespPadrao(true, "Parâmetros salvos com sucesso.");
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return new RespPadrao(false, "Erro interno ao salvar os parâmetros. " + e.Message);
            }
        }

        public async Task<RespPadrao> ConsultaParametrosConfiguracaoValePedagio()
        {
            try
            {
                new LogHelper().LogOperationStart("ConsultaParametrosConfiguracaoValePedagio");

                var contaValePedagio = await Repository.Query.GetParametrosAsync(-3,
                    Domain.Models.Parametros.Parametros.TipoDoParametro.ContaValePedagio,
                    Domain.Models.Parametros.Parametros.TipoDoValor.Number);

                var contaTarifaValePedagio = await Repository.Query.GetParametrosAsync(-3,
                    Domain.Models.Parametros.Parametros.TipoDoParametro.ContaTarifaValePedagio,
                    Domain.Models.Parametros.Parametros.TipoDoValor.Number);

                var tentativaReenvioValePedagio = await Repository.Query.GetParametrosAsync(-3,
                    Domain.Models.Parametros.Parametros.TipoDoParametro.ConfiguracaoTentativaReenvioValePedagio,
                    Domain.Models.Parametros.Parametros.TipoDoValor.Number);

                var configuracaoCancelamentoPagamentoPedagio = await Repository.Query.GetParametrosAsync(-3,
                    Domain.Models.Parametros.Parametros.TipoDoParametro.PrazoMaximaParaCancelamentoPagamentoPedagio,
                    Domain.Models.Parametros.Parametros.TipoDoValor.Number);
                
                var valorMaximoPagamentoComplemento =
                    await Repository.Query.GetByTipoDoParametroAsync(Domain.Models.Parametros.Parametros.TipoDoParametro
                        .ValorMaximoPagamentoComplemento);

                if (valorMaximoPagamentoComplemento.TipoValor ==
                    Domain.Models.Parametros.Parametros.TipoDoValor.Number)
                {
                    valorMaximoPagamentoComplemento = await Repository.Query.GetParametrosAsync(-3,
                        Domain.Models.Parametros.Parametros.TipoDoParametro.ValorMaximoPagamentoComplemento,
                        Domain.Models.Parametros.Parametros.TipoDoValor.Number);
                }
                else
                {
                    valorMaximoPagamentoComplemento = await Repository.Query.GetParametrosAsync(-3,
                        Domain.Models.Parametros.Parametros.TipoDoParametro.ValorMaximoPagamentoComplemento,
                        Domain.Models.Parametros.Parametros.TipoDoValor.Percentual);
                }

                var valorMaximoPagamentoValePedagio = await Repository.Query.GetParametrosAsync(-3,
                    Domain.Models.Parametros.Parametros.TipoDoParametro.ValorMaximoPagamentoValePedagio,
                    Domain.Models.Parametros.Parametros.TipoDoValor.Number);

                var response = new ConfiguracaoValePedagioResponse
                {
                    ContaValePedagio = contaValePedagio.Valor.ToIntSafe(),
                    ContaTarifaValePedagio = contaTarifaValePedagio.Valor.ToIntSafe(),
                    ConfiguracaoTentativaReenvioValePedagio = tentativaReenvioValePedagio.Valor.ToIntSafe(),
                    ValorMaximoPagamentoComplemento = valorMaximoPagamentoComplemento.Valor,
                    ValorMaximoPagamentoValePedagio = valorMaximoPagamentoValePedagio.Valor,
                    TipoDoValorParametroValorMaxPagComplemento = valorMaximoPagamentoComplemento.TipoValor.ToIntSafe(),
                    ConfiguracaoCancelamentoPagamentoPedagio = configuracaoCancelamentoPagamentoPedagio.Valor.ToIntSafe()
                };


                return new RespPadrao
                {
                    data = response,
                    sucesso = true,
                    mensagem = "Parâmetros de Vale Pedágio consultados com sucesso."
                };
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar ConsultaParametrosConfiguracaoValePedagio");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("ConsultaParametrosConfiguracaoValePedagio");
            }
        }

        public async Task<RespPadrao> SalvarConfiguracaoValePedagio(ConfiguracaoValePedagioRequest request)
        {
            try
            {
                var contaValePedagio = await Repository.Query.GetParametrosAsync(-3,
                    Domain.Models.Parametros.Parametros.TipoDoParametro.ContaValePedagio,
                    Domain.Models.Parametros.Parametros.TipoDoValor.Number);

                contaValePedagio.Valor = request.ContaValePedagio.ToStringSafe();

                var contaTarifaValePedagio = await Repository.Query.GetParametrosAsync(-3,
                    Domain.Models.Parametros.Parametros.TipoDoParametro.ContaTarifaValePedagio,
                    Domain.Models.Parametros.Parametros.TipoDoValor.Number);

                contaTarifaValePedagio.Valor = request.ContaTarifaValePedagio.ToStringSafe();

                var tentativaReenvioValePedagio = await Repository.Query.GetParametrosAsync(-3,
                    Domain.Models.Parametros.Parametros.TipoDoParametro.ConfiguracaoTentativaReenvioValePedagio,
                    Domain.Models.Parametros.Parametros.TipoDoValor.Number);

                tentativaReenvioValePedagio.Valor = request.ConfiguracaoTentativaReenvioValePedagio.ToString();
                
                var configuracaoCancelamentoPagamentoPedagio = await Repository.Query.GetParametrosAsync(-3,
                    Domain.Models.Parametros.Parametros.TipoDoParametro.PrazoMaximaParaCancelamentoPagamentoPedagio,
                    Domain.Models.Parametros.Parametros.TipoDoValor.Number);

                configuracaoCancelamentoPagamentoPedagio.Valor = request.ConfiguracaoCancelamentoPagamentoPedagio.ToString();
                
                var valorMaximoPagamentoComplemento = await Repository.Query.GetParametrosAsync(-3,
                    Domain.Models.Parametros.Parametros.TipoDoParametro.ValorMaximoPagamentoComplemento,
                    Domain.Models.Parametros.Parametros.TipoDoValor.Number);

                if (request.TipoValorMaximoPagamentoComplemento == 2)
                {
                    valorMaximoPagamentoComplemento.Valor =
                        request.ValorMaximoPagamentoComplemento?.ToString(CultureInfo.InvariantCulture) ?? "";
                    valorMaximoPagamentoComplemento.TipoValor = Domain.Models.Parametros.Parametros.TipoDoValor.Number;
                }
                else
                {
                    valorMaximoPagamentoComplemento.Valor =
                        request.ValorMaximoPagamentoComplemento?.ToString(CultureInfo.InvariantCulture) ?? "";
                    valorMaximoPagamentoComplemento.TipoValor =
                        Domain.Models.Parametros.Parametros.TipoDoValor.Percentual;
                }

                var valorMaximoValePedagio = await Repository.Query.GetParametrosAsync(-3,
                    Domain.Models.Parametros.Parametros.TipoDoParametro.ValorMaximoPagamentoValePedagio,
                    Domain.Models.Parametros.Parametros.TipoDoValor.Number);

                valorMaximoValePedagio.Valor =
                    request.ValorMaximoPagamentoValePedagio?.ToString(CultureInfo.InvariantCulture) ?? "";

                await Repository.Command.SaveChangesAsync();
                await SincronizaParametrosComMicroServico();
                return new RespPadrao(true, "Parâmetros de vale pedágio salvos com sucesso.");
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return new RespPadrao(false, "Erro interno ao salvar os parâmetros. " + e.Message);
            }
        }
        
        public async Task<RespPadrao> ConsultaParametrosConfiguracaoTelaoSaldo()
        {
            try
            {
                var parametroLayout = await
                    Repository.Query.GetParametrosAsync(-3,
                        Domain.Models.Parametros.Parametros.TipoDoParametro.QuantidadeLayoutTelao,
                        Domain.Models.Parametros.Parametros.TipoDoValor.Number);
                var parametroTempoAtualizacao = await
                    Repository.Query.GetParametrosAsync(-3,
                        Domain.Models.Parametros.Parametros.TipoDoParametro.TempoAtualizacaoTelao,
                        Domain.Models.Parametros.Parametros.TipoDoValor.Number);
                var parametroTempoPaginacao = await
                    Repository.Query.GetParametrosAsync(-3,
                        Domain.Models.Parametros.Parametros.TipoDoParametro.TempoPaginacaoTelao,
                        Domain.Models.Parametros.Parametros.TipoDoValor.Number);

                var response = new ConfiguracaoTelaoSaldoResponse()
                {
                    tempoAtualizacaoTelao = parametroTempoAtualizacao.Valor.ToIntSafe(),
                    QuantidadeLayoutTelao = parametroLayout.Valor.ToIntSafe(),
                    TempoPaginacaoTelao = parametroTempoPaginacao.Valor.ToIntSafe()
                };
                
                return new RespPadrao
                {
                    data = response,
                    sucesso = true,
                    mensagem = "Parâmetros de Telão de Saldo consultados com sucesso."
                };
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return new RespPadrao(false, e.Message);
            }
        }

        public async Task<RespPadrao> SalvarConfiguracaoTelaoSaldo(ConfiguracaoTelaoSaldoRequest request)
        {
            try
            {
                var parametroLayout = await
                    Repository.Query.GetParametrosAsync(-3,
                        Domain.Models.Parametros.Parametros.TipoDoParametro.QuantidadeLayoutTelao,
                        Domain.Models.Parametros.Parametros.TipoDoValor.Number);

                parametroLayout.Valor = request.QuantidadeLayoutTelao.ToString();

                var parametroTempoAtualizacao = await
                    Repository.Query.GetParametrosAsync(-3,
                        Domain.Models.Parametros.Parametros.TipoDoParametro.TempoAtualizacaoTelao,
                        Domain.Models.Parametros.Parametros.TipoDoValor.Number);
                parametroTempoAtualizacao.Valor = request.tempoAtualizacaoTelao.ToString();
                var parametroTempoPaginacao = await
                    Repository.Query.GetParametrosAsync(-3,
                        Domain.Models.Parametros.Parametros.TipoDoParametro.TempoPaginacaoTelao,
                        Domain.Models.Parametros.Parametros.TipoDoValor.Number);

                parametroTempoPaginacao.Valor = request.TempoPaginacaoTelao.ToString();

                await Repository.Command.SaveChangesAsync();
                return new RespPadrao(true, "Sucesso ao salvar configuração!");
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return new RespPadrao(false, e.Message);
            }
        }

        public async Task<RespPadrao> ConsultaParametrosConfiguracaoQualificacaoTransacao()
        {
            try
            {
                var parametroLinkApiQualificacaoFrete = await
                    Repository.Query.GetParametrosAsync(-3,
                        Domain.Models.Parametros.Parametros.TipoDoParametro.LinkApiQualificacaoFrete,
                        Domain.Models.Parametros.Parametros.TipoDoValor.String);
                var parametroAutorizacaoLinkApiQualificacaoFrete = await
                    Repository.Query.GetParametrosAsync(-3,
                        Domain.Models.Parametros.Parametros.TipoDoParametro.AutorizacaoLinkApiQualificacaoFrete,
                        Domain.Models.Parametros.Parametros.TipoDoValor.String);

                var response = new ConfiguracaoQualificacaoTransacaoResponse()
                {
                    LinkApiQualificacaoFrete = parametroLinkApiQualificacaoFrete.Valor,
                    AutorizacaoLinkApiQualificacaoFrete =
                        Encoding.UTF8.GetString(
                            Convert.FromBase64String(parametroAutorizacaoLinkApiQualificacaoFrete.Valor)),
                };


                return new RespPadrao
                {
                    data = response,
                    sucesso = true,
                    mensagem = "Parâmetros de Qualificação de Transação consultados com sucesso."
                };
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return new RespPadrao(false, e.Message);
            }
        }

        public async Task<RespPadrao> SalvarConfiguracaoQualificacaoTransacao(
            ConfiguracaoQualificacaoTransacaoRequest request)
        {
            try
            {
                var parametroLinkApiQualificacaoFrete = await
                    Repository.Query.GetParametrosAsync(-3,
                        Domain.Models.Parametros.Parametros.TipoDoParametro.LinkApiQualificacaoFrete,
                        Domain.Models.Parametros.Parametros.TipoDoValor.String);

                parametroLinkApiQualificacaoFrete.Valor = request.LinkApiQualificacaoFrete;

                var parametroAutorizacaoLinkApiQualificacaoFrete = await
                    Repository.Query.GetParametrosAsync(-3,
                        Domain.Models.Parametros.Parametros.TipoDoParametro.AutorizacaoLinkApiQualificacaoFrete,
                        Domain.Models.Parametros.Parametros.TipoDoValor.Number);

                parametroAutorizacaoLinkApiQualificacaoFrete.Valor =
                    Convert.ToBase64String(
                        System.Text.Encoding.UTF8.GetBytes(request.AutorizacaoLinkApiQualificacaoFrete));

                await Repository.Command.SaveChangesAsync();
                return new RespPadrao(true, "Sucesso ao salvar configuração!");
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return new RespPadrao(false, e.Message);
            }
        }


        private async Task<RespPadrao> SincronizarComCiotMs(ConfiguracaoMonitoramentoCiotRequest request)
        {
            try
            {
                await Engine.CommandBus.SendCommandAsync(Mapper.Map<SincronizarParametroCiotMessage>(request));
                return new RespPadrao(true, "");
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return new RespPadrao(false, e.Message);
            }
        }
    }
}
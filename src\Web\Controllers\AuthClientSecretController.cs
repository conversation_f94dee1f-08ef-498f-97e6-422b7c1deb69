// using System;
// using System.Threading.Tasks;
// using Microsoft.AspNetCore.Mvc;
// using SistemaInfo.BBC.Application.Interface.AuthClientSecret;
// using SistemaInfo.BBC.Application.Objects.Base;
// using SistemaInfo.BBC.Application.Objects.Web.AuthClientSecret;
// using SistemaInfo.BBC.Domain.Enum;
// using SistemaInfo.BBC.Web.Attributes;
// using SistemaInfo.BBC.Web.Controllers.Base;
// using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
//
//
// namespace SistemaInfo.BBC.Web.Controllers
// {
//     /// <summary>
//     /// Controller Web dos Auth Client Secrets
//     /// </summary>
//     [Route("AuthClientSecret")]
//     public class AuthClientSecretController : WebControllerBase<IAuthClientSecretAppService>
//     {
//         /// <summary>
//         /// Injeções de dependência do Controller Web dos Auth Client Secrets
//         /// </summary>
//         /// <param name="engine"></param>
//         /// <param name="AppService"></param>
//         public AuthClientSecretController(IAppEngine engine, IAuthClientSecretAppService AppService) : base(engine, AppService)
//         {
//         }
//
//         /// <summary>
//         /// Consultar Auth Client Secret por ID
//         /// </summary>
//         /// <param name="idAuthClientSecret"></param>
//         /// <returns></returns>
//         [HttpGet("ConsultarPorId")]
//         [Menu(new[] { EMenus.AuthClientSecret })]
//         public async Task<JsonResult> ConsultarPorId(int idAuthClientSecret) =>
//             ResponseBase.Responder(await AppService.ConsultarPorId(idAuthClientSecret));
//
//         /// <summary>
//         /// Consultar Grid de Auth Client Secrets
//         /// </summary>
//         /// <param name="aGridRequest"></param>
//         /// <returns></returns>
//         [HttpPost("ConsultarGridAuthClientSecret")]
//         [Menu(new[] { EMenus.AuthClientSecret })]
//         public async Task<JsonResult> ConsultarGridAuthClientSecret([FromBody] BaseGridRequest request) =>
//             ResponseBase.Responder(await AppService.ConsultarGridAuthClientSecret(request.Take, request.Page,
//                 request.Order, request.Filters));
//         
//         /// <summary>
//         /// Alterar Status do Auth Client Secret
//         /// </summary>
//         /// <param name="aAuthClientStatus"></param>
//         /// <returns></returns>
//         [HttpPost("AlterarStatus")]
//         [Menu(new[] { EMenus.AuthClientSecret })]
//         public async Task<JsonResult> AlterarStatus([FromBody] AuthClientSecretAlterarStatusRequest aAuthClientStatus) =>
//             ResponseBase.Responder(await AppService.AlterarStatus(aAuthClientStatus));
//
//         /// <summary>
//         /// Salvar Auth Client Secret
//         /// </summary>
//         /// <param name="aModel"></param>
//         /// <returns></returns>
//         [HttpPost("SaveAuthClientSecret")]
//         [Menu(new[] { EMenus.AuthClientSecret })]
//         public async Task<JsonResult> SaveAuthClientSecret([FromBody] AuthClientSecretRequest aModel) =>
//             ResponseBase.Responder(await AppService.SaveAuthClientSecret(aModel));
//     }
// }

﻿using Microsoft.EntityFrameworkCore.Migrations;
using System;
using System.Collections.Generic;

namespace SistemaInfo.BBC.Infra.Data.Migrations
{
    public partial class SPRINT_56_VIAGEM_ADD_COLUMN_VALORCOMPLEMENTO : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<decimal>(
                name: "ValorComplemento",
                schema: "BBC",
                table: "Viagem",
                type: "numeric(16,2)",
                nullable: true);
            
            //Adicionado na 55
            // migrationBuilder.AddColumn<int>(
            //     name: "StatusAntecipacaoParcelaProprietario",
            //     schema: "BBC",
            //     table: "PagamentoEvento",
            //     type: "int",
            //     nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ValorComplemento",
                schema: "BBC",
                table: "Viagem");

            //Adicionado na 55
            // migrationBuilder.DropColumn(
            //     name: "StatusAntecipacaoParcelaProprietario",
            //     schema: "BBC",
            //     table: "PagamentoEvento");
        }
    }
}

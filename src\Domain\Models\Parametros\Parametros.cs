using System;
using SistemaInfo.BBC.Domain.Models.Parametros.Exeptions;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Models;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Models.Validator;

namespace SistemaInfo.BBC.Domain.Models.Parametros
{
    public class Parametros : Entity<Parametros, int, NotImplementedEntityValidator<Parametros>>
    {
        
        public const int ParametroGeralId = -1;
        
        /// <summary>
        /// Id vínculo da tabela
        /// </summary>
        public int ReferenciaId { get; set; }

        /// <summary>
        /// Tipo do parâmetro da tabela, enumerador do código fonte
        /// </summary>
        public TipoDoParametro TipoParametros { get; set; }

        /// <summary>
        /// Valor do parâmetro
        /// </summary>
        public string Valor { get; set; }
        
        /// <summary>
        /// ValorCriptografado do parâmetro quando informção sigilosa
        /// </summary>
        public string ValorCriptografado { get; set; }

        /// <summary>
        /// Tipo do valor do parâmetro
        /// </summary>
        public TipoDoValor TipoValor { get; set; }

        /// <summary>
        /// Informação adicional do parâmetro, caso tenha
        /// </summary>
        public string InfoAdicional { get; set; }

        /// <summary>
        /// Informação adicional do parâmetro, caso tenha
        /// </summary>
        public int? UsuarioCadastroId { get; set; }

        /// <summary>
        /// Informação adicional do parâmetro, caso tenha
        /// </summary>
        public DateTime? DataCadastro { get; set; }

        /// <summary>
        /// Informação adicional do parâmetro, caso tenha
        /// </summary>
        public int? UsuarioAlteracaoId { get; set; }

        /// <summary>
        /// Informação adicional do parâmetro, caso tenha
        /// </summary>
        public DateTime? DataAlteracao { get; set; }
        
        #region enum's

        /// <summary>
        ///  Refencia Id -1 APARECE NA GRID de parametros gerais
        /// diferente disso pode servir para referenciar outra entidade por ex empresaId
        ///  Parâmetros Gerais (REFERENCIA_ID = -1)
        /// </summary>
        public enum TipoDoParametro
        {
            /// <summary>
            ///  Parâmetros Gerais (REFERENCIA_ID = -1)
            /// </summary>
            CodigoContaTransferenciaValorRetencao = 1,
            
            /// <summary>
            ///  Parâmetros Gerais (REFERENCIA_ID = -1)
            /// </summary>
            CodigoLinkEmpresa = 2,
            
            /// <summary>
            ///  Parâmetros Gerais (REFERENCIA_ID = -1)
            /// </summary>
            CodigoTipoEmissaoCiot = 3,
            
            /// <summary>
            ///  Parâmetros Gerais (REFERENCIA_ID = -1)
            /// </summary>
            CodigoPeriodoMaximoProcessamento = 4,
            
            /// <summary>
            ///  Parâmetros Gerais (REFERENCIA_ID = -1)
            /// </summary>
            CodigoReenvioPagamentoEvento = 5,

            /// <summary>
            ///  Parâmetros Gerais (REFERENCIA_ID = -1)
            /// </summary>
            CodigoLinkCiot = 6,
            
            /// <summary>
            ///  Parâmetros Gerais (REFERENCIA_ID = -1)
            /// </summary>
            LimiteMaximoRetentativaFrete = 7,
            
            /// <summary>
            ///  Parâmetros Gerais (REFERENCIA_ID = -1)
            /// </summary>
            IntervaloMinimoRetentativaCancelamentoPagamentoFrete = 8,
            
            /// <summary>
            ///  Parâmetros Gerais (REFERENCIA_ID = -1)
            /// </summary>
            ForcarGeracaoPagamento = 9,
            
            /// <summary>
            ///  Parâmetros Gerais (REFERENCIA_ID = -1)
            /// </summary>
            NumeroRetentativaEnvioPagamento = 10,
            
            /// <summary>
            ///  Parâmetros Gerais (REFERENCIA_ID = -1)
            /// </summary>
            AprovarPagamentosAutomaticamente = 11,
            
            /// <summary>
            ///  Parâmetros Gerais (REFERENCIA_ID = -1)
            /// </summary>
            PrazoMaximaParaCancelamentoPagamentoFrete = 12,
            
            /// <summary>
            ///  Parâmetros Gerais (REFERENCIA_ID = -1)
            /// </summary>
            LimiteMaximoRetentativaCancelamentoPagamentoFrete = 13,
            
            /// <summary>
            ///  Parâmetros Gerais (REFERENCIA_ID = -1)
            /// </summary>
            PeriodoMaximoInatividadePortador = 14,
            
            /// <summary>
            ///  Parâmetros Gerais (REFERENCIA_ID = -1)
            /// </summary>
            PeriodoMaximoInatividadeSenhaProvisoria = 15,
            
            /// <summary>
            ///  Parâmetros Gerais (REFERENCIA_ID = -1)
            /// </summary>
            CodigoContaCorrenteReceita = 16,
            
            /// <summary>
            ///  Parâmetros Gerais (REFERENCIA_ID = -1)
            /// </summary>
            PeriodoDuracaoSenha = 17,
            
            /// <summary>
            ///  Parâmetros Gerais (REFERENCIA_ID = -2)
            /// </summary>
            ConfiguracaoDeSLA = 18,
            
            /// <summary>
            ///  Parâmetros Gerais (REFERENCIA_ID = -1)
            /// </summary>
            UrlComunicacaoConductor = 19,
            
            /// <summary>
            ///  Parâmetros Gerais (REFERENCIA_ID = -1)
            /// </summary>
            UrlComunicacaoConductorAuth = 20,
            
            /// <summary>
            ///  Parâmetros Gerais (REFERENCIA_ID = -1)
            /// </summary>
            ComunicacaoConductorUsuario = 21,
            
            /// <summary>
            ///  Parâmetros Gerais (REFERENCIA_ID = -1)
            /// </summary>
            ComunicacaoConductorSenha = 22,
            
            /// <summary>
            ///  Parâmetros Gerais (REFERENCIA_ID = -1)
            /// </summary>
            UrlComunicacaoConductorRegDocs = 23,
            
            /// <summary>
            ///  Parâmetros Gerais (REFERENCIA_ID = -1)
            /// </summary>
            UrlComunicacaoConductorAliasBank = 24,
            
            /// <summary>
            ///  Parâmetros Gerais (REFERENCIA_ID = -1)
            /// </summary>
            UrlComunicacaoConductorCompanies = 25,
            
            /// <summary>
            ///  Parâmetros Gerais (REFERENCIA_ID = -1)
            /// </summary>
            UrlComunicacaoCiot = 26,
            
            /// <summary>
            ///  Parâmetros Gerais (REFERENCIA_ID = -1)
            /// </summary>
            UrlConfiguracaoWeb = 27,
            
            /// <summary>
            ///  Parâmetros Gerais (REFERENCIA_ID = -1)
            /// </summary>
            ConfiguracaoWebToken = 28,
            
            /// <summary>
            ///  Parâmetros Gerais (REFERENCIA_ID = -1)
            /// </summary>
            ConfiguracaoWebHostName = 29,
            
            /// <summary>
            ///  Parâmetros Gerais (REFERENCIA_ID = -1)
            /// </summary>
            VerificaContigencia = 30,
            
            /// <summary>
            ///  Parâmetros Gerais (REFERENCIA_ID = -1)
            /// </summary>
            UrlComunicacaoCaptalys = 31,
            
            /// <summary>
            ///  Parâmetros Gerais (REFERENCIA_ID = -1)
            /// </summary>
            ComunicacaoCaptalysToken = 32,
            
            /// <summary>
            ///  Parâmetros Gerais (REFERENCIA_ID = -1)
            /// </summary>
            UrlComunicacaoCaptalysRetencao = 33,
            
            /// <summary>
            ///  Parâmetros Gerais (REFERENCIA_ID = -1)
            /// </summary>
            ComunicacaoCaptalysTokenRetencao = 34,
            
            /// <summary>
            ///  Parâmetros Gerais (REFERENCIA_ID = -1)
            /// </summary>
            UrlComunicacaoCaruana = 35,
            
            /// <summary>
            ///  Parâmetros Gerais (REFERENCIA_ID = -1)
            /// </summary>
            UrlComunicacaoLesing = 36,
            
            /// <summary>
            ///  Parâmetros Gerais (REFERENCIA_ID = -1)
            /// </summary>
            EmailSmtpClient = 37,
            
            /// <summary>
            ///  Parâmetros Gerais (REFERENCIA_ID = -1)
            /// </summary>
            EmailPort = 38,
            
            /// <summary>
            ///  Parâmetros Gerais (REFERENCIA_ID = -1)
            /// </summary>
            EmailUsuario = 39,
            
            /// <summary>
            ///  Parâmetros Gerais (REFERENCIA_ID = -1)
            /// </summary>
            EmailSenha = 40,
            
            /// <summary>
            ///  Parâmetros Gerais (REFERENCIA_ID = -1)
            /// </summary>
            EmailSsl = 41,
            
            /// <summary>
            ///  Parâmetros Gerais (REFERENCIA_ID = -1)
            /// </summary>
            UrlComunicacaoMobile2You = 42,
            
            /// <summary>
            ///  Parâmetros Gerais (REFERENCIA_ID = -1)
            /// </summary>
            ComunicacaoMobile2YouToken = 43,
            
            /// <summary>
            ///  Parâmetros Gerais (REFERENCIA_ID = -1)
            /// </summary>
            EmpresaPagamento = 44,
            
            /// <summary>
            ///  Parâmetros Gerais (REFERENCIA_ID = -1)
            /// </summary>
            CodigoContaTransferenciaTarifaValorRetencao = 45,
            
            /// <summary>
            ///  Parâmetros Gerais (REFERENCIA_ID = -1)
            /// </summary>
            UrlComunicacaoPixBaas = 46,
            
            /// <summary>
            ///  Parâmetros Gerais (REFERENCIA_ID = -1)
            /// </summary>
            TempoMaximoUsuarioInativo = 47,
            
            /// <summary>
            ///  Diferença máxima a baixo do valor atual do combustível para aprovação automática (REFERENCIA_ID = -3)
            /// </summary>
            AprovacaoAutomaticaPrecoCombustivelLimiteAbaixo = 48,
            
            /// <summary>
            ///  Diferença máxima acima do valor atual do combustível para aprovação automática (REFERENCIA_ID = -3)
            /// </summary>
            AprovacaoAutomaticaPrecoCombustivelLimiteAcima = 49,
            
            /// <summary>
            ///  Habilita limite de valores menores que o atual para aprovação automática (REFERENCIA_ID = -3)
            /// </summary>
            AprovacaoAutomaticaPrecoCombustivelLimiteAbaixoHabilitado = 50,
            
            /// <summary>
            ///  Habilita limite de valores maiores que o atual para aprovação automática  (REFERENCIA_ID = -3)
            /// </summary>
            AprovacaoAutomaticaPrecoCombustivelLimiteAcimaHabilitado = 51,
            
            /// <summary>
            ///  Registro para aparecer na grid e redirecionar pra tela que configura os outros 4 parametros de atualização de preço de combustível (REFERENCIA_ID = -2)
            /// </summary>
            AprovacaoAutomaticaPrecoCombustivel = 52,
            
            /// <summary>
            ///  Email do gestor responsável pelos abastecimentos com falha na integração à Movida (Vetor) (REFERENCIA_ID = -1)
            /// </summary>
            EmailGestorAbastecimentosMovida = 53,
            
            /// <summary>
            ///  Quantidade necessária de tentativas de integração com a Movida (Vetor) para o envio do Email do gestor responsável (REFERENCIA_ID = -1)
            /// </summary>
            QuantidadeTentativasReenvioAbastecimentoMovida = 54,
            
            /// <summary>
            /// Define em qual hora do dia o ServicosCentralPendencias.ReenviarAbastecimentosMovida envia emails. (REFERENCIA_ID = -1)
            /// </summary>
            HorarioEnvioEmailGestorAbastecimentosMovida = 55,
            
            /// <summary>
            ///  Dias retroativos para gerar pagamento de receita (REFERENCIA_ID = -1)
            /// </summary>
            DiasRetroativosGerarReceita = 56,
            
            /// <summary>
            ///  Margem de erro na hora da validação do xml, alteração de nome Margem arredondamento casas decimais de valor unitário 
            /// </summary>
            /// 
            MargemErroArredondamentoXmlProtocolo = 57,
            
            /// <summary>
            ///  Numero do banco na dock(REFERENCIA_ID = -1)
            /// </summary>
            BankNumberDock = 58,
            
            /// <summary>
            ///  Link do aplicativo a ser mandado no email de cadastro do portador(REFERENCIA_ID = -1)
            /// </summary>
            LinkAplicativoCadastroPortador = 59,
            
            /// <summary>
            ///  Será configurado a conta que será creditada as tarifas dos pagamentos de vale pedágio
            /// </summary>
            ContaTarifaValePedagio = 60,
            
            /// <summary>
            ///  Será configurado a conta que será creditada as tarifas dos pagamentos de vale pedágio.
            /// </summary>
            ContaValePedagio = 61,
            
            /// <summary>
            ///  Número de tentativas que será realizado o reenvio dos pagamentos pendentes de vale pedágio.
            /// </summary>
            ConfiguracaoTentativaReenvioValePedagio = 62,
            /// <summary>
            ///  Tempo de espera para receber resposta da dock após finalizar o pix
            /// </summary>
            TempoEsperaSegundosPix = 63,
            /// <summary>
            ///  Quantidade de vezes que vai ser consultado na dock após finalizar o pix
            /// </summary>
            QuantidadeVezesConsultaPix = 64,
            /// <summary>
            ///  Tempo retroativo para considerar um pix duplicado
            /// </summary>
            TempoRetroativoPixDuplicado = 65,
            /// <summary>
            ///  
            /// </summary>
            ConfiguracaoMonitoramentoCIOT = 66,
            /// <summary>
            ///  
            /// </summary>
            EmailsNotificacaoInternaContingenciaForcada = 67,
            /// <summary>
            ///  
            /// </summary>
            EmailsNotificacaoInternaContingenciaAutomatica = 68,
            /// <summary>
            ///  
            /// </summary>
            TempoChecagemAmbiente = 69,
            /// <summary>
            ///  
            /// </summary>
            QuantidadeErroCiot = 70,
            /// <summary>
            ///  
            /// </summary>
            TempoNotificacaoExterna = 71,
            /// <summary>
            ///  
            /// </summary>
            ObrigaValorFrete = 72,
            /// <summary>
            ///  
            /// </summary>
            DataInicioObrigacaoValorFrete = 73,
            /// <summary>
            ///  Parâmetros Gerais (REFERENCIA_ID = -1)
            /// </summary>
            ConfiguracaoValePedagio = 74,
            /// <summary>
            ///  Parâmetro Geral Pedágio (REFERENCIA_ID = -3)
            /// </summary>
            ValorMaximoPagamentoComplemento = 75,
            /// <summary>
            ///  Parâmetro Geral Pedágio (REFERENCIA_ID = -3)
            /// </summary>
            ValorMaximoPagamentoValePedagio = 76,
            /// <summary>
            ///  Parâmetros Gerais (REFERENCIA_ID = -1)
            /// </summary>
            ConfiguracaoTentativaReenvioPagamentoFrete = 77,
            /// <summary>
            ///  
            /// </summary>
            PeriodoReenvioCiot = 78,
            /// <summary>
            ///  Define a quantidade de telas que aparece no telão.
            /// </summary>
            QuantidadeLayoutTelao = 79,
            /// <summary>
            ///  Define quantos segundos leva para paginar o telão
            /// </summary>
            TempoPaginacaoTelao = 80,
            /// <summary>
            ///   Inteiro - Define quantos minutos leva para atualizar a tela.
            /// </summary>
            TempoAtualizacaoTelao = 81,
            /// <summary>
            ///   Tela configuração
            /// </summary>
            ConfiguracaoTelaoSaldo = 82,
            /// <summary>
            ///  Margem arredondamento casas decimais de litragem da validação do xml
            /// </summary>
            MargemArredondamentoCasasDecimaisLitragemXml = 83,
            /// <summary>
            ///  
            /// </summary>
            SenhaApiCiot = 88,
            /// <summary>
            ///   Tela de configuração de parâmetros de qualificação na transação
            /// </summary>
            ConfiguracaoQualificacaoTransacao = 89,
            /// <summary>
            ///   Link da api para qualificação de transação frete
            /// </summary>
            LinkApiQualificacaoFrete = 90,
            /// <summary>
            ///   Autorização para utlizar a API
            /// </summary>
            AutorizacaoLinkApiQualificacaoFrete = 91,
            /// <summary>
            ///  Margem de erro na hora da validação do xml, alteração de nome Margem arredondamento casas decimais de valor unitário 
            /// </summary>
            /// 
            MargemErroTotalAbastecimentoXmlProtocolo = 92,
            /// <summary>
            ///  Número de dias para realizar o cancelamento de um pagamento ou de complemento.
            /// </summary>
            PrazoMaximaParaCancelamentoPagamentoPedagio = 93,
            /// <summary>
            ///  Quantidade de vezes que o usuário pode errar a senha ao realiar o login no app.
            /// </summary>
            /// 
            QuantidadeErroSenhaPortadorFrota = 94,
            /// <summary>
            ///  Parâmetro de tempo (min) para reenviar pagamentos com status em Aberto
            /// </summary>
            TempoReenvioPagamentoStatusAberto = 99,
            /// <summary>
            ///  Dia(s) para a disponibilização da antecipação de recebível.
            /// </summary>
            DiasParaNaoDisponibilizacaoAntecipacaoRecebivel = 101
        }

        public enum TipoDoValor
        {
            String = 1,
            Number = 2,
            Criptografia = 3,
            Decimal = 6,
            Percentual = 4
        }
        
        #endregion

        #region FK's

        public virtual Usuario.Usuario UsuarioCadastro { get; set; }
        
        public virtual Usuario.Usuario UsuarioAlteracao { get; set; }

        #endregion

        public void ValidarCriacao()
        {
            if (string.IsNullOrEmpty(Valor))
                throw new ParametroException("Valor é obrigatório");
            
            if (Valor.Length > 100)
                throw new ParametroException("Valor permite apenas 100 caracteres");
        }

    }
}
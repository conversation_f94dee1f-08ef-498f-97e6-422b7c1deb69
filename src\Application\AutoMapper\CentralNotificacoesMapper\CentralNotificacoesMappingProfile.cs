﻿using System.Globalization;
using SistemaInfo.BBC.Application.Objects.Web.CentralNotificacoes;
using SistemaInfo.BBC.Domain.Models.Notificacao;
using SistemaInfo.BBC.Domain.Models.Notificacao.Commands;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.AutoMapper.CentralNotificacoesMapper
{
    public class CentralNotificacoesMappingProfile : SistemaInfoMappingProfile
    {
        public CentralNotificacoesMappingProfile()
        {
            CreateMap<CentralNotificacoesRequest, NotificacaoSalvarCommand>();
            
            CreateMap<CentralNotificacoesRequest, NotificacaoSalvarComRetornoCommand>();
            
            CreateMap<NotificacaoSalvarCommand, Notificacao>();

            CreateMap<NotificacaoSalvarComRetornoCommand, Notificacao>();

            CreateMap<Notificacao, ConsultarGridCentralNotificacoes>()
                .ForMember(dest => dest.Status, opts => opts.MapFrom(s => (int) s.PagamentoEvento.Status))
                .ForMember(dest => dest.ViagemId, opts => opts.MapFrom(s => s.PagamentoEvento.ViagemId))
                .ForMember(dest => dest.Ciot, opts => opts.MapFrom(s => 
                    s.PagamentoEvento.Viagem.Ciot == null
                        ? null
                        : s.PagamentoEvento.Viagem.Ciot + "/" + (string.IsNullOrWhiteSpace(s.PagamentoEvento.Viagem.VerificadorCiot) ? "XXXX" : s.PagamentoEvento.Viagem.VerificadorCiot)
                ))
                .ForMember(dest => dest.PagamentoExternoId, opts => opts.MapFrom(s => s.PagamentoEvento.PagamentoExternoId))
                .ForMember(dest => dest.PagamentoEventoId, opts => opts.MapFrom(s => s.PagamentoEventoId))
                .ForMember(dest => dest.DataAlteracao, opts => opts.MapFrom(s => s.DataAlteracao != null ? s.DataAlteracao.ToStringBr(FormatDateTimeMethod.ShortDateTime) : s.DataCadastro.ToStringBr(FormatDateTimeMethod.ShortDateTime)))
                .ForMember(dest => dest.FilialExternoId, opts => opts.MapFrom(s => s.PagamentoEvento.Viagem.FilialId))
                .ForMember(dest => dest.EmpresaNome, opts => opts.MapFrom(s => s.PagamentoEvento.Viagem.Empresa.RazaoSocial))
                .ForMember(dest => dest.NomeProprietario, opts => opts.MapFrom(s => s.PagamentoEvento.Viagem.PortadorProprietario.Nome))
                .ForMember(dest => dest.CpfcnpjProprietario, opts => opts.MapFrom(s => FormatUtils.CpfCnpj(s.PagamentoEvento.Viagem.PortadorProprietario.CpfCnpj)))
                .ForMember(dest => dest.NomeMotorista, opts => opts.MapFrom(s => s.PagamentoEvento.Viagem.PortadorMotorista.Nome))
                .ForMember(dest => dest.CpfcnpjMotorista, opts => opts.MapFrom(s => FormatUtils.CpfCnpj(s.PagamentoEvento.Viagem.PortadorMotorista.CpfCnpj)))
                .ForMember(dest => dest.Tipo, opts => opts.MapFrom(s => s.PagamentoEvento.Tipo.GetHashCode()))
                .ForMember(dest => dest.Valor, opts => opts.MapFrom(s => s.PagamentoEvento.Valor.ToString("C2", CultureInfo.GetCultureInfo("pt-BR"))))
                .ForMember(dest => dest.Descricao, opts => opts.MapFrom(s => s.Descricao))
                .ForMember(dest => dest.ValorTransferenciaMotorista, opts => opts.MapFrom(s => s.PagamentoEvento.ValorTransferenciaMotorista != null ? ((decimal) s.PagamentoEvento.ValorTransferenciaMotorista).ToString("C2", CultureInfo.GetCultureInfo("pt-BR")) : ""))
                .ForMember(dest => dest.FormaPagamento, opts => opts.MapFrom(s => (int?)s.PagamentoEvento.FormaPagamento));

            
            CreateMap<Notificacao, CentralNotificacoesResponse>()
                .ForMember(dest => dest.Status, opts => opts.MapFrom(s => s.PagamentoEvento.Status));
        }
    }
}
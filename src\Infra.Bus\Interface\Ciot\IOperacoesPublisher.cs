﻿using System.Threading.Tasks;
using SistemaInfo.BBC.Domain.Contracts.Operacoes;
using SistemaInfo.BBC.Domain.External.CIOT.DTO;

namespace SistemaInfo.BBC.Infra.Bus.Interface.Ciot;

public interface IOperacoesPublisher
{
    Task<DeclararOperacaoTransporteRespMessage> DeclararOperacaoTransporte(
        DeclararOperacaoTransporteReqMessage mensagem);
    Task<EncerrarOperacaoTransporteRespMessage> EncerrarOperacaoTransporte(EncerrarOperacaoTransporteReqMessage mensagem);
    Task<CancelarOperacaoTransporteRespMessage> CancelarOperacaoTransporte(CancelarOperacaoTransporteReqMessage mensagem);
    Task<ConsultarSituacaoCiotRespMessage> ConsultarSituacaoCiot(ConsultarSituacaoCiotReqMessage mensagem);
    Task<ConsultarSituacaoTransportadorRespMessage> ConsultarSituacaoTransportador(ConsultarSituacaoTransportadorReqMessage mensagem);
    Task<ConsultarOperacaoTacAgregadoRespMessage> ConsultarOperacaoTacAgregado(ConsultarOperacaoTacAgregadoReqMessage mensagem);
    Task<RetificarOperacaoTransporteRespMessage> RetificarOperacaoTransporte(RetificarOperacaoTransporteReqMessage mensagem);
    Task<ConsultarFrotaTransportadorRespMessage> ConsultarFrotaTransportador(ConsultarFrotaTransportadorReqMessage mensagem);
    Task<ConsultaGridOperacaoTransporteMessage> ConsultarOperacoesTransporte(ConsultaGridOperacaoTransporteMessageRequest mensagem);
    Task<ConsultaGridOperacaoTransporteHistoricoMessage> ConsultarOperacoesTransporteHistorico(ConsultaGridOperacaoTransporteHistoricoMessageRequest mensagem);
    Task<ConsultarOperacaoTransportePorIdRespMessage> ConsultarOperacaoTransportePorId(ConsultarOperacaoTransportePorIdReqMessage mensagem);
    Task<ConsultarVeiculosCiotRespMessage> ConsultarVeiculoCiot(ConsultarVeiculoCiotReqMessage mensagem);
    Task<ConsultarBancosRespMessage> ConsultarBancos();
    Task<ConsultarEstadosRespMessage> ConsultarEstados();
    Task<ConsultarCidadesRespMessage> ConsultarCidades(ConsultarCidadesReqMessage mensagem);
    Task<ConsultarNaturezasCargaRespMessage> ConsultarNaturezasCarga(ConsultarNaturezasCargaReqMessage mensagem);
    Task<ConsultarTiposCargaRespMessage> ConsultarTiposCarga(ConsultarTiposCargaReqMessage mensagem);
    Task<ConsultarNaturezaCargaPorIdRespMessage> ConsultarNaturezaCargaPorId(ConsultarNaturezaCargaPorIdReqMessage mensagem);
    Task<ConsultarDadosEncerramentoRespMessage> ConsultarDadosEncerramento(ConsultarDadosEncerramentoReqMessage mensagem);
    Task<ConsultarDadosRetificacaoRespMessage> ConsultarDadosRetificacao(ConsultarDadosRetificacaoReqMessage mensagem);
    Task<ConsultarRelatorioCiotRespMessage> ConsultarRelatorioGestaoCiot(ConsultarRelatorioCiotReqMessage mensagem);
}
using System;
using System.Linq;
using System.Threading.Tasks;
using SistemaInfo.BBC.Application.Helpers;
using SistemaInfo.BBC.Application.Interface.Transacao;
using SistemaInfo.BBC.Application.Objects.Api.Transacao;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Domain.Models.Transacao.Commands;
using SistemaInfo.BBC.Domain.Models.Transacao.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;

namespace SistemaInfo.BBC.Application.Services.Transacao
{
    public class TransacaoRegisterAppService : AppService<Domain.Models.Transacao.Transacao,
        ITransacaoReadRepository, ITransacaoWriteRepository>, ITransacaoRegisterAppService
    {
        public TransacaoRegisterAppService(IAppEngine engine,
            ITransacaoReadRepository readRepository,
            ITransacaoWriteRepository writeRepository) :
            base(engine, readRepository, writeRepository)
        {
        }

        public async Task<RespPadrao> RegistrarTransacao(TransacaoRequest transacaoRequest)
        {
            try
            {
                var ltransacao = Mapper.Map<TransacaoSalvarComRetornoCommand>(transacaoRequest);

                var result = await Engine.CommandBus.SendCommandAsync<Domain.Models.Transacao.Transacao>(ltransacao);

                return new RespPadrao()
                {
                    id = result.Id,
                    sucesso = true
                };
            }
            catch (Exception e)
            {
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = e.Message
                };
            }
        }

        public IQueryable<Domain.Models.Transacao.Transacao> GetTransacoesByIdPagamentoEvento(int idPagamentoEvento)
        {
            try
            {
                new LogHelper().LogOperationStart("GetTransacoesByIdPagamentoEvento");
                return Repository.Query.GetTransacoesByIdPagamentoEvento(idPagamentoEvento);
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar GetTransacoesByIdPagamentoEvento");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("GetTransacoesByIdPagamentoEvento");
            }
        }

        public async Task AtualizarTransacao(TransacaoAlterarStatusRequest ltransacaoRequest)
        {
            try
            {
                new LogHelper().LogOperationStart("AtualizarTransacao");
                await Engine.CommandBus.SendCommandAsync(Mapper.Map<TransacaoAlterarStatusCommand>(ltransacaoRequest));
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar AtualizarTransacao");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("AtualizarTransacao");
            }
        }
    }
}
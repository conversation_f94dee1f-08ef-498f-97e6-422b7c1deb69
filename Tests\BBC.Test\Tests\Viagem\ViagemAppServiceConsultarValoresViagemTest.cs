using System;
using System.Threading.Tasks;
using BBC.Test.Tests.Viagem.Fixture;
using Moq;
using SistemaInfo.BBC.Application.Objects.Web.Viagem;
using SistemaInfo.BBC.Application.Services.Viagem;
using SistemaInfo.BBC.Domain.Models.Viagem.Repository;
using Xunit;

namespace BBC.Test.Tests.Viagem
{
    [Collection(nameof(ViagemCollection))]
    public class ViagemAppServiceConsultarValoresViagemTest
    {
        private readonly ViagemFixture _fixture;
        private readonly ViagemAppService _appService;

        public ViagemAppServiceConsultarValoresViagemTest(ViagemFixture fixture)
        {
            _fixture = fixture;
            _appService = fixture.Mocker.CreateInstance<ViagemAppService>();
        }

        [Fact(DisplayName = "ConsultarValoresViagem - ID válido - Deve retornar sucesso com dados da viagem")]
        [Trait("ViagemAppService", "ConsultarValoresViagem")]
        public async Task ConsultarValoresViagem_IdValido_DeveRetornarSucessoComDados()
        {
            // Arrange
            const int viagemId = 1;
            var viagem = _fixture.GerarViagem();
            viagem.Id = viagemId;
            viagem.ValorFrete = 5000.50m;
            viagem.ValorAdiantamento = 2000.25m;
            viagem.ValorSaldo = 3000.25m;
            viagem.QuantidadeTarifas = 5;
            viagem.ValorTarifas = 150.75m;
            viagem.ValorCombustivel = 800.00m;
            viagem.ValorDespesa = 200.50m;
            viagem.TotalImposto = 300.00m;
            viagem.TotalPedagio = 120.30m;

            _fixture.Mocker.GetMock<IViagemReadRepository>()
                .Setup(x => x.GetByIdAsync(viagemId))
                .ReturnsAsync(viagem);

            // Act
            var resultado = await _appService.ConsultarValoresViagem(viagemId);

            // Assert
            Assert.True(resultado.sucesso);
            Assert.Equal("Sucesso!", resultado.mensagem);
            Assert.NotNull(resultado.data);
            
            var dados = resultado.data as ConsultarValoresViagemResponse;
            Assert.NotNull(dados);
        }

        [Fact(DisplayName = "ConsultarValoresViagem - ID zero - Deve retornar erro")]
        [Trait("ViagemAppService", "ConsultarValoresViagem")]
        public async Task ConsultarValoresViagem_IdZero_DeveRetornarErro()
        {
            // Arrange
            const int viagemId = 0;

            // Act
            var resultado = await _appService.ConsultarValoresViagem(viagemId);

            // Assert
            Assert.False(resultado.sucesso);
            Assert.Equal("Id inválido.", resultado.mensagem);
            Assert.Null(resultado.data);
        }

        [Fact(DisplayName = "ConsultarValoresViagem - ID negativo - Deve retornar erro")]
        [Trait("ViagemAppService", "ConsultarValoresViagem")]
        public async Task ConsultarValoresViagem_IdNegativo_DeveRetornarErro()
        {
            // Arrange
            const int viagemId = -1;

            // Act
            var resultado = await _appService.ConsultarValoresViagem(viagemId);

            // Assert
            Assert.False(resultado.sucesso);
            Assert.Equal("Id inválido.", resultado.mensagem);
            Assert.Null(resultado.data);
        }

        [Fact(DisplayName = "ConsultarValoresViagem - Viagem não encontrada - Deve retornar erro")]
        [Trait("ViagemAppService", "ConsultarValoresViagem")]
        public async Task ConsultarValoresViagem_ViagemNaoEncontrada_DeveRetornarErro()
        {
            // Arrange
            const int viagemId = 999;

            _fixture.Mocker.GetMock<IViagemReadRepository>()
                .Setup(x => x.GetByIdAsync(viagemId))
                .ReturnsAsync((SistemaInfo.BBC.Domain.Models.Viagem.Viagem)null);

            // Act
            var resultado = await _appService.ConsultarValoresViagem(viagemId);

            // Assert
            Assert.False(resultado.sucesso);
            Assert.Equal("Viagem não encontrada.", resultado.mensagem);
            Assert.Null(resultado.data);
        }

        [Fact(DisplayName = "ConsultarValoresViagem - Viagem com valores nulos - Deve retornar sucesso")]
        [Trait("ViagemAppService", "ConsultarValoresViagem")]
        public async Task ConsultarValoresViagem_ViagemComValoresNulos_DeveRetornarSucesso()
        {
            // Arrange
            const int viagemId = 1;
            var viagem = _fixture.GerarViagem();
            viagem.Id = viagemId;
            viagem.ValorFrete = null;
            viagem.ValorAdiantamento = null;
            viagem.ValorSaldo = null;
            viagem.QuantidadeTarifas = null;
            viagem.ValorTarifas = null;
            viagem.ValorCombustivel = null;
            viagem.ValorDespesa = null;
            viagem.TotalImposto = null;
            viagem.TotalPedagio = null;

            _fixture.Mocker.GetMock<IViagemReadRepository>()
                .Setup(x => x.GetByIdAsync(viagemId))
                .ReturnsAsync(viagem);

            // Act
            var resultado = await _appService.ConsultarValoresViagem(viagemId);

            // Assert
            Assert.True(resultado.sucesso);
            Assert.Equal("Sucesso!", resultado.mensagem);
            Assert.NotNull(resultado.data);
        }

        [Fact(DisplayName = "ConsultarValoresViagem - Viagem com valores zerados - Deve retornar sucesso")]
        [Trait("ViagemAppService", "ConsultarValoresViagem")]
        public async Task ConsultarValoresViagem_ViagemComValoresZerados_DeveRetornarSucesso()
        {
            // Arrange
            const int viagemId = 1;
            var viagem = _fixture.GerarViagem();
            viagem.Id = viagemId;
            viagem.ValorFrete = 0;
            viagem.ValorAdiantamento = 0;
            viagem.ValorSaldo = 0;
            viagem.QuantidadeTarifas = 0;
            viagem.ValorTarifas = 0;
            viagem.ValorCombustivel = 0;
            viagem.ValorDespesa = 0;
            viagem.TotalImposto = 0;
            viagem.TotalPedagio = 0;

            _fixture.Mocker.GetMock<IViagemReadRepository>()
                .Setup(x => x.GetByIdAsync(viagemId))
                .ReturnsAsync(viagem);

            // Act
            var resultado = await _appService.ConsultarValoresViagem(viagemId);

            // Assert
            Assert.True(resultado.sucesso);
            Assert.Equal("Sucesso!", resultado.mensagem);
            Assert.NotNull(resultado.data);
        }

        [Fact(DisplayName = "ConsultarValoresViagem - Exceção no repositório - Deve retornar erro")]
        [Trait("ViagemAppService", "ConsultarValoresViagem")]
        public async Task ConsultarValoresViagem_ExcecaoNoRepositorio_DeveRetornarErro()
        {
            // Arrange
            const int viagemId = 1;
            const string mensagemErro = "Erro de conexão com banco de dados";

            _fixture.Mocker.GetMock<IViagemReadRepository>()
                .Setup(x => x.GetByIdAsync(viagemId))
                .ThrowsAsync(new Exception(mensagemErro));

            // Act
            var resultado = await _appService.ConsultarValoresViagem(viagemId);

            // Assert
            Assert.False(resultado.sucesso);
            Assert.Equal(mensagemErro, resultado.mensagem);
            Assert.Null(resultado.data);
        }

        [Theory(DisplayName = "ConsultarValoresViagem - Diferentes IDs válidos - Deve retornar sucesso")]
        [Trait("ViagemAppService", "ConsultarValoresViagem")]
        [InlineData(1)]
        [InlineData(100)]
        [InlineData(999999)]
        public async Task ConsultarValoresViagem_DiferentesIdsValidos_DeveRetornarSucesso(int viagemId)
        {
            // Arrange
            var viagem = _fixture.GerarViagem();
            viagem.Id = viagemId;

            _fixture.Mocker.GetMock<IViagemReadRepository>()
                .Setup(x => x.GetByIdAsync(viagemId))
                .ReturnsAsync(viagem);

            // Act
            var resultado = await _appService.ConsultarValoresViagem(viagemId);

            // Assert
            Assert.True(resultado.sucesso);
            Assert.Equal("Sucesso!", resultado.mensagem);
            Assert.NotNull(resultado.data);
        }
    }
}

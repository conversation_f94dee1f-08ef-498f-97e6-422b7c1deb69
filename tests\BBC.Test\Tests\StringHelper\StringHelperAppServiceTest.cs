using System;
using Helper = SistemaInfo.BBC.Domain.Helper.StringHelper;
using BBC.Test.Tests.StringHelper.Fixture;
using Xunit;

namespace BBC.Test.Tests.StringHelper
{
    [Collection(nameof(StringHelperCollection))]
    public class StringHelperAppServiceTest
    {
        private readonly StringHelperFixture _fixture;

        public StringHelperAppServiceTest(StringHelperFixture fixture)
        {
            _fixture = fixture;
        }
        
        [Theory(DisplayName = "Valida se é um CPF ou CNPJ permitido")]
        [Trait("StringHelper", "isCPFCNPJ")]
        [InlineData(0)]
        [InlineData(1)]
        [InlineData(2)]
        [InlineData(3)]
        [InlineData(4)]
        [InlineData(5)]
        public void StringHelperIsCPFCNPJ_CPFCNPJInvalido_DeveRetornarNull(int s)
        {
            string a;
            bool resultado;

            a = s switch
            {
                0 => "11117250989", 
                5 => "11111111111",
                1 => "46847316000114",
                2 => "113172809899",
                3 => "46847316012114",
                _ => ""
            };

            //action
            resultado = Helper.isCPFCNPJ(a,false);
            
            // Assert
            Assert.True((s == 0||s ==1) ? resultado : !resultado);
        }
        
        [Fact(DisplayName = "Formatação de cnpj")]
        [Trait("String Helper", "ToCNPJFormato")]
        public void StringHelper_ToCNPJFormato_DeveRetornarTextoFormatado()
        {
            //arrange
            var cnpj = "01671743000156";
            
            //act 
            var retorno = Helper.ToCNPJFormato(cnpj);
            
            Assert.Equal("01.671.743/0001-56", retorno);

        }
        
        [Theory(DisplayName = "Valida mascara de placa")]
        [Trait("StringHelper", "ToPlacaFormato")]
        [InlineData(0)]
        [InlineData(1)]
        [InlineData(2)]
        public void StringHelperPlacaFormato_PlacaInvalida(int s)
        {
            //arrange
            string placa;
            string retornoEsperado;

            placa = s switch
            {
                0 => "HZS5325", 
                1 => "HMR657",
                2 => "HZS-53256",
                _ => ""
            };
            
            retornoEsperado = s switch
            {
                0 => "HZS-5325", 
                1 => "HMR657",
                2 => "HZS-53256",
                _ => ""
            };
            
            //act 
            var retorno = Helper.ToPlacaFormato(placa);

            // Assert
            Assert.Equal(retornoEsperado, retorno);
        }
        
        
        [Fact(DisplayName = "Formatação de placa")]
        [Trait("String Helper", "FormatarPlaca")]
        public void StringHelper_FormataMascara_DeveRetornarTextoFormatado()
        {
            //arrange
            var placa = "JUO6366";
            
            //act 
            var retorno = Helper.FormatarPlaca(placa);
            
            Assert.Equal("JUO-6366", retorno);

        }
        
        
        [Theory(DisplayName = "Formatação de telefone com valor de caracteres maior ou igual a 10")]
        [Trait("StringHelper", "ToTelefoneFormato")]
        [InlineData(0)]
        [InlineData(1)]
        [InlineData(2)]
        [InlineData(3)]
        public void StringHelperTelefoneFormato_TelefoneInvalido_DeveRetornarNull(int s)
        {
            //arrange
            string telefone;
            string retornoEsperado;

            telefone = s switch
            {
                0 => "", 
                1 => "00 000000000 ",
                2 => "1234567891AFE1",
                3 => "63921481274",
                _ => ""
            };
            
            retornoEsperado = s switch
            {
                0 => "", 
                1 => "00 000000000 ",
                2 => "1234567891AFE1",
                3 => "(63) 92148-1274",
                _ => ""
            };
            
            //act 
            var retorno = Helper.ToTelefoneFormato(telefone);

            // Assert
            Assert.Equal(retornoEsperado, retorno);
        }
        
        [Theory(DisplayName = "Formatação de cpf e cnpj")]
        [Trait("StringHelper", "FormatarCpfCnpj")]
        [InlineData(0)]
        [InlineData(1)]
        [InlineData(2)]
        [InlineData(3)]
        [InlineData(4)]
        [InlineData(5)]
        public void StringHelperFormatarCpfCnpj_CpfCnpjSemFormatacao_DeveRetornarVazio(int s)
        {
            //arrange
            string cpfCnpj;
            string retornoEsperado;

            cpfCnpj = s switch
            {
                0 => "", 
                1 => "15801204075",
                2 => "83126153000107",
                3 => "50857817",
                4 => "63737322000138234342",
                5 => "asddgsgjdaa",
                _ => ""
            };
            
            retornoEsperado = s switch
            {
                0 => "", 
                1 => "158.012.040-75",
                2 => "83.126.153/0001-07",
                3 => "CNPJ/CPF com valor inválido.",
                4 => "Value was either too large or too small for an Int64.",
                5 => "Input string was not in a correct format.",
                _ => ""
            };

            var retorno = string.Empty;
            
            //act 
            try
            {
                retorno = Helper.FormatarCpfCnpj(cpfCnpj);
            }
            catch (Exception e)
            {
                retorno = e.Message;
            }

            // Assert
            Assert.Equal(retornoEsperado, retorno);
        }
        
        
        [Theory]
        [InlineData("11987654321", "11987654321")] // Celular com 9 dígitos
        [InlineData("1134567890", "(11) 3456-7890")]  // Telefone fixo com 10 dígitos
        [InlineData("08001234567", "************")]  // 0800 com 11 dígitos
        [InlineData("0300123456", "(3) 0012-3456")]    // 0300 com 10 dígitos
        [InlineData("40001234", "4000 1234")]         // Número especial 4000
        [InlineData("09001234", "900 1234")]         // Número especial 0900
        [InlineData("", "")]                          // Número vazio
        [InlineData("invalid", "invalid")]            // Número inválido (não numérico)
        [InlineData("12345", "12345")]                // Número com formato não coberto
        public void ToTelefoneFormatoNovo_ValidInputs_ShouldFormatCorrectly(string input, string expected)
        {
            // Act
            var result = Helper.ToTelefoneFormatoNovo(input);

            // Assert
            Assert.Equal(expected, result);
        }
        
        [Theory(DisplayName = "Formatação de telefone com tamanhos diferentes")]
        [Trait("StringHelper", "FormatarTelefone")]
        [InlineData(0)]
        [InlineData(1)]
        [InlineData(2)]
        [InlineData(3)]
        [InlineData(4)]
        [InlineData(5)]
        [InlineData(6)]
        [InlineData(7)]
        [InlineData(8)]
        public void StringHelperToTELFormato_TelefoneInválido_DeveRetornarDefault(int s)
        {
            //arrange
            string telefone;
            string retornoEsperado;

            telefone = s switch
            {
                0 => "", 
                1 => " ",
                2 => "9999999", // 7 dígitos
                3 => "99999999", // 8 dígitos
                4 => "999999999", // 9 dígitos
                5 => "9999999999", // 10 dígitos
                6 => "99999999999", // 11 dígitos
                7 => "999999999999", // 12 dígitos
                8 => "asdasdasd",
                _ => ""
            };
            
            retornoEsperado = s switch
            {
                0 => "", 
                1 => " ",
                2 => "9999999",
                3 => "9999-9999",
                4 => "99999-9999",
                5 => "(99)9999-9999",
                6 => "(99)99999-9999",
                7 => "999999999999",
                8 => "Erro ao formatar o número de telefone: Input string was not in a correct format.",
                _ => ""
            };
            
            
            //act 
            var retorno = Helper.ToTELFormato(telefone);

            // Assert
            Assert.Equal(retornoEsperado, retorno);
        }
        
        
        [Theory(DisplayName = "Formata valor decimal em valor monetário brasileiro")]
        [Trait("StringHelper", "FormatMonetario")]
        [InlineData(0)]
        [InlineData(1)]
        [InlineData(2)]
        public void StringHelperFormatMonetario_ValorInvalido_DeveRetornarVazio(decimal s)
        {
            //arrange
            decimal valor;
            string retornoEsperado;

            valor = s switch
            {
                0 => 0, 
                1 => 111m,
                2 => 12345.6789m,
                _ => 0
            };
            
            retornoEsperado = s switch
            {
                0 => "0,00", 
                1 => "111,00",
                2 => "12345,68",
                _ => ""
            };
            
            
            //act 
            var retorno = Helper.FormatMonetario(valor);

            // Assert
            Assert.Equal(retornoEsperado, retorno);
        }
        
        [Theory(DisplayName = "Verifica se o texto é cpf ou cnpj e formata o mesmo")]
        [Trait("StringHelper", "VerificaFormataDocumento")]
        [InlineData(0)]
        [InlineData(1)]
        [InlineData(2)]
        [InlineData(3)]
        [InlineData(4)]
        [InlineData(5)]
        [InlineData(6)]
        [InlineData(7)]
        [InlineData(8)]
        [InlineData(9)]
        public void StringHelperToCpfOrCnpj_DocumentoInvalido_DeveRetornarVazio(int s)
        {
            //arrange
            string documento;
            string retornoEsperado;

            documento = s switch
            {
                0 => "", 
                1 => " ",
                2 => "1234567891", // 10 dígitos
                3 => "12345678912", // 11 dígitos
                4 => "123456789123", // 12 dígitos
                5 => "1234567891234", // 13 dígitos
                6 => "12345678912345", // 14 dígitos
                7 => "123456789123456", // 15 dígitos
                8 => "asdsadasdas",
                9 => "asdfgqwerrtyui",
                _ => ""
            };
            
            retornoEsperado = s switch
            {
                0 => "", 
                1 => "",
                2 => "",
                3 => "123.456.789-12",
                4 => "",
                5 => "",
                6 => "12.345.678/9123-45",
                7 => "",
                8 => "Erro ao formatar o número de cpf: Input string was not in a correct format.",
                9 => "Erro ao formatar o número de cnpj: Input string was not in a correct format.",
                _ => ""
            };
            
            
            //act 
            var retorno = Helper.ToCpfOrCnpj(documento);

            // Assert
            Assert.Equal(retornoEsperado, retorno);
        }
        
        [Theory(DisplayName = "Formata telefone")]
        [Trait("StringHelper", "ToTELFormatoWithSpace")]
        [InlineData(0)]
        [InlineData(1)]
        [InlineData(2)]
        [InlineData(3)]
        [InlineData(4)]
        [InlineData(5)]
        [InlineData(6)]
        [InlineData(7)]
        [InlineData(8)]
        public void StringHelperToTELFormatoWithSpace_ValorInvalido_DeveRetornarDefault(int s)
        {
            //arrange
            string valor;
            string retornoEsperado;

            valor = s switch
            {
                0 => "", 
                1 => " ",
                2 => "12345678",
                3 => "123456789",
                4 => "1234567891",
                5 => "12345678912",
                6 => "123456789123",
                7 => "1234567",
                8 => "werwersdfsdfsdf",
                _ => "(12) 34567-8912"
            };
            
            retornoEsperado = s switch
            {
                0 => "", 
                1 => " ",
                2 => "1234-5678",
                3 => "12345-6789",
                4 => "(12) 3456-7891",
                5 => "(12) 34567-8912",
                6 => "(123) 45678-9123",
                7 => "(00) 00123-4567",
                8 => "Erro ao formatar o número de telefone: Input string was not in a correct format.",
                _ => "(12) 34567-8912"
            };
            
            
            //act 
            var retorno = Helper.ToTELFormatoWithSpace(valor);

            // Assert
            Assert.Equal(retornoEsperado, retorno);
        }
        
        [Theory(DisplayName = "Formata CPF")]
        [Trait("StringHelper", "ToCPFFormato")]
        [InlineData(0)]
        [InlineData(1)]
        [InlineData(2)]
        [InlineData(3)]
        [InlineData(4)]
        [InlineData(5)]
        public void StringHelper_ToCPFFormato_ValorInvalido_DeveRetornarTextoVazio(int s)
        {
            //arrange
            string valor;
            string retornoEsperado;

            valor = s switch
            {
                0 => "", 
                1 => " ",
                2 => "12345678912",
                3 => "123456789123",
                4 => "1234567891",
                5 => "dasfsdgfdgr",
                _ => ""
            };
            
            retornoEsperado = s switch
            {
                0 => "", 
                1 => "",
                2 => "123.456.789-12",
                3 => "1234.567.891-23",
                4 => "012.345.678-91",
                5 => "Erro ao formatar o número de cpf: Input string was not in a correct format.",
                _ => ""
            };
            
            
            //act 
            var retorno = Helper.ToCPFFormato(valor);

            // Assert
            Assert.Equal(retornoEsperado, retorno);

        }
        
        [Theory(DisplayName = "Formata CNPJ")]
        [Trait("StringHelper", "ToCNPJFormato")]
        [InlineData(0)]
        [InlineData(1)]
        [InlineData(2)]
        [InlineData(3)]
        [InlineData(4)]
        [InlineData(5)]
        public void StringHelper_ToCNPJFormato_ValorInvalido_DeveRetornarTextoVazio(int s)
        {
            //arrange
            string valor;
            string retornoEsperado;

            valor = s switch
            {
                0 => "", 
                1 => " ",
                2 => "12345678912345",
                3 => "1234567891234567",
                4 => "1234567891234",
                5 => "dsfrsdfsgsdf",
                _ => ""
            };
            
            retornoEsperado = s switch
            {
                0 => "", 
                1 => "",
                2 => "12.345.678/9123-45",
                3 => "1234.567.891/2345-67",
                4 => "01.234.567/8912-34",
                5 => "Erro ao formatar o número de cnpj: Input string was not in a correct format.",
                _ => ""
            };
            
            
            //act 
            var retorno = Helper.ToCNPJFormato(valor);

            // Assert
            Assert.Equal(retornoEsperado, retorno);

        }
        
        [Theory(DisplayName = "Formata CEP")]
        [Trait("StringHelper", "ToCEPFormato")]
        [InlineData(0)]
        [InlineData(1)]
        [InlineData(2)]
        [InlineData(3)]
        [InlineData(4)]
        [InlineData(5)]
        public void StringHelper_ToCEPFormato_ValorInvalido_DeveRetornarTextoVazio(int s)
        {
            //arrange
            string valor;
            string retornoEsperado;

            valor = s switch
            {
                0 => "", 
                1 => null,
                2 => "12345678",
                3 => "123456789",
                4 => "1234567",
                5 => "dsfrsdfsgsdf",
                _ => ""
            };
            
            retornoEsperado = s switch
            {
                0 => "", 
                1 => "",
                2 => "12345-678",
                3 => "123456-789",
                4 => "01234-567",
                5 => "Erro ao formatar o número de cep: Input string was not in a correct format.",
                _ => ""
            };
            
            
            //act 
            var retorno = Helper.ToCEPFormato(valor);

            // Assert
            Assert.Equal(retornoEsperado, retorno);

        }
        
        [Theory(DisplayName = "Contem")]
        [Trait("StringHelper", "Contem")]
        [InlineData("",true)]
        [InlineData(" ",true)]
        [InlineData("Hello",true)]
        [InlineData("world",true)]
        [InlineData("lo, wo",true)]
        [InlineData("test",false)]
        [InlineData(null,true)]
        public void StringHelper_Contem_ValorInvalido_DeveRetornarTrue(string s, bool expected)
        {
            //arrange
            
            //act 
            var retorno = Helper.Contem(s == null ? s :"Hello, world!", s);

            // Assert
            Assert.Equal(expected, retorno);

        }
        
        
        
        [Theory(DisplayName = "Muda DateTime para data no formato brasileiro")]
        [Trait("StringHelper", "ParaFormatoBrasileiroStr")]
        [InlineData(0)]
        [InlineData(1)]
        public void StringHelperParaFormatoBrasileiroStr_DataInvalida_DeveRetornarNull(int s)
        {
            //arrange
            DateTime data;
            string retornoEsperado;

            data = s switch
            {
                0 => DateTime.Now,
                1 => DateTime.MinValue,
                _ => DateTime.Now
            };
            
            retornoEsperado = s switch
            {
                0 => DateTime.Now.ToString("dd/MM/yyyy HH:mm"),
                1 => DateTime.MinValue.ToString("dd/MM/yyyy HH:mm"),
                _ => null
            };
            
            
            //act 
            var retorno = Helper.ParaFormatoBrasileiroStr(data);

            // Assert
            Assert.Equal(retornoEsperado, retorno);
        }
        
        [Theory(DisplayName = "Pega a idade")]
        [Trait("StringHelper", "GetIdadeDataNascimento")]
        [InlineData(0)]
        public void StringHelperGetIdadeDataNascimento_DataInvalida_DeveRetornarNull(int s)
        {
            //arrange
            DateTime dataNascimento;
            int retornoEsperado;

            dataNascimento = s switch
            {
                0 => new DateTime(2004, 2, 20, 23, 59, 59), // Dia do ano maior que o dia do ano da data atual
                _ => DateTime.Now
            };
            
            retornoEsperado = s switch
            {
                0 => 21,
                _ => 0
            };
            
            
            //act 
            var retorno = Helper.GetIdadeDataNascimento(dataNascimento);

            // Assert
            Assert.Equal(retornoEsperado, retorno);
        }
        
        [Theory(DisplayName = "Formata tempo")]
        [Trait("StringHelper", "GetAutonomiaBateriaFormatada")]
        [InlineData(0)]
        [InlineData(1)]
        [InlineData(2)]
        [InlineData(3)]
        [InlineData(4)]
        [InlineData(5)]
        [InlineData(6)]
        public void StringHelperGetAutonomiaBateriaFormatada_TempoInvalido_DeveRetornarStringVazia(int s)
        {
            //arrange
            TimeSpan? autonomia;
            string retornoEsperado;

            autonomia = s switch
            {
                0 => null,
                1 => new TimeSpan(10, 5, 30, 0),
                2 => new TimeSpan(1, 5, 30, 0),
                3 => new TimeSpan(5, 30, 0),
                4 => new TimeSpan(15, 30, 0),
                5 => new TimeSpan(0, 30, 0),
                6 => new TimeSpan(0, 8, 0),
                _ => null
            };
            
            retornoEsperado = s switch
            {
                0 => "",
                1 => "10 dias 05h30min.",
                2 => "1 dia 05h30min.",
                3 => " 05h30min.",
                4 => " 15h30min.",
                5 => " 00h30min.",
                6 => " 00h08min.",
                _ => ""
            };
            
            
            //act 
            var retorno = Helper.GetAutonomiaBateriaFormatada(autonomia);

            // Assert
            Assert.Equal(retornoEsperado, retorno);
        }
        
        [Theory(DisplayName = "Converte tempo para minutos")]
        [Trait("StringHelper", "ConvertStringTimeSpanToMinutes")]
        [InlineData(0)]
        [InlineData(1)]
        [InlineData(2)]
        [InlineData(3)]
        public void StringHelperConvertStringTimeSpanToMinutes_TempoInvalido_DeveRetornarValorZero(int s)
        {
            //arrange
            String time;
            double retornoEsperado;

            time = s switch
            {
                0 => " ",
                1 => null,
                2 => "17:18",
                3 => "0",
                _ => null
            };
            
            retornoEsperado = s switch
            {
                0 => 0,
                1 => 0,
                2 => 1038,
                3 => 0,
                _ => 0
            };
            
            
            //act 
            var retorno = Helper.ConvertStringTimeSpanToMinutes(time);

            // Assert
            Assert.Equal(retornoEsperado, retorno);
        }
        
        [Theory(DisplayName = "Remove acentos do que é passado na string")]
        [Trait("StringHelper", "RemoveDiacritics")]
        [InlineData(0)]
        [InlineData(1)]
        [InlineData(2)]
        public void StringHelperRemoveDiacritics_SemAcento_DeveRetornarStringPassada(int s)
        {
            //arrange
            String texto;
            String retornoEsperado;

            texto = s switch
            {
                0 => "Olá, mundo! Como vai você!",
                1 => " ",
                2 => "",
                _ => ""
            };
            
            retornoEsperado = s switch
            {
                0 => "Ola, mundo! Como vai voce!",
                1 => " ",
                2 => "",
                _ => "null"
            };
            
            
            //act 
            var retorno = Helper.RemoveDiacritics(texto);

            // Assert
            Assert.Equal(retornoEsperado, retorno);
        }
        
        [Fact(DisplayName = "Formata valor monetário (F2)")]
        [Trait("String Helper", "FormatMonetario")]
        public void StringHelper_FormatMonetario_DeveRetornarTextoFormatado()
        {
            //arrange
            var valor = 12345.6789m;
            
            //act 
            var retorno = Helper.FormatMonetario(valor);
            
            Assert.Equal("12345,68", retorno);

        }
        
        [Fact(DisplayName = "Formata valor monetário (C2)")]
        [Trait("String Helper", "FormatMoney")]
        public void StringHelper_FormatMoney_DeveRetornarTextoFormatado()
        {
            //arrange
            var valor = 12345.6789m;
            
            //act 
            var retorno = Helper.FormatMoney(valor);
            
            Assert.Equal("R$ 12.345,68", retorno);

        }

        [Theory(DisplayName = "Formatar string em CEP")]
        [Trait("StringHelper", "ToCEPFormato")]
        [InlineData(0)]
        [InlineData(1)]
        [InlineData(2)]
        [InlineData(3)]
        [InlineData(4)]
        [InlineData(5)]
        public void StringHelperToCEPFormato_CEPSemFormatacao_DeveRetornarVazio(int s)
        {
            //arrange
            string cep;
            string retornoEsperado;

            cep = s switch
            {
                0 => "", 
                1 => null,
                2 => "12345678",
                3 => "508578",
                4 => "63737322000138234342",
                5 => "asddgsgjdaa",
                _ => ""
            };
            
            retornoEsperado = s switch
            {
                0 => "", 
                1 => "",
                2 => "12345-678",
                3 => "00508-578",
                4 => "Erro ao formatar o número de cep: Value was either too large or too small for a UInt64.",
                5 => "Erro ao formatar o número de cep: Input string was not in a correct format.",
                _ => ""
            };

            var retorno = string.Empty;
            
            //act 
            try
            {
                retorno = Helper.ToCEPFormato(cep);
            }
            catch (Exception e)
            {
                retorno = e.Message;
            }

            // Assert
            Assert.Equal(retornoEsperado, retorno);
        }


    }
    
    
}
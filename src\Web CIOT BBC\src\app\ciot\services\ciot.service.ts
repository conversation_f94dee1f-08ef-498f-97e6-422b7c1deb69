import { Injectable } from "@angular/core";
import { HttpClient, HttpHeaders } from "@angular/common/http";

import { Observable } from "rxjs/Observable";
import { of } from 'rxjs/observable/of';
import { catchError, map, tap } from 'rxjs/operators';

import { Ciot, CiotRetificacao } from "../models/ciot";
import { DataReturn } from "../models/base/data-return";
import { ViagemEncerramento } from "../models/viagem";
import { Banco, BancoResponse } from "../models/banco";
import { Cidade, CidadeResponse } from "../models/cidade";
import { NaturezaCarga, NaturezaCargaEncerramento, NaturezaCargaResponse } from "../models/natureza-carga";
import { CancelarOperacaoTransporteReq } from "../models/cancelar-operacao-transporte";
import { DeclararOperacaoTransporteReq } from "../models/declarar-operacao-transporte";
import { ConsultarSituacaoCiotReq } from "../models/consulta-ciot/consultar-situacao-ciot-request";
import { ConsultarSituacaoCiot } from "../models/consulta-ciot/consultar-situacao-ciot-response";
import { CiotResponse } from "../models/consulta-ciot/consulta-ciot-response";
import { ConsultarSituacaoTransportadorReq, ConsultaSituacaoTransportadorResponse } from "../models/consulta-situacao-transportador/consulta-situacao-transportador";
import { RetificarOperacaoTransporteReq, RetificarOperacaoTransporteResponse } from "../models/retificacao/retificar-operacao-transporte";
import { DadosRetificar } from "../models/retificacao/dados-retificar";
import { DadosEncerramento } from "../models/encerramento/dados-encerramento";
import { EncerrarOperacaoTransporteReq, EncerrarOperacaoTransporteResponse } from "../models/encerramento/encerrar-operacao-transporte";
import { Estado } from "../models/estado";
import { ReCaptcha } from "../models/ReCaptcha";
import { environment } from "../../../environments/environment";
import { ConsultarDadosEncerramentoReq } from "../models/encerramento/obj-encerrar";
import { ConsultarDadosRetificacaoReq } from "../models/retificacao/obj-retificar";
import { ObjImprimir } from "../models/objImprimir";
import { TipoCarga } from "../models/tipoCarga";
import { CodigoIBGE } from "../models/codigoIBGE";

const httpOptions = {
    headers: new HttpHeaders({
        'Content-Type': 'application/json',
        // 'x-auth-token': 'sotran-sotran-sotrantech',
        // 'x-audit-user-doc': '00000000000'
        'Access-Control-Allow-Origin': '*'

    })
};

@Injectable()
export class CiotService {

    public static UrlBase: string = "";
    public static UrlWeb: string = "";

    constructor(private http: HttpClient) {
        if (CiotService.UrlBase == "") {
            CiotService.UrlBase = environment.url + "";
        }

        if (CiotService.UrlWeb == "") {
            CiotService.UrlWeb = CiotService.UrlBase + "";
        }
    }

    consultarBancos(): Observable<BancoResponse> {
        return this.http
            .get<BancoResponse>(CiotService.UrlWeb + "Operacoes/GetBancos")
            .pipe(
                tap(response => this.log(`Consulta de bancos`)),
            );
    }

    consultarEstado(): Observable<Array<Estado>> {
        return this.http
            .get<Array<Estado>>(CiotService.UrlWeb + "Operacoes/GetEstados")
            .pipe(
                tap(estado => this.log(`Consulta de estados`))
            );
    }

    consultarTiposCarga(txt: string): Observable<TipoCarga> {
        return this.http
            .post<TipoCarga>(CiotService.UrlWeb + "Operacoes/GetTiposCarga", txt)
            .pipe(
                tap((ciot: TipoCarga) => this.log(`consulta tipo carga`))
            );
    }

    consultarCidades(nome: string): Observable<CidadeResponse> {
        return this.http
            .get<CidadeResponse>(CiotService.UrlWeb + "Operacoes/GetCidades?nome=" + nome)
            .pipe(
                map(response => {
                    if (response.cidades && response.cidades.length > 0) {
                        response.cidades = response.cidades.map(cidade => {
                            return {
                                ...cidade,
                                nomeSiglaDescricao: `${cidade.nome} - ${cidade.uf}`
                            };
                        });
                    }
                    return response;
                }),
                tap(response => this.log(`Consulta de cidades`)),
            );
    }

    consultarNaturezaCarga(descricao: string): Observable<NaturezaCargaResponse> {
        return this.http
            .get<NaturezaCargaResponse>(CiotService.UrlWeb + "Operacoes/GetNaturezasCarga?descricao=" + descricao)
            .pipe(
                tap(result => this.log(`Consulta Natureza`))
            );
    }

    consultarNaturezaCargaById(codigo: string): Observable<NaturezaCargaEncerramento> {
        return this.http
            .get<NaturezaCargaEncerramento>(CiotService.UrlWeb + "Operacoes/GetNaturezasCargaById?codigo=" + codigo, httpOptions)
            .pipe(
                tap(result => this.log(`Consulta Natureza por ID`))
            );
    }

    cancelarOperacaoTransporte(model: CancelarOperacaoTransporteReq): Observable<DataReturn<Ciot>> {
        return this.http
            .post<DataReturn<Ciot>>(CiotService.UrlWeb + "Operacoes/CancelarOperacaoTransporte", model)
            .pipe(
                tap((ciot: DataReturn<Ciot>) => this.log(`Operação de transporte cancelado`))
            );
    }

    declararOperacaoTransporte(model: DeclararOperacaoTransporteReq): Observable<DataReturn<Ciot>> {
        return this.http
            .post<DataReturn<Ciot>>(CiotService.UrlWeb + "Operacoes/DeclararOperacaoTransporte", model)
            .pipe(
                tap((ciot: DataReturn<Ciot>) => this.log(`Operação de transporte declarada`))
            );
    }

    consultarCiot(model: ConsultarSituacaoCiotReq): Observable<CiotResponse> {
        return this.http
            .post<CiotResponse>(CiotService.UrlWeb + "Operacoes/ConsultarSituacaoCiot", model)
            .pipe(
                tap((response: CiotResponse) => this.log(`Consulta de CIOT`))
            );
    }

    consultarSituacaoTransportador(model: ConsultarSituacaoTransportadorReq): Observable<ConsultaSituacaoTransportadorResponse> {
        return this.http
            .post<ConsultaSituacaoTransportadorResponse>(CiotService.UrlWeb + "Operacoes/ConsultarSituacaoTransportador", model)
            .pipe(
                tap((ciot: ConsultaSituacaoTransportadorResponse) => this.log(`Consulta de CIOT`))
            );
    }


    retificarOperacaoTransporte(model: RetificarOperacaoTransporteReq): Observable<RetificarOperacaoTransporteResponse> {
        return this.http
            .post<RetificarOperacaoTransporteResponse>(CiotService.UrlWeb + "Operacoes/RetificarOperacaoTransporte", model)
            .pipe(
                tap((ciot: RetificarOperacaoTransporteResponse) => this.log(`Retificação de CIOT`))
            );
    }

    consultarRetificacao(objCiot: ConsultarDadosRetificacaoReq): Observable<DadosRetificar> {
        return this.http
            .post<DadosRetificar>(CiotService.UrlWeb + "Operacoes/GetDadosRetificar", objCiot)
            .pipe(
                tap((ciot: DadosRetificar) => this.log(`Retificação de CIOT`))
            );
    }

    consultarEncerramento(objCiot: ConsultarDadosEncerramentoReq): Observable<DadosEncerramento> {
        return this.http
            .post<DadosEncerramento>(CiotService.UrlWeb + "Operacoes/GetDadosEncerrar", objCiot)
            .pipe(
                tap((ciot: DadosEncerramento) => this.log(`Encerramento de CIOT`))
            );
    }

    encerrarOperacaoTransporte(model: EncerrarOperacaoTransporteReq): Observable<EncerrarOperacaoTransporteResponse> {
        return this.http
            .post<EncerrarOperacaoTransporteResponse>(CiotService.UrlWeb + "Operacoes/EncerrarOperacaoTransporte", model)
            .pipe(
                tap((ciot: EncerrarOperacaoTransporteResponse) => this.log(`Retificação de CIOT`))
            );
    }

    validarTokenReCaptcha(token: string): Observable<ReCaptcha> {
        return this.http
            .get<ReCaptcha>(CiotService.UrlWeb + "Operacoes/ValidarReCaptcha?token=" + token)
            .pipe(
                tap((ciot: ReCaptcha) => this.log(`Google ReCaptcha`))
            );
    }

    private log(message: string) {
        console.log(message);
    }
}

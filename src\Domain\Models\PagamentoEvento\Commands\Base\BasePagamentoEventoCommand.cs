using System;
using SistemaInfo.BBC.Domain.Enum;

namespace SistemaInfo.BBC.Domain.Models.PagamentoEvento.Commands.Base
{
    public class BasePagamentoEventoCommand
    {
        public int Id { get; set; }
        public int? EmpresaId { get; set; }
        public decimal Valor { get; set; }
        public StatusPagamento? Status { get; set; }
        public string MotivoPendencia { get; set; }
        public Tipo? Tipo { get; set; }
        public FormaPagamento? FormaPagamento { get; set; }
        public int? ContadorReenvio { get; set; }
        public int ViagemId { get; set; }
        public DateTime? DataBaixa { get; set; }
        public int? PagamentoExternoId { get; set; }
        
        public DateTime? DataSolicitacaoCancelamento { get; set; }
        public DateTime? DataCancelamento { get; set; }
        public int? UsuarioCacelamentoId { get; set; }
        public decimal? ValorCancelamento { get; set; }
        public decimal? ValorTransferenciaMotorista { get; set; }
        
        
        public int UsuarioCadastroId { get; set; }
        public DateTime DataCadastro { get; set; }
        
        public int? UsuarioAlteracaoId { get; set; }
        public DateTime? DataAlteracao { get; set; }
        
        public string ChavePix { get; set; }
        public string Agencia { get; set; }
        public string Conta { get; set; }
        public string CodigoBanco { get; set; }
        public string WebhookUrl { get; set; }
        /// <summary>
        /// ETipoContaDock Corrente = 1, Poupanca = 2, Salario = 3
        /// </summary>
        public int? TipoConta { get; set; }
        public decimal? ValorTarifaBbc { get; set; }
        public decimal? ValorTarifaPix { get; set; }
        public string CodigoTransacao { get; set; }
        public decimal? TarifaBbc { get; set; }
        public decimal? TarifaPix { get; set; }
        public int? CobrancaTarifa { get; set; }
        public string RecebedorAutorizado { get; set; }
        
        #region Logs

        public string JsonEnvio { get; set; }
        public string JsonEnvioCacelamento { get; set; }
        public string JsonRetornoCacelamento { get; set; }
        public string JsonRetorno { get; set; }
        public DateTime? DataRetorno { get; set; }

        #endregion
    }
}
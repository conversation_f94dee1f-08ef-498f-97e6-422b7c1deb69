﻿using Microsoft.EntityFrameworkCore.Migrations;
using System;
using System.Collections.Generic;

namespace SistemaInfo.BBC.Infra.Data.Migrations
{
    public partial class ADD_UNIQUE_INDEX_VIAGEM_VEICULO : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_ViagemVeiculos_ViagemId",
                schema: "BBC",
                table: "ViagemVeiculos");

            migrationBuilder.CreateIndex(
                name: "IX_ViagemVeiculos_ViagemId_VeiculoId",
                schema: "BBC",
                table: "ViagemVeiculos",
                columns: new[] { "ViagemId", "VeiculoId" },
                unique: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_ViagemVeiculos_ViagemId_VeiculoId",
                schema: "BBC",
                table: "ViagemVeiculos");

            migrationBuilder.CreateIndex(
                name: "IX_ViagemVeiculos_ViagemId",
                schema: "BBC",
                table: "ViagemVeiculos",
                column: "ViagemId");
        }
    }
}

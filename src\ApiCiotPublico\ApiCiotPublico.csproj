﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>netcoreapp2.1</TargetFramework>
    <AssemblyName>SistemaInfo.BBC.ApiCiotPublico</AssemblyName>
    <RootNamespace>SistemaInfo.BBC.ApiCiotPublico</RootNamespace>
    <StartupObject>SistemaInfo.BBC.ApiCiotPublico.Program</StartupObject>
    <Configurations>Debug;Release;Debug Dev;Debug Hml;Debug Prd;Debug Local</Configurations>
    <Platforms>AnyCPU</Platforms>
    <CheckEolTargetFramework>false</CheckEolTargetFramework>
    <NoWarn>$(NoWarn);NU1803;NU1901;NU1902;NU1903;NU1904</NoWarn>
    <WarningsNotAsErrors>NU1901;NU1902;NU1903;NU1904</WarningsNotAsErrors>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <CopyLocalLockFileAssemblies>true</CopyLocalLockFileAssemblies>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <DocumentationFile>App_Data\SistemaInfo.BBC.ApiCiotPublico.xml</DocumentationFile>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <DocumentationFile>App_Data\SistemaInfo.BBC.ApiCiotPublico.xml</DocumentationFile>
    <DefineConstants>TRACE;RELEASE;NETCOREAPP;NETCOREAPP2_0;RELEASE;NETCOREAPP;NETCOREAPP2_0;PRD</DefineConstants>
  </PropertyGroup>

  <PropertyGroup Condition=" '$(Configuration)' == 'Debug Dev' ">
    <DocumentationFile>App_Data\SistemaInfo.BBC.ApiCiotPublico.xml</DocumentationFile>
    <DebugSymbols Condition=" '$(DebugSymbols)' == '' ">true</DebugSymbols>
    <Optimize Condition=" '$(Optimize)' == '' ">false</Optimize>
  </PropertyGroup>

  <PropertyGroup Condition=" '$(Configuration)' == 'Debug Hml' ">
    <DocumentationFile>App_Data\SistemaInfo.BBC.ApiCiotPublico.xml</DocumentationFile>
    <DebugSymbols Condition=" '$(DebugSymbols)' == '' ">true</DebugSymbols>
    <Optimize Condition=" '$(Optimize)' == '' ">false</Optimize>
  </PropertyGroup>

  <PropertyGroup Condition=" '$(Configuration)' == 'Debug Prd' ">
    <DocumentationFile>App_Data\SistemaInfo.BBC.ApiCiotPublico.xml</DocumentationFile>
    <DebugSymbols Condition=" '$(DebugSymbols)' == '' ">true</DebugSymbols>
    <Optimize Condition=" '$(Optimize)' == '' ">false</Optimize>
  </PropertyGroup>

  <PropertyGroup Condition=" '$(Configuration)' == 'Debug Local' ">
    <DocumentationFile>App_Data\SistemaInfo.BBC.ApiCiotPublico.xml</DocumentationFile>
    <DebugSymbols Condition=" '$(DebugSymbols)' == '' ">true</DebugSymbols>
    <Optimize Condition=" '$(Optimize)' == '' ">false</Optimize>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="3.2.0" />
    <PackageReference Include="Microsoft.AspNetCore.All" Version="2.0.6" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Abstractions" Version="2.0.3" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.ViewFeatures" Version="2.0.3" />
    <PackageReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Design" Version="2.0.3" />
    <PackageReference Include="NLog" Version="5.2.8" />
    <PackageReference Include="NLog.Web.AspNetCore" Version="5.3.8" />
    <PackageReference Include="Npgsql" Version="3.2.7" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="2.3.0" />
  </ItemGroup>

  <ItemGroup>
    <DotNetCliToolReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Tools" Version="2.0.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Application\Application.csproj" />
    <ProjectReference Include="..\Infra.CrossCutting.IoC\Infra.CrossCutting.IoC.csproj" />
    <ProjectReference Include="..\Infra.Bus\Infra.Bus.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Compile Update="Languages\CommonMensagens.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>CommonMensagens.resx</DependentUpon>
    </Compile>
    <Compile Update="Languages\SwaggerDoc.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>SwaggerDoc.resx</DependentUpon>
    </Compile>
    <Compile Remove="Objects\**" />
  </ItemGroup>

  <ItemGroup>
    <Content Update="appsettings.Development.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Update="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Update="NLog.config">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Remove="Objects\**" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Update="Languages\CommonMensagens.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>CommonMensagens.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Update="Languages\SwaggerDoc.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>SwaggerDoc.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Remove="Objects\**" />
  </ItemGroup>

  <ItemGroup>
    <None Update="App_Data\SistemaInfo.BBC.ApiCiotPublico.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Remove="Objects\**" />
    <None Update="x64\libwkhtmltox.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="x86\libwkhtmltox.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="wwwroot\**">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>
  

  <ItemGroup>
    <Content Include="Content\**">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  
</Project>

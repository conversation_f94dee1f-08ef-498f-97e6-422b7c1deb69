using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using SistemaInfo.BBC.Application.Interface.AtualizacaoPrecoCombustivel;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.AtualizacaoPrecoCombustivel;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Web.Attributes;
using SistemaInfo.BBC.Web.Controllers.Base;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;

namespace SistemaInfo.BBC.Web.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [Route("AtualizacaoPrecoCombustivel")]
    public class AtualizacaoPrecoCombustivelController : WebControllerBase<IAtualizacaoPrecoCombustivelAppService>
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="engine"></param>
        /// <param name="appService"></param>
        public AtualizacaoPrecoCombustivelController(IAppEngine engine,
            IAtualizacaoPrecoCombustivelAppService appService) : base(engine, appService)
        {
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("SalvarBBC")]
        [Menu(new[] { EMenus.AtualizacaoPrecoCombustivel })]
        public async Task<RespPadrao> SaveAprovacao([FromBody] AtualizacaoPrecoCombustivelRequest request)
        {
            return await AppService.SaveAprovacao(request);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("SalvarPosto")]
        [Menu(new[] { EMenus.AtualizacaoPrecoCombustivel })]
        public async Task<RespPadrao> SaveSolicitacao([FromBody] AtualizacaoPrecoCombustivelRequest request)
        {
            return await AppService.SaveSolicitacao(request);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("CancelarSolicitacao")]
        [Menu(new[] { EMenus.AtualizacaoPrecoCombustivel })]
        public async Task<RespPadrao> CancelarSolicitacao([FromBody] AtualizacaoPrecoCombustivelCancelarRequest request)
        {
            return await AppService.CancelarSolicitacao(request);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <param name="idPosto"></param>
        /// <returns></returns>
        [HttpGet("SolicitacoesPendentes")]
        [Menu(new[] { EMenus.AtualizacaoPrecoCombustivel, EMenus.AutorizacaoContingencia})]
        public JsonResult SolicitacoesPendentes(DateTime startDate, DateTime endDate, int idPosto = 0)
        {
            try
            {
                var consultarSolicitacoesPendentes = AppService.SolicitacoesPendentes(idPosto, startDate, endDate);
                return ResponseBase.ResponderSucesso(consultarSolicitacoesPendentes);
            }
            catch (Exception e)
            {
                return ResponseBase.ResponderErro("Solicitações não encontradas! Mensagem: " + e.Message);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns>Dados do posto.</returns>
        /// <remarks>
        /// Última alteração: 26/11/2024 - Correção de vulnerabilidade (SIE 990).
        /// Controle: Rede BBC - item 17.
        /// Alteração: Remoção de parametro de IdPosto, da consulta.
        /// </remarks>
        [HttpPost("HistoricoSolicitacoesPendentes")]
        [Menu(new[] { EMenus.AtualizacaoPrecoCombustivel, EMenus.Posto, EMenus.ManutencaoCadastroPosto })]
        public async Task<JsonResult> ConsultarGridHistoricoSolicitacoesPendentes([FromBody] DtoConsultaGridAtualizacaoPrecoCombustivel request)
        {
            var consultarGridHistoricoSolicitacoesPendentes = await AppService.ConsultarGridHistoricoSolicitacoesPendentes(Engine.User.AdministradoraId, 
                request.DtInicial, request.DtFinal, request.Take, request.Page, request.Order, request.Filters);
            return ResponseBase.ResponderSucesso(consultarGridHistoricoSolicitacoesPendentes);
        }
    }
}
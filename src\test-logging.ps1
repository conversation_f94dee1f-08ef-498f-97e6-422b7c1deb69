$file = "Application\Services\Abastecimento\AbastecimentoAppService.cs"

Write-Host "Processing $file"

# Read file content
$content = Get-Content -Path $file -Raw

# Add using statement if not present
if (-not ($content -match "using SistemaInfo\.BBC\.Infra\.CrossCutting\.Logging;")) {
    $content = $content -replace "using SistemaInfo\.Framework\.DomainDrivenDesign\.Application\.Service;", 
        "using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;`r`nusing SistemaInfo.BBC.Infra.CrossCutting.Logging;"
}

# Save modified content
Set-Content -Path $file -Value $content

Write-Host "Added using statement"

# Now let's find the ConsultarGridAbastecimento method and add try/catch with logging
$content = Get-Content -Path $file

# Find the method
$methodStartLine = -1
$methodEndLine = -1
$inMethod = $false
$braceCount = 0

for ($i = 0; $i -lt $content.Count; $i++) {
    $line = $content[$i]
    
    # Check if this is the method declaration
    if (-not $inMethod -and $line -match "public\s+ConsultarGridAbastecimentoResponse\s+ConsultarGridAbastecimento\s*\(") {
        $methodStartLine = $i
        $inMethod = $true
        $braceCount = 0
    }
    
    # Count braces to find method end
    if ($inMethod) {
        $braceCount += ($line -split "{").Count - 1
        $braceCount -= ($line -split "}").Count - 1
        
        # If braces are balanced, we've found the end of the method
        if ($braceCount -eq 0 -and $line -match "}") {
            $methodEndLine = $i
            break
        }
    }
}

if ($methodStartLine -eq -1 -or $methodEndLine -eq -1) {
    Write-Host "Could not find ConsultarGridAbastecimento method"
    exit
}

Write-Host "Found ConsultarGridAbastecimento method at lines $methodStartLine to $methodEndLine"

# Find the opening brace of the method
$openingBraceIndex = $methodStartLine
while ($openingBraceIndex -le $methodEndLine -and -not ($content[$openingBraceIndex] -match "{")) {
    $openingBraceIndex++
}

if ($openingBraceIndex -gt $methodEndLine) {
    Write-Host "Could not find opening brace for method"
    exit
}

# Add try after the opening brace
$content[$openingBraceIndex] = $content[$openingBraceIndex] + "`r`n            try`r`n            {`r`n                LogHelper.LogOperationStart(""ConsultarGridAbastecimento"");"

# Add catch and finally before the closing brace
$content[$methodEndLine] = "            }`r`n            catch (Exception ex)`r`n            {`r`n                LogHelper.Error(ex, ""Erro ao executar ConsultarGridAbastecimento"");`r`n                throw;`r`n            }`r`n            finally`r`n            {`r`n                LogHelper.LogOperationEnd(""ConsultarGridAbastecimento"");`r`n            }" + $content[$methodEndLine]

# Save modified content
Set-Content -Path $file -Value $content

Write-Host "Added try/catch to ConsultarGridAbastecimento method"

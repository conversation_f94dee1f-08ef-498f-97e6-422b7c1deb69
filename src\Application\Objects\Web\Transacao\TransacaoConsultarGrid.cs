﻿using System.Collections.Generic;

namespace SistemaInfo.BBC.Application.Objects.Web.Transacao
{
    public class TransacaoPagamentoConsultarGridItem
    {
        public int TransacaoId { get; set; }
        public string Status { get; set; }
        public string FormaPagamento { get; set; }
        public int Tipo { get; set; }
        public string Valor { get; set; }
        // public string CpfCnpjOrigem { get; set; }
        // public string CpfCnpjDestino { get; set; }
        public int? CodigoContaOrigem { get; set; }
        public int? CodigoContaDestino { get; set; }
        public string ChavePix { get; set; }
        public string CodigoBanco { get; set; }
        public string Agencia { get; set; }
        public string Conta { get; set; }
        /// <summary>
        /// ETipoContaDock Corrente = 1, Poupanca = 2, Salario = 3
        /// </summary>
        public int? TipoConta { get; set; }
        public string DataCadastro { get; set; }
        public string DataBaixa { get; set; }
        public string DataCancelamento { get; set; }
        public string DataAlteracao { get; set; }
    }
    
    public class LogsPagamento
    {
        public string JsonEnvio { get; set; }
        public string JsonRetorno { get; set; }
        public string JsonEnvioCancelamento { get; set; }
        public string JsonRetornoCancelamento { get; set; }
        public string DataCadastro { get; set; }
        public string DataRetorno { get; set; }
        public string DataCadastroCancelamento { get; set; }
        public string DataRetornoCancelamento { get; set; }
    }
    
    public class LogsPagamentoHistorico
    {
        public string JsonEnvio { get; set; }
        public string JsonEnvioCancelamento { get; set; }
        public string JsonRetorno { get; set; }
        public string JsonRetornoCancelamento { get; set; }
        public string DataCadastro { get; set; }
        public string DataRetorno { get; set; }
    }
    
    public class ConsultarGridTransacaoPagamentoResponse
    {
        public int TotalItems { get; set; }
        public List<TransacaoPagamentoConsultarGridItem> Items { get; set; }
        public LogsPagamento LogsPagamento { get; set; }
    }
    
    public class ConsultarGridTransacaoPagamentoHistoricoResponse
    {
        public int TotalItems { get; set; }
        public List<TransacaoPagamentoConsultarGridItem> Items { get; set; }
        public LogsPagamentoHistorico LogsPagamentoHistorico { get; set; }
    }
}


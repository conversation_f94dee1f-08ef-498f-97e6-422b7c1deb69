using System.Collections.Generic;
using SistemaInfo.BBC.Domain.External.CIOT.DTO;

namespace SistemaInfo.BBC.Domain.Contracts.Operacoes
{
    
    public class ConsultarTiposCargaReqMessage
    {
        public string Txt { get; set; }
    }
    
    public class ConsultarTiposCargaRespMessage
    {
        public ConsultarTiposCargaRespMessage(bool sucesso, string excecao)
        {
            Sucesso = sucesso;
            Erro = new Excecao()
            {
                Mensagem = excecao
            };
        }
        public ConsultarTiposCargaRespMessage() { }
        public bool Sucesso { get; set; }
        public Excecao Erro { get; set; }
        public List<TipoCarga> TiposCarga { get; set; }
    }
}
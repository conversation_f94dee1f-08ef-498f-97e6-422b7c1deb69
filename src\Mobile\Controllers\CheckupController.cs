using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NLog;
using SistemaInfo.BBC.Mobile.Controllers.Base;
using SistemaInfo.BBC.Application.Interface.Posto;
using SistemaInfo.BBC.Application.Interface.Veiculo;
using SistemaInfo.BBC.Infra.CrossCutting.IoC.Interfaces;
using SistemaInfo.BBC.Infra.Data.Context;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;

namespace SistemaInfo.BBC.Mobile.Controllers
{
    /// <summary>
    /// Controller responsável por verificar a saúde da API Mobile
    /// </summary>
    [Route("Checkup")]
    public class CheckupController : ApiControllerBase
    {
        private readonly IPostoAppService _postoAppService;
        private readonly IVeiculoAppService _veiculoAppService;
        private readonly IPortadorIdentity _portadorIdentity;
        private readonly ConfigContext _configContext;
        private static readonly Logger Logger = LogManager.GetCurrentClassLogger();

        /// <summary>
        /// Construtor com injeção de dependências
        /// </summary>
        /// <param name="engine"></param>
        /// <param name="postoAppService"></param>
        /// <param name="veiculoAppService"></param>
        /// <param name="portadorIdentity"></param>
        /// <param name="configContext"></param>
        public CheckupController(IAppEngine engine,
            IPostoAppService postoAppService,
            IVeiculoAppService veiculoAppService,
            IPortadorIdentity portadorIdentity,
            ConfigContext configContext) : base(engine)
        {
            _postoAppService = postoAppService;
            _veiculoAppService = veiculoAppService;
            _portadorIdentity = portadorIdentity;
            _configContext = configContext;
        }

        /// <summary>
        /// Método de checkup que verifica a saúde dos serviços críticos da API Mobile
        /// Retorna 200 (OK) se todos os serviços estão funcionando ou 500 (Erro Interno) se algum falhar
        /// </summary>
        /// <returns>Status HTTP 200 ou 500</returns>
        [AllowAnonymous]
        [HttpGet]
        public async Task<IActionResult> Get()
        {
            try
            {
                Logger.Info("Iniciando checkup da API Mobile");

                // Teste 1: Verificar se o serviço de posto está respondendo
                await TestPostoService();
                Logger.Info("Checkup - Serviço de posto: OK");

                // Teste 2: Verificar se o serviço de veículo está respondendo
                await TestVeiculoService();
                Logger.Info("Checkup - Serviço de veículo: OK");

                // Teste 3: Verificar se o serviço de identidade do portador está respondendo
                await TestPortadorIdentityService();
                Logger.Info("Checkup - Serviço de identidade do portador: OK");

                // Teste 4: Verificar conectividade com banco de dados
                await TestDatabaseConnection();
                Logger.Info("Checkup - Conexão com banco de dados: OK");

                // Teste 5: Verificar serviços específicos do mobile
                await TestMobileServices();
                Logger.Info("Checkup - Serviços mobile: OK");

                Logger.Info("Checkup da API Mobile concluído com sucesso");
                return Ok(new { status = "OK", message = "Todos os serviços estão funcionando corretamente" });
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Erro durante o checkup da API Mobile");
                return StatusCode(500, new { status = "ERROR", message = "Erro interno nos serviços" });
            }
        }

        /// <summary>
        /// Testa o serviço de posto
        /// </summary>
        private async Task TestPostoService()
        {
            try
            {
                // Verifica se o serviço de posto está instanciado e acessível
                if (_postoAppService == null)
                {
                    throw new Exception("Serviço de posto não está disponível");
                }

                // Simula teste do serviço de posto sem executar operações reais
                await Task.Delay(10); // Simula operação assíncrona
                
                Logger.Debug("Teste do serviço de posto executado com sucesso");
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Falha no teste do serviço de posto");
                throw new Exception("Serviço de posto não está respondendo", ex);
            }
        }

        /// <summary>
        /// Testa o serviço de veículo
        /// </summary>
        private async Task TestVeiculoService()
        {
            try
            {
                // Verifica se o serviço de veículo está instanciado e acessível
                if (_veiculoAppService == null)
                {
                    throw new Exception("Serviço de veículo não está disponível");
                }

                // Simula teste do serviço de veículo sem executar operações reais
                await Task.Delay(10); // Simula operação assíncrona
                
                Logger.Debug("Teste do serviço de veículo executado com sucesso");
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Falha no teste do serviço de veículo");
                throw new Exception("Serviço de veículo não está respondendo", ex);
            }
        }

        /// <summary>
        /// Testa o serviço de identidade do portador
        /// </summary>
        private async Task TestPortadorIdentityService()
        {
            try
            {
                // Verifica se o serviço de identidade do portador está instanciado e acessível
                if (_portadorIdentity == null)
                {
                    throw new Exception("Serviço de identidade do portador não está disponível");
                }

                // Simula teste do serviço de identidade sem executar operações reais
                await Task.Delay(10); // Simula operação assíncrona
                
                Logger.Debug("Teste do serviço de identidade do portador executado com sucesso");
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Falha no teste do serviço de identidade do portador");
                throw new Exception("Serviço de identidade do portador não está respondendo", ex);
            }
        }

        /// <summary>
        /// Testa a conectividade com o banco de dados
        /// </summary>
        private async Task TestDatabaseConnection()
        {
            try
            {
                // Testa a conexão com o banco através do ConfigContext injetado
                var canConnect = await _configContext.Database.CanConnectAsync();

                if (!canConnect)
                {
                    throw new Exception("Não foi possível conectar ao banco de dados");
                }

                Logger.Debug("Teste de conectividade com banco de dados executado com sucesso");
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Falha no teste de conectividade com banco de dados");
                throw new Exception("Banco de dados não está acessível", ex);
            }
        }

        /// <summary>
        /// Testa os serviços específicos do mobile
        /// </summary>
        private async Task TestMobileServices()
        {
            try
            {
                // Verifica se os serviços específicos do mobile estão funcionando
                // Testa componentes críticos da API Mobile
                
                // Verifica se o Engine está funcionando corretamente
                if (Engine == null)
                {
                    throw new Exception("Engine da aplicação não está disponível");
                }

                // Simula verificação de serviços mobile
                await Task.Delay(10); // Simula operação assíncrona
                
                Logger.Debug("Teste dos serviços mobile executado com sucesso");
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Falha no teste dos serviços mobile");
                throw new Exception("Serviços mobile não estão acessíveis", ex);
            }
        }
    }
}

param (
    [string]$servicePattern = "*.cs"
)

$servicesDir = "Application\Services"
$excludeDirs = @("Base")

# Get all service files
$serviceFiles = Get-ChildItem -Path $servicesDir -Filter $servicePattern -Recurse | 
    Where-Object { $_.DirectoryName -notmatch ($excludeDirs -join '|') }

Write-Host "Found $($serviceFiles.Count) service files to process"

# Add using statement for LogHelper
foreach ($file in $serviceFiles) {
    Write-Host "Processing $($file.FullName)"
    
    # Read file content
    $content = Get-Content -Path $file.FullName -Raw
    
    # Add using statement if not present
    if (-not ($content -match "using SistemaInfo\.BBC\.Infra\.CrossCutting\.Logging;")) {
        $content = $content -replace "using SistemaInfo\.Framework\.DomainDrivenDesign\.Application\.Service;", 
            "using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;`r`nusing SistemaInfo.BBC.Infra.CrossCutting.Logging;"
    }
    
    # Save modified content
    Set-Content -Path $file.FullName -Value $content
    
    Write-Host "  Added using statement"
}

Write-Host "Completed adding using statements to all service files"

# Now let's find all public methods in the services and add try/catch with logging
foreach ($file in $serviceFiles) {
    Write-Host "Processing methods in $($file.FullName)"
    
    # Read file content
    $content = Get-Content -Path $file.FullName
    
    # Find class name
    $className = ""
    foreach ($line in $content) {
        if ($line -match "public class (\w+)") {
            $className = $matches[1]
            break
        }
    }
    
    if ($className -eq "") {
        Write-Host "  Could not find class name, skipping"
        continue
    }
    
    Write-Host "  Found class $className"
    
    # Find all public methods
    $methodStartLines = @()
    $methodEndLines = @()
    $methodNames = @()
    $inMethod = $false
    $currentMethodStart = 0
    $currentMethodName = ""
    $braceCount = 0
    
    for ($i = 0; $i -lt $content.Count; $i++) {
        $line = $content[$i]
        
        # Check if this is a method declaration
        if (-not $inMethod -and $line -match "public\s+(?:async\s+)?(?:\w+(?:<[^>]+>)?)\s+(\w+)\s*\([^)]*\)") {
            $currentMethodName = $matches[1]
            $currentMethodStart = $i
            $inMethod = $true
            $braceCount = 0
        }
        
        # Count braces to find method end
        if ($inMethod) {
            $braceCount += ($line -split "{").Count - 1
            $braceCount -= ($line -split "}").Count - 1
            
            # If braces are balanced, we've found the end of the method
            if ($braceCount -eq 0 -and $line -match "}") {
                $methodStartLines += $currentMethodStart
                $methodEndLines += $i
                $methodNames += $currentMethodName
                $inMethod = $false
            }
        }
    }
    
    # Process methods in reverse order to avoid line number issues
    for ($i = $methodStartLines.Count - 1; $i -ge 0; $i--) {
        $methodStart = $methodStartLines[$i]
        $methodEnd = $methodEndLines[$i]
        $methodName = $methodNames[$i]
        
        # Skip methods that already have try/catch
        $methodContent = $content[$methodStart..$methodEnd] -join "`r`n"
        if ($methodContent -match "try\s*{" -and $methodContent -match "catch\s*\(\s*Exception") {
            Write-Host "  Method $methodName already has try/catch, skipping"
            continue
        }
        
        Write-Host "  Adding try/catch to method $methodName"
        
        # Find the opening brace of the method
        $openingBraceIndex = $methodStart
        while ($openingBraceIndex -le $methodEnd -and -not ($content[$openingBraceIndex] -match "{")) {
            $openingBraceIndex++
        }
        
        if ($openingBraceIndex -gt $methodEnd) {
            Write-Host "  Could not find opening brace for method $methodName, skipping"
            continue
        }
        
        # Add try after the opening brace
        $content[$openingBraceIndex] = $content[$openingBraceIndex] + "`r`n            try`r`n            {`r`n                LogHelper.LogOperationStart(""$methodName"");"
        
        # Add catch and finally before the closing brace
        $content[$methodEnd] = "            }`r`n            catch (Exception ex)`r`n            {`r`n                LogHelper.Error(ex, ""Erro ao executar $methodName"");`r`n                throw;`r`n            }`r`n            finally`r`n            {`r`n                LogHelper.LogOperationEnd(""$methodName"");`r`n            }" + $content[$methodEnd]
    }
    
    # Save modified content
    Set-Content -Path $file.FullName -Value $content
    
    Write-Host "  Updated methods in $className"
}

Write-Host "Completed adding try/catch with logging to all service methods"

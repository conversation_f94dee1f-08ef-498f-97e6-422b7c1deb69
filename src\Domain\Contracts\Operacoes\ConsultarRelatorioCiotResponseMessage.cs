using System;
using System.Collections.Generic;
using SistemaInfo.BBC.Domain.External.CIOT.DTO;

namespace SistemaInfo.BBC.Domain.Contracts.Operacoes
{
    
    public class ConsultarRelatorioCiotReqMessage
    {
        public string Ciot { get; set; }
        public string SenhaAlteracao { get; set; }
    }
    
    public class ConsultarRelatorioCiotRespMessage
    {
        public ConsultarRelatorioCiotRespMessage(bool sucesso, string mensagem)
        {
            Sucesso = sucesso;
            Mensagem = mensagem;
        }

        public bool Sucesso { get; set; }
        public string Mensagem { get; set; }
        public RelatorioGestaoCiotResponseMessageInfo Data { get; set; }
    }
    public class RelatorioGestaoCiotResponseMessageInfo
    {
        public string Tipo { get; set; }
        public string Ciot { get; set; }
        public string SenhaAlteracao { get; set; }
        public string Status { get; set; }
        public Proprietario Proprietario { get; set; }
        public Contratante Contratante { get; set; }
        public Motorista Motorista { get; set; }
        public DateTime DataInicioFrete { get; set; }
        public DateTime DataFim { get; set; }
        public int? QuantidadeTarifas { get; set; }
        public string ValorTarifas { get; set; }
        public List<Veiculo> VeiculosList { get; set; }
        public List<ItensViagemEncerramento> ViagensList { get; set; }
        public Remetente Remetente { get; set; }
        public Consignatario Consignatario { get; set; }
        public Destinatario Destinatario { get; set; }
        public Cidade CidadeOrigemPadrao { get; set; }
        public Cidade CidadeDestinoPadrao { get; set; }
        public string Peso { get; set; }
        public string CodNaturezaCarga { get; set; }
        public string ValorFrete { get; set; }
        public string ValorImposto { get; set; }
        public string ValorDespesas { get; set; }
        public string ValorCombustivel { get; set; }
        public string ValorPedagio { get; set; }
        public Pagamento Pagamento { get; set; }
        public string Encerrado { get; set; }
        
    }
}
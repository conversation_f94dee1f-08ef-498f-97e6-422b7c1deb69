# Script para atualizar chamadas estáticas do LogHelper para usar a nova sintaxe com instanciação
# Exemplo: LogHelper.Error(ex, "mensagem") -> new LogHelper().Error(ex, "mensagem")

# Diretório raiz do projeto
$rootDir = "."

# Encontrar todos os arquivos .cs que podem conter chamadas ao LogHelper
$files = Get-ChildItem -Path $rootDir -Filter "*.cs" -Recurse -File | Where-Object { 
    $_.FullName -notlike "*\obj\*" -and 
    $_.FullName -notlike "*\bin\*" -and
    $_.FullName -notlike "*\node_modules\*"
}

Write-Host "Encontrados $($files.Count) arquivos .cs para processar"

# Padrões de chamadas estáticas do LogHelper que precisam ser substituídas
$patterns = @(
    @{
        Pattern = "LogHelper\.LogOperationStart\((.*?)\)"
        Replacement = "new LogHelper().LogOperationStart($1)"
    },
    @{
        Pattern = "LogHelper\.LogOperationEnd\((.*?)\)"
        Replacement = "new LogHelper().LogOperationEnd($1)"
    },
    @{
        Pattern = "LogHelper\.Info\((.*?)\)"
        Replacement = "new LogHelper().Info($1)"
    },
    @{
        Pattern = "LogHelper\.Error\((.*?)\)"
        Replacement = "new LogHelper().Error($1)"
    }
)

# Contador de arquivos e substituições
$fileCount = 0
$replacementCount = 0

# Processar cada arquivo
foreach ($file in $files) {
    $content = Get-Content -Path $file.FullName -Raw
    $originalContent = $content
    $fileModified = $false
    
    # Verificar se o arquivo contém alguma chamada ao LogHelper
    if ($content -match "LogHelper\.") {
        Write-Host "Processando $($file.FullName)"
        
        # Aplicar cada padrão de substituição
        foreach ($pattern in $patterns) {
            # Usar regex com captura de grupos para preservar os argumentos
            $content = [regex]::Replace(
                $content, 
                $pattern.Pattern, 
                $pattern.Replacement, 
                [System.Text.RegularExpressions.RegexOptions]::Multiline
            )
        }
        
        # Verificar se o conteúdo foi modificado
        if ($content -ne $originalContent) {
            # Salvar o arquivo modificado
            Set-Content -Path $file.FullName -Value $content -NoNewline
            $fileModified = $true
            $fileCount++
            
            # Contar substituições
            $matches = [regex]::Matches($originalContent, "LogHelper\.")
            $replacementCount += $matches.Count
            
            Write-Host "  Modificado: $($matches.Count) substituições"
        }
    }
}

Write-Host "Concluído! Modificados $fileCount arquivos com um total de $replacementCount substituições."

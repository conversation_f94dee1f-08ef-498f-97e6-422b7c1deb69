Para gerar versão:
Desenvolvimento: npm run-script dev
Homologação: npm run-script hml
Produção: npm run-script prd

Executar apenas a versão em desenvolvimento na própria máquina: ng serve

------------------------------------

 CIOT Front-end APP
  -

Tecnologias utilizadas:
 
 * Front-end
   * HTML5;
   * Angular;
   * Angular-Bootstrap;
   * npm
   * Sass
   * Typescript
   * Docker (opcional)
 * Servidor de Aplicação;
   * webpack (dev);
   * IIS (prod)

Instruções para subir a aplicação:
-
* REQUISITOS
  * Instalar NodeJS
  * Instalar angular cli para dar suporte aos comandos ng***: npm install -g @angular/cli

* DEV (webpack local):
	* "npm install" na pasta raiz /Web/ para instalar as dependências;
	* "ng serve" na pasta raiz para realizar o build e disponibilizar via webpack server.
	** Por padrão do webpack, a aplicação será disponibilizada em http://localhost:4200/
* PROD (IIS):
	* "npm install" na pasta raiz /Web/ para instalar as dependências;
	* Executar na pasta raiz, "ng build --prod --base-href /ciot/"
	* Verificar que na pasta raiz, será criada uma pasta temporária chamada "dist";
	* Para publicar no IIS, criar um diretório no local desejado e colocar os arquivos do diretório dist dentro.
	* Verificar no arquivo index.html que a tag foi gerada desta forma: <base href="/ciot/">
	* Criar um aplicativo um novo pool de aplicação e converter a pasta em aplicativo.
	* Reiniciar o pool e verificar que a aplicação está rodando :).
	
﻿using System.Threading.Tasks;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.PagamentoEvento;

namespace SistemaInfo.BBC.Application.Interface.PagamentoEvento
{
    public interface IPagamentoEventoAppSerivce
    {
        ConsultaGridPagamentoEventoResponse ConsultarGridPagamentoEvento(ConsultaGridPagamentoEventoRequest eventoRequest);
        Task<RespPadrao> SalvarOcorrencia(SalvarOcorrenciaRequest request);
        Task<RespPadrao> ConsultarPorId(int pagamentoEventoId);
        Task EnviaWebHookAtualizacaoStatus(Domain.Models.PagamentoEvento.PagamentoEvento pix);
        Task<Domain.Models.PagamentoEvento.PagamentoEvento> BuscarPagamentoEventoCompleto(int idPagamentoEvento);
        Task<Domain.Models.PagamentoEvento.PagamentoEvento> BuscarPagamentoEvento(int idPagamentoEvento, int viagemId);
    }
}
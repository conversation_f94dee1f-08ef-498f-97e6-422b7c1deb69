import { AfterViewInit, Component, ElementRef, OnInit, ViewChildren, ViewContainerRef, Input } from '@angular/core';
import { FormBuilder, FormControlName, FormGroup } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";

import { Observable } from "rxjs/Observable";
import 'rxjs/add/operator/debounceTime';
import 'rxjs/add/observable/fromEvent';
import 'rxjs/add/observable/merge';

import { GenericValidator } from "../../commom/generic.form.validator";
import { Ciot } from "../models/ciot"
import { CiotService } from "../services/ciot.service";
import { BsDatepickerConfig } from 'ngx-bootstrap/datepicker/bs-datepicker.config';
import { Subscription } from 'rxjs/Subscription';
import { DatePipe } from '@angular/common';
import { ConsultarSituacaoCiotReq } from '../models/consulta-ciot/consultar-situacao-ciot-request';
import { ConsultarSituacaoCiotDTO } from '../models/consulta-ciot/consultar-situacao-ciot-dto';
import { CiotResponse } from '../models/consulta-ciot/consulta-ciot-response';
import { ToastsManager } from 'ng2-toastr';
import { Mensagens, TipoViagem } from '../util/enums';
import { ObjImprimir } from '../models/objImprimir';

@Component({
  selector: 'app-consulta-ciot',
  templateUrl: './consulta-ciot.component.html',
  styles: []
})
export class ConsultaCiotComponent implements OnInit, AfterViewInit {
  @ViewChildren(FormControlName, { read: ElementRef }) formInputElements: ElementRef[];

  ciotId: string;
  senha: string;
  cpfMotorista: string;
  cpfContratante: string;
  cpfDestinatario: string;

  public bsConfig: Partial<BsDatepickerConfig>;
  public errors: any[] = [];
  public consultaCiotForm: FormGroup;
  public ciot: Ciot;
  public displayMessage: { [key: string]: string } = {};
  private validationMessages: { [key: string]: { [key: string]: string } };
  private genericValidator: GenericValidator;
  private title;
  public sub: Subscription;
  public value: Date = new Date(2000, 2, 10);
  private consultaDTO: ConsultarSituacaoCiotDTO = new ConsultarSituacaoCiotDTO();
  public objImprimir: ObjImprimir = new ObjImprimir();
  private fromDeclaracao: boolean = false;

  public botaoImprimirHabilitado: boolean = true;

  private situacoes: { [key: number]: string } = {
    1: 'Ciot Gerado',
    2: 'Contingência',
    3: 'Cancelado'
  };

  constructor(private fb: FormBuilder,
    private service: CiotService,
    vRef: ViewContainerRef,
    private toastr: ToastsManager,
    private router: Router,
    private route: ActivatedRoute) {

    this.validationMessages = {
      dtInicioFrete: {
        required: 'A data início do frete é obrigatório.'
      },
      dtTerminoFrete: {
        required: 'A data término do frete é obrigatório.'
      },
      naturezaCarga: {
        required: 'A natureza da carga é obrigatório.',
      },
      valorFrete: {
        required: 'O valor do frete é obrigatório.',
      },
      quantidadeTarifas: {
        required: 'A quantidade de tarifas é obrigatório.',
      },
      valorTotalTarifas: {
        required: 'O valor total das tarifas é obrigatório.',
      },
      rntrc: {
        required: '',
      }
    };

    this.genericValidator = new GenericValidator(this.validationMessages);

  }

  consultarCiot(ciotId, senha) {
    let a = new ConsultarSituacaoCiotReq();
    a.ciot = ciotId;
    a.senhaAlteracao = senha;
    this.service.consultarCiot(a)
      .subscribe(
        response => this.preencherForm(response),
        error => { this.onError(error) }
      );
  }

  preencherForm(consulta: CiotResponse): void {
    this.consultaCiotForm.markAsDirty;
    if (consulta.sucesso) {
      this.consultaDTO.ciot = consulta.ciot;
      this.consultaDTO.contratado = consulta.nomeProprietario;
      this.consultaDTO.dtInicioFrete = new DatePipe('en-US').transform(String(consulta.dataInicioFrete).substr(0, 10), "dd/MM/yyyy");
      this.consultaDTO.dtTerminoFrete = new DatePipe('en-US').transform(String(consulta.dataTerminoFrete).substr(0, 10), "dd/MM/yyyy");
      this.consultaDTO.rntrc = consulta.rntrcProprietario;
      this.consultaDTO.senha = this.senha;
      this.consultaDTO.tipoViagem = Number(TipoViagem.Padrao) == consulta.tipoViagem ? Mensagens.PADRAO : Mensagens.TAC_AGREGADO;
      //this.objImprimir.aTipoViagem = consulta.tipoViagem;
      //this.objImprimir.aCpfCnpjProprietario = consulta.cpfCnpjProprietario;
      this.consultaDTO.totalImposto = consulta.totalImposto;
      this.consultaDTO.totalPedagio = consulta.totalPegadio;
      this.consultaDTO.valorFrete = consulta.valorFrete;
      this.consultaDTO.encerrado = consulta.encerrado ? "Sim" : "Não";
      this.consultaDTO.situacaoDeclaracao = this.consultaDTO.encerrado == "Sim" ? "Encerrado" : this.situacoes[consulta.situacao] || 'Desconhecido';

      this.consultaCiotForm.setValue(this.consultaDTO);
    } else {
      this.router.navigate(['/']).then(() => this.toastr.error(Mensagens.CIOT_NAO_ENCONTRADO_CREDENCIAIS_INFORMADAS, Mensagens.OOPS));
    }
  }

  ngOnInit() {
    this.consultaCiotForm = this.fb.group({
      ciot: ['',],

      rntrc: ['',],
      contratado: ['',],
      dtInicioFrete: ['',],

      dtTerminoFrete: ['',],
      valorFrete: ['',],
      totalImposto: ['',],
      totalPedagio: ['',],
      tipoViagem: ['',],
      senha: ['',],
      situacaoDeclaracao: ['',],
      encerrado: ['',],

      //aviso: ['',],
      // documentoContratante: ['', [Validators.required, Validators.minLength(11), Validators.maxLength(14)]],
    });
    this.ciot = new Ciot();
    this.sub = this.route.params.subscribe(
      params => {
        this.ciotId = params['ciot'];
        this.senha = params['senha'];
        this.cpfMotorista = params['cpfMotorista'];
        this.cpfContratante = params['cpfContratante'];
        this.fromDeclaracao = params['fromDeclaracao'] == "true";
        this.consultarCiot(this.ciotId, this.senha);
      });

    if (this.fromDeclaracao) {
      this.toastr.success('CIOT declarado com sucesso!', Mensagens.SUCESSO);
    }
  }

  ngAfterViewInit(): void {
    let controlBlurs: Observable<any>[] = this.formInputElements
      .map((formControl: ElementRef) => Observable.fromEvent(formControl.nativeElement, 'blur'));

    Observable.merge(...controlBlurs).subscribe(value => {
      this.displayMessage = this.genericValidator.processMessages(this.consultaCiotForm);
    });
  }

  voltar() {
    this.router.navigateByUrl('/', { skipLocationChange: true });
  }

  imprimir() {
    //this.objImprimir.aDataInicio = this.consultaDTO.dtInicioFrete;
    //this.objImprimir.aDataTermino = this.consultaDTO.dtTerminoFrete;
    // this.objImprimir.aTipoViagem = this.objImprimir.aTipoViagem;
    // this.objImprimir.aCpfCnpjProprietario = this.objImprimir.aCpfCnpjProprietario;
    ///this.objImprimir.aCpfCnpjContratante = this.fromDeclaracao == true ? this.cpfContratante : "";
    //this.objImprimir.aStatus = Number(this.consultaDTO.situacaoDeclaracao);
    //this.objImprimir.aCpfCnpjCliente = "00000000000001"; //No momento esta fixo, pois não tem a opção de selecionar o cliente na tela
    //this.objImprimir.aCpfCnpjMotorista = this.fromDeclaracao == true ? this.cpfMotorista : "";

    var url = CiotService.UrlBase + "Operacoes/GestaoCiot/";
    var data = JSON.stringify({
      ciot: this.ciotId,
      senhaAlteracao: this.senha
    });
    var args = { json: data };
    this.openWindowWithPost(url, args);

    this.timeoutBotao();
  }

  openWindowWithPost(url, data) {
    var form = document.createElement("form");
    form.target = "_blank";
    form.method = "post";
    form.action = url;
    form.style.display = "none";
    //form.data = data;

    for (var key in data) {
      var input = document.createElement("input");
      input.type = "hidden";
      input.name = key;
      input.value = data[key];
      form.appendChild(input);
    }

    document.body.appendChild(form);
    form.submit();
    document.body.removeChild(form);
  }

  timeoutBotao() {
    this.botaoImprimirHabilitado = false;
    setTimeout(() => {
      this.botaoImprimirHabilitado = true;
    }, 3000);
  }

  onError(serviceReturn) {
    this.errors = Object.assign([], serviceReturn.error.errors);
  }

  onSaveComplete(): void {
    this.consultaCiotForm.reset();
    this.errors = [];
  }


}

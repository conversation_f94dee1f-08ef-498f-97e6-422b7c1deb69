﻿using System;
using System.Threading.Tasks;
using MassTransit;
using Microsoft.Extensions.Configuration;
using NLog;
using SistemaInfo.BBC.Domain.Contracts.Operacoes;
using SistemaInfo.BBC.Infra.Bus.Interface.Ciot;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Infra.Bus.Publishers.OperacoesCiot;

public class OperacoesPublisher : IOperacoesPublisher
{
    private readonly IBus _busControl;
    private readonly int _timeout;

    public OperacoesPublisher(IBus busControl, IConfiguration configuration)
    {
        _busControl = busControl;
        _timeout = (configuration["RabbitMQ:Timeout"]??"90").ToIntSafe();
    }

    public async Task<DeclararOperacaoTransporteRespMessage> DeclararOperacaoTransporte(DeclararOperacaoTransporteReqMessage mensagem)
    {
        try
        {
            var client = _busControl.CreateRequestClient<DeclararOperacaoTransporteReqMessage>(TimeSpan.FromSeconds(_timeout));
            return (await client.GetResponse<DeclararOperacaoTransporteRespMessage>(mensagem)).Message;
        }
        catch (RequestTimeoutException)
        {
            return new DeclararOperacaoTransporteRespMessage(false, "Timeout ao conectar com o microserviço.");
        }
        catch (Exception e)
        {
            LogManager.GetCurrentClassLogger().Error(e);
            return new DeclararOperacaoTransporteRespMessage(false, "Erro interno ao comunicar com o microserviço. Mensagem: "+ e.Message);
        }
    }

    public async Task<EncerrarOperacaoTransporteRespMessage> EncerrarOperacaoTransporte(EncerrarOperacaoTransporteReqMessage mensagem)
    {
        try
        {
            var client = _busControl.CreateRequestClient<EncerrarOperacaoTransporteReqMessage>(TimeSpan.FromSeconds(_timeout));
            return (await client.GetResponse<EncerrarOperacaoTransporteRespMessage>(mensagem)).Message;
        }
        catch (RequestTimeoutException)
        {
            return new EncerrarOperacaoTransporteRespMessage(false, "Timeout ao conectar com o microserviço.");
        }
        catch (Exception e)
        {
            LogManager.GetCurrentClassLogger().Error(e);
            return new EncerrarOperacaoTransporteRespMessage(false, "Erro interno ao comunicar com o microserviço. Mensagem: "+ e.Message);
        }
    }
    
    public async Task<EncerrarOperacaoTransporteRespMessage> EncerrarOperacaoTransporteBbc(EncerrarOperacaoTransporteBbcReqMessage mensagem)
    {
        try
        {
            var client = _busControl.CreateRequestClient<EncerrarOperacaoTransporteBbcReqMessage>(TimeSpan.FromSeconds(_timeout));
            return (await client.GetResponse<EncerrarOperacaoTransporteRespMessage>(mensagem)).Message;
        }
        catch (RequestTimeoutException)
        {
            return new EncerrarOperacaoTransporteRespMessage(false, "Timeout ao conectar com o microserviço.");
        }
        catch (Exception e)
        {
            LogManager.GetCurrentClassLogger().Error(e);
            return new EncerrarOperacaoTransporteRespMessage(false, "Erro interno ao comunicar com o microserviço. Mensagem: "+ e.Message);
        }
    }

    public async Task<CancelarOperacaoTransporteRespMessage> CancelarOperacaoTransporte(CancelarOperacaoTransporteReqMessage mensagem)
    {
        try
        {
            var client = _busControl.CreateRequestClient<CancelarOperacaoTransporteReqMessage>(TimeSpan.FromSeconds(_timeout));
            return (await client.GetResponse<CancelarOperacaoTransporteRespMessage>(mensagem)).Message;
        }
        catch (RequestTimeoutException)
        {
            return new CancelarOperacaoTransporteRespMessage(false, "Timeout ao conectar com o microserviço.");
        }
        catch (Exception e)
        {
            LogManager.GetCurrentClassLogger().Error(e);
            return new CancelarOperacaoTransporteRespMessage(false, "Erro interno ao comunicar com o microserviço. Mensagem: "+ e.Message);
        }
    }

    public async Task<ConsultarSituacaoCiotRespMessage> ConsultarSituacaoCiot(ConsultarSituacaoCiotReqMessage mensagem)
    {
        try
        {
            var client = _busControl.CreateRequestClient<ConsultarSituacaoCiotReqMessage>(TimeSpan.FromSeconds(_timeout));
            return (await client.GetResponse<ConsultarSituacaoCiotRespMessage>(mensagem)).Message;
        }
        catch (RequestTimeoutException)
        {
            return new ConsultarSituacaoCiotRespMessage(false, "Timeout ao conectar com o microserviço.");
        }
        catch (Exception e)
        {
            LogManager.GetCurrentClassLogger().Error(e);
            return new ConsultarSituacaoCiotRespMessage(false, "Erro interno ao comunicar com o microserviço. Mensagem: "+ e.Message);
        }
    }

    public async Task<ConsultarSituacaoTransportadorRespMessage> ConsultarSituacaoTransportador(ConsultarSituacaoTransportadorReqMessage mensagem)
    {
        try
        {
            var client = _busControl.CreateRequestClient<ConsultarSituacaoTransportadorReqMessage>(TimeSpan.FromSeconds(_timeout));
            return (await client.GetResponse<ConsultarSituacaoTransportadorRespMessage>(mensagem)).Message;
        }
        catch (RequestTimeoutException)
        {
            return new ConsultarSituacaoTransportadorRespMessage(false, "Timeout ao conectar com o microserviço.");
        }
        catch (Exception e)
        {
            LogManager.GetCurrentClassLogger().Error(e);
            return new ConsultarSituacaoTransportadorRespMessage(false, "Erro interno ao comunicar com o microserviço. Mensagem: "+ e.Message);
        }
    }

    public async Task<ConsultarOperacaoTacAgregadoRespMessage> ConsultarOperacaoTacAgregado(ConsultarOperacaoTacAgregadoReqMessage mensagem)
    {
        try
        {
            var client = _busControl.CreateRequestClient<ConsultarOperacaoTacAgregadoReqMessage>(TimeSpan.FromSeconds(_timeout));
            return (await client.GetResponse<ConsultarOperacaoTacAgregadoRespMessage>(mensagem)).Message;
        }
        catch (RequestTimeoutException)
        {
            return new ConsultarOperacaoTacAgregadoRespMessage(false, "Timeout ao conectar com o microserviço.");
        }
        catch (Exception e)
        {
            LogManager.GetCurrentClassLogger().Error(e);
            return new ConsultarOperacaoTacAgregadoRespMessage(false, "Erro interno ao comunicar com o microserviço. Mensagem: "+ e.Message);
        }
    }

    public async Task<ConsultarTacAgregadoResponseMessage> VerificarOperacaoTacAgregado(ConsultarTacAgregadoRequestMessage message)
    {
        try
        {
            var client = _busControl.CreateRequestClient<ConsultarTacAgregadoRequestMessage>(TimeSpan.FromSeconds(_timeout));
            return (await client.GetResponse<ConsultarTacAgregadoResponseMessage>(message)).Message;
        }
        catch (RequestTimeoutException)
        {
            return new ConsultarTacAgregadoResponseMessage(false, "Timeout ao conectar com o microserviço.");
        }
        catch (Exception e)
        {
            LogManager.GetCurrentClassLogger().Error(e);
            return new ConsultarTacAgregadoResponseMessage(false, "Erro interno ao comunicar com o microserviço. Mensagem: "+ e.Message);
        }
    }

    public async Task<RetificarOperacaoTransporteRespMessage> RetificarOperacaoTransporte(RetificarOperacaoTransporteReqMessage mensagem)
    {
        try
        {
            var client = _busControl.CreateRequestClient<RetificarOperacaoTransporteReqMessage>(TimeSpan.FromSeconds(_timeout));
            return (await client.GetResponse<RetificarOperacaoTransporteRespMessage>(mensagem)).Message;
        }
        catch (RequestTimeoutException)
        {
            return new RetificarOperacaoTransporteRespMessage(false, "Timeout ao conectar com o microserviço.");
        }
        catch (Exception e)
        {
            LogManager.GetCurrentClassLogger().Error(e);
            return new RetificarOperacaoTransporteRespMessage(false, "Erro interno ao comunicar com o microserviço. Mensagem: "+ e.Message);
        }
    }

    public async Task<ConsultarFrotaTransportadorRespMessage> ConsultarFrotaTransportador(ConsultarFrotaTransportadorReqMessage mensagem)
    {
        try
        {
            var client = _busControl.CreateRequestClient<ConsultarFrotaTransportadorReqMessage>(TimeSpan.FromSeconds(_timeout));
            return (await client.GetResponse<ConsultarFrotaTransportadorRespMessage>(mensagem)).Message;
        }
        catch (RequestTimeoutException)
        {
            return new ConsultarFrotaTransportadorRespMessage(false, "Timeout ao conectar com o microserviço.");
        }
        catch (Exception e)
        {
            LogManager.GetCurrentClassLogger().Error(e);
            return new ConsultarFrotaTransportadorRespMessage(false, "Erro interno ao comunicar com o microserviço. Mensagem: "+ e.Message);
        }
    }

    public async Task<ConsultaGridOperacaoTransporteMessage> ConsultarOperacoesTransporte(ConsultaGridOperacaoTransporteMessageRequest mensagem)
    {
        try
        {
            var client = _busControl.CreateRequestClient<ConsultaGridOperacaoTransporteMessageRequest>(TimeSpan.FromSeconds(_timeout));
            return (await client.GetResponse<ConsultaGridOperacaoTransporteMessage>(mensagem)).Message;
        }
        catch (RequestTimeoutException)
        {
            return new ConsultaGridOperacaoTransporteMessage(false, "Timeout ao conectar com o microserviço.");
        }
        catch (Exception e)
        {
            LogManager.GetCurrentClassLogger().Error(e);
            return new ConsultaGridOperacaoTransporteMessage(false, "Erro interno ao comunicar com o microserviço. Mensagem: "+ e.Message);
        }
    }
    
    public async Task<ConsultaGridOperacaoTransporteHistoricoMessage> ConsultarOperacoesTransporteHistorico(ConsultaGridOperacaoTransporteHistoricoMessageRequest mensagem)
    {
        try
        {
            var client = _busControl.CreateRequestClient<ConsultaGridOperacaoTransporteHistoricoMessageRequest>(TimeSpan.FromSeconds(_timeout));
            return (await client.GetResponse<ConsultaGridOperacaoTransporteHistoricoMessage>(mensagem)).Message;
        }
        catch (RequestTimeoutException)
        {
            return new ConsultaGridOperacaoTransporteHistoricoMessage(false, "Timeout ao conectar com o microserviço.");
        }
        catch (Exception e)
        {
            LogManager.GetCurrentClassLogger().Error(e);
            return new ConsultaGridOperacaoTransporteHistoricoMessage(false, "Erro interno ao comunicar com o microserviço. Mensagem: "+ e.Message);
        }
    }

    public async Task<ConsultarOperacaoTransportePorIdRespMessage> ConsultarOperacaoTransportePorId(ConsultarOperacaoTransportePorIdReqMessage mensagem)
    {
        try
        {
            var client = _busControl.CreateRequestClient<ConsultarOperacaoTransportePorIdReqMessage>(TimeSpan.FromSeconds(_timeout));
            return (await client.GetResponse<ConsultarOperacaoTransportePorIdRespMessage>(mensagem)).Message;
        }
        catch (RequestTimeoutException)
        {
            return new ConsultarOperacaoTransportePorIdRespMessage(false, "Timeout ao conectar com o microserviço.");
        }
        catch (Exception e)
        {
            LogManager.GetCurrentClassLogger().Error(e);
            return new ConsultarOperacaoTransportePorIdRespMessage(false, "Erro interno ao comunicar com o microserviço. Mensagem: "+ e.Message);
        }
    }

    public async Task<ConsultarVeiculosCiotRespMessage> ConsultarVeiculoCiot(ConsultarVeiculoCiotReqMessage mensagem)
    {
        try
        {
            var client = _busControl.CreateRequestClient<ConsultarVeiculoCiotReqMessage>(TimeSpan.FromSeconds(_timeout));
            return (await client.GetResponse<ConsultarVeiculosCiotRespMessage>(mensagem)).Message;
        }
        catch (RequestTimeoutException)
        {
            return new ConsultarVeiculosCiotRespMessage(false, "Timeout ao conectar com o microserviço.");
        }
        catch (Exception e)
        {
            LogManager.GetCurrentClassLogger().Error(e);
            return new ConsultarVeiculosCiotRespMessage(false, "Erro interno ao comunicar com o microserviço. Mensagem: "+ e.Message);
        }
    }
}
﻿using System;
using System.Text;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using SistemaInfo.Framework.DomainDrivenDesign.EntityFramework;
using SistemaInfo.Framework.DomainDrivenDesign.Infra.DataTransferObjects;
using SistemaInfo.Framework.EntityFramework.Extensions;
using SistemaInfo.Framework.EntityFramework.Log;
using System.Threading;
using System.Threading.Tasks;
using JetBrains.Annotations;
using Microsoft.Extensions.Configuration;
using SistemaInfo.BBC.Domain.Models.Abastecimento;
using SistemaInfo.BBC.Domain.Models.AtualizacaoPrecoCombustivel;
using SistemaInfo.BBC.Domain.Models.AuditoriaSeguranca;
using SistemaInfo.BBC.Domain.Models.AuthSession;
using SistemaInfo.BBC.Domain.Models.AuthSessionApi;
using SistemaInfo.BBC.Domain.Models.AutorizacaoAbastecimento;
using SistemaInfo.BBC.Domain.Models.Banco;
using SistemaInfo.BBC.Domain.Models.BloqueioSpd;
using SistemaInfo.BBC.Domain.Models.CentroCusto;
using SistemaInfo.BBC.Domain.Models.CFOP;
using SistemaInfo.BBC.Domain.Models.MDRPrazos;
using SistemaInfo.BBC.Domain.Models.Cidade;
using SistemaInfo.BBC.Domain.Models.CiotVeiculo;
using SistemaInfo.BBC.Domain.Models.CiotViagem;
using SistemaInfo.BBC.Domain.Models.Cliente;
using SistemaInfo.BBC.Domain.Models.ClientSecret;
using SistemaInfo.BBC.Domain.Models.ClientSecretAdm;
using SistemaInfo.BBC.Domain.Models.Combustivel;
using SistemaInfo.BBC.Domain.Models.DeclaracaoCiot;
using SistemaInfo.BBC.Domain.Models.Documento;
using SistemaInfo.BBC.Domain.Models.DocumentosProcessoVinculado;
using SistemaInfo.BBC.Domain.Models.Empresa;
using SistemaInfo.BBC.Domain.Models.EmpresaCfop;
using SistemaInfo.BBC.Domain.Models.EmpresaUsuario;
using SistemaInfo.BBC.Domain.Models.Emprestimo;
using SistemaInfo.BBC.Domain.Models.Estado;
using SistemaInfo.BBC.Domain.Models.Fabricante;
using SistemaInfo.BBC.Domain.Models.Filial;
using SistemaInfo.BBC.Domain.Models.GrupoEmpresa;
using SistemaInfo.BBC.Domain.Models.GrupoUsuario;
using SistemaInfo.BBC.Domain.Models.GrupoUsuarioMenu;
using SistemaInfo.BBC.Domain.Models.LotePagamento;
using SistemaInfo.BBC.Domain.Models.Mensagem;
using SistemaInfo.BBC.Domain.Models.Menu;
using SistemaInfo.BBC.Domain.Models.Modulo;
using SistemaInfo.BBC.Domain.Models.ModuloMenu;
using SistemaInfo.BBC.Domain.Models.NaturezaCarga;
using SistemaInfo.BBC.Domain.Models.Notificacao;
using SistemaInfo.BBC.Domain.Models.PagamentoAbastecimento;
using SistemaInfo.BBC.Domain.Models.PagamentoEvento;
using SistemaInfo.BBC.Domain.Models.PagamentoEventoHistorico;
using SistemaInfo.BBC.Domain.Models.Pagamentos;
using SistemaInfo.BBC.Domain.Models.PagamentosHistorico;
using SistemaInfo.BBC.Domain.Models.ParametroConfiguracaoSla;
using SistemaInfo.BBC.Domain.Models.Parametros;
using SistemaInfo.BBC.Domain.Models.PercentualTransferencia;
using SistemaInfo.BBC.Domain.Models.PercentualTransferenciaHistorico;
using SistemaInfo.BBC.Domain.Models.PercentualTransferenciaPortador;
using SistemaInfo.BBC.Domain.Models.PercentualTransferenciaPortadorHistorico;
using SistemaInfo.BBC.Domain.Models.Portador;
using SistemaInfo.BBC.Domain.Models.PortadorCentroCusto;
using SistemaInfo.BBC.Domain.Models.PortadorEmpresa;
using SistemaInfo.BBC.Domain.Models.PortadorRepresentanteLegal;
using SistemaInfo.BBC.Domain.Models.Posto;
using SistemaInfo.BBC.Domain.Models.PostoCombustivel;
using SistemaInfo.BBC.Domain.Models.PostoCombustivelProduto;
using SistemaInfo.BBC.Domain.Models.PostoContato;
using SistemaInfo.BBC.Domain.Models.ProtocoloAbastecimento;
using SistemaInfo.BBC.Domain.Models.Retencao;
using SistemaInfo.BBC.Domain.Models.ServidorCiot;
using SistemaInfo.BBC.Domain.Models.Transacao;
using SistemaInfo.BBC.Domain.Models.Usuario;
using SistemaInfo.BBC.Domain.Models.UsuarioCentroCusto;
using SistemaInfo.BBC.Domain.Models.UsuarioFilial;
using SistemaInfo.BBC.Domain.Models.UsuarioHistorico;
using SistemaInfo.BBC.Domain.Models.Veiculo;
using SistemaInfo.BBC.Domain.Models.VeiculoCombustivel;
using SistemaInfo.BBC.Domain.Models.VeiculoEmpresa;
using SistemaInfo.BBC.Domain.Models.Viagem;
using SistemaInfo.BBC.Infra.Data.Mappings;
using SistemaInfo.Framework.Utils.AppConfiguration;

namespace SistemaInfo.BBC.Infra.Data.Context
{
    public class ConfigContext : DbContext
    {
        private readonly IConfiguration _appConfiguration;
        public const string DefaultSchema = "BBC";

        /// <summary>
        /// Usuário gado na aplicação executando a ação 
        /// </summary>
        public ISessionUser User { get; }
        
        public DbSet<AuthSession> AuthSession { get; set; }
        public DbSet<Banco> Banco { get; set; }
        public DbSet<Cidade> Cidade { get; set; }
        public DbSet<CiotVeiculo> CiotVeiculo { get; set; }
        public DbSet<CiotViagem> CiotViagem { get; set; }
        public DbSet<Cliente> Cliente { get; set; }
        public DbSet<DeclaracaoCiot> DeclaracaoCiot { get; set; }
        public DbSet<Empresa> Empresa { get; set; }
        public DbSet<Emprestimo> Emprestimo { get; set; }
        public DbSet<Estado> Estado { get; set; }
        public DbSet<Filial> Filial { get; set; }
        public DbSet<ServidorCiot> ServidorCiot { get; set; }
        public DbSet<GrupoUsuario> GrupoUsuario { get; set; }
        public DbSet<GrupoUsuarioMenu> GrupoUsuarioMenu { get; set; }
        public DbSet<Menu> Menu { get; set; }
        public DbSet<Modulo> Modulo { get; set; }
        public DbSet<ModuloMenu> ModuloMenu { get; set; }
        public DbSet<NaturezaCarga> NaturezaCarga { get; set; }
        public DbSet<Pagamentos> Pagamentos { get; set; }
        public DbSet<Parametros> Parametros { get; set; }
        public DbSet<Portador> Portador { get; set; }
        public DbSet<PortadorRepresentanteLegal> PortadorRepresentanteLegal { get; set; }
        public DbSet<Retencao> Retencao { get; set; }
        public DbSet<Usuario> Usuario { get; set; }
        public DbSet<Veiculo> Veiculo { get; set; }
        public DbSet<VeiculoEmpresa> VeiculoEmpresa { get; set; }
        public DbSet<PortadorEmpresa> PortadorEmpresa { get; set; }
        public DbSet<Posto> Posto { get; set; }
        public DbSet<PostoContato> PostoContato { get; set; }
        public DbSet<PostoCombustivel> PostoCombustivel { get; set; }
        public DbSet<PostoCombustivelProduto> PostoCombustivelProduto { get; set; }

        public DbSet<Combustivel> Combustivel { get; set; }
        public DbSet<BloqueioSpd> LiberacaoBloqueioSPD { get; set; }
        
        public DbSet<Fabricante> Fabricante { get; set; }
        public DbSet<Mensagem> Mensagem { get; set; }
        public DbSet<Documento> Documento { get; set; }
        public DbSet<AtualizacaoPrecoCombustivel> AtualizacaoPrecoCombustivel { get; set; }
        public DbSet<CentroCusto> CentroCusto { get; set; }
        public DbSet<MDRPrazos> MDRPrazos { get; set; }
        public DbSet<Notificacao> Notificacao { get; set; }
        public DbSet<Viagem> Viagem { get; set; }
        public DbSet<PagamentoEvento> PagamentoEvento { get; set; }
        public DbSet<PortadorCentroCusto> PortadorCentroCusto { get; set; }
        public DbSet<Abastecimento> Abastecimentos { get; set; }
        public DbSet<Transacao> Transacao { get; set; }
        public DbSet<AuditoriaSeguranca> AuditoriaSeguranca { get; set; }
        public DbSet<VeiculoCombustivel> VeiculoCombustivel { get; set; }
        public DbSet<AutorizacaoAbastecimento> AutorizacaoAbastecimento { get; set; }
        public DbSet<PagamentoAbastecimento> PagamentoAbastecimento { get; set; }
        public DbSet<ProtocoloAbastecimento> ProtocoloAbastecimento { get; set; }
        public DbSet<UsuarioFilial> UsuarioFilial { get; set; }
        public DbSet<UsuarioCentroCusto> UsuarioCentroCusto { get; set; }
        public DbSet<LotePagamento> LotePagamentos { get; set; }
        public DbSet<CFOP> CFOP { get; set; }
        public DbSet<PagamentoEventoHistorico> PagamentoEventoHistorico { get; set; }
        public DbSet<PagamentosHistorico> PagamentosHistorico { get; set; }
        public DbSet<EmpresaCfop> EmpresaCfop { get; set; }
        public DbSet<PercentualTransferencia> PercentualTransferencia { get; set; }
        public DbSet<PercentualTransferenciaHistorico> PercentualTransferenciaHistorico { get; set; }
        public DbSet<PercentualTransferenciaPortador> PercentualTransferenciaPortador { get; set; }
        public DbSet<PercentualTransferenciaPortadorHistorico> PercentualTransferenciaPortadorHistorico { get; set; }
        public DbSet<UsuarioHistorico> UsuarioHistorico { get; set; }
        public DbSet<DocumentosProcessoVinculado> DocumentosProcessoVinculado { get; set; }
        public DbSet<ParametroConfiguracaoSla> ParametroConfiguracaoSla { get; set; }
        public DbSet<GrupoEmpresa> GrupoEmpresa { get; set; }
        public DbSet<AuthSessionApi> AuthSessionApi { get; set; }
        public DbSet<ClientSecret> ClientSecret { get; set; }
        public DbSet<ClientSecretAdm> ClientSecretAdm { get; set; }
        public DbSet<EmpresaUsuario> EmpresaUsuario { get; set; }

#if DEBUG
        private static readonly LoggerFactory LoggerFactory;
        private static bool _debugInitialized = false;

        static ConfigContext()
        {
            if (LoggerFactory == null)
            {
                LoggerFactory = new LoggerFactory();
                LoggerFactory.AddProvider(new ConsoleLoggerProvider());
            }
        }
#endif

        public ConfigContext(AppConfiguration appConfiguration, ISessionUser user)
        {
            User = user;
            _appConfiguration = appConfiguration;
        }

        public ConfigContext([NotNull] DbContextOptions options, IConfiguration appConfiguration, ISessionUser user)
        : base(options)
        {
            User = user;
            _appConfiguration = appConfiguration;
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.HasDefaultSchema(DefaultSchema);
            modelBuilder.ConfigurePostgres();

            #region Fluent API

            modelBuilder.AddConfiguration(new BancoMapping());
            modelBuilder.AddConfiguration(new CidadeMapping());
            modelBuilder.AddConfiguration(new ClienteMapping());
            modelBuilder.AddConfiguration(new NaturezaCargaMapping());
            modelBuilder.AddConfiguration(new VeiculoMapping());
            modelBuilder.AddConfiguration(new CiotVeiculoMapping());
            modelBuilder.AddConfiguration(new CiotViagemMapping());
            modelBuilder.AddConfiguration(new DeclaracaoCiotMapping());
            modelBuilder.AddConfiguration(new EmpresaMapping());
            modelBuilder.AddConfiguration(new EmprestimoMapping());
            modelBuilder.AddConfiguration(new EstadoMapping());
            modelBuilder.AddConfiguration(new FilialMapping());
            modelBuilder.AddConfiguration(new GrupoUsuarioMapping());
            modelBuilder.AddConfiguration(new GrupoUsuarioMenuMapping());
            modelBuilder.AddConfiguration(new MenuMapping());
            modelBuilder.AddConfiguration(new ModuloMapping());
            modelBuilder.AddConfiguration(new ModuloMenuMapping());
            modelBuilder.AddConfiguration(new PagamentosMapping());
            modelBuilder.AddConfiguration(new ParametrosMapping());
            modelBuilder.AddConfiguration(new PortadorMapping());
            modelBuilder.AddConfiguration(new PortadorRepresentanteLegalMapping());
            modelBuilder.AddConfiguration(new RetencaoMapping());
            modelBuilder.AddConfiguration(new UsuarioMapping());
            modelBuilder.AddConfiguration(new VeiculoEmpresaMapping());
            modelBuilder.AddConfiguration(new ContaConductorMapping());
            modelBuilder.AddConfiguration(new DocumentoMapping());
            modelBuilder.AddConfiguration(new BloqueioSpdMapping());
            modelBuilder.AddConfiguration(new PostoMapping());
            modelBuilder.AddConfiguration(new PostoContatoMapping());
            modelBuilder.AddConfiguration(new PostoCombustivelMapping());
            modelBuilder.AddConfiguration(new CombustivelMapping());
            modelBuilder.AddConfiguration(new ServidorCiotMapping());
            modelBuilder.AddConfiguration(new FabricanteMapping());
            modelBuilder.AddConfiguration(new ProtocoloAbastecimentoMapping());
            modelBuilder.AddConfiguration(new TipoEmpresaMapping());
            modelBuilder.AddConfiguration(new TransacaoMapping());
            modelBuilder.AddConfiguration(new MensagemMapping());
            modelBuilder.AddConfiguration(new AtualizacaoPrecoCombustivelMapping());
            modelBuilder.AddConfiguration(new CentroCustoMapping());
            modelBuilder.AddConfiguration(new MDRPrazosMapping());
            modelBuilder.AddConfiguration(new NotificacaoMapping());
            modelBuilder.AddConfiguration(new ViagemMapping());
            modelBuilder.AddConfiguration(new PagamentoEventoMapping());
            modelBuilder.AddConfiguration(new PagamentosHistoricoMapping());
            modelBuilder.AddConfiguration(new PagamentoEventoHistoricoMapping());
            modelBuilder.AddConfiguration(new AbastecimentoMapping());
            modelBuilder.AddConfiguration(new VeiculoCombustivelMapping());
            modelBuilder.AddConfiguration(new AuditoriaSegurancaMapping());
            modelBuilder.AddConfiguration(new AutorizacaoAbastecimentoMapping());
            modelBuilder.AddConfiguration(new AuthSessionMapping());
            modelBuilder.AddConfiguration(new PagamentoAbastecimentoMapping());
            modelBuilder.AddConfiguration(new CFOPMapping());
            modelBuilder.AddConfiguration(new EmpresaCfopMapping());
            modelBuilder.AddConfiguration(new LotePagamentoMapping());
            modelBuilder.AddConfiguration(new UsuarioHistoricoMapping());
            modelBuilder.AddConfiguration(new DocumentosProcessoVinculadoMapping());
            modelBuilder.AddConfiguration(new ParametroConfiguracaoSlaMapping());
            modelBuilder.AddConfiguration(new GrupoEmpresaMapping());
            modelBuilder.AddConfiguration(new AuthSessionApiMapping());
            modelBuilder.AddConfiguration(new ClientSecretMapping());
            modelBuilder.AddConfiguration(new ClientSecretAdmMapping());
            modelBuilder.AddConfiguration(new ParametrosHistoricoMapping());
            modelBuilder.AddConfiguration(new EmpresaUsuarioMapping());
            modelBuilder.AddConfiguration(new EmpresaGrupoUsuarioMapping());
            
            #endregion

            modelBuilder.DisableCascadeDelete();

            base.OnModelCreating(modelBuilder);
        }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            var endressConfig = Encoding.UTF8.GetString(Convert.FromBase64String(_appConfiguration.GetConnectionString("Config")));
#if DEBUG
            if (!_debugInitialized)
            {
                Console.WriteLine($"{GetType().FullName} ConnectionString: {endressConfig}");
                _debugInitialized = true;
            }
#endif
            if (!optionsBuilder.IsConfigured)
                optionsBuilder.UseNpgsql(endressConfig, x => x.MigrationsHistoryTable("__EFMigrationHistory", DefaultSchema));

            optionsBuilder.EnableSensitiveDataLogging();
            
#if DEBUG
            if (LoggerFactory != null)
                optionsBuilder.UseLoggerFactory(LoggerFactory);
#endif
        }

        public override int SaveChanges()
        {
            Domain.Util.DbContextSaveUtils.ConfigureDefaultValues(this, User);
            return base.SaveChanges();
        }

        public override Task<int> SaveChangesAsync(bool acceptAllChangesOnSuccess, CancellationToken cancellationToken = new CancellationToken())
        {
            Domain.Util.DbContextSaveUtils.ConfigureDefaultValues(this, User);
            return base.SaveChangesAsync(acceptAllChangesOnSuccess, cancellationToken);
        }
    }
}
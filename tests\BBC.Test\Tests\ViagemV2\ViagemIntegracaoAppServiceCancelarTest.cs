using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using BBC.Test.Tests.ViagemV2.Fixture;
using Moq;
using SistemaInfo.BBC.Application.Objects.Api.Viagem;
using SistemaInfo.BBC.Application.Services.Viagem;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Domain.External.CIOT.DTO;
using SistemaInfo.BBC.Domain.Models.Viagem.Repository;
using SistemaInfo.BBC.Application.Interface.Operacoes;
using Xunit;

namespace BBC.Test.Tests.ViagemV2
{
    [Collection(nameof(ViagemV2Collection))]
    public class ViagemIntegracaoAppServiceCancelarTest
    {
        private readonly ViagemV2Fixture _fixture;
        private readonly ViagemIntegracaoAppService _appService;

        public ViagemIntegracaoAppServiceCancelarTest(ViagemV2Fixture fixture)
        {
            _fixture = fixture;
            _appService = fixture.Mocker.CreateInstance<ViagemIntegracaoAppService>();
        }

        [Fact(DisplayName = "Cancelar - ViagemExternoId nulo - Deve retornar erro")]
        [Trait("ViagemIntegracaoAppService", "Cancelar")]
        public async Task Cancelar_ViagemExternoIdNulo_DeveRetornarErro()
        {
            // Arrange
            var request = _fixture.GerarCancelamentoEventoViagemV2Request();
            request.ViagemExternoId = null;
            const int empresaId = 1;

            // Act
            var resultado = await _appService.Cancelar(request, empresaId);

            // Assert
            Assert.False(resultado.Sucesso);
            Assert.Equal("ViagemExternoId é um campo obrigatório!", resultado.Mensagem);
        }

        [Fact(DisplayName = "Cancelar - EmpresaId zero - Deve retornar erro")]
        [Trait("ViagemIntegracaoAppService", "Cancelar")]
        public async Task Cancelar_EmpresaIdZero_DeveRetornarErro()
        {
            // Arrange
            var request = _fixture.GerarCancelamentoEventoViagemV2Request();
            const int empresaId = 0;

            // Act
            var resultado = await _appService.Cancelar(request, empresaId);

            // Assert
            Assert.False(resultado.Sucesso);
            Assert.Equal("IdEmpresa é um campo obrigatório!", resultado.Mensagem);
        }
    }
}

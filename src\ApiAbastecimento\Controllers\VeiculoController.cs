using System;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using NLog;
using SistemaInfo.BBC.ApiAbastecimento.Controllers.Base;
using SistemaInfo.BBC.Application.Interface.Veiculo;
using SistemaInfo.BBC.Application.Objects.Api.Veiculo;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;

namespace SistemaInfo.BBC.ApiAbastecimento.Controllers
{
    /// <summary>
    /// class contendo metodos utilizado na aplicação de veiculo
    /// </summary>
    [Route("Veiculos")]
    public class VeiculoController : ApiControllerBase<IVeiculoAppService>
    {
        /// <summary>
        /// Injeção de dependencias para a aplicação veiculo
        /// </summary>
        /// <param name="engine"></param>
        /// <param name="appService"></param>
        public VeiculoController(IAppEngine engine, IVeiculoAppService appService) : base(engine, appService)
        {
        }
        
        // [Produces("application/json")]
        // [HttpPost("Consultar")]
        // public JsonResult Consultar([FromBody]VeiculoConsultarApiRequest request)
        // {            
        //     try
        //     {                
        //         var consultarVeiculo = AppService.Consultar(request).Result;
        //         return ResponseBaseApi.ResponderSucesso(consultarVeiculo);
        //     }
        //     catch (Exception)
        //     {
        //         return ResponseBaseApi.ResponderErro("Nenhum Veículo encontrado!");
        //     }
        // }
        
        /// <summary>
        /// metodo responsavel pela aintegração de veiculo
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [Produces("application/json")]
        [HttpPost("Integrar")]
        public JsonResult Integrar([FromBody]VeiculoIntegrarApiRequest request)
        {       
            var lLog = LogManager.GetCurrentClassLogger();
            try
            {
                lLog.Info("Integrar Veículo Request: " + JsonConvert.SerializeObject(request));
                var response = AppService.Integrar(request).Result;
                lLog.Info("Integrar Veículo Response: " + JsonConvert.SerializeObject(request));
                return ResponseBaseApi.BigJson(response);
            }
            catch (Exception e)
            {
                lLog.Error(e);
                return ResponseBaseApi.ResponderErro("Não foi possível realizar a operação");
            }
        }        
    }
 }
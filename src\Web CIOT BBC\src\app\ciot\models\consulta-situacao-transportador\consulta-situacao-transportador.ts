import { Excecao } from "../common/excecao";

export class ConsultaSituacaoTransportadorResponse {
    cpfCnpjTransportador: string;
    rntrcTransportador: string;
    nomeRazaoSocialTransportador: string;
    rntrcAtivo: boolean;
    dataValidadeRNTRC?: any;
    tipoTransportador: string;
    equiparadoTAC: boolean;
    sucesso: boolean;
    excecao?: {
        tipo: number;
        codigo: string;
        mensagem: string;
    };
}

export class ConsultarSituacaoTransportadorReq {
    CpfCnpjInteressado: string;
    CpfCnpjTransportador: string;
    RNTRCTransportador: string;
}

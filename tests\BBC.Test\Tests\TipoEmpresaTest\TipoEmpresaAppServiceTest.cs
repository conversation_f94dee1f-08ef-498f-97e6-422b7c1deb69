﻿using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using BBC.Test.Tests.TipoEmpresa.Fixture;
using Moq;
using SistemaInfo.BBC.Application.Objects.Web.TipoEmpresa;
using SistemaInfo.BBC.Application.Services.TipoEmpresa;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Models.TipoEmpresa.Repository;
using Xunit;

namespace BBC.Test.Tests.TipoEmpresaTest
{
    [Collection(nameof(TipoEmpresaCollection))]
    public class TipoEmpresaAppServiceTest
    {
        private readonly TipoEmpresaFixture _fixture;
        private readonly TipoEmpresaAppService _appService;
        private readonly Mock<ITipoEmpresaReadRepository> _readRepository;
        public TipoEmpresaAppServiceTest(TipoEmpresaFixture fixture)
        {
            _fixture = fixture;
            _appService = fixture.Mocker.CreateInstance<TipoEmpresaAppService>();
            _readRepository = fixture.Mocker.GetMock<ITipoEmpresaReadRepository>();
        }
        
        private bool TemMesmaEstrutura(object obj1, object obj2)
        {
            var propriedades1 = obj1.GetType().GetProperties(BindingFlags.Public | BindingFlags.Instance);
            var propriedades2 = obj2.GetType().GetProperties(BindingFlags.Public | BindingFlags.Instance);

            if (propriedades1.Length != propriedades2.Length)
                return false;

            return propriedades1.All(p1 =>
                propriedades2.Any(p2 =>
                    p1.Name == p2.Name &&
                    p1.PropertyType == p2.PropertyType));
        }
        
        [Fact(DisplayName = "Consulta padrão da grid de Painel TipoEmpresa")]
        [Trait("TipoEmpresaAppService", "ConsultarGridPainelTipoEmpresa")]
        public void ConsultarGridPainelTipoEmpresa_ConsultaPadrao_DeveRetornarConsultarGridPainelTipoEmpresaResponse()
        {
            //Arrange
            var lOrderFilters = new OrderFilters
            {
                Campo = "id",
                Operador = EOperadorOrder.Descending
            };

            //Action
            var lResponse = _appService
                .ConsultarGridTipoEmpresa(10, 1, lOrderFilters, new List<QueryFilters>(), false);

            //Assert
            Assert.True(TemMesmaEstrutura(lResponse, new ConsultarGridTipoEmpresaResponse()));
        }
        [Fact(DisplayName = "TipoEmpresa buscar por Id com sucesso")]
        [Trait("TipoEmpresaAppService", "BuscarPorId")]
        public void TipoEmpresaBuscarPorId_RetornaSucesso()
        {
            // Arrange
            var tipoEmpresaId = 1;
            var fakeTipoEmpresa = _fixture.GerarTipoEmpresa();
            _readRepository.Setup(e => e.FirstOrDefault(a => a.Id == tipoEmpresaId))
                .Returns(fakeTipoEmpresa);

            // Action
            var lResponse = _appService.ConsultarPorId(tipoEmpresaId);

            // Assert
            Assert.True(TemMesmaEstrutura(lResponse, new TipoEmpresaResponse()));
        }

        [Fact(DisplayName = "TipoEmpresa buscar por Id com ID negativo")]
        [Trait("TipoEmpresaAppService", "BuscarPorId")]
        public void TipoEmpresaBuscarPorId_IdNegativo_RetornaNull()
        {
            // Arrange
            var tipoEmpresaId = -1;

            // Action
            var response = _appService.ConsultarPorId(tipoEmpresaId);

            // Assert
            Assert.Null(response);
        }

        [Fact(DisplayName = "TipoEmpresa buscar por Id sem tipo de empresa encontrada")]
        [Trait("TipoEmpresaAppService", "BuscarPorId")]
        public void TipoEmpresaBuscarPorId_SemTipoEmpresa_RetornaNull()
        {
            // Arrange
            var tipoEmpresaId = 1;
            _readRepository.Setup(e => e.GetById(tipoEmpresaId))
                .Returns((SistemaInfo.BBC.Domain.Models.TipoEmpresa.TipoEmpresa)null);

            // Action
            var response = _appService.ConsultarPorId(tipoEmpresaId);

            // Assert
            Assert.Null(response);
        }
    }
}
using Microsoft.AspNetCore.Http;

namespace SistemaInfo.BBC.ApiIntegracao.Extensions
{
    /// <summary>
    /// Extensions para HttpContext relacionadas à versão da API
    /// </summary>
    public static class HttpContextExtensions
    {
        /// <summary>
        /// Verifica se a request atual é para API V2
        /// </summary>
        /// <param name="context">HttpContext atual</param>
        /// <returns>True se for API V2, False se for V1</returns>
        public static bool IsApiV2(this HttpContext context)
        {
            if (context.Items.TryGetValue("IsApiV2", out var isV2))
            {
                return (bool)isV2;
            }
            
            // Fallback: verificar pela URL se o middleware não executou
            return context.Request.Path.Value?.ToLowerInvariant().Contains("/v2/") == true;
        }

        /// <summary>
        /// Obtém a versão da API atual
        /// </summary>
        /// <param name="context">HttpContext atual</param>
        /// <returns>Versão da API (1.0 ou 2.0)</returns>
        public static string GetApiVersion(this HttpContext context)
        {
            if (context.Items.TryGetValue("ApiVersion", out var version))
            {
                return version.ToString();
            }
            
            // Fallback
            return context.IsApiV2() ? "2.0" : "1.0";
        }
    }
}

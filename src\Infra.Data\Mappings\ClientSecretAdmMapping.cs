using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using SistemaInfo.BBC.Domain.Models.ClientSecretAdm;
using SistemaInfo.Framework.EntityFramework.Configuration;

namespace SistemaInfo.BBC.Infra.Data.Mappings
{
    public class ClientSecretAdmMapping : EntityTypeConfiguration<ClientSecretAdm>
    {
        public override void Map(EntityTypeBuilder<ClientSecretAdm> builder)
        {
            {
                builder.ToTable("ClientSecretAdm");
                builder.HasKey(b => b.Id);
                builder.Property(b => b.Id).IsRequired().HasColumnName("Id").ValueGeneratedOnAdd();

                builder.Property(b => b.Login).IsRequired().HasColumnName("Login").HasColumnType("varchar(100)");
                builder.Property(b => b.<PERSON>).IsRequired().HasColumnName("Senha").HasColumnType("varchar(300)");
                builder.Property(b => b.ClientSecret).IsRequired().HasColumnName("ClientSecret").HasColumnType("varchar(300)");
                builder.Property(b => b.Descricao).HasColumnName("Descricao").HasColumnType("varchar(200)");
                builder.Property(b => b.DataCadastro).IsRequired().HasColumnName("DataCadastro").HasColumnType("timestamp");
                builder.Property(b => b.DataAlteracao).HasColumnName("DataAlteracao").HasColumnType("timestamp");
                builder.Property(b => b.UsuarioCadastroId).IsRequired().HasColumnName("UsuarioCadastroId").HasColumnType("int");
                builder.Property(b => b.UsuarioAlteracaoId).HasColumnName("UsuarioAlteracaoId").HasColumnType("int");
                builder.Property(b => b.Ativo).HasColumnName("Ativo").HasColumnType("int");
                
                builder.HasOne(b => b.Usuario).WithMany().HasForeignKey(b => b.UsuarioCadastroId);
                builder.HasOne(b => b.UsuarioAlteracao).WithMany().HasForeignKey(b => b.UsuarioAlteracaoId);
            }
        }
    }
}

<div class="container main-container">
    <br/>
    <form novalidate (ngSubmit)="declarar()" [formGroup]="declaracaoForm">

        <div class="form-horizontal">
            <div class="alert alert-danger" *ngIf="errors.length > 0">
                <h5 id="msgRetorno">Opa! Alguma coisa não deu certo:</h5>
                <ul>
                    <li *ngFor="let error of errors">{{ error }}</li>
                </ul>
            </div>
            <div class="row">
                <div class="col-sm-12 col-md-6 col-lg-3">
                    <div class="form-group required" [ngClass]="{'has-error': displayMessage.documentoTransportador }">
                        <label class="control-label" for="documentoTransportador">CPF/CNPJ do transportador</label>
                        <input [mask]="maskCpfCnpj" type="text" class="form-control" id="documentoTransportador" formControlName="documentoTransportador" placeholder="Informe o documento" maxlength="18" />
                        <span class="text-danger" *ngIf="displayMessage.documentoTransportador">
              <p [innerHTML]="displayMessage.documentoTransportador"></p>
            </span>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12 col-md-6 col-lg-3">
                    <div class="form-group required" [ngClass]="{'has-error': displayMessage.rntrc }">
                        <label class="control-label" for="rntrc">RNTRC do transportador</label>
                        <input type="text" class="form-control" id="rntrc" formControlName="rntrc" aria-describedby="rntrcHelp" placeholder="Informe o RNTRC" maxlength="9" RntrcMask />
                        <span class="text-danger" *ngIf="displayMessage.rntrc">
              <p [innerHTML]="displayMessage.rntrc"></p>
            </span>
                        <small id="rntrcHelp" class="form-text text-muted">Registro Nacional de Transportadores Rodoviários de Carga.</small>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12 col-md-6 col-lg-3">
                    <div class="form-group required" [ngClass]="{'has-error': displayMessage.documentoContratante }">
                        <label class="control-label" for="documentoContratante">CPF/CNPJ do contratante</label>
                        <input [mask]="maskCpfCnpjContratante" type="text" class="form-control" id="documentoContratante" formControlName="documentoContratante" placeholder="Informe o documento" />
                        <span class="text-danger" *ngIf="displayMessage.documentoContratante">
              <p [innerHTML]="displayMessage.documentoContratante"></p>
            </span>
                    </div>
                </div>
            </div>
            <!-- <div class="g-recaptcha" data-sitekey="6LdTj0cUAAAAAJMT7t5dVnEU4k6chdIc0m-y-PZF"></div> -->
            <br>

<!--            <re-captcha (resolved)="resolved($event)" siteKey="6LcqvgEVAAAAAEtksgMX9T9-7zHU9rje34hsJ3_5"></re-captcha>-->

            <br/>
            <button class="btn btn-danger" id="declarar" type="submit" style="background-color: #00622c; border-color: #00622c;">Declarar</button>
        </div>
    </form>
</div>
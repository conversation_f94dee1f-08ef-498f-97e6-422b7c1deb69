using System;
using System.Collections.Generic;
using SistemaInfo.BBC.Application.Objects.Web.Ciot;
using SistemaInfo.BBC.Application.Objects.Web.EmpresaCfop;
using SistemaInfo.BBC.Application.Objects.Web.Portador;

namespace SistemaInfo.BBC.Application.Objects.Web.Empresa
{
    public class EmpresaRequest
    {
        public int Id { get; set; }
        public string Cnpj { get; set; }
        public string RNTRC { get; set; }
        public string RazaoSocial { get; set; }
        public string NomeFantasia { get; set; }
        public string Celular { get; set; }
        public string Telefone { get; set; }
        public string Email { get; set; }
        public string Cep { get; set; }
        public int EstadoId { get; set; }
        public int CidadeId { get; set; }
        public string Endereco { get; set; }
        public string Bairro { get; set; }
        public string EnderecoNumero { get; set; }
        public string ParecerInterno { get; set; }
        public string ParecerExterno { get; set; }
        public int? StatusCadastro { get; set; }
        public int? UsuarioValidacaoId { get; set; }
        public DateTime? DataValidacao { get; set; }
        public int? UsuarioDesbloqueioId { get; set; }
        public DateTime? DataDesbloqueio { get; set; }
        public string Complemento { get; set; }
        public int? Ativo { get; set; }
        public string InscricaoEstadual { get; set; }
        public DateTime? DataAberturaEmpresa { get; set; }
        public string FormaConstituicao { get; set; }
        public int? UsuarioCadastro { get; set; }
        public DateTime DataCadastro { get; set; }
        public int? UsuarioBloqueioId { get; set; }
        public DateTime? DataBloqueio { get; set; }
        public int? TipoEmissaoCiot { get; set; }
        public bool LiberaBloqueioSPD { get; set; }
        public bool CobrancaTarifa { get; set; }
        public int? TempoAbastecimento { get; set; } = 0;
        public decimal? ValorTolerancia { get; set; } = 0;
        public bool ControlaOdometro { get; set; }
        public bool ControlaAutonomia { get; set; }
        public decimal? TaxaAbastecimento { get; set; } = 0;
        public decimal? Cashback { get; set; }
        //public TipoEmpresa? TipoEmpresa { get; set; }
        public List<PortadorRequest> RepLegalList { get; set; }
        public List<PortadorRepLegalRequest> RepLegaisList { get; set; }
        public List<EmpresaCfopRequest> EmpresaCfop { get; set; }
        public List<DocumentosEmpresaRequest> DocumentosEmpresa { get; set; }
        public bool RegistraCiot { get; set; }
        public string Link { get; set; }
        public string SenhaLink { get; set; }
        public string LinkSAP { get; set; }
        public string UsuarioSAP { get; set; }
        public string SenhaSAP { get; set; }
        public int? TipoEmpresaId { get; set; }
        public decimal? ImpostoIRRF { get; set; } = 0;
        public decimal? ImpostoCSLL { get; set; } = 0;
        public decimal? ImpostoCOFINS { get; set; } = 0;
        public decimal? ImpostoPIS { get; set; } = 0;
        public decimal? PercentualAutonomiaInferior { get; set; }
        public decimal? PercentualAutonomiaSuperior { get; set; }
        public bool ControlaContingencia { get; set; }
        public int? Prazo { get; set; }
        public DateTime? DataAlteracaoModelo { get; set; }        
        public bool DebitoProtocolo { get; set; }
        public bool? DebitoPrazo { get; set; }
        public List<CiotClienteIpRequest> CiotClienteIps { get; set; }
        public int? ContaAbastecimento { get; set; }
        public int? ContaValePedagio { get; set; }
        public int? ContaFrete { get; set; }
        public int? QtdMensalSemTaxaPix { get; set; } = 0;
        public decimal? ValorTarifaPix { get; set; } = 0;
        public decimal? ValorTarifaBbc { get; set; } = 0;
        public int? GrupoEmpresaId { get; set; } = 0;
        public bool UtilizaTarifaEmpresa { get; set; }
        public bool? RecebedorAutorizado { get; set; }
        
        public decimal? PorcentagemTarifaServiceValePedagio { get; set; }
        public bool PermitirPagamentoValePedagio { get; set; }
        public bool CobrarTarifaBbcValePedagio { get; set; }
        public bool UtilizaTarifaEmpresaPagamentoPedagio { get; set; }
        public string NotificacaoContingenciaCiot { get; set; }
        public bool StatusReprocessamentoPagamentoFrete { get; set; }
        public bool HabilitaReprocessamentoValePedagio { get; set; }
        public bool HabilitaPainelSaldo { get; set; }
        public string ImagemCartao { get; set; }
        public decimal ValorAdiantamentoBbc { get; set; }
        public bool PermitirEncerramentoPainelCiot { get; set; }
        public bool UtilizaCiot { get; set; }
        
    }

    public class DocumentosEmpresaRequest
    {
        public int Id { get; set; }
        public string Descricao { get; set; }
        public string Nome { get; set; }
        public string Tipo { get; set; }
        public string Arquivo { get; set; }
        public int Status { get; set; }
        
    }
}
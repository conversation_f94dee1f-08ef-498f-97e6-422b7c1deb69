﻿using Microsoft.EntityFrameworkCore.Migrations;
using System;
using System.Collections.Generic;

namespace SistemaInfo.BBC.Infra.Data.Migrations
{
    public partial class SPRINT_54_2_SIE_441_USUARIO_FROTA : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "CNH",
                schema: "BBC",
                table: "Portador",
                type: "varchar(200)",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DataCancelamento",
                schema: "BBC",
                table: "Portador",
                type: "timestamp",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DataEmissaoCNH",
                schema: "BBC",
                table: "Portador",
                type: "date",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DataVencimentoCNH",
                schema: "BBC",
                table: "Portador",
                type: "date",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "MotivoCancelamento",
                schema: "BBC",
                table: "Portador",
                type: "varchar(500)",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "Status",
                schema: "BBC",
                table: "Portador",
                type: "int",
                nullable: false,
                defaultValue: 1);

            migrationBuilder.AddColumn<int>(
                name: "UsuarioCancelamentoId",
                schema: "BBC",
                table: "Portador",
                type: "int",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_Portador_UsuarioCancelamentoId",
                schema: "BBC",
                table: "Portador",
                column: "UsuarioCancelamentoId");

            migrationBuilder.AddForeignKey(
                name: "FK_Portador_Usuario_UsuarioCancelamentoId",
                schema: "BBC",
                table: "Portador",
                column: "UsuarioCancelamentoId",
                principalSchema: "BBC",
                principalTable: "Usuario",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Portador_Usuario_UsuarioCancelamentoId",
                schema: "BBC",
                table: "Portador");

            migrationBuilder.DropIndex(
                name: "IX_Portador_UsuarioCancelamentoId",
                schema: "BBC",
                table: "Portador");

            migrationBuilder.DropColumn(
                name: "CNH",
                schema: "BBC",
                table: "Portador");

            migrationBuilder.DropColumn(
                name: "DataCancelamento",
                schema: "BBC",
                table: "Portador");

            migrationBuilder.DropColumn(
                name: "DataEmissaoCNH",
                schema: "BBC",
                table: "Portador");

            migrationBuilder.DropColumn(
                name: "DataVencimentoCNH",
                schema: "BBC",
                table: "Portador");

            migrationBuilder.DropColumn(
                name: "MotivoCancelamento",
                schema: "BBC",
                table: "Portador");

            migrationBuilder.DropColumn(
                name: "Status",
                schema: "BBC",
                table: "Portador");

            migrationBuilder.DropColumn(
                name: "UsuarioCancelamentoId",
                schema: "BBC",
                table: "Portador");
        }
    }
}

﻿using AutoMapper;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc.Authorization;
using Microsoft.AspNetCore.Mvc.Formatters;
using Microsoft.AspNetCore.Mvc.Versioning;
using Microsoft.AspNetCore.Mvc.ApiExplorer;

using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using SistemaInfo.BBC.Infra.CrossCutting.IoC;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using SistemaInfo.Framework.DomainDrivenDesign.Web.Filters;
using SistemaInfo.Framework.DomainDrivenDesign.Web.Secutiry.UserSession;
using SistemaInfo.Framework.DomainDrivenDesign.Web.Secutiry.UserSession.Swagger;
using SistemaInfo.Framework.DomainDrivenDesign.Web.Swagger;
using SistemaInfo.Framework.Web.Filters;
using Swashbuckle.AspNetCore.Swagger;
using System;
using System.IdentityModel.Tokens.Jwt;
using System.IO;
using System.Text;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.FileProviders;
using Microsoft.IdentityModel.Tokens;
using Newtonsoft.Json.Converters;
using NLog;
using SistemaInfo.BBC.Infra.Bus;
using SistemaInfo.BBC.Infra.Data.Context;
using SistemaInfo.Framework.Utils;
using SistemaInfo.Framework.Utils.AppConfiguration;
using SistemaInfo.Framework.Web.Utils;
using SistemaInfo.BBC.ApiIntegracao.Middleware;
using SistemaInfo.BBC.ApiIntegracao.Versioning;

namespace SistemaInfo.BBC.ApiIntegracao
{
    /// <summary>
    ///
    /// </summary>
    public class Startup
    {
        /// <inheritdoc />
        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        /// <summary>
        ///
        /// </summary>
        public IConfiguration Configuration { get; }

        /// <summary>
        ///
        /// </summary>
        /// <param name="services"></param>
        public void ConfigureServices(IServiceCollection services)
        {
            services
                .AddMvc(opts =>
                {
                    opts.Filters.Add<ApiActionExceptionFilter>();
                    opts.Filters.Add<ApiFaultResultCodeFilter>();
                    opts.Filters.Add<ApiIntegracao.Filters.ApiBeforeActionFilter>();
                    opts.Filters.Add(new AuthorizeFilter("AppToken"));
                    opts.OutputFormatters.Add(new XmlDataContractSerializerOutputFormatter());
                    opts.InputFormatters.Add(new XmlDataContractSerializerInputFormatter());
                })
                .AddJsonOptions(opts => opts.SerializerSettings.Converters.Add(new StringEnumConverter()));

            // Configuração de versionamento de API
            services.AddApiVersioning(opt =>
            {
                opt.DefaultApiVersion = new Microsoft.AspNetCore.Mvc.ApiVersion(1, 0);
                opt.AssumeDefaultVersionWhenUnspecified = true;
                // Usar nosso reader customizado que detecta versão pela URL sem exigir parâmetros
                opt.ApiVersionReader = new CustomUrlApiVersionReader();
            });

            // Configuração do explorador de versões para Swagger
            services.AddMvcCore().AddVersionedApiExplorer(setup =>
            {
                setup.GroupNameFormat = "'v'VVV";
                setup.SubstituteApiVersionInUrl = true;
            });

            services.AddAuthorization(opts =>
                opts.AddPolicy("AppToken", policy =>
                    policy.Requirements.Add(new AppTokenAuthorizationRequirement())));


            services.AddAutoMapper();

            // Configuração do Swagger com versionamento automático
            services.AddSwaggerGen(c =>
            {
                // Versão 1.0 - Configuração manual para garantir que funcione
                c.SwaggerDoc("v1", new Info
                {
                    Title = "BBC ApiIntegracao",
                    Version = "v1.0",
                    Description = "API de Integração BBC - Versão 1.0"
                });

                // Versão 2.0 - Configuração manual para garantir que funcione
                c.SwaggerDoc("v2", new Info
                {
                    Title = "BBC ApiIntegracao",
                    Version = "v2.0",
                    Description = "API de Integração BBC - Versão 2.0 (Novas implementações)"
                });

                c.OperationFilter<FileUploadOperation>();
                c.OperationFilter<WebSessionTokenHeaderParameter>();
                //c.OperationFilter<AuditUserDocHeaderParameter>(); // AuditUserDocHeaderParameter está momentaneamente, o correto é gerar o web session token para idnetificação da sessão do usuário, e envia-lo no campo acima
                c.SchemaFilter<SwaggerExcludeFilter>();
                c.DocumentFilter<ApiIntegracao.Filters.ApiVersionDocumentFilter>();
                c.DescribeAllEnumsAsStrings();

                var basePath = AppContext.BaseDirectory;
                var lXmlPath = Path.Combine(basePath, "App_Data",
                    System.Reflection.Assembly.GetExecutingAssembly().GetName().Name + ".xml");
                c.IncludeXmlComments(lXmlPath);
            });

            var secretKey = Encoding.UTF8.GetString(Convert.FromBase64String(Configuration["Authentication:SecretKey"]));
            
            services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
                .AddJwtBearer(options =>
                {
                    options.TokenValidationParameters = new TokenValidationParameters
                    {
                        ValidateIssuer = true,
                        ValidIssuer = "MyServer",

                        ValidateAudience = true,
                        ValidAudience = "EveryApplication",

                        ValidateIssuerSigningKey = true,
                        IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(secretKey)),
                        

                        RequireExpirationTime = true,
                        ValidateLifetime = true,
                        ClockSkew = TimeSpan.Zero
                    };
                });
            //
            RegisterServices(services, Configuration);
            using (var intermediateServiceProvider = services.BuildServiceProvider())
            {
                OnServicesConfigured(intermediateServiceProvider);
            }
        }
        
        private static void OnServicesConfigured(ServiceProvider serviceProvider) {

            using (var db = serviceProvider.GetRequiredService<ConfigContext>()) {
                var lLog = LogManager.GetCurrentClassLogger();
                try
                {
                    db.Database.OpenConnection();
                    db.Database.CloseConnection();
                    lLog.Info($"Conexão com banco validado com sucesso!");
                    Console.WriteLine($"Conexão com banco validado com sucesso!");
                }
                catch (Exception e)
                {
                    lLog.Error(e, "Erro ao verificar conexão com o banco: ");
                    Console.WriteLine($"Erro ao verificar conexão com o banco: {e}");
                }
            }
        }
        /// <summary>
        ///
        /// </summary>
        /// <param name="app"></param>
        /// <param name="env"></param>
        /// <param name="httpAccessor"></param>
        /// <param name="appConfiguration"></param>
        /// <param name="serviceProvider"></param>
        public void Configure(IApplicationBuilder app, IHostingEnvironment env, IHttpContextAccessor httpAccessor, AppConfiguration appConfiguration, IServiceProvider serviceProvider)
        {
            app.UseLogForApplicationEvents();
            app.UseSistemaInfoFramework(() => httpAccessor?.HttpContext?.RequestServices ?? serviceProvider);

            app.UsePathBase("/BBC/ApiIntegracao");

            #region Swagger Autenticacao
            
            app.UseStaticFiles();
            
            app.Use(async (context, next) =>
            {
                var caminho = Configuration["Swagger:SwaggerEndpoint"];
                string caminhoBase;
                if (string.IsNullOrWhiteSpace(caminho))
                {
                    caminhoBase = string.Empty;
                }
                else
                {
                    caminhoBase = caminho.Substring(0, caminho.IndexOf("/swagger", StringComparison.Ordinal));
                }
                
                try
                {
                    if (context.Request.Path.Value.Contains("/swagger"))
                    {
                        var handler = new JwtSecurityTokenHandler();
                        var sessionToken = context.Request.Cookies["SessionKey"].ToStringSafe();
                        if (sessionToken.IsNullOrWhiteSpace())
                        {
                            context.Response.Redirect(caminhoBase + "/login/login.html");
                            return;
                        }
                        
                        var tokenS = handler.ReadToken(sessionToken) as JwtSecurityToken;

                        var expDate = tokenS.ValidTo;

                        if (expDate < DateTime.UtcNow)
                        {
                            context.Response.Redirect(caminhoBase + "/login/login.html");
                            return;
                        }
                    }
                    await next.Invoke();
                }
                catch (Exception ex)
                {
                    LogManager.GetCurrentClassLogger().Error(ex);
                    context.Response.StatusCode = 500;
                    await context.Response.WriteAsync("Erro interno do servidor");
                }
            });

            #endregion
            
            app.UseCustomStatusCodePages();
            app.UseCustomDefaultFiles();
            app.UseCustomStaticFiles();
            
            app.UseAuthentication();
            app.UseMiddleware<AuthenticationExceptionHandlingMiddleware>();
            app.UseMiddleware<ApiVersionDetectionMiddleware>();
            app.UseMvc(routes =>
            {
                routes.MapRoute("ApiIntegracao", "BBC/ApiIntegracao/{controller}/{action}/{id}");
            });
            SetSwagger(app);
        }

        private void SetSwagger(IApplicationBuilder app)
        {
            app.UseSwagger();

            app.UseSwaggerUI(c =>
            {
                // Configuração automática dos endpoints baseada nas versões descobertas
                var provider = app.ApplicationServices.GetRequiredService<IApiVersionDescriptionProvider>();

                foreach (var description in provider.ApiVersionDescriptions)
                {
                    c.SwaggerEndpoint(
                        $"/BBC/ApiIntegracao/swagger/{description.GroupName}/swagger.json",
                        $"BBC ApiIntegracao {description.ApiVersion}"
                    );
                }

                c.RoutePrefix = "swagger";
                c.DocumentTitle = "BBC ApiIntegracao - Documentação da API";
            });
        }

        private static void RegisterServices(IServiceCollection serviceCollection, IConfiguration configuration)
        {
            DependencyInjector.RegisterServices(serviceCollection);
            MessageBusDependencyInjector.RegisterServices(serviceCollection, configuration);
            DependencyInjectorCartaoApi.RegisterServices(serviceCollection);
        }
    }
}
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using NLog;
using SistemaInfo.BBC.Application.Interface.Modulo;
using SistemaInfo.BBC.Application.Objects.Web.Modulo;
using SistemaInfo.BBC.Domain.Models.Modulo.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;

namespace SistemaInfo.BBC.Application.Services.Modulo;

public class ModuloAppService : AppService<Domain.Models.Modulo.Modulo, IModuloReadRepository, IModuloWriteRepository>,
    IModuloAppService
{
    public ModuloAppService(IAppEngine engine,
        IModuloReadRepository readRepository,
        IModuloWriteRepository writeRepository)
        : base(engine, readRepository, writeRepository)
    {
    }

    // adicionar parametro novo int multiempresas
    // pode ser null
    public async Task<List<ConsultarModuloResponse>> ConsultarModulos(int? empresaId, int? postoId, int sistema)
    {
        try
        {
            return User.EmpresaId == 0 ? 
                Mapper.Map<List<ConsultarModuloResponse>>(await Repository.Query.GetBySistemaAndAdminAsync(sistema)) : 
                Mapper.Map<List<ConsultarModuloResponse>>(await Repository.Query.GetBySistemaNotAdminAsync(sistema));
        }
        catch (Exception e)
        {
            LogManager.GetCurrentClassLogger().Error(e);
            return null;
        }
    }
}
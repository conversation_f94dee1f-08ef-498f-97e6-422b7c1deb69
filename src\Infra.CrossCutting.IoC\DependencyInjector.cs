﻿using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using SistemaInfo.BBC.Application.AutoMapper;
using SistemaInfo.BBC.Application.External.Captalys.Interface;
using SistemaInfo.BBC.Application.External.Captalys.Services;
using SistemaInfo.BBC.Application.External.Conductor.Interface;
using SistemaInfo.BBC.Application.External.Conductor.Services;
using SistemaInfo.BBC.Application.External.Mobile2You.Interface;
using SistemaInfo.BBC.Application.External.Mobile2You.Services;
using SistemaInfo.BBC.Application.Interface.AceiteTermo;
using SistemaInfo.BBC.Application.Interface.AtualizacaoPrecoCombustivel;
using SistemaInfo.BBC.Application.Interface.Banco;
using SistemaInfo.BBC.Application.Interface.BloqueioSpd;
using SistemaInfo.BBC.Application.Interface.CentralNotificacoes;
using SistemaInfo.BBC.Application.Interface.CentralPendencias;
using SistemaInfo.BBC.Application.Interface.Cidade;
using SistemaInfo.BBC.Application.Interface.Cliente;
using SistemaInfo.BBC.Application.Interface.Combustivel;
using SistemaInfo.BBC.Application.Interface.ContaConductor;
using SistemaInfo.BBC.Application.Interface.DeclaracaoCiot;
using SistemaInfo.BBC.Application.Interface.Documento;
using SistemaInfo.BBC.Application.Interface.Empresa;
using SistemaInfo.BBC.Application.Interface.Emprestimo;
using SistemaInfo.BBC.Application.Interface.Abastecimento;
using SistemaInfo.BBC.Application.Interface.AuditoriaSeguranca;
using SistemaInfo.BBC.Application.Interface.AuthSession;
using SistemaInfo.BBC.Application.Interface.AuthSessionApi;
using SistemaInfo.BBC.Application.Interface.AutorizacaoAbastecimento;
using SistemaInfo.BBC.Application.Interface.AutorizacaoContingecia;
using SistemaInfo.BBC.Application.Interface.MDRPrazos;
using SistemaInfo.BBC.Application.Interface.CentroCusto;
using SistemaInfo.BBC.Application.Interface.CFOP;
using SistemaInfo.BBC.Application.Interface.ClientSecret;
using SistemaInfo.BBC.Application.Interface.ClientSecretAdm;
using SistemaInfo.BBC.Application.Interface.DocumentosProcessoVinculado;
using SistemaInfo.BBC.Application.Interface.Fabricante;
using SistemaInfo.BBC.Application.Interface.Modelo;
using SistemaInfo.BBC.Application.Interface.Filial;
using SistemaInfo.BBC.Application.Interface.GrupoEmpresa;
using SistemaInfo.BBC.Application.Interface.GrupoUsuario;
using SistemaInfo.BBC.Application.Interface.Mensagem;
using SistemaInfo.BBC.Application.Interface.Menu;
using SistemaInfo.BBC.Application.Interface.Modulo;
using SistemaInfo.BBC.Application.Interface.ModuloMenu;
using SistemaInfo.BBC.Application.Interface.MonitoramentoCiot;
using SistemaInfo.BBC.Application.Interface.NaturezaCarga;
using SistemaInfo.BBC.Application.Interface.Operacoes;
using SistemaInfo.BBC.Application.Interface.PagamentoAbastecimento;
using SistemaInfo.BBC.Application.Interface.Pagamentos;
using SistemaInfo.BBC.Application.Interface.PagamentoEvento;
using SistemaInfo.BBC.Application.Interface.Parametros;
using SistemaInfo.BBC.Application.Interface.Pedagio;
using SistemaInfo.BBC.Application.Interface.PercentualTransferencia;
using SistemaInfo.BBC.Application.Interface.Pix;
using SistemaInfo.BBC.Application.Interface.Portador;
using SistemaInfo.BBC.Application.Interface.Posto;
using SistemaInfo.BBC.Application.Interface.PostoCombustivel;
using SistemaInfo.BBC.Application.Interface.PostoContato;
using SistemaInfo.BBC.Application.Interface.ProtocoloAbastecimento;
using SistemaInfo.BBC.Application.Interface.Retencao;
using SistemaInfo.BBC.Application.Interface.Saldo;
using SistemaInfo.BBC.Application.Interface.TipoEmpresa;
using SistemaInfo.BBC.Application.Interface.Transacao;
using SistemaInfo.BBC.Application.Interface.Usuario;
using SistemaInfo.BBC.Application.Interface.Veiculo;
using SistemaInfo.BBC.Application.Interface.VeiculoCombustivel;
using SistemaInfo.BBC.Application.Interface.Viagem;
using SistemaInfo.BBC.Application.Services.AtualizacaoPrecoCombustivel;
using SistemaInfo.BBC.Application.Services.Banco;
using SistemaInfo.BBC.Application.Services.BloqueioSpd;
using SistemaInfo.BBC.Application.Services.CentralNotificacoes;
using SistemaInfo.BBC.Application.Services.CentralPendencias;
using SistemaInfo.BBC.Application.Services.Cidade;
using SistemaInfo.BBC.Application.Services.Cliente;
using SistemaInfo.BBC.Application.Services.Combustivel;
using SistemaInfo.BBC.Application.Services.ContaConductor;
using SistemaInfo.BBC.Application.Services.DeclaracaoCiot;
using SistemaInfo.BBC.Application.Services.Documento;
using SistemaInfo.BBC.Application.Services.Empresa;
using SistemaInfo.BBC.Application.Services.Emprestimo;
using SistemaInfo.BBC.Application.Services.Fabricante;
using SistemaInfo.BBC.Application.Services.TipoEmpresa;
using SistemaInfo.BBC.Application.Services.Abastecimento;
using SistemaInfo.BBC.Application.Services.AuditoriaSeguranca;
using SistemaInfo.BBC.Application.Services.AuthSession;
using SistemaInfo.BBC.Application.Services.AuthSessionApi;
using SistemaInfo.BBC.Application.Services.AutorizacaoAbastecimento;
using SistemaInfo.BBC.Application.Services.AutorizacaoContingecia;
using SistemaInfo.BBC.Application.Services.MDRPrazos;
using SistemaInfo.BBC.Application.Services.CentroCusto;
using SistemaInfo.BBC.Application.Services.CFOP;
using SistemaInfo.BBC.Application.Services.ClientSecret;
using SistemaInfo.BBC.Application.Services.ClientSecretAdm;
using SistemaInfo.BBC.Application.Services.DocumentosProcessoVinculado;
using SistemaInfo.BBC.Application.Services.Modelo;
using SistemaInfo.BBC.Application.Services.Filial;
using SistemaInfo.BBC.Application.Services.GrupoEmpresa;
using SistemaInfo.BBC.Application.Services.GrupoUsuario;
using SistemaInfo.BBC.Application.Services.Mensagem;
using SistemaInfo.BBC.Application.Services.Menu;
using SistemaInfo.BBC.Application.Services.Modulo;
using SistemaInfo.BBC.Application.Services.ModuloMenu;
using SistemaInfo.BBC.Application.Services.MonitoramentoCiot;
using SistemaInfo.BBC.Application.Services.NaturezaCarga;
using SistemaInfo.BBC.Application.Services.Operacoes;
using SistemaInfo.BBC.Application.Services.PagamentoAbastecimento;
using SistemaInfo.BBC.Application.Services.PagamentoEvento;
using SistemaInfo.BBC.Application.Services.Pagamentos;
using SistemaInfo.BBC.Application.Services.Parametros;
using SistemaInfo.BBC.Application.Services.Pedagio;
using SistemaInfo.BBC.Application.Services.PercentualTransferencia;
using SistemaInfo.BBC.Application.Services.Pix;
using SistemaInfo.BBC.Application.Services.Portador;
using SistemaInfo.BBC.Application.Services.Posto;
using SistemaInfo.BBC.Application.Services.PostoCombustivel;
using SistemaInfo.BBC.Application.Services.PostoContato;
using SistemaInfo.BBC.Application.Services.ProtocoloAbastecimento;
using SistemaInfo.BBC.Application.Services.Retencao;
using SistemaInfo.BBC.Application.Services.Saldo;
using SistemaInfo.BBC.Application.Services.Transacao;
using SistemaInfo.BBC.Application.Services.Usuario;
using SistemaInfo.BBC.Application.Services.Veiculo;
using SistemaInfo.BBC.Application.Services.VeiculoCombustivel;
using SistemaInfo.BBC.Application.Services.Viagem;
using SistemaInfo.BBC.Domain.CloudTranslationService;
using SistemaInfo.BBC.Domain.Components.Email;
using SistemaInfo.BBC.Domain.External.Captalys.Interface;
using SistemaInfo.BBC.Domain.External.CIOT.Interface;
using SistemaInfo.BBC.Domain.External.CIOT.Repository;
using SistemaInfo.BBC.Domain.External.Conductor.DTO;
using SistemaInfo.BBC.Domain.External.Conductor.Interface;
using SistemaInfo.BBC.Domain.External.Infra.Interface;
using SistemaInfo.BBC.Domain.External.Mobile2You.Interface;
using SistemaInfo.BBC.Domain.External.Movida.Interface;
using SistemaInfo.BBC.Domain.External.SAP.Interface;
using SistemaInfo.BBC.Domain.Models.AtualizacaoPrecoCombustivel;
using SistemaInfo.BBC.Domain.Models.AtualizacaoPrecoCombustivel.Commands;
using SistemaInfo.BBC.Domain.Models.AtualizacaoPrecoCombustivel.Commands.CommandHandlers;
using SistemaInfo.BBC.Domain.Models.AtualizacaoPrecoCombustivel.Repository;
using SistemaInfo.BBC.Domain.Models.Banco;
using SistemaInfo.BBC.Domain.Models.Banco.Commands;
using SistemaInfo.BBC.Domain.Models.Banco.Commands.CommandHandlers;
using SistemaInfo.BBC.Domain.Models.Banco.Repository;
using SistemaInfo.BBC.Domain.Models.BloqueioSpd;
using SistemaInfo.BBC.Domain.Models.BloqueioSpd.Commands;
using SistemaInfo.BBC.Domain.Models.BloqueioSpd.Commands.CommandHandlers;
using SistemaInfo.BBC.Domain.Models.BloqueioSpd.Repository;
using SistemaInfo.BBC.Domain.Models.Cidade.Repository;
using SistemaInfo.BBC.Domain.Models.Cliente;
using SistemaInfo.BBC.Domain.Models.Cliente.Commands;
using SistemaInfo.BBC.Domain.Models.Cliente.Commands.CommandHandlers;
using SistemaInfo.BBC.Domain.Models.Cliente.Repository;
using SistemaInfo.BBC.Domain.Models.Combustivel;
using SistemaInfo.BBC.Domain.Models.Combustivel.Commands;
using SistemaInfo.BBC.Domain.Models.Combustivel.Commands.CommandHandlers;
using SistemaInfo.BBC.Domain.Models.Combustivel.Repository;
using SistemaInfo.BBC.Domain.Models.ContasConductor.Repository;
using SistemaInfo.BBC.Domain.Models.ContasConductor;
using SistemaInfo.BBC.Domain.Models.ContasConductor.CommandHandlers;
using SistemaInfo.BBC.Domain.Models.DeclaracaoCiot.Commands;
using SistemaInfo.BBC.Domain.Models.DeclaracaoCiot.Commands.CommandHandlers;
using SistemaInfo.BBC.Domain.Models.DeclaracaoCiot.Repository;
using SistemaInfo.BBC.Domain.Models.Documento.Commands;
using SistemaInfo.BBC.Domain.Models.Documento.Commands.CommandHandlers;
using SistemaInfo.BBC.Domain.Models.Documento.Repository;
using SistemaInfo.BBC.Domain.Models.Empresa;
using SistemaInfo.BBC.Domain.Models.Empresa.Commands;
using SistemaInfo.BBC.Domain.Models.Empresa.Commands.CommandHandlers;
using SistemaInfo.BBC.Domain.Models.Empresa.Repository;
using SistemaInfo.BBC.Domain.Models.Emprestimo.Commands;
using SistemaInfo.BBC.Domain.Models.Emprestimo.Commands.CommandHandlers;
using SistemaInfo.BBC.Domain.Models.Emprestimo.Repository;
using SistemaInfo.BBC.Domain.Models.Estado.Repository;
using SistemaInfo.BBC.Domain.Models.Fabricante;
using SistemaInfo.BBC.Domain.Models.Fabricante.Commands;
using SistemaInfo.BBC.Domain.Models.Fabricante.Commands.CommandHandlers;
using SistemaInfo.BBC.Domain.Models.Fabricante.Repository;
using SistemaInfo.BBC.Domain.Models.Abastecimento;
using SistemaInfo.BBC.Domain.Models.Abastecimento.Commands;
using SistemaInfo.BBC.Domain.Models.Abastecimento.Commands.CommandHandlers;
using SistemaInfo.BBC.Domain.Models.Abastecimento.Repository;
using SistemaInfo.BBC.Domain.Models.AuditoriaSeguranca;
using SistemaInfo.BBC.Domain.Models.AuditoriaSeguranca.Commands;
using SistemaInfo.BBC.Domain.Models.AuditoriaSeguranca.Commands.CommandHandlers;
using SistemaInfo.BBC.Domain.Models.AuditoriaSeguranca.Repository;
using SistemaInfo.BBC.Domain.Models.AuthSession;
using SistemaInfo.BBC.Domain.Models.AuthSession.Commands;
using SistemaInfo.BBC.Domain.Models.AuthSession.Commands.CommandHandlers;
using SistemaInfo.BBC.Domain.Models.AuthSession.Repository;
using SistemaInfo.BBC.Domain.Models.AuthSessionApi.Repository;
using SistemaInfo.BBC.Domain.Models.AutorizacaoAbastecimento;
using SistemaInfo.BBC.Domain.Models.AutorizacaoAbastecimento.Commands;
using SistemaInfo.BBC.Domain.Models.AutorizacaoAbastecimento.Commands.CommandHandlers;
using SistemaInfo.BBC.Domain.Models.AutorizacaoAbastecimento.Repository;
using SistemaInfo.BBC.Domain.Models.MDRPrazos;
using SistemaInfo.BBC.Domain.Models.MDRPrazos.Commands;
using SistemaInfo.BBC.Domain.Models.MDRPrazos.Commands.CommandHandlers;
using SistemaInfo.BBC.Domain.Models.MDRPrazos.Repository;
using SistemaInfo.BBC.Domain.Models.CentroCusto;
using SistemaInfo.BBC.Domain.Models.CentroCusto.Commands;
using SistemaInfo.BBC.Domain.Models.CentroCusto.Commands.CommandHandlers;
using SistemaInfo.BBC.Domain.Models.CentroCusto.Repository;
using SistemaInfo.BBC.Domain.Models.CFOP.Repository;
using SistemaInfo.BBC.Domain.Models.ClientSecret;
using SistemaInfo.BBC.Domain.Models.ClientSecretAdm;
using SistemaInfo.BBC.Domain.Models.ClientSecret.Commands;
using SistemaInfo.BBC.Domain.Models.ClientSecretAdm.Commands;
using SistemaInfo.BBC.Domain.Models.ClientSecret.Commands.CommandHandlers;
using SistemaInfo.BBC.Domain.Models.ClientSecretAdm.Commands.CommandHandlers;
using SistemaInfo.BBC.Domain.Models.ClientSecret.Repository;
using SistemaInfo.BBC.Domain.Models.ClientSecretAdm.Repository;
using SistemaInfo.BBC.Domain.Models.ClientSecretAdm.Commands;
using SistemaInfo.BBC.Domain.Models.DocumentosProcessoVinculado;
using SistemaInfo.BBC.Domain.Models.DocumentosProcessoVinculado.Commands;
using SistemaInfo.BBC.Domain.Models.DocumentosProcessoVinculado.Commands.CommandHandlers;
using SistemaInfo.BBC.Domain.Models.DocumentosProcessoVinculado.Repository;
using SistemaInfo.BBC.Domain.Models.EmpresaCfop.Repository;
using SistemaInfo.BBC.Domain.Models.EmpresaGrupoUsuario;
using SistemaInfo.BBC.Domain.Models.EmpresaGrupoUsuario.Commands;
using SistemaInfo.BBC.Domain.Models.EmpresaGrupoUsuario.Commands.CommandHandlers;
using SistemaInfo.BBC.Domain.Models.EmpresaGrupoUsuario.Repository;
using SistemaInfo.BBC.Domain.Models.EmpresaUsuario;
using SistemaInfo.BBC.Domain.Models.EmpresaUsuario.Commands;
using SistemaInfo.BBC.Domain.Models.EmpresaUsuario.Commands.CommandHandlers;
using SistemaInfo.BBC.Domain.Models.EmpresaUsuario.Repository;
using SistemaInfo.BBC.Domain.Models.Modelo;
using SistemaInfo.BBC.Domain.Models.Modelo.Commands;
using SistemaInfo.BBC.Domain.Models.Modelo.Commands.CommandHandlers;
using SistemaInfo.BBC.Domain.Models.Modelo.Repository;
using SistemaInfo.BBC.Domain.Models.Filial;
using SistemaInfo.BBC.Domain.Models.Filial.Commands;
using SistemaInfo.BBC.Domain.Models.Filial.Commands.CommandHandlers;
using SistemaInfo.BBC.Domain.Models.Filial.Repository;
using SistemaInfo.BBC.Domain.Models.GrupoEmpresa.Commands;
using SistemaInfo.BBC.Domain.Models.GrupoEmpresa.Commands.CommandHandlers;
using SistemaInfo.BBC.Domain.Models.GrupoEmpresa.Repository;
using SistemaInfo.BBC.Domain.Models.GrupoUsuario;
using SistemaInfo.BBC.Domain.Models.GrupoUsuario.Commands;
using SistemaInfo.BBC.Domain.Models.GrupoUsuario.Commands.Base;
using SistemaInfo.BBC.Domain.Models.GrupoUsuario.Commands.CommandHandlers;
using SistemaInfo.BBC.Domain.Models.GrupoUsuario.Repository;
using SistemaInfo.BBC.Domain.Models.GrupoUsuarioMenu.Repository;
using SistemaInfo.BBC.Domain.Models.LotePagamento;
using SistemaInfo.BBC.Domain.Models.LotePagamento.Commands;
using SistemaInfo.BBC.Domain.Models.LotePagamento.Commands.CommandHandlers;
using SistemaInfo.BBC.Domain.Models.LotePagamento.Repository;
using SistemaInfo.BBC.Domain.Models.Mensagem;
using SistemaInfo.BBC.Domain.Models.Mensagem.Commands;
using SistemaInfo.BBC.Domain.Models.Mensagem.Commands.CommandHandlers;
using SistemaInfo.BBC.Domain.Models.Mensagem.Repository;
using SistemaInfo.BBC.Domain.Models.Menu;
using SistemaInfo.BBC.Domain.Models.Menu.Commands;
using SistemaInfo.BBC.Domain.Models.Menu.Commands.CommandHandlers;
using SistemaInfo.BBC.Domain.Models.Menu.Repository;
using SistemaInfo.BBC.Domain.Models.Modulo;
using SistemaInfo.BBC.Domain.Models.Modulo.Commands;
using SistemaInfo.BBC.Domain.Models.Modulo.Commands.CommandHandlers;
using SistemaInfo.BBC.Domain.Models.Modulo.Repository;
using SistemaInfo.BBC.Domain.Models.ModuloMenu;
using SistemaInfo.BBC.Domain.Models.ModuloMenu.Commands;
using SistemaInfo.BBC.Domain.Models.ModuloMenu.Commands.CommandHandlers;
using SistemaInfo.BBC.Domain.Models.ModuloMenu.Repository;
using SistemaInfo.BBC.Domain.Models.NaturezaCarga.Repository;
using SistemaInfo.BBC.Domain.Models.Notificacao;
using SistemaInfo.BBC.Domain.Models.Notificacao.Commands;
using SistemaInfo.BBC.Domain.Models.Notificacao.Commands.CommandHandlers;
using SistemaInfo.BBC.Domain.Models.Notificacao.Repository;
using SistemaInfo.BBC.Domain.Models.PagamentoAbastecimento;
using SistemaInfo.BBC.Domain.Models.PagamentoAbastecimento.Commands;
using SistemaInfo.BBC.Domain.Models.PagamentoAbastecimento.Commands.CommandHandlers;
using SistemaInfo.BBC.Domain.Models.PagamentoAbastecimento.Repository;
using SistemaInfo.BBC.Domain.Models.PagamentoEvento;
using SistemaInfo.BBC.Domain.Models.PagamentoEvento.Commands;
using SistemaInfo.BBC.Domain.Models.PagamentoEvento.Commands.CommandHandlers;
using SistemaInfo.BBC.Domain.Models.PagamentoEvento.Repository;
using SistemaInfo.BBC.Domain.Models.PagamentoEventoHistorico.Repository;
using SistemaInfo.BBC.Domain.Models.Pagamentos;
using SistemaInfo.BBC.Domain.Models.Pagamentos.Commands;
using SistemaInfo.BBC.Domain.Models.Pagamentos.Commands.CommandHandlers;
using SistemaInfo.BBC.Domain.Models.Pagamentos.Repository;
using SistemaInfo.BBC.Domain.Models.ParametroConfiguracaoSla.Commands;
using SistemaInfo.BBC.Domain.Models.ParametroConfiguracaoSla.Commands.CommandHandlers;
using SistemaInfo.BBC.Domain.Models.ParametroConfiguracaoSla.Repository;
using SistemaInfo.BBC.Domain.Models.Parametros;
using SistemaInfo.BBC.Domain.Models.Parametros.Commands;
using SistemaInfo.BBC.Domain.Models.Parametros.Commands.CommandHandlers;
using SistemaInfo.BBC.Domain.Models.Parametros.Repository;
using SistemaInfo.BBC.Domain.Models.PercentualTransferencia;
using SistemaInfo.BBC.Domain.Models.PercentualTransferencia.Commands;
using SistemaInfo.BBC.Domain.Models.PercentualTransferencia.Commands.CommandHandlers;
using SistemaInfo.BBC.Domain.Models.PercentualTransferencia.Repository;
using SistemaInfo.BBC.Domain.Models.PercentualTransferenciaHistorico.Repository;
using SistemaInfo.BBC.Domain.Models.PercentualTransferenciaPortador.Commands;
using SistemaInfo.BBC.Domain.Models.PercentualTransferenciaPortador.Commands.CommandHandlers;
using SistemaInfo.BBC.Domain.Models.PercentualTransferenciaPortador.Repository;
using SistemaInfo.BBC.Domain.Models.PercentualTransferenciaPortadorHistorico.Repository;
using SistemaInfo.BBC.Domain.Models.Portador;
using SistemaInfo.BBC.Domain.Models.Portador.Commands;
using SistemaInfo.BBC.Domain.Models.Portador.Commands.CommandHandlers;
using SistemaInfo.BBC.Domain.Models.Portador.Repository;
using SistemaInfo.BBC.Domain.Models.PortadorCentroCusto.Commands;
using SistemaInfo.BBC.Domain.Models.PortadorCentroCusto.Commands.CommandHandlers;
using SistemaInfo.BBC.Domain.Models.PortadorCentroCusto.Repository;
using SistemaInfo.BBC.Domain.Models.PortadorEmpresa.Commands;
using SistemaInfo.BBC.Domain.Models.PortadorEmpresa.Commands.CommandHandlers;
using SistemaInfo.BBC.Domain.Models.PortadorEmpresa.Repository;
using SistemaInfo.BBC.Domain.Models.Posto;
using SistemaInfo.BBC.Domain.Models.Posto.Commands;
using SistemaInfo.BBC.Domain.Models.Posto.Commands.CommandHandlers;
using SistemaInfo.BBC.Domain.Models.Posto.Repository;
using SistemaInfo.BBC.Domain.Models.PostoCombustivel;
using SistemaInfo.BBC.Domain.Models.PostoCombustivel.Commands;
using SistemaInfo.BBC.Domain.Models.PostoCombustivel.Commands.CommandHandlers;
using SistemaInfo.BBC.Domain.Models.PostoCombustivel.Repository;
using SistemaInfo.BBC.Domain.Models.PostoCombustivelProduto.Commands;
using SistemaInfo.BBC.Domain.Models.PostoCombustivelProduto.Commands.CommandHandlers;
using SistemaInfo.BBC.Domain.Models.PostoCombustivelProduto.Repository;
using SistemaInfo.BBC.Domain.Models.PostoContato;
using SistemaInfo.BBC.Domain.Models.PostoContato.Commands;
using SistemaInfo.BBC.Domain.Models.PostoContato.Commands.CommandHandlers;
using SistemaInfo.BBC.Domain.Models.PostoContato.Repository;
using SistemaInfo.BBC.Domain.Models.ProtocoloAbastecimento;
using SistemaInfo.BBC.Domain.Models.ProtocoloAbastecimento.Commands;
using SistemaInfo.BBC.Domain.Models.ProtocoloAbastecimento.Commands.CommandHandlers;
using SistemaInfo.BBC.Domain.Models.ProtocoloAbastecimento.Repository;
using SistemaInfo.BBC.Domain.Models.Retencao.Commands;
using SistemaInfo.BBC.Domain.Models.Retencao.Commands.CommandHandlers;
using SistemaInfo.BBC.Domain.Models.Retencao.Repository;
using SistemaInfo.BBC.Domain.Models.ServidorCiot;
using SistemaInfo.BBC.Domain.Models.ServidorCiot.Commands;
using SistemaInfo.BBC.Domain.Models.ServidorCiot.Commands.CommandHandlers;
using SistemaInfo.BBC.Domain.Models.ServidorCiot.Repository;
using SistemaInfo.BBC.Domain.Models.TipoEmpresa;
using SistemaInfo.BBC.Domain.Models.TipoEmpresa.Commands;
using SistemaInfo.BBC.Domain.Models.TipoEmpresa.Commands.CommandHandlers;
using SistemaInfo.BBC.Domain.Models.TipoEmpresa.Repository;
using SistemaInfo.BBC.Domain.Models.Transacao;
using SistemaInfo.BBC.Domain.Models.Transacao.Commands;
using SistemaInfo.BBC.Domain.Models.Transacao.Commands.CommandHandlers;
using SistemaInfo.BBC.Domain.Models.Transacao.Repository;
using SistemaInfo.BBC.Domain.Models.Usuario;
using SistemaInfo.BBC.Domain.Models.Usuario.Commands;
using SistemaInfo.BBC.Domain.Models.Usuario.Commands.CommandHandlers;
using SistemaInfo.BBC.Domain.Models.Usuario.Repository;
using SistemaInfo.BBC.Domain.Models.UsuarioFilial;
using SistemaInfo.BBC.Domain.Models.UsuarioFilial.Commands;
using SistemaInfo.BBC.Domain.Models.UsuarioFilial.Commands.CommandHandlers;
using SistemaInfo.BBC.Domain.Models.UsuarioHistorico.Commands;
using SistemaInfo.BBC.Domain.Models.UsuarioHistorico.Commands.CommandHandlers;
using SistemaInfo.BBC.Domain.Models.UsuarioHistorico.Repository;
using SistemaInfo.BBC.Domain.Models.Veiculo;
using SistemaInfo.BBC.Domain.Models.Veiculo.Commands;
using SistemaInfo.BBC.Domain.Models.Veiculo.Commands.CommandHandlers;
using SistemaInfo.BBC.Domain.Models.Veiculo.Repository;
using SistemaInfo.BBC.Domain.Models.VeiculoCombustivel;
using SistemaInfo.BBC.Domain.Models.VeiculoCombustivel.Commands;
using SistemaInfo.BBC.Domain.Models.VeiculoCombustivel.Commands.CommandHandlers;
using SistemaInfo.BBC.Domain.Models.VeiculoCombustivel.Repository;
using SistemaInfo.BBC.Domain.Models.VeiculoEmpresa.Commands;
using SistemaInfo.BBC.Domain.Models.VeiculoEmpresa.Commands.CommandHandlers;
using SistemaInfo.BBC.Domain.Models.VeiculoEmpresa.Repository;
using SistemaInfo.BBC.Domain.Models.Viagem;
using SistemaInfo.BBC.Domain.Models.Viagem.Commands;
using SistemaInfo.BBC.Domain.Models.Viagem.Commands.CommandHandlers;
using SistemaInfo.BBC.Domain.Models.Viagem.Repository;
using SistemaInfo.BBC.Domain.Models.ViagemVeiculos.Commands;
using SistemaInfo.BBC.Domain.Models.ViagemVeiculos.Commands.CommandHandlers;
using SistemaInfo.BBC.Domain.Models.ViagemVeiculos.Repository;
using SistemaInfo.BBC.Infra.Data.Repository.ViagemVeiculos;
using SistemaInfo.BBC.Domain.TenantContext;
using SistemaInfo.BBC.Infra.CrossCutting.IoC.Interfaces;
using SistemaInfo.BBC.Infra.CrossCutting.IoC.Models;
using SistemaInfo.BBC.Infra.Data.Components.Email;
using SistemaInfo.BBC.Infra.Data.Context;
using SistemaInfo.BBC.Infra.Data.External.Captalys;
using SistemaInfo.BBC.Infra.Data.External.Caruana.Repository;
using SistemaInfo.BBC.Infra.Data.External.CIOT;
using SistemaInfo.BBC.Infra.Data.External.Conductor;
using SistemaInfo.BBC.Infra.Data.External.Infra;
using SistemaInfo.BBC.Infra.Data.External.JSLLeasing.Repository;
using SistemaInfo.BBC.Infra.Data.External.Mobile2You;
using SistemaInfo.BBC.Infra.Data.External.Movida;
using SistemaInfo.BBC.Infra.Data.External.SAP;
using SistemaInfo.BBC.Infra.Data.Repository;
using SistemaInfo.BBC.Infra.Data.Repository.Api;
using SistemaInfo.BBC.Infra.Data.Repository.AtualizacaoPrecoCombustivel;
using SistemaInfo.BBC.Infra.Data.Repository.Banco;
using SistemaInfo.BBC.Infra.Data.Repository.BloqueioSpd;
using SistemaInfo.BBC.Infra.Data.Repository.Cidade;
using SistemaInfo.BBC.Infra.Data.Repository.Cliente;
using SistemaInfo.BBC.Infra.Data.Repository.Combustivel;
using SistemaInfo.BBC.Infra.Data.Repository.ContaConductor;
using SistemaInfo.BBC.Infra.Data.Repository.DeclaracaoCiot;
using SistemaInfo.BBC.Infra.Data.Repository.Documento;
using SistemaInfo.BBC.Infra.Data.Repository.Empresa;
using SistemaInfo.BBC.Infra.Data.Repository.Emprestimo;
using SistemaInfo.BBC.Infra.Data.Repository.Estado;
using SistemaInfo.BBC.Infra.Data.Repository.Fabricante;
using SistemaInfo.BBC.Infra.Data.Repository.TipoEmpresa;
using SistemaInfo.BBC.Infra.Data.Repository.Abastecimento;
using SistemaInfo.BBC.Infra.Data.Repository.AuditoriaSeguranca;
using SistemaInfo.BBC.Infra.Data.Repository.AuthSession;
using SistemaInfo.BBC.Infra.Data.Repository.AuthSessionApi;
using SistemaInfo.BBC.Infra.Data.Repository.AutorizacaoAbastecimento;
using SistemaInfo.BBC.Infra.Data.Repository.MDRPrazos;
using SistemaInfo.BBC.Infra.Data.Repository.CentroCusto;
using SistemaInfo.BBC.Infra.Data.Repository.CFOP;
using SistemaInfo.BBC.Infra.Data.Repository.ClientSecret;
using SistemaInfo.BBC.Infra.Data.Repository.ClientSecretAdm;
using SistemaInfo.BBC.Infra.Data.Repository.DocumentosProcessoVinculado;
using SistemaInfo.BBC.Infra.Data.Repository.EmpresaCfop;
using SistemaInfo.BBC.Infra.Data.Repository.EmpresaGrupoUsuario;
using SistemaInfo.BBC.Infra.Data.Repository.EmpresaUsuario;
using SistemaInfo.BBC.Infra.Data.Repository.Filial;
using SistemaInfo.BBC.Infra.Data.Repository.GrupoEmpresa;
using SistemaInfo.BBC.Infra.Data.Repository.GrupoUsuario;
using SistemaInfo.BBC.Infra.Data.Repository.GrupoUsuarioMenu;
using SistemaInfo.BBC.Infra.Data.Repository.LotePagamento;
using SistemaInfo.BBC.Infra.Data.Repository.Mensagem;
using SistemaInfo.BBC.Infra.Data.Repository.Menu;
using SistemaInfo.BBC.Infra.Data.Repository.Modelo;
using SistemaInfo.BBC.Infra.Data.Repository.Modulo;
using SistemaInfo.BBC.Infra.Data.Repository.ModuloMenu;
using SistemaInfo.BBC.Infra.Data.Repository.NaturezaCarga;
using SistemaInfo.BBC.Infra.Data.Repository.Notificacao;
using SistemaInfo.BBC.Infra.Data.Repository.PagamentoAbastecimento;
using SistemaInfo.BBC.Infra.Data.Repository.PagamentoEvento;
using SistemaInfo.BBC.Infra.Data.Repository.PagamentoEventoHistorico;
using SistemaInfo.BBC.Infra.Data.Repository.Pagamentos;
using SistemaInfo.BBC.Infra.Data.Repository.ParametroConfiguracaoSla;
using SistemaInfo.BBC.Infra.Data.Repository.Parametros;
using SistemaInfo.BBC.Infra.Data.Repository.PercentualTransferencia;
using SistemaInfo.BBC.Infra.Data.Repository.PercentualTransferenciaHistorico;
using SistemaInfo.BBC.Infra.Data.Repository.PercentualTransferenciaPortador;
using SistemaInfo.BBC.Infra.Data.Repository.PercentualTransferenciaPortadorHistorico;
using SistemaInfo.BBC.Infra.Data.Repository.Portador;
using SistemaInfo.BBC.Infra.Data.Repository.PortadorCentroCusto;
using SistemaInfo.BBC.Infra.Data.Repository.PortadorEmpresa;
using SistemaInfo.BBC.Infra.Data.Repository.Posto;
using SistemaInfo.BBC.Infra.Data.Repository.PostoCombustivel;
using SistemaInfo.BBC.Infra.Data.Repository.PostoCombustivelProduto;
using SistemaInfo.BBC.Infra.Data.Repository.PostoContato;
using SistemaInfo.BBC.Infra.Data.Repository.ProtocoloAbastecimento;
using SistemaInfo.BBC.Infra.Data.Repository.Retencao;
using SistemaInfo.BBC.Infra.Data.Repository.ServidorCiot;
using SistemaInfo.BBC.Infra.Data.Repository.Transacao;
using SistemaInfo.BBC.Infra.Data.Repository.Usuario;
using SistemaInfo.BBC.Infra.Data.Repository.UsuarioHistorico;
using SistemaInfo.BBC.Infra.Data.Repository.Veiculo;
using SistemaInfo.BBC.Infra.Data.Repository.VeiculoCombustivel;
using SistemaInfo.BBC.Infra.Data.Repository.VeiculoEmpresa;
using SistemaInfo.BBC.Infra.Data.Repository.Viagem;
using SistemaInfo.BBC.Infra.Data.ServiceCloudTranslation;
using SistemaInfo.BBC.Infra.Data.TenantConnections;
using SistemaInfo.BBC.Infra.Reports.Interfaces;
using SistemaInfo.BBC.Infra.Reports.Services;
using SistemaInfo.Framework.CQRS;
using SistemaInfo.Framework.CQRS.Bus;
using SistemaInfo.Framework.CQRS.Models;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.UnityOfWork;
using SistemaInfo.Framework.DomainDrivenDesign.Infra.CQRS;
using SistemaInfo.Framework.DomainDrivenDesign.Infra.DataTransferObjects;
using SistemaInfo.Framework.DomainDrivenDesign.Web.Filters;
using SistemaInfo.Framework.Utils.AppConfiguration;
using SistemaInfo.Framework.Web.Filters;
using ContaConductorAdicionarCommand = SistemaInfo.BBC.Domain.Models.ContasConductor.Commands.ContaConductorAdicionarCommand;
using IContaConductorReadRepository = SistemaInfo.BBC.Domain.Models.ContasConductor.Repository.IContaConductorReadRepository;
using InMemoryBus = SistemaInfo.Framework.DomainDrivenDesign.CrossCutting.InMemoryBus;
using ModuloWriteRepository = SistemaInfo.BBC.Infra.Data.Repository.Modulo.ModuloWriteRepository;

namespace SistemaInfo.BBC.Infra.CrossCutting.IoC
{
    /// <summary>
    /// Registrar serviços necessários para funcionamento da aplicação
    /// </summary>
    public class DependencyInjector
    {
        /// <summary>
        /// Registrar serviços necessários para funcionamento da aplicação
        /// </summary>
        /// <param name="services"></param>
        public static void RegisterServices(IServiceCollection services)
        {
            AddAppInfrastructure(services);
            AddAppServices(services);
            AddMemoryHandlers(services);
            AddRepositories(services);
            AddComponents(services);
        }

        private static void AddComponents(IServiceCollection services)
        {
            services.AddScoped<INotificationEmailExecutor, NotificationEmailExecutor>();
        }

        /// <summary>
        /// Recuros ténicos para funcionamento da aplicação, framework's de baixo nível da aplicação
        /// </summary>
        /// <param name="services"></param>
        private static void AddAppInfrastructure(IServiceCollection services)
        {
            // Lista de serviços registrados para o container de injeção de dependência. Utilizando no InMemoryBus
            services.AddSingleton(services);
            services.AddSingleton<UrlConsts>();
            services.AddSingleton<AppConfiguration>();

            services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();
            
            // Obter instância única de recursos de infra centralizado
            services.AddScoped<IUnityOfWork, UnityOfWork<ConfigContext>>();
            services.AddScoped<IAppEngine, AppEngine>();

            // API's de integrações / Web
            services.AddScoped<IApiActionExceptionFilterResultResolver, ApiActionExceptionFilterResultDefaultResolver>();

            // Barramento
            services.AddScoped<IInMemoryBus, InMemoryBus>();
            services.AddScoped<IQueryBus, InMemoryBus>();
            services.AddScoped<ICommandBus, InMemoryBus>();
            services.AddScoped<IRemoteBus, AmqpBus>();
            services.AddScoped<IAmqpEvents, AmqpDomainDrivenDesignEvents>();
            services.AddScoped<IPortadorIdentity, PortadorIdentity>();
            
            
            
            var config = new MapperConfiguration(cfg =>
            {
                cfg.AddProfiles(typeof(SistemaInfoMappingProfile).Assembly);
                cfg.AllowNullCollections = true;
                cfg.AllowNullDestinationValues = true;
            });
            
            services.AddScoped<IMapper>(c => new Mapper(config, c.GetService));

            // Autenticação
            services.AddScoped<ISessionUser, SessionUser>();
        }

        /// <summary>
        /// Handlers resposáveis por executar interações de gravação com o banco de dados.
        /// Core business da aplicação, adicione seus manipuladores de regras de negócios que deve ser processadas dentro do contexto de informção do sistema final em execução
        /// </summary>
        /// <param name="services"></param>
        private static void AddMemoryHandlers(IServiceCollection services)
        {
            services.AddScoped<IDomainNotificationHandler<DomainNotification>, DomainNotificationHandler>();
            services.AddScoped<IHandler<EmpresaSaveCommand, Empresa>, EmpresaCommandHandler>();
            services.AddScoped<IHandler<EmpresaSaveStatusCommand>, EmpresaCommandHandler>();
            services.AddScoped<IHandler<EmpresaSaveComRetornoCommand, Empresa>, EmpresaCommandHandler>();
            services.AddScoped<IHandler<GrupoUsuarioAdicionarCommand>, GrupoUsuarioCommandHandler>();
            services.AddScoped<IHandler<GrupoUsuarioAdicionarComRetornoCommand, GrupoUsuario>, GrupoUsuarioCommandHandler>();
            services.AddScoped<IHandler<GrupoUsuarioEditarCommand>, GrupoUsuarioCommandHandler>();
            services.AddScoped<IHandler<GrupoEmpresaAdicionarCommand>, GrupoEmpresaCommandHandler>();
            services.AddScoped<IHandler<ClientSecretEditarCommand>, ClientSecretCommandHandler>();
            services.AddScoped<IHandler<ClientSecretAdicionarCommand>, ClientSecretCommandHandler>();
            services.AddScoped<IHandler<ClientSecretAlterarStatusCommand, ClientSecret>, ClientSecretCommandHandler>();
            services.AddScoped<IHandler<ClientSecretAdmEditarCommand, ClientSecretAdm>, ClientSecretAdmCommandHandler>();
            services.AddScoped<IHandler<ClientSecretAdmAdicionarCommand>, ClientSecretAdmCommandHandler>();
            services.AddScoped<IHandler<ClientSecretAdmAlterarStatusCommand, ClientSecretAdm>, ClientSecretAdmCommandHandler>();
            services.AddScoped<IHandler<GrupoEmpresaEditarCommand>, GrupoEmpresaCommandHandler>();
            services.AddScoped<IHandler<PagamentosAdicionarCommand, Pagamentos>, PagamentosCommandHandler>();
            services.AddScoped<IHandler<PagamentosAtualizarCommand, Pagamentos>, PagamentosCommandHandler>();
            services.AddScoped<IHandler<PagamentosAlterarStatusCommand>, PagamentosCommandHandler>();
            services.AddScoped<IHandler<PagamentosAlterarEtapaCommand>, PagamentosCommandHandler>();
            services.AddScoped<IHandler<UsuarioAdicionarCommand>, UsuarioCommandHandler>();
            services.AddScoped<IHandler<UsuarioAdicionarComRetornoCommand, Usuario>, UsuarioCommandHandler>();
            services.AddScoped<IHandler<AuthSessionEditarCommand, AuthSession>, AuthSessionCommandHandler>();
            services.AddScoped<IHandler<AuthSessionAdicionarCommand,AuthSession>, AuthSessionCommandHandler>();
            services.AddScoped<IHandler<UsuarioEditarCommand>, UsuarioCommandHandler>();
            services.AddScoped<IHandler<UsuarioAlterarSenhaCommand>, UsuarioCommandHandler>();
            services.AddScoped<IHandler<PortadorAlterarSenhaCommand>, PortadorCommandHandler>();
            services.AddScoped<IHandler<PortadorSenhaErradaCommand>, PortadorCommandHandler>();
            services.AddScoped<IHandler<PortadorSenhaCorretaCommand>, PortadorCommandHandler>();
            services.AddScoped<IHandler<PortadorUltimoAcessoCommand>, PortadorCommandHandler>();
            services.AddScoped<IHandler<PortadorSalvarCommand>, PortadorCommandHandler>();
            services.AddScoped<IHandler<PortadorSalvarComRetornoCommand, Portador>, PortadorCommandHandler>();
            services.AddScoped<IHandler<PortadorAtualizarV2Command, Portador>, PortadorCommandHandler>();
            services.AddScoped<IHandler<PortadorAlterarStatusCommand>, PortadorCommandHandler>();
            services.AddScoped<IHandler<BaseGrupoUsuarioComand, GrupoUsuario>, GrupoUsuarioCommandHandler>();
            services.AddScoped<IHandler<GrupoUsuarioSaveCommand>, GrupoUsuarioCommandHandler>();
            services.AddScoped<IHandler<GrupoUsuarioSaveComRetornoCommand, GrupoUsuario>, GrupoUsuarioCommandHandler>();
            services.AddScoped<IHandler<DeclaracaoCiotSalvarCommand>, DeclaracaoCiotCommandHandler>();
            services.AddScoped<IHandler<DeclaracaoCiotAlterarStatusCommand>, DeclaracaoCiotCommandHandler>();
            services.AddScoped<IHandler<FilialSaveCommand>, FilialCommandHandler>();
            services.AddScoped<IHandler<VeiculoSalvarCommand>, VeiculoCommandHandler>();
            services.AddScoped<IHandler<VeiculoSalvarComRetornoCommand, Veiculo>, VeiculoCommandHandler>();
            services.AddScoped<IHandler<VeiculoAlterarStatusCommand>, VeiculoCommandHandler>();
            services.AddScoped<IHandler<ClienteSalvarCommand>, ClienteCommandHandler>();
            services.AddScoped<IHandler<ClienteAlterarStatusCommand>, ClienteCommandHandler>();
            services.AddScoped<IHandler<ClienteSalvarComRetornoCommand>, ClienteCommandHandler>();
            services.AddScoped<IHandler<ClienteSalvarComRetornoCommand, Cliente>, ClienteCommandHandler>();
            services.AddScoped<IHandler<FilialAlterarStatusCommand>, FilialCommandHandler>();
            services.AddScoped<IHandler<EmprestimoSalvarCommand>, EmprestimoCommandHandler>();
            services.AddScoped<IHandler<FilialSaveComRetornoCommand, Filial>, FilialCommandHandler>();
            services.AddScoped<IHandler<RetencaoSalvarCommand>, RetencaoCommandHandler>();
            services.AddScoped<IHandler<ParametrosSalvarCommand>, ParametrosCommandHandler>();
            services.AddScoped<IHandler<VeiculoEmpresaSalvarCommand>, VeiculoEmpresaCommandHandler>();
            services.AddScoped<IHandler<PortadorEmpresaSalvarCommand>, PortadorEmpresaCommandHandler>();
            services.AddScoped<IHandler<PortadorCentroCustoSalvarCommand>, PortadorCentroCustoCommandHandler>();
            services.AddScoped<IHandler<ContaConductorAdicionarCommand, ContaConductor>, ContaConductorCommandHandler>();
            services.AddScoped<IHandler<DocumentoSalvarCommand>, DocumentoCommandHandler>();
            services.AddScoped<IHandler<DocumentoExcluirCommand>, DocumentoCommandHandler>();
            services.AddScoped<IHandler<DocumentoSetarEnviadoBBCCommand>, DocumentoCommandHandler>();
            services.AddScoped<IHandler<BloqueioSpdAlterarStatusCommandComRentorno, BloqueioSpd>, BloqueioSpdCommandHandler>();
            services.AddScoped<IHandler<BloqueioSpdSaveComRetornoCommand, BloqueioSpd>, BloqueioSpdCommandHandler>();
            services.AddScoped<IHandler<PostoSaveCommand>, PostoCommandHandler>();
            services.AddScoped<IHandler<PostoAlterarStatusCommand>, PostoCommandHandler>();
            services.AddScoped<IHandler<PostoSaveComRetornoCommand, Posto>, PostoCommandHandler>();
            services.AddScoped<IHandler<PostoSavePasswordCommand, Posto>, PostoCommandHandler>();
            services.AddScoped<IHandler<PostoCombustivelSaveCommand>, PostoCombustivelCommandHandler>();
            services.AddScoped<IHandler<PostoCombustivelSaveComRetornoCommand, PostoCombustivel>, PostoCombustivelCommandHandler>();
            services.AddScoped<IHandler<PostoCombustivelProdutoSalvarCommand>, PostoCombustivelProdutoCommandHandler>();
            services.AddScoped<IHandler<PostoContatoSaveCommand>, PostoContatoCommandHandler>();
            services.AddScoped<IHandler<PostoContatoSaveComRetornoCommand, PostoContato>, PostoContatoCommandHandler>();
            services.AddScoped<IHandler<FabricanteSalvarComRetornoCommand, Fabricante>, FabricanteCommandHandler>();
            services.AddScoped<IHandler<FabricanteAlterarStatusCommand, Fabricante>, FabricanteCommandHandler>();
            services.AddScoped<IHandler<ProtocoloAbastecimentoSalvarComRetornoCommand, ProtocoloAbastecimento>, ProtocoloAbastecimentoCommandHandler>();
            services.AddScoped<IHandler<TipoEmpresaSalvarComRetornoCommand, TipoEmpresa>, TipoEmpresaCommandHandler>();
            services.AddScoped<IHandler<TipoEmpresaAlterarStatusCommand>, TipoEmpresaCommandHandler>();
            services.AddScoped<IHandler<AbastecimentoSalvarComRetornoCommand, Abastecimento>, AbastecimentoCommandHandler>();
            services.AddScoped<IHandler<RemoverAutorizacaoAbastecimentoCommand>, AbastecimentoCommandHandler>();
            services.AddScoped<IHandler<AbastecimentoSalvarCommand>, AbastecimentoCommandHandler>();
            services.AddScoped<IHandler<MDRPrazosSalvarComRetornoCommand, MDRPrazos>, MDRPrazosCommandHandler>();
            services.AddScoped<IHandler<MDRPrazosAlterarStatusCommand, MDRPrazos>, MDRPrazosCommandHandler>();
            services.AddScoped<IHandler<BancoSalvarComRetornoCommand, Banco>, BancoCommandHandler>();
            services.AddScoped<IHandler<BancoAlterarStatusCommand, Banco>, BancoCommandHandler>();
            services.AddScoped<IHandler<ModeloSalvarComRetornoCommand, Modelo>, ModeloCommandHandler>();
            services.AddScoped<IHandler<ModeloAlterarStatusCommand>, ModeloCommandHandler>();
            services.AddScoped<IHandler<MensagemSalvarComRetornoCommand, Mensagem>, MensagemCommandHandler>();
            services.AddScoped<IHandler<MensagemAdicionarComRetornoCommand, Mensagem>, MensagemCommandHandler>();
            services.AddScoped<IHandler<TransacaoSalvarComRetornoCommand, Transacao>, TransacaoCommandHandler>();
            services.AddScoped<IHandler<TransacaoAlterarStatusCommand>, TransacaoCommandHandler>();
            services.AddScoped<IHandler<Transacao>, TransacaoCommandHandler>();
            services.AddScoped<IHandler<TransacaoListAlterarStatusCommand>, TransacaoCommandHandler>();
            services.AddScoped<IHandler<MensagemAlterarStatusCommand, Mensagem>, MensagemCommandHandler>();
            services.AddScoped<IHandler<CombustivelSalvarComRetornoCommand, Combustivel>, CombustivelCommandHandler>();
            services.AddScoped<IHandler<CombustivelAlterarStatusCommand>, CombustivelCommandHandler>();
            services.AddScoped<IHandler<AtualizacaoPrecoCombustivelSalvarComRetornoCommand, AtualizacaoPrecoCombustivel>, AtualizacaoPrecoCombustivelCommandHandler>();
            services.AddScoped<IHandler<CentroCustoSalvarComRetornoCommand, CentroCusto>, CentroCustoCommandHandler>();
            services.AddScoped<IHandler<CentroCustoAlterarStatusCommand>, CentroCustoCommandHandler>();
            services.AddScoped<IHandler<CentroCustoAlterarVinculoFilialCommand>, CentroCustoCommandHandler>();
            services.AddScoped<IHandler<CentroCustoExcluirVinculoCommand>, CentroCustoCommandHandler>();
            services.AddScoped<IHandler<PagamentoEventoSalvarComRetornoCommand, PagamentoEvento>, CentralPendenciasCommandHandler>();
            services.AddScoped<IHandler<PagamentoEvento,PagamentoEvento>, CentralPendenciasCommandHandler>();
            services.AddScoped<IHandler<CodigoTransacaoSalvarCommand, PagamentoEvento>, CentralPendenciasCommandHandler>();
            services.AddScoped<IHandler<ValorTransferenciaMotoristaCommand, PagamentoEvento>, CentralPendenciasCommandHandler>();
            services.AddScoped<IHandler<NotificacaoSalvarComRetornoCommand, Notificacao>, CentralNotificacoesCommandHandler>();
            services.AddScoped<IHandler<ViagemSalvarCommand>, ViagemCommandHandler>();
            services.AddScoped<IHandler<ViagemSalvarComRetornoCommand, Viagem>, ViagemCommandHandler>();
            services.AddScoped<IHandler<ViagemAlterarStatusCommand>, ViagemCommandHandler>();
            services.AddScoped<IHandler<AuditoriaSegurancaSalvarCommand>, AuditoriaSegurancaCommandHandler>();
            services.AddScoped<IHandler<AuditoriaSegurancaSalvarComRetornoCommand, AuditoriaSeguranca>, AuditoriaSegurancaCommandHandler>();
            services.AddScoped<IHandler<VeiculoCombustivelSalvarCommand>, VeiculoCombustivelCommandHandler>();
            services.AddScoped<IHandler<VeiculoCombustivelSalvarComRetornoCommand, VeiculoCombustivel>, VeiculoCombustivelCommandHandler>();
            services.AddScoped<IHandler<AutorizacaoAbastecimentoSalvarCommand>, AutorizacaoAbastecimentoCommandHandler>();
            services.AddScoped<IHandler<AutorizacaoAbastecimentoSalvarComRetornoCommand, AutorizacaoAbastecimento>, AutorizacaoAbastecimentoCommandHandler>();
            services.AddScoped<IHandler<MenuSalvarCommand>, MenuCommandHandler>();
            services.AddScoped<IHandler<MenuSalvarComRetornoCommand, Menu>, MenuCommandHandler>();
            services.AddScoped<IHandler<ModuloSalvarCommand>, ModuloCommandHandler>();
            services.AddScoped<IHandler<ModuloSalvarComRetornoCommand, Modulo>, ModuloCommandHandler>();
            services.AddScoped<IHandler<ModuloMenuSalvarComRetornoCommand, ModuloMenu>, ModuloMenuCommandHandler>();
            services.AddScoped<IHandler<PagamentoAbastecimentoAdicionarCommand, PagamentoAbastecimento>, PagamentoAbastecimentoCommandHandler>();
            services.AddScoped<IHandler<PagamentoAbastecimentoAtualizarCommand, PagamentoAbastecimento>, PagamentoAbastecimentoCommandHandler>();
            services.AddScoped<IHandler<ProtocoloAbastecimentoAlterarStatusCommand, ProtocoloAbastecimento>, ProtocoloAbastecimentoCommandHandler>();
            services.AddScoped<IHandler<ProtocoloAbastecimentoSalvarLoteCommand>, ProtocoloAbastecimentoCommandHandler>();
            services.AddScoped<IHandler<AutorizacaoAbastecimentoCancelarComRetornoCommand, AutorizacaoAbastecimento>, AutorizacaoAbastecimentoCommandHandler>();
            services.AddScoped<IHandler<UsuarioFilialSalvarComRetornoCommand, UsuarioFilial>, UsuarioFilialCommandHandler>();
            services.AddScoped<IHandler<LotePagamentoSalvarCommand>, LotePagamentoCommandHandler>();
            services.AddScoped<IHandler<LotePagamentoSalvarComRetornoCommand, LotePagamento>, LotePagamentoCommandHandler>();
            services.AddScoped<IHandler<PercentualTransferenciaSalvarCommand>, PercentualTransferenciaCommandHandler>();
            services.AddScoped<IHandler<PercentualTransferenciaPortadorSalvarCommand>, PercentualTransferenciaPortadorCommandHandler>();
            services.AddScoped<IHandler<PercentualTransferenciaPortadorAlterarStatusCommand>, PercentualTransferenciaPortadorCommandHandler>();
            services.AddScoped<IHandler<PercentualTransferenciaSalvarComRetornoCommand, PercentualTransferencia>, PercentualTransferenciaCommandHandler>();
            services.AddScoped<IHandler<UsuarioHistoricoAdicionarCommand>, UsuarioHistoricoCommandHandler>();
            services.AddScoped<IHandler<DocumentosProcessoVinculadoSalvarComRetornoCommand, DocumentosProcessoVinculado>, DocumentosProcessoVinculadoCommandHandler>();
            services.AddScoped<IHandler<DocumentosProcessoVinculadoSalvarCommand>, DocumentosProcessoVinculadoCommandHandler>();
            services.AddScoped<IHandler<DocumentosProcessoVinculadoAlterarStatusCommand>, DocumentosProcessoVinculadoCommandHandler>();
            services.AddScoped<IHandler<PostoAprovarCredenciamentoCommand>, PostoCommandHandler>();
            services.AddScoped<IHandler<PostoReprovarCredenciamentoCommand>, PostoCommandHandler>();
            services.AddScoped<IHandler<ParametroConfiguracaoSlaSalvarCommand>, ParametroConfiguracaoSlaCommandHandler>();
            services.AddScoped<IHandler<AbastecimentoSalvarLoteRetencaoCommand>, AbastecimentoCommandHandler>();
            services.AddScoped<IHandler<TransportadorSalvarCommand>, PortadorCommandHandler>();
            services.AddScoped<IHandler<TransportadorSalvarComRetornoCommand, Portador>, PortadorCommandHandler>();
            services.AddScoped<IHandler<ServidorCiotSalvarCommand, ServidorCiot>, ServidorCiotCommandHandler>();
            services.AddScoped<IHandler<ParametroSalvarComRetornoCommand, Parametros>, ParametrosCommandHandler>();
            services.AddScoped<IHandler<EmpresaGrupoUsuarioSalvarCommand>, EmpresaGrupoUsuarioCommandHandler>();
            services.AddScoped<IHandler<EmpresaGrupoUsuarioSalvarComRetornoCommand, EmpresaGrupoUsuario>, EmpresaGrupoUsuarioCommandHandler>();
            services.AddScoped<IHandler<EmpresaGrupoUsuarioExcluirCommand>, EmpresaGrupoUsuarioCommandHandler>();
            services.AddScoped<IHandler<EmpresaUsuarioSalvarCommand>, EmpresaUsuarioCommandHandler>();
            services.AddScoped<IHandler<EmpresaUsuarioSalvarComRetornoCommand, EmpresaUsuario>, EmpresaUsuarioCommandHandler>();
            services.AddScoped<IHandler<EmpresaUsuarioExcluirCommand>, EmpresaUsuarioCommandHandler>();
            services.AddScoped<IHandler<ViagemVeiculosSalvarCommand>, ViagemVeiculosCommandHandler>();
        }

        /// <summary>
        /// Serviços de consultas para obter informações
        /// </summary>
        /// <param name="services"></param>
        private static void AddAppServices(IServiceCollection services)
        {
            services.AddScoped<IEmpresaAppService, EmpresaAppService>();
            services.AddScoped<IPostoAppService, PostoAppService>();
            services.AddScoped<IPedagioIntegracaoAppService, PedagioIntegracaoAppService>();
            services.AddScoped<IViagemIntegracaoAppService, ViagemIntegracaoAppService>();
            services.AddScoped<IPostoCombustivelAppService, PostoCombustivelAppService>();
            services.AddScoped<IPostoContatoAppService, PostoContatoAppService>();
            services.AddScoped<IGrupoUsuarioAppService, GrupoUsuarioAppService>();
            services.AddScoped<IGrupoEmpresaAppService, GrupoEmpresaAppService>();
            services.AddScoped<IClientSecretAppService, ClientSecretAppService>();
            services.AddScoped<IClientSecretAdmAppService, ClientSecretAdmAppService>();
            services.AddScoped<IUsuarioAppService, UsuarioAppService>();
            services.AddScoped<IMenuAppService, MenuAppService>();
            services.AddScoped<IPagamentosAppService, PagamentosAppService>();
            services.AddScoped<IModuloAppService, ModuloAppService>();
            services.AddScoped<INaturezaCargaAppService, NaturezaCargaAppService>();
            services.AddScoped<IPortadorAppService, PortadorAppService>();
            services.AddScoped<IPixAppService, PixAppService>();
            services.AddScoped<IPixWebhookAppService, PixWebhookAppService>();
            services.AddScoped<IDeclaracaoCiotAppService, DeclaracaoCiotAppService>();
            services.AddScoped<IVeiculoAppService, VeiculoAppService>();
            services.AddScoped<ICidadeAppService, CidadeAppService>();
            services.AddScoped<IClienteAppService, ClienteAppService>();
            services.AddScoped<IFilialAppService, FilialAppService>();
            services.AddScoped<ICartaoAppService, CartaoAppService>();
            services.AddScoped<IBancoAppService, BancoAppService>();
            services.AddScoped<IContaAppService, ContaAppService>();
            services.AddScoped<IEmprestimoAppService, EmprestimoAppService>();
            services.AddScoped<IEmprestimoCaptalysAppService, EmprestimoCaptalysAppService>();
            services.AddScoped<IRetencaoAppService, RetencaoAppService>();
            services.AddScoped<IParametrosAppService, ParametrosAppService>();
            services.AddScoped<IPedagioAppService, PedagioAppService>();
            services.AddScoped<IRetencaoCaptalysAppService, RetencaoCaptalysAppService>();
            services.AddScoped<ITransacaoRegisterAppService, TransacaoRegisterAppService>();
            services.AddScoped<ITransacaoAppService, TransacaoAppService>();
            services.AddScoped<IContaConductorAppService, ContaConductorAppService>();
            services.AddScoped<IAceiteTermoAppService, AceiteTermoAppService>();
            services.AddScoped<IDocumentoAppService, DocumentoAppService>();
            services.AddScoped<IBloqueioSpdAppService, BloqueioSpdAppService>();
            services.AddScoped<IFabricanteAppService, FabricanteAppService>();
            services.AddScoped<IProtocoloAbastecimentoAppService, ProtocoloAbastecimentoAppService>();
            services.AddScoped<ITipoEmpresaAppService, TipoEmpresaAppService>();
            services.AddScoped<IAbastecimentoAppService, AbastecimentoAppService>();
            services.AddScoped<IMDRPrazosAppService, MDRPrazosAppService>();
            services.AddScoped<IMensagemAppService, MensagemAppService>();
            services.AddScoped<ICombustivelAppService, CombustivelAppService>();
            services.AddScoped<IModeloAppService, ModeloAppService>();
            services.AddScoped<IAtualizacaoPrecoCombustivelAppService, AtualizacaoPrecoCombustivelAppService>();
            services.AddScoped<ICentroCustoAppService, CentroCustoAppService>();
            services.AddScoped<ICentralNotificacoesAppService, CentralNotificacoesAppService>();
            services.AddScoped<ICentralPendenciasAppService, CentralPendenciasAppService>();
            services.AddScoped<IViagemAppService, ViagemAppService>();
            services.AddScoped<IViagemMobileAppService, ViagemMobileAppService>();
            services.AddScoped<IAuditoriaSegurancaAppService, AuditoriaSegurancaAppService>();
            services.AddScoped<IClientSecretAppService, ClientSecretAppService>();
            services.AddScoped<IVeiculoCombustivelAppService, VeiculoCombustivelAppService>();
            services.AddScoped<IAutorizacaoAbastecimentoAppService, AutorizacaoAbastecimentoAppService>();
            services.AddScoped<IAutorizacaoContingeciaAppService, AutorizacaoContingeciaAppService>();
            services.AddScoped<IModuloMenuAppService, ModuloMenuAppService>();
            services.AddScoped<IAbastecimentoAppService, AbastecimentoAppService>();
            services.AddScoped<IAuthSessionAppService, AuthSessionAppService>();
            services.AddScoped<IAuthSessionApiAppService, AuthSessionApiAppService>();
            services.AddScoped<IPagamentoAbastecimentoAppService, PagamentoAbastecimentoAppService>();
            services.AddScoped<IPagamentoEventoAppSerivce, PagamentoEventoAppService>();
            services.AddScoped<ICFOPAppService, CFOPAppService>();
            services.AddScoped<IPercentualTransferenciaAppService, PercentualTransferenciaAppService>();
            services.AddScoped<IDocumentosProcessoVinculadoAppService, DocumentosProcessoVinculadoAppService>();
            services.AddScoped<IReportExporterFinanceiro, ReportExporterFinanceiro>();
            services.AddScoped<IMonitoramentoCiotAppService, MonitoramentoCiotAppService>();
            services.AddScoped<ICloudTranslationService, CloudTranslationService>();
            services.AddScoped<IOperacoesAppService, OperacoesAppService>();
            services.AddScoped<IAuthSessionApiCiotAppService, AuthSessionApiCiotAppService>();
            services.AddScoped<ISaldoAppService, SaldoAppService>();
        }

        /// <summary>
        /// Repositoróios responsáveis por prover/salvar dados da aplicação
        /// </summary>
        /// <param name="services"></param>
        private static void AddRepositories(IServiceCollection services)
        {
            services.AddScoped<ConfigContext>();

            services.AddScoped<IBancoReadRepository, BancoReadRepository>();
            services.AddScoped<IBancoWriteRepository, BancoWriteRepository>();

            //Definição da configuração de dados utilizado
            services.AddScoped<ITenatConfig, PostgresTenantConfig>();

            services.AddScoped<ICidadeReadRepository, CidadeReadRepository>();
            services.AddScoped<ICidadeWriteRepository, CidadeWriteRepository>();
            services.AddScoped<IClienteReadRepository, ClienteReadRepository>();
            services.AddScoped<IClienteWriteRepository, ClienteWriteRepository>();
            services.AddScoped<IDeclaracaoCiotReadRepository, DeclaracaoCiotReadRepository>();
            services.AddScoped<IDeclaracaoCiotWriteRepository, DeclaracaoCiotWriteRepository>();
            services.AddScoped<IEstadoReadRepository, EstadoReadRepository>();
            services.AddScoped<IEstadoWriteRepository, EstadoWriteRepository>();
            services.AddScoped<IEmpresaReadRepository, EmpresaReadRepository>();
            services.AddScoped<IEmpresaWriteRepository, EmpresaWriteRepository>();
            services.AddScoped<IPostoReadRepository, PostoReadRepository>();
            services.AddScoped<IPostoCombustivelWriteRepository, PostoCombustivelWriteRepository>();
            services.AddScoped<IPostoCombustivelReadRepository, PostoCombustivelReadRepository>();
            services.AddScoped<IPostoCombustivelProdutoWriteRepository, PostoCombustivelProdutoWriteRepository>();
            services.AddScoped<IPostoCombustivelProdutoReadRepository, PostoCombustivelProdutoReadRepository>();
            services.AddScoped<IPostoWriteRepository, PostoWriteRepository>();
            services.AddScoped<IPostoContatoReadRepository, PostoContatoReadRepository>();
            services.AddScoped<IPostoContatoWriteRepository, PostoContatoWriteRepository>();
            services.AddScoped<IGrupoUsuarioReadRepository, GrupoUsuarioReadRepository>();
            services.AddScoped<IGrupoUsuarioWriteRepository, GrupoUsuarioWriteRepository>();
            services.AddScoped<IGrupoEmpresaReadRepository, GrupoEmpresaReadRepository>();
            services.AddScoped<IGrupoEmpresaWriteRepository, GrupoEmpresaWriteRepository>();
            services.AddScoped<IGrupoUsuarioMenuReadRepository, GrupoUsuarioMenuReadRepository>();
            services.AddScoped<IGrupoUsuarioMenuWriteRepository, GrupoUsuarioMenuWriteRepository>();
            services.AddScoped<IClientSecretReadRepository, ClientSecretReadRepository>();
            services.AddScoped<IClientSecretWriteRepository, ClientSecretWriteRepository>();
            services.AddScoped<IClientSecretAdmReadRepository, ClientSecretAdmReadRepository>();
            services.AddScoped<IClientSecretAdmWriteRepository, ClientSecretAdmWriteRepository>();
            services.AddScoped<IMenuReadRepository, MenuReadRepository>();
            services.AddScoped<IMenuWriteRepository, MenuWriteRepository>();
            services.AddScoped<IModuloReadRepository, ModuloReadRepository>();
            services.AddScoped<IModuloWriteRepository, ModuloWriteRepository>();
            services.AddScoped<INaturezaCargaReadRepository, NaturezaCargaReadRepository>();
            services.AddScoped<INaturezaCargaWriteRepository, NaturezaCargaWriteRepository>();
            services.AddScoped<IPagamentosReadRepository, PagamentosReadRepository>();
            services.AddScoped<IPagamentosWriteRepository, PagamentosWriteRepository>();
            services.AddScoped<IPortadorWriteRepository, PortadorWriteRepository>();
            services.AddScoped<IPortadorReadRepository, PortadorReadRepository>();
            services.AddScoped<IUsuarioReadRepository, UsuarioReadRepository>();
            services.AddScoped<IUsuarioWriteRepository, UsuarioWriteRepository>();
            services.AddScoped<IEmpresaUsuarioReadRepository, EmpresaUsuarioReadRepository>();
            services.AddScoped<IEmpresaUsuarioWriteRepository, EmpresaUsuarioWriteRepository>();
            services.AddScoped<IEmpresaGrupoUsuarioReadRepository, EmpresaGrupoUsuarioReadRepository>();
            services.AddScoped<IEmpresaGrupoUsuarioWriteRepository, EmpresaGrupoUsuarioWriteRepository>();
            services.AddScoped<IVeiculoReadRepository, VeiculoReadRepository>();
            services.AddScoped<IVeiculoWriteRepository, VeiculoWriteRepository>();
            services.AddScoped<IFilialReadRepository, FilialReadRepository>();
            services.AddScoped<IFilialWriteRepository, FilialWriteRepository>();
            services.AddScoped<ICartaoRepository, CartaoRepository>();
            services.AddScoped<IUsuarioRepository, UsuarioRepository>();
            services.AddScoped<IConductorRepository, ConductorRepository>();
            services.AddScoped<ITransferenciaRepository, TransferenciaRepository>();
            services.AddScoped<IContaRepository, ContaRepository>();
            services.AddScoped<IEmprestimoReadRepository, EmprestimoReadRepository>();
            services.AddScoped<IEmprestimoWriteRepository, EmprestimoWriteRepository>();
            services.AddScoped<IEmprestimoCaptalysRepository, EmprestimoCaptalysRepository>();
            services.AddScoped<IRetencaoReadRepository, RetencaoReadRepository>();
            services.AddScoped<IRetencaoWriteRepository, RetencaoWriteRepository>();
            services.AddScoped<IParametrosReadRepository, ParametrosReadRepository>();
            services.AddScoped<IParametrosWriteRepository, ParametrosWriteRepository>();
            services.AddScoped<IParametroConfiguracaoSlaReadRepository, ParametroConfiguracaoSlaReadRepository>();
            services.AddScoped<IParametroConfiguracaoSlaWriteRepository, ParametroConfiguracaoSlaWriteRepository>();
            services.AddScoped<IRetencaoCaptalysRepository, RetencaoCaptalysRepository>();
            services.AddScoped<ITransacaoRepository, TransacaoRepository>();
            services.AddScoped<IMobile2YouRepository, Mobile2YouRepository>();
            services.AddScoped<IVeiculoEmpresaWriteRepository, VeiculoEmpresaWriteRepository>();
            services.AddScoped<IVeiculoEmpresaReadRepository, VeiculoEmpresaReadRepository>();
            services.AddScoped<IPortadorEmpresaWriteRepository, PortadorEmpresaWriteRepository>();
            services.AddScoped<IPortadorEmpresaReadRepository, PortadorEmpresaReadRepository>();
            services.AddScoped<IPortadorCentroCustoWriteRepository, PortadorCentroCustoWriteRepository>();
            services.AddScoped<IPortadorCentroCustoReadRepository, PortadorCentroCustoReadRepository>();
            services.AddScoped<IContaConductorReadRepository, ContaConductorReadRepository>();
            services.AddScoped<IContaConductorWriteRepository, ContaConductorWriteRepository>();
            services.AddScoped<IConductorPixRepository, ConductorPixRepository>();
            services.AddScoped<IAceiteTermoRepository, AceiteTermoRepository>();
            services.AddScoped<IDocumentoReadRepository, DocumentoReadRepository>();
            services.AddScoped<IDocumentoWriteRepository, DocumentoWriteRepository>();
            services.AddScoped<IBloqueioSpdReadRepository, BloqueioSpdReadRepository>();
            services.AddScoped<IBloqueioSpdWriteRepository, BloqueioSpdWriteRepository>();
            services.AddScoped<IFabricanteWriteRepository, FabricanteWriteRepository>();
            services.AddScoped<IFabricanteReadRepository, FabricanteReadRepository>();
            services.AddScoped<IProtocoloAbastecimentoWriteRepository, ProtocoloAbastecimentoWriteRepository>();
            services.AddScoped<IProtocoloAbastecimentoReadRepository, ProtocoloAbastecimentoReadRepository>();
            services.AddScoped<ITipoEmpresaWriteRepository, TipoEmpresaWriteRepository>();
            services.AddScoped<ITipoEmpresaReadRepository, TipoEmpresaReadRepository>();
            services.AddScoped<IAbastecimentoWriteRepository, AbastecimentoWriteRepository>();
            services.AddScoped<IAbastecimentoReadRepository, AbastecimentoReadRepository>();
            services.AddScoped<IMDRPrazosWriteRepository, MDRPrazosWriteRepository>();
            services.AddScoped<IMDRPrazosReadRepository, MDRPrazosReadRepository>();
            services.AddScoped<IMensagemWriteRepository, MensagemWriteRepository>();
            services.AddScoped<ITransacaoWriteRepository, TransacaoWriteRepository>();
            services.AddScoped<IMensagemReadRepository, MensagemReadRepository>();
            services.AddScoped<ITransacaoReadRepository, TransacaoReadRepository>();
            services.AddScoped<ICombustivelWriteRepository, CombustivelWriteRepository>();
            services.AddScoped<ICombustivelReadRepository, CombustivelReadRepository>();
            services.AddScoped<IModeloWriteRepository, ModeloWriteRepository>();
            services.AddScoped<IModeloReadRepository, ModeloReadRepository>();
            services.AddScoped<IAtualizacaoPrecoCombustivelWriteRepository, AtualizacaoPrecoCombustivelWriteRepository>();
            services.AddScoped<IAtualizacaoPrecoCombustivelReadRepository, AtualizacaoPrecoCombustivelReadRepository>();
            services.AddScoped<ICentroCustoWriteRepository, CentroCustoWriteRepository>();
            services.AddScoped<ICentroCustoReadRepository, CentroCustoReadRepository>();
            services.AddScoped<IPagamentoEventoWriteRepository, PagamentoEventoWriteRepository>();
            services.AddScoped<IPagamentoEventoReadRepository, PagamentoEventoReadRepository>();
            services.AddScoped<IPagamentoEventoHistoricoWriteRepository, PagamentoEventoHistoricoWriteRepository>();
            services.AddScoped<IPagamentoEventoHistoricoReadRepository, PagamentoEventoHistoricoReadRepository>();
            services.AddScoped<INotificacaoWriteRepository, NotificacaoWriteRepository>();
            services.AddScoped<INotificacaoReadRepository, NotificacaoReadRepository>();
            services.AddScoped<IViagemWriteRepository, ViagemWriteRepository>();
            services.AddScoped<IViagemReadRepository, ViagemReadRepository>();
            services.AddScoped<IViagemVeiculosWriteRepository, ViagemVeiculosWriteRepository>();
            services.AddScoped<IViagemVeiculosReadRepository, ViagemVeiculosReadRepository>();
            services.AddScoped<IAuditoriaSegurancaWriteRepository, AuditoriaSegurancaWriteRepository>();
            services.AddScoped<IAuditoriaSegurancaReadRepository, AuditoriaSegurancaReadRepository>();
            services.AddScoped<IVeiculoCombustivelWriteRepository, VeiculoCombustivelWriteRepository>();
            services.AddScoped<IVeiculoCombustivelReadRepository, VeiculoCombustivelReadRepository>();
            services.AddScoped<IAutorizacaoAbastecimentoWriteRepository, AutorizacaoAbastecimentoWriteRepository>();
            services.AddScoped<IAutorizacaoAbastecimentoReadRepository, AutorizacaoAbastecimentoReadRepository>();
            services.AddScoped<IModuloMenuWriteRepository, ModuloMenuWriteRepository>();
            services.AddScoped<IModuloMenuReadRepository, ModuloMenuReadRepository>();
            services.AddScoped<IAuthSessionWriteRepository, AuthSessionWriteRepository>();
            services.AddScoped<IAuthSessionReadRepository, AuthSessionReadRepository>();
            services.AddScoped<IAuthSessionApiWriteRepository, AuthSessionApiWriteRepository>();
            services.AddScoped<IAuthSessionApiReadRepository, AuthSessionApiReadRepository>();
            services.AddScoped<IAbastecimentoMovidaRepository, AbastecimentoMovidaRepository>();
            services.AddScoped<IPagamentoAbastecimentoReadRepository, PagamentoAbastecimentoReadRepository>();
            services.AddScoped<IPagamentoAbastecimentoWriteRepository, PagamentoAbastecimentoWriteRepository>();
            services.AddScoped<ICFOPWriteRepository, CFOPWriteRepository>();
            services.AddScoped<ICFOPReadRepository, CFOPReadRepository>();
            services.AddScoped<IEmpresaCfopReadRepository, EmpresaCfopReadRepository>();
            services.AddScoped<IEmpresaCfopWriteRepository, EmpresaCfopWriteRepository>();
            services.AddScoped<ILotePagamentoReadRepository, LotePagamentoReadRepository>();
            services.AddScoped<ILotePagamentoWriteRepository, LotePagamentoWriteRepository>();
            services.AddScoped<IPedidoSAPRepository, PedidoSAPRepository>();
            services.AddScoped<ISAPRepository, SAPRepository>();
            services.AddScoped<ICiotClienteIpRepository, CiotClienteIpRepository>();
            services.AddScoped<IInfraLogRepository, InfraLogRepository>();
            

            services.AddScoped<IPercentualTransferenciaReadRepository, PercentualTransferenciaReadRepository>();
            services.AddScoped<IPercentualTransferenciaWriteRepository, PercentualTransferenciaWriteRepository>();
            services.AddScoped<IPercentualTransferenciaHistoricoReadRepository, PercentualTransferenciaHistoricoReadRepository>();
            services.AddScoped<IPercentualTransferenciaHistoricoWriteRepository, PercentualTransferenciaHistoricoWriteRepository>();
            services.AddScoped<IPercentualTransferenciaPortadorReadRepository, PercentualTransferenciaPortadorReadRepository>();
            services.AddScoped<IPercentualTransferenciaPortadorWriteRepository, PercentualTransferenciaPortadorWriteRepository>();
            services.AddScoped<IPercentualTransferenciaPortadorHistoricoReadRepository, PercentualTransferenciaPortadorHistoricoReadRepository>();
            services.AddScoped<IPercentualTransferenciaPortadorHistoricoWriteRepository, PercentualTransferenciaPortadorHistoricoWriteRepository>();
            
            services.AddScoped<IUsuarioHistoricoReadRepository, UsuarioHistoricoReadRepository>();
            services.AddScoped<IUsuarioHistoricoWriteRepository, UsuarioHistoricoWriteRepository>();

            services.AddScoped<IDocumentosProcessoVinculadoReadRepository, DocumentosProcessoVinculadoReadRepository>();
            services.AddScoped<IDocumentosProcessoVinculadoWriteRepository, DocumentosProcessoVinculadoWriteRepository>();

            services.AddScoped<IServidorCiotReadRepository, ServidorCiotReadRepository>();
            services.AddScoped<IServidorCiotWriteRepository, ServidorCiotWriteRepository>();

            //CARUANA
            services.AddScoped<ICiotClientEngine, CaruanaCiotEngineRepository>();
            services.AddScoped<ICiotClientEngine, JslLeasingEngineRepository>();

            // External - Sistema Info
            services.AddSingleton<AuthExternalRepository>();
        }
    }

}

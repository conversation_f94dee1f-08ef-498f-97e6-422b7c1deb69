﻿using System;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Domain.Helper;

public static class DateTimeHelper
{
    public static (DateTime, DateTime) ObterInicioEFimDaSemana(this DateTime data)
    {
        var diaSemana = data.DayOfWeek;
        var diferencaSegunda = (diaSemana == DayOfWeek.Sunday) ? -6 : DayOfWeek.Monday - diaSemana;
        var primeiroDia = data.AddDays(diferencaSegunda).Date;
        var ultimoDia = primeiroDia.AddDays(6);
        return (primeiroDia, ultimoDia);
    }
    
    public static string FormatarDataObrigatoria(this DateTime data, FormatDateTimeMethod format)
    {
        return data == DateTime.MinValue ? "" : data.ToStringBr(FormatDateTimeMethod.ShortDateTime);
    }
    
    public static (DateTime, DateTime) ObterInicioEFimDataHora(this DateTime data)
    {
        var inicioDoDia = new DateTime(data.Year, data.Month, data.Day, 0, 0, 0);
        var fimDoDia = new DateTime(data.Year, data.Month, data.Day, 23, 59, 59);
        return (inicioDoDia, fimDoDia);
    }
    
}
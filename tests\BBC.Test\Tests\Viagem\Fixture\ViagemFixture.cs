﻿using System;
using System.Collections.Generic;
using System.Security.Cryptography;
using System.Text;
using Bogus;
using Bogus.Extensions;
using Bogus.Extensions.Brazil;
using SistemaInfo.BBC.Application.Objects.Api.Viagem;
using SistemaInfo.BBC.Application.Objects.Mobile.Viagem.Request;
using SistemaInfo.BBC.Application.Objects.Web.Viagem;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Domain.External.Conductor.DTO.Conta;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Models.PercentualTransferencia;
using SistemaInfo.BBC.Domain.Models.PercentualTransferenciaPortador;
using SistemaInfo.BBC.Domain.Models.Transacao;
using Xunit;

namespace BBC.Test.Tests.Viagem.Fixture
{
    [CollectionDefinition(nameof(ViagemCollection))]
    public class ViagemCollection : ICollectionFixture<ViagemFixture>
    {
    }

    public class ViagemFixture : MockEngine
    {
        public SistemaInfo.BBC.Domain.Models.Viagem.Viagem GerarViagem()
        {
            var faker = new Faker<SistemaInfo.BBC.Domain.Models.Viagem.Viagem>("pt_BR")
                .CustomInstantiator(f => new SistemaInfo.BBC.Domain.Models.Viagem.Viagem
                {
                    Id = f.Random.Number(1, 100000),
                    EmpresaId = f.Random.Number(1, 100),
                    Status = f.PickRandom<StatusViagem>(),
                    FilialId = f.Random.Number(1, 999).ToString(),
                    PortadorProprietarioId = f.Random.Number(1, 1000),
                    NomeProprietario = f.Person.FullName,
                    PortadorMotoristaId = f.Random.Number(1, 1000),
                    NomeMotorista = f.Person.FullName,
                    CiotViagemId = f.Random.Number(1, 10000),
                    ViagemExternoId = f.Random.Number(1, 100000),
                    CidadeOrigemId = f.Random.Number(1, 10000),
                    CidadeDestinoId = f.Random.Number(1, 10000),
                    TipoBanco = f.PickRandom<TipoBanco>(),
                    PagamentoExternoId = f.Random.Number(1, 100000),
                    Agencia = f.Random.Number(1000, 9999).ToString(),
                    Conta = f.Finance.Account(10),
                    Ciot = f.Random.Number(*********, *********).ToString(),
                    CiotId = f.Random.Number(1, 100000),
                    DataDeclaracaoCiot = f.Date.Recent(30),
                    TipoCiot = f.PickRandom<TipoCiot?>(),
                    StatusCiot = f.PickRandom<StatusCiot?>(),
                    DescricaoCiot = f.Lorem.Sentence(5),
                    DataCancelamento = f.Date.Recent(10),
                    ValorFrete = f.Random.Decimal(1000, 50000),
                    ValorSaldo = f.Random.Decimal(0, 10000),
                    ValorAdiantamento = f.Random.Decimal(0, 20000),
                    QuantidadeTarifas = f.Random.Number(0, 10),
                    ValorTarifas = f.Random.Decimal(0, 1000),
                    ValorCombustivel = f.Random.Decimal(0, 5000),
                    ValorDespesa = f.Random.Decimal(0, 2000),
                    TotalImposto = f.Random.Decimal(0, 3000),
                    TotalPedagio = f.Random.Decimal(0, 1500),
                    DataCadastro = f.Date.Past(1),
                    UsuarioCadastroId = f.Random.Number(1, 100),
                    DataAlteracao = f.Date.Recent(30),
                    UsuarioAlteracaoId = f.Random.Number(1, 100)
                });

            return faker;
        }

        public SistemaInfo.BBC.Domain.Models.PagamentoEvento.PagamentoEvento GerarPagamentoEvento(Tipo tipoPagamentoEvento = Tipo.Adiantamento)
        {
            var lFakeObject = new Faker<SistemaInfo.BBC.Domain.Models.PagamentoEvento.PagamentoEvento>("pt_BR")
                .CustomInstantiator(f => new SistemaInfo.BBC.Domain.Models.PagamentoEvento.PagamentoEvento
                {
                    Valor = f.Random.Decimal(1000, 5000),
                    Tipo = tipoPagamentoEvento,
                    EmpresaId = f.Random.Number(1, 100),
                    Viagem = new SistemaInfo.BBC.Domain.Models.Viagem.Viagem
                    {
                        PortadorProprietario = new SistemaInfo.BBC.Domain.Models.Portador.Portador
                        {
                            CpfCnpj = f.Person.Cpf()
                        },
                        PortadorMotorista = new SistemaInfo.BBC.Domain.Models.Portador.Portador
                        {
                            CpfCnpj = f.Person.Cpf()
                        }
                    }
                });

            return lFakeObject;
        }

        public PagamentoViagemIntegrarRequest GerarPagamentoViagemIntegrarRequest()
        {
            var fakerPagamento = new Faker<PagamentoViagemIntegrarRequest>("pt_BR")
                .CustomInstantiator(f => new PagamentoViagemIntegrarRequest
                {
                    ViagemExternoId = f.Random.Number(1, 100),
                    Tipo = f.PickRandom<Tipo>(),
                    FormaPagamento = f.PickRandom<FormaPagamentoEvento>(),
                    CpfCnpjContratado = f.Person.Cpf(),
                    NomeContratado = f.Person.FullName,
                    CpfMotorista = f.Person.Cpf(),
                    NomeMotorista = f.Person.FullName,
                    RecebedorAutorizado = f.Person.FullName,
                    Valor = f.Random.Decimal(1, 1000),
                    TipoBanco = f.PickRandom<TipoBanco>(),
                    PagamentoExternoId = f.Random.Number(1, 100),
                    Agencia = f.Random.Number(1000, 9999).ToString(),
                    Conta = f.Finance.Account(10),
                    TipoConta = f.PickRandom<ETipoContaDock>(),
                    ChavePix = f.Finance.BitcoinAddress(),
                    FilialId = f.Random.Int(1, 99999).ToString(),
                    IbgeOrigem = f.Random.Number(1000000, 9999999),
                    IbgeDestino = f.Random.Number(1000000, 9999999),
                    HashValidacao = f.Random.Hash()
                });
            return fakerPagamento;
        }
        
        public PagamentoViagemIntegrarRequest GerarPagamentoIntegrarRequestSemCpfCnpjContratado()
        {
            var fakerPagamento = new Faker<PagamentoViagemIntegrarRequest>("pt_BR")
                .CustomInstantiator(f => new PagamentoViagemIntegrarRequest
                {
                    ViagemExternoId = f.Random.Number(1, 100),
                    Tipo = f.PickRandom<Tipo>(),
                    FormaPagamento = f.PickRandom<FormaPagamentoEvento>(),
                    CpfCnpjContratado = "",
                    NomeContratado = f.Person.FullName,
                    CpfMotorista = f.Person.Cpf(),
                    NomeMotorista = f.Person.FullName,
                    RecebedorAutorizado = f.Person.FullName,
                    Valor = f.Random.Decimal(1, 1000),
                    TipoBanco = f.PickRandom<TipoBanco>(),
                    PagamentoExternoId = f.Random.Number(1, 100),
                    Agencia = f.Random.Number(1000, 9999).ToString(),
                    Conta = f.Finance.Account(10),
                    TipoConta = f.PickRandom<ETipoContaDock>(),
                    ChavePix = f.Finance.BitcoinAddress(),
                    FilialId = "1",
                    IbgeOrigem = f.Random.Number(1000000, 9999999),
                    IbgeDestino = f.Random.Number(1000000, 9999999),
                    HashValidacao = f.Random.Hash()
                });
            return fakerPagamento;
        }

        public PercentualTransferencia GerarPercentualTransferencia(int paraTodosMotoristas = 0)
        {
            var lFakeObject = new Faker<PercentualTransferencia>("pt_BR")
                .CustomInstantiator(f => new PercentualTransferencia
                {
                    Id = f.Random.Int(1,999),
                    ParaTodosMotoristas = paraTodosMotoristas,
                    Saldo = f.Random.Decimal(0, 50),
                    Adiantamento = f.Random.Decimal(0, 50)
                });

            return lFakeObject;
        }
        
        public SistemaInfo.BBC.Domain.Models.Filial.Filial GerarFilial()
        {
            var lFilial = new Faker<SistemaInfo.BBC.Domain.Models.Filial.Filial>("pt_BR")
                .CustomInstantiator(f => new SistemaInfo.BBC.Domain.Models.Filial.Filial
                {
                    NomeFantasia = f.Company.CompanyName(),
                    RazaoSocial = f.Person.FullName,
                    Ativo = f.Random.Int(1),
                    Cnpj = f.Company.Cnpj(),
                    Bairro = f.Address.County(),
                    Id = 99999,
                    Cep = f.Address.ZipCode("########")
                });

            return lFilial;
        }
        
        public SistemaInfo.BBC.Domain.Models.Portador.Portador GerarPortador()
        {
            var lPortador = new Faker<SistemaInfo.BBC.Domain.Models.Portador.Portador>("pt_BR")
                .CustomInstantiator(f => new SistemaInfo.BBC.Domain.Models.Portador.Portador
                {
                    Nome = f.Person.FullName,
                CpfCnpj = f.Person.Cpf(),
                Email = f.Person.Email,
                Endereco = f.Address.StreetAddress(),
                EstadoId = f.Random.Number(1, 100),
                CidadeId = f.Random.Number(1, 100),
                Cep = f.Address.ZipCode(),
                EnderecoNumero = f.Random.Number(1, 1000),
                Telefone = f.Phone.PhoneNumber(),
                UsuarioCadastroId = f.Random.Number(1, 100),
                DataCadastro = f.Date.Past(),
                Celular = f.Phone.PhoneNumber(),
                Bairro = f.Address.SecondaryAddress(),
                Complemento = f.Address.BuildingNumber(),
                Ativo = f.Random.Number(0, 1),
                RNTRC = f.Random.Number(10000, 9999).ToString(),
                NumeroCNH = f.Random.Number(10000, 9999).ToString(),
                UsuarioBloqueioId = f.Random.Number(1, 100),
                DataBloqueio = f.Date.Past(),
                UsuarioDesbloqueioId = f.Random.Number(1, 100),
                DataDesbloqueio = f.Date.Past(),
                RazaoSocial = f.Company.CompanyName(),
                NomePai = f.Person.FirstName,
                NomeMae = f.Person.FirstName,
                Sexo = f.PickRandom<ESexo>(),
                NumeroIdentidade = f.Random.Number(1000, 9999).ToString(),
                OrgaoEmissor = "SSP",
                UfEmissao = "1",
                EmissaoIdentidade = f.Date.Past(),
                InscricaoEstadual = f.Random.Number(10000, 99999).ToString(),
                DataAberturaEmpresa = f.Date.Past(),
                FormaConstituicao = f.Company.CatchPhrase(),
                Cnae = f.Random.Number(1000, 9999).ToString(),
                TipoPessoa = f.PickRandom<SistemaInfo.BBC.Domain.Enum.ETipoPessoa>(),
                DataNascimento = f.Person.DateOfBirth,
                NaturezaJuridica = f.Company.CatchPhrase(),
                Atividade = f.PickRandom<SistemaInfo.BBC.Domain.Enum.EAtividade>(),
                Placa = f.Vehicle.Vin(),
                CarretaId = f.Random.Number(1, 100),
                Carreta2Id = f.Random.Number(1, 100),
                Carreta3Id = f.Random.Number(1, 100),
                ControlaAbastecimentoCentroCusto = f.Random.Number(0, 1),
                EmpresaIdFrota = f.Random.Number(1, 100)
                })
                .RuleFor(e => e.Nome, (f, e) => f.Company.CompanyName().ClampLength(1,200))
                .RuleFor(e => e.RazaoSocial, (f, e) => f.Person.FullName.ClampLength(1,200))
                .RuleFor(e => e.Endereco, (f, e) => f.Address.FullAddress().ClampLength(1,200))
                .RuleFor(e => e.Bairro, (f, e) => f.Address.County().ClampLength(1,200))
                .RuleFor(e => e.Email, (f, e) => f.Internet.Email().ClampLength(1,200))
                .RuleFor(e => e.Email, (f, e) => f.Internet.Email(e.RazaoSocial))
                .RuleFor(e => e.Email, (f, e) => f.Internet.Email());
            
            return lPortador;
        }
        
        public SistemaInfo.BBC.Domain.Models.Empresa.Empresa GerarEmpresa()
        {
            var lEmpresa = new Faker<SistemaInfo.BBC.Domain.Models.Empresa.Empresa>("pt_BR")
                .CustomInstantiator(f => new SistemaInfo.BBC.Domain.Models.Empresa.Empresa
                {
                    Id = f.Random.Int(1,99999),
                    NomeFantasia = f.Company.CompanyName(),
                    RazaoSocial = f.Person.FullName,
                    Email = f.Internet.Email(),
                    Cnpj = f.Company.Cnpj(),
                    Telefone = f.Phone.PhoneNumber(),
                    Celular = f.Phone.PhoneNumber(),
                    Endereco = f.Address.FullAddress(),
                    Bairro = f.Address.County(),
                    CidadeId = f.Random.Int(1, 99999),
                    RecebedorAutorizado = true
                })
                .RuleFor(e => e.NomeFantasia, (f, e) => f.Company.CompanyName().ClampLength(1,200))
                .RuleFor(e => e.RazaoSocial, (f, e) => f.Person.FullName.ClampLength(1,200))
                .RuleFor(e => e.Endereco, (f, e) => f.Address.FullAddress().ClampLength(1,200))
                .RuleFor(e => e.Bairro, (f, e) => f.Address.County().ClampLength(1,200))
                .RuleFor(e => e.Email, (f, e) => f.Internet.Email().ClampLength(1,200))
                .RuleFor(e => e.Email, (f, e) => f.Internet.Email(e.RazaoSocial))
                .RuleFor(e => e.Email, (f, e) => f.Internet.Email());
            
            return lEmpresa;
        }

        public PercentualTransferenciaPortador GerarPercentualTransferenciaPortador()
        {
            var lFakeObject = new Faker<PercentualTransferenciaPortador>("pt_BR")
                .CustomInstantiator(f => new PercentualTransferenciaPortador
                {
                    Saldo = f.Random.Decimal(0, 50),
                    Adiantamento = f.Random.Decimal(0, 50)
                });

            return lFakeObject;
        }

        public ConsultarGridViagemRequest RequestConsultaGridViagem()
        {
            var requestConsultaGridViagem = new Faker<ConsultarGridViagemRequest>("pt_BR")
                .CustomInstantiator(f => new ConsultarGridViagemRequest
                {
                    EmpresaId = f.Random.Number(100),
                    dataInicial = DateTime.Now.AddDays(-7).ToShortDateString(),
                    dataFinal = DateTime.Now.ToShortDateString(),
                    CodViagemInterno = f.Random.Number(5000),
                    Take = 10,
                    Page = 1,
                    Order = new OrderFilters
                    {
                        Campo = "id",
                        Operador = EOperadorOrder.Descending
                    },
                    Filters = new List<QueryFilters>()
                });
            
            return requestConsultaGridViagem;
        }
        
        public ConsultarGridViagemRequest RequestConsultaGridPagamentoEventoViagem()
        {
            var requestConsultaGridViagem = new Faker<ConsultarGridViagemRequest>("pt_BR")
                .CustomInstantiator(f => new ConsultarGridViagemRequest
                {
                    EmpresaId = f.Random.Number(100),
                    dataInicial = DateTime.Now.AddDays(-7).ToShortDateString(),
                    dataFinal = DateTime.Now.ToShortDateString(),
                    CodViagemInterno = f.Random.Number(5000),
                    Take = 10,
                    Page = 1,
                    Order = new OrderFilters
                    {
                        Campo = "id",
                        Operador = EOperadorOrder.Descending
                    },
                    Filters = new List<QueryFilters>()
                });
            
            return requestConsultaGridViagem;
        }
        
        public Transacao GerarTransacao()
        {
            var lTransacao = new Faker<Transacao>("pt_BR")
                .CustomInstantiator(f => new Transacao
                { 
                    Id = f.Random.Number(999)
                });
            return lTransacao;
        }

        public SaldoResp GerarSaldo()
        {
            var lSaldo = new Faker<SaldoResp>("pt_BR")
                .CustomInstantiator(f => new SaldoResp
                { 
                    id = f.Random.Number(999),
                    idPessoa = f.Random.Number(999),
                    nome = "Teste",
                    saldoDisponivelGlobal = 50000
                });
            return lSaldo;
        }

        public string GerarHashValidacao(string lToken, string CpfCnpjContratado, string Valor)
        {
            string hashValidacao;

            using (var md5 = MD5.Create())
            {
                var hash = lToken + CpfCnpjContratado + Valor;
                var bytes = md5.ComputeHash(Encoding.UTF8.GetBytes(hash));
                var sb = new StringBuilder();
                foreach (var b in bytes) sb.Append(b.ToString("x2"));
                hashValidacao = sb.ToString();
            }
            return hashValidacao;
        }

        public SistemaInfo.BBC.Domain.Models.PagamentoEvento.PagamentoEvento GerarPagamentoEventoComStatusAntecipacao(
            StatusAntecipacaoParcelaProprietario? status = null)
        {
            var lFakeObject = new Faker<SistemaInfo.BBC.Domain.Models.PagamentoEvento.PagamentoEvento>("pt_BR")
                .CustomInstantiator(f => new SistemaInfo.BBC.Domain.Models.PagamentoEvento.PagamentoEvento
                {
                    Id = f.Random.Int(1, 99999),
                    Valor = f.Random.Decimal(1000, 5000),
                    Tipo = Tipo.Adiantamento,
                    EmpresaId = f.Random.Number(1, 100),
                    ViagemId = f.Random.Int(1, 99999),
                    StatusAntecipacaoParcelaProprietario = status ?? StatusAntecipacaoParcelaProprietario.Disponivel,
                    DataCadastro = f.Date.Past(),
                    DataAlteracao = f.Date.Recent(),
                    Viagem = new SistemaInfo.BBC.Domain.Models.Viagem.Viagem
                    {
                        Id = f.Random.Int(1, 99999),
                        PortadorProprietario = new SistemaInfo.BBC.Domain.Models.Portador.Portador
                        {
                            CpfCnpj = f.Person.Cpf()
                        },
                        PortadorMotorista = new SistemaInfo.BBC.Domain.Models.Portador.Portador
                        {
                            CpfCnpj = f.Person.Cpf()
                        }
                    }
                });

            return lFakeObject;
        }

        public AtualizarStatusAntecipacaoRequest GerarAtualizarStatusAntecipacaoRequest(
            int? viagemId = null, int? pagamentoId = null, StatusAntecipacaoRequest? status = null)
        {
            var lFakeObject = new Faker<AtualizarStatusAntecipacaoRequest>("pt_BR")
                .CustomInstantiator(f => new AtualizarStatusAntecipacaoRequest
                {
                    ViagemId = viagemId ?? f.Random.Int(1, 99999),
                    PagamentoId = pagamentoId ?? f.Random.Int(1, 99999),
                    StatusAntecipacao = status ?? f.PickRandom<StatusAntecipacaoRequest>()
                });

            return lFakeObject;
        }

        public SistemaInfo.BBC.Domain.Models.Transacao.Transacao GerarTransacaoRetencao(int pagamentoEventoId)
        {
            var lTransacao = new Faker<SistemaInfo.BBC.Domain.Models.Transacao.Transacao>("pt_BR")
                .CustomInstantiator(f => new SistemaInfo.BBC.Domain.Models.Transacao.Transacao
                {
                    Id = f.Random.Number(999),
                    IdPagamentoEvento = pagamentoEventoId,
                    FormaPagamento = FormaPagamentoEvento.RetencaoAntecipacao,
                    Status = StatusPagamento.Fechado,
                    Valor = f.Random.Decimal(100, 1000),
                    DataCadastro = f.Date.Past()
                });
            return lTransacao;
        }

        public List<SistemaInfo.BBC.Domain.Models.ClientSecret.ClientSecret> GerarListaClientSecret(int dias = 10)
        {
            return new List<SistemaInfo.BBC.Domain.Models.ClientSecret.ClientSecret>()
            {
                new SistemaInfo.BBC.Domain.Models.ClientSecret.ClientSecret()
                {
                    DataExpiracao = DateTime.Now.AddDays(dias)
                }
            };
        }
    }
}
﻿using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SistemaInfo.BBC.Application.Objects.Api.Token;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using SistemaInfo.Framework.DomainDrivenDesign.Web.Controllers;

namespace SistemaInfo.BBC.ApiCiotPublico.Controllers.Base
{
    /// <inheritdoc />
    /// <summary>ApiCiot de operações da ANTT.</summary>
    [Route("Operacoes")]
    public class ApiControllerBase : SistemaController
    {
        /// <inheritdoc />
        public ApiControllerBase(IAppEngine engine) : base(engine)
        {
        }
    }

    /// <inheritdoc />
    public class ApiControllerBase<TAppService> : SistemaController<TAppService>
    {
        /// <inheritdoc />
        public ApiControllerBase(IAppEngine engine, TAppService appService) : base(engine, appService)
        {
        }
        
    }
    
    /// <inheritdoc />
    public class ApiControllerBase<TAppService, TInterface> : SistemaController<TAppService, TInterface>
    {
        /// <inheritdoc />
        public ApiControllerBase(IAppEngine engine, TAppService appService, TInterface tinterface) : base(engine, appService, tinterface)
        {
            
        }
    }
}
using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using SistemaInfo.BBC.Application.Interface.CentralPendencias;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.CentralPendencias;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Web.Attributes;
using SistemaInfo.BBC.Web.Controllers.Base;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;

namespace SistemaInfo.BBC.Web.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [Route("CentralPendencias")]
    public class CentralPendenciasController : WebControllerBase<ICentralPendenciasAppService>
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="engine"></param>
        /// <param name="appService"></param>
        public CentralPendenciasController(IAppEngine engine, ICentralPendenciasAppService appService) : base(engine, appService)
        {
        }
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("ConsultarGridCentralPendencias")]
        [Menu(new[] { EMenus.CentralPendencias })]
        public async Task<JsonResult> ConsultarGridCentralPendencias([FromBody]DtoConsultaGridPendencias request)
        {
            var consultarGridCentralPendencias = await AppService.ConsultarGridCentralPendencias(request.EmpresaId, request.dataInicial, request.dataFinal, request.Perfil, request.Take, request.Page, request.Order, request.Filters);
            return ResponseBase.ResponderSucesso(consultarGridCentralPendencias);
        }
        
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("ConsultarGridCentralPendenciasValePedagio")]
        [Menu(new[] { EMenus.CentralPendencias })]
        public async Task<JsonResult> ConsultarGridCentralPendenciasValePedagio([FromBody] ConsultarGridCentralPendenciasValePedagioRequest request)
        {
            var response = await AppService.ConsultarGridCentralPendenciasValePedagio(request);
            return ResponseBase.ResponderSucesso(response);
        }
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("ConsultarGridCentralPendenciasMovida")]
        [Menu(new[] { EMenus.CentralPendenciasMovida })]
        public async Task<JsonResult> ConsultarGridCentralPendenciasMovida([FromBody] ConsultarGridCentralPendenciasMovida request)
        {
            var response = await AppService.ConsultarGridCentralPendenciasMovida(request);
            return ResponseBase.ResponderSucesso(response);
        }
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("ReenviarAbastecimentoMovida")]
        [Menu(new[] { EMenus.CentralPendenciasMovida })]
        public async Task<JsonResult> ReenviarAbastecimentoMovida([FromBody] ReenviarAbastecimentoMovidaRequest request)
        {
            var response = await AppService.ReenviarAbastecimentoMovida(request.Id);
            return ResponseBase.Responder(response.sucesso, response.mensagem, response.data);
        }
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="PagamentoEventoId"></param>
        /// <returns></returns>
        [HttpGet("ReenviarPagamentoEvento")]
        [Menu(new[] { EMenus.CentralPendencias })]
        public JsonResult ReenviarPagamentoEvento(int PagamentoEventoId)
        {
            try
            {
                var reenvioCentralPendencias = AppService.ReenviarPagamentoEvento(PagamentoEventoId).Result;
                
                return ResponseBase.BigJson(reenvioCentralPendencias);
            }
            catch (Exception e)
            {
                return ResponseBase.ResponderErro("Não foi possível realizar a operação. Mensagem: " + e.Message);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="lCentralPendenciasReq"></param>
        /// <returns></returns>
        [HttpPost("Salvar")]
        [Menu(new[] { EMenus.CentralPendencias })]
        public JsonResult SaveCentralPendencias([FromBody]CentralPendenciasRequest lCentralPendenciasReq)
        {
            try
            {
                var lSaveCentralPendencias = AppService.Save(lCentralPendenciasReq).Result;
                
                return ResponseBase.BigJson(lSaveCentralPendencias);
            }
            catch (Exception e)
            {
                return ResponseBase.ResponderErro("Não foi possível realizar a operação. Mensagem: " + e.Message);
            }
        }
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="idCentralPendencias"></param>
        /// <returns></returns>
        [HttpGet("ConsultarPorId")]
        [Menu(new[] { EMenus.CentralPendencias })]
        public JsonResult ConsultarPorId(int idCentralPendencias)
        {
            try
            {
                var consultarCentralPendencias = AppService.ConsultarPorId(idCentralPendencias);
                return ResponseBase.ResponderSucesso(consultarCentralPendencias);
            }
            catch (Exception e)
            {
                return ResponseBase.ResponderErro("Pendencias não encontradas! Mensagem: " + e.Message);
            }
        }
        
        [HttpGet("ConsultarPendenciaPagamentoPorId")]
        [Menu(new[] { EMenus.CentralPendenciasAntecipados })]
        public JsonResult ConsultarPendenciaPagamentoPorId(int idCentralPendencias)
        {
            try
            {
                var consultarCentralPendencias = AppService.ConsultarDetalhesPorId(idCentralPendencias);
                if (consultarCentralPendencias == null)
                {
                    return ResponseBase.ResponderErro("Pendência não encontrada!");
                }
                return ResponseBase.ResponderSucesso(consultarCentralPendencias);
            }
            catch (Exception e)
            {
                return ResponseBase.ResponderErro("Pendencias não encontradas! Mensagem: " + e.Message);
            }
        }
    }
}
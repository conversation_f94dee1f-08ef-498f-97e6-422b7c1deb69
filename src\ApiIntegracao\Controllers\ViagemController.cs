using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using NLog;
using SistemaInfo.BBC.ApiIntegracao.Controllers.Base;
using SistemaInfo.BBC.Application.Helpers;
using SistemaInfo.BBC.Application.Interface.Viagem;
using SistemaInfo.BBC.Application.Objects.Api.Viagem;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;

namespace SistemaInfo.BBC.ApiIntegracao.Controllers
{
    /// <summary>
    /// Classe da aplicação de pagamento
    /// </summary>
    [Route("Viagem")]
    public class ViagemController : ApiControllerBase
    {
        private readonly IViagemAppService _viagemAppService;

        /// <summary>
        /// Injeção de dependecias e herança
        /// </summary>
        /// <param name="engine"></param>
        /// <param name="viagemAppService"></param>
        public ViagemController(IAppEngine engine, IViagemAppService viagemAppService) : base(engine)
        {
            _viagemAppService = viagemAppService;
        }


        /// <summary>
        /// Metodo responsavel por integrar uma viagem e gerar um evento de pagamento com infomraçoes contidas dentro do parametro
        /// </summary>
        /// <param name="viagemIntegrarRequest"></param>
        /// <returns></returns>
        [Produces("application/json")]
        [HttpPost("IntegrarPagamentoViagem")]
        public async Task<JsonResult> IntegrarPagamentoViagem([FromBody] PagamentoViagemIntegrarRequest viagemIntegrarRequest)
        {
            try
            {
                new LogHelper().LogOperationStart("IntegrarPagamentoViagem");
                var lIntegrarPag = await _viagemAppService
                    .IntegrarPagamentoViagem(viagemIntegrarRequest, HttpContext.Request.Headers["x-web-auth-token"]);

                return lIntegrarPag.Sucesso
                    ? ResponseBaseApi.ResponderSucesso(lIntegrarPag)
                    : ResponseBaseApi.ResponderErro(lIntegrarPag.Mensagem);
            }
            catch (Exception e)
            {
                new LogHelper().Error(e, "IntegrarPagamentoViagem " + viagemIntegrarRequest.ViagemExternoId + " Exception: " + e.Message);
                return ResponseBaseApi.ResponderErro("Não foi possível realizar a operação. Mensagem: " + e.Message);
            }
            finally
            {
                new LogHelper().LogOperationEnd("IntegrarPagamentoViagem");
            }
        }

        /// <summary>
        /// Metodo responsavel pelo cancelamento de Eventos de pagamento relacionado a viagem
        /// </summary>
        /// <param name="cancelarEventoViagemRequest"></param>
        /// <returns></returns>
        [Produces("application/json")]
        [HttpPost("CancelamentoPagamentoViagem")]
        public async Task<JsonResult> CancelarPagamentoViagem(
            [FromBody] CancelamentoEventoViagemRequest cancelarEventoViagemRequest)
        {
            try
            {
                new LogHelper().LogOperationStart("CancelarPagamentoViagem");
                var lIntegrarPag =
                    await _viagemAppService.CancelarEventoViagem(cancelarEventoViagemRequest, Engine.User.EmpresaId);

                return lIntegrarPag.Sucesso
                    ? ResponseBaseApi.ResponderSucesso(lIntegrarPag)
                    : ResponseBaseApi.ResponderErro(lIntegrarPag.Mensagem);
            }
            catch (Exception e)
            {
                new LogHelper().Error(e, "CancelarPagamentoViagem");
                return ResponseBaseApi.ResponderErro("Não foi possível realizar a operação. Mensagem: " + e.Message);
            } 
            finally
            {
                new LogHelper().LogOperationEnd("CancelarPagamentoViagem");
            }
        }
    }
}
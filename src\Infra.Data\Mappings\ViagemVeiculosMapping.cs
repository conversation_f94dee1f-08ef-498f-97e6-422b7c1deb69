using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using SistemaInfo.BBC.Domain.Models.ViagemVeiculos;
using SistemaInfo.Framework.EntityFramework.Configuration;

namespace SistemaInfo.BBC.Infra.Data.Mappings
{
    public class ViagemVeiculosMapping : EntityTypeConfiguration<ViagemVeiculos>
    {
        public override void Map(EntityTypeBuilder<ViagemVeiculos> builder)
        {
            builder.ToTable("ViagemVeiculos");
            builder.HasKey(b => b.Id);

            builder.Property(p => p.Id).IsRequired().HasColumnName("Id").ValueGeneratedOnAdd();
            builder.Property(b => b.VeiculoId).IsRequired().HasColumnName("VeiculoId").HasColumnType("int");
            builder.Property(b => b.ViagemId).IsRequired().HasColumnName("ViagemId").HasColumnType("int");
            builder.Property(b => b.DataCriacao).HasColumnName("DataCriacao").HasColumnType("timestamp");
            builder.Property(b => b.DataAlteracao).HasColumnName("DataAlteracao").HasColumnType("timestamp");

            builder.HasOne(p => p.Veiculo).WithMany().HasForeignKey(p => p.VeiculoId);
            builder.HasOne(p => p.Viagem).WithMany().HasForeignKey(p => p.ViagemId);
        }
    }
}

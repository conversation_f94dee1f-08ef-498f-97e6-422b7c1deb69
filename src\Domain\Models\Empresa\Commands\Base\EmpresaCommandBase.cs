using System;

namespace SistemaInfo.BBC.Domain.Models.Empresa.Commands.Base
{
    public class EmpresaCommandBase
    {
        public int Id { get; set; }
        public string Cnpj { get; set; }
        public string RNTRC { get; set; }
        public string RazaoSocial { get; set; }
        public string NomeFantasia { get; set; }
        public string Celular { get; set; }
        public string Telefone { get; set; }
        public string Email { get; set; }
        public string Cep { get; set; }
        public int EstadoId { get; set; }
        public int CidadeId { get; set; }
        public string Endereco { get; set; }
        public string Bairro { get; set; }
        public string EnderecoNumero { get; set; }
        public string ParecerInterno { get; set; }
        public string ParecerExterno { get; set; }
        public int? StatusCadastro { get; set; }
        public int? UsuarioValidacaoId { get; set; }
        public DateTime? DataValidacao { get; set; }
        public int? UsuarioDesbloqueioId { get; set; }
        public DateTime DataDesbloqueio { get; set; }
        public string Complemento { get; set; }
        public int? Ativo { get; set; }
        public string InscricaoEstadual { get; set; }
        public DateTime? DataAberturaEmpresa { get; set; }
        public string FormaConstituicao { get; set; }
        public int? UsuarioCadastro { get; set; }
        public DateTime DataCadastro { get; set; }
        public int? UsuarioBloqueioId { get; set; }
        public DateTime DataBloqueio { get; set; }
        public int? TempoAbastecimento { get; set; }
        public decimal? ValorTolerancia { get; set; }
        public int? ControlaOdometro { get; set; }
        public int? ControlaAutonomia { get; set; }
        public decimal? TaxaAbastecimento { get; set; }
        public decimal? Cashback { get; set; }
        public int LiberaBloqueioSPD { get; set; } = 1;
        public int CobrancaTarifa { get; set; } = 1;
        public int? RegistraCiot { get; set; } = 0;
        public int? TipoEmpresaId { get; set; }
        public decimal? ImpostoIRRF { get; set; } = 0;
        public decimal? ImpostoCSLL { get; set; } = 0;
        public decimal? ImpostoCOFINS { get; set; } = 0;
        public decimal? ImpostoPIS { get; set; } = 0;
        public string Link { get; set; }
        public string SenhaLink { get; set; }
        public string LinkSAP { get; set; }
        public string UsuarioSAP { get; set; }
        public string SenhaSAP { get; set; }
        public decimal? PercentualAutonomiaInferior { get; set; }
        public decimal? PercentualAutonomiaSuperior { get; set; }
        public int ControlaContingencia { get; set; } = 1;
        public int? ContaAbastecimento { get; set; }
        public int? ContaValePedagio { get; set; }
        public int? ContaFrete { get; set; }
        public int? Prazo { get; set; }
        public DateTime? DataAlteracaoModelo { get; set; }        
        public int? DebitoProtocolo { get; set; }
        public int? DebitoPrazo { get; set; }
        public int? QtdMensalSemTaxaPix { get; set; } = 0;
        public decimal? ValorTarifaPix { get; set; } = 0;
        public decimal? ValorTarifaBbc { get; set; } = 0;
        public int UtilizaTarifaEmpresa { get; set; } = 1;
        public int? GrupoEmpresaId { get; set; } = 0;
        public bool? RecebedorAutorizado { get; set; }
        public decimal? PorcentagemTarifaServiceValePedagio { get; set; }
        public int? PermitirPagamentoValePedagio { get; set; } = 0;
        public int? CobrarTarifaBbcValePedagio { get; set; } = 0;
        public int? UtilizaTarifaEmpresaPagamentoPedagio { get; set; } = 1;
        public string NotificacaoContingenciaCiot { get; set; }
        public int? StatusReprocessamentoPagamentoFrete { get; set; } = 0;
        public int HabilitaReprocessamentoValePedagio { get; set; }
        public int HabilitaPainelSaldo { get; set; }

        public byte[] ImagemCartao { get; set; }
        public decimal ValorAdiantamentoBbc { get; set; }
        public int PermitirEncerramentoPainelCiot { get; set; } = 0;
        public int UtilizaCiot { get; set; } = 0;
    }
}
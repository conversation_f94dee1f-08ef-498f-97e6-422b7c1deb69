using System;
using NLog;
using NLog.Config;
using NLog.Targets;
using SistemaInfo.BBC.Application.Helpers;
using Xunit;

namespace BBC.Test.Tests.Logging
{
    public class LogHelperNLogIntegrationTests
    {
        [Fact]
        public void LogHelper_ShouldIntegrateWithNLog()
        {
            // Arrange
            // Configuração temporária do NLog para testes
            var config = new LoggingConfiguration();
            var memoryTarget = new MemoryTarget("memory");
            config.AddTarget(memoryTarget);
            config.AddRuleForAllLevels(memoryTarget);
            LogManager.Configuration = config;
            
            var testMessage = "Test message " + Guid.NewGuid();
            
            // Act
            new LogHelper().Info(testMessage);
            
            // Assert
            // Verificamos se a mensagem foi registrada no target de memória
            Assert.Contains(memoryTarget.Logs, log => log.Contains(testMessage));
            
            // Limpar a configuração
            LogManager.Configuration = null;
        }
        
        [Fact]
        public void LogHelper_ShouldLogExceptions()
        {
            // Arrange
            // Configuração temporária do NLog para testes
            var config = new LoggingConfiguration();
            var memoryTarget = new MemoryTarget("memory");
            config.AddTarget(memoryTarget);
            config.AddRuleForAllLevels(memoryTarget);
            LogManager.Configuration = config;
            
            var testException = new Exception("Test exception " + Guid.NewGuid());
            
            // Act
            new LogHelper().Error(testException, "LogHelperNLogIntegrationTests");
            
            // Assert
            // Verificamos se a exceção foi registrada no target de memória
            Assert.Contains(memoryTarget.Logs, log => log.Contains(testException.Message));
            
            // Limpar a configuração
            LogManager.Configuration = null;
        }
        
        [Fact]
        public void LogHelper_ShouldLogOperationStartAndEnd()
        {
            // Arrange
            // Configuração temporária do NLog para testes
            var config = new LoggingConfiguration();
            var memoryTarget = new MemoryTarget("memory");
            config.AddTarget(memoryTarget);
            config.AddRuleForAllLevels(memoryTarget);
            LogManager.Configuration = config;
            
            var operationName = "TestOperation" + Guid.NewGuid();
            
            // Act
            new LogHelper().LogOperationStart(operationName);
            new LogHelper().LogOperationEnd(operationName);
            
            // Assert
            // Verificamos se as mensagens de início e fim foram registradas
            Assert.Contains(memoryTarget.Logs, log => log.Contains($"Início da operação: {operationName}"));
            Assert.Contains(memoryTarget.Logs, log => log.Contains($"Fim da operação: {operationName}"));
            
            // Limpar a configuração
            LogManager.Configuration = null;
        }
    }
}

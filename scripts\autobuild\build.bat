echo off
echo %~dp0
set pastaOrigem=%~dp0
call %pastaOrigem%variaveis.bat
setlocal enabledelayedexpansion

cd %projeto%
call :log git status
if exist "%publish_dir%" rd /S /Q "%publish_dir%"
mkdir "%publish_dir%"
set temp=%publish_dir%\branch-build.h
git branch --show-current >> %temp%
set /p branchname=<%temp%
call :log %branchname%
git status

:input1
	call :info Selecione tipo pacote
	call :opt 0 - Full
	call :opt 1 - Dlls padrao
	call :opt 2 - Todas dlls
	call :opt 3 - Dll padrao + front padrao
	call :opt 4 - Front padrao
	call :opt 5 - Full Front
	echo.
	set /p tipoPacote=""
	call :info tipoPacote selecionado: %tipoPacote%
)

if %tipoPacote%==0 (
	set extensaoBack=*
	set extensaoFront=*
	goto :input
)
if %tipoPacote%==1 (
	set extensaoBack=%dllPadrao%*.dll
	goto :input
)
if %tipoPacote%==2 (
	set extensaoBack=*.dll *%dllPadrao%*.exe
	goto :input
)
if %tipoPacote%==3 (
	set extensaoBack=%dllPadrao%*.dll
	set extensaoFront=*.html *.js *.css
	goto :input
)
if %tipoPacote%==4 (
	set extensaoFront=*.html *.js *.css
	goto :input
)
if %tipoPacote%==5 (
	set extensaoFront=*
	goto :input
) else (
	goto :input1
)

:input
	call :info Digite o projeto para build
	call :opt 0 - Completo
	call :opt 1 - Controle
	call :opt 2 - Posto
	call :opt 3 - API JSL
	call :opt 4 - API Integracao
	call :opt 5 - API Abastecimento
	call :opt 6 - API Mobile Abastecimentos
	call :opt 7 - API Service
	call :opt 8 - API Ciot
	call :opt 9 - API Mobile Pagamentos
	call :opt A - API Ciot Publico
	echo.
	set /p build=""
	call :info build selecionado: %build%

if "%build%"=="0" (
	set build=123456789A
)
set clean=0
set webBuild=0

set "length_str=0"
:loop_length
if not "!build:~%length_str%,1!"=="" (
    set /a length_str+=1
    goto :loop_length
)
set /a length_str-=1
for /L %%i in (0,1,%length_str%) do (
    set "caractere=!build:~%%i,1!"
	if "!caractere!"=="1" (
		if %webBuild%==0 (
			if "%extensaoBack%" neq "" (call :Build-back Web "BBC - BBC Controle\bin")
			set webBuild=1
			if "%extensaoFront%" neq "" (call :Build-front Front "BBC - BBC Controle")
		) else (
			call :Copy Web "BBC - BBC Controle\bin"
		)
	) else if "!caractere!"=="2" (
		if %webBuild%==0 (
			if "%extensaoBack%" neq "" (call :Build-back Web "BBC - Rede BBC Controle\bin")
			set webBuild=1
			if "%extensaoFront%" neq "" (call :Build-front Front.Posto "BBC - Rede BBC Controle")
		) else (
			call :Copy Web "BBC - BBC Controle\bin"
		)
	) else if "!caractere!"=="3" (
		if "%extensaoBack%" neq "" (call :Build-back Api "BBC - API")
	) else if "!caractere!"=="4" (
		if "%extensaoBack%" neq "" (call :Build-back ApiIntegracao "BBC - API Integracao")
	) else if "!caractere!"=="5" (
		if "%extensaoBack%" neq "" (call :Build-back ApiAbastecimento "BBC - API Abastecimento")
	) else if "!caractere!"=="6" (
		if not exist "%projeto%\src\Mobile" (
			if "%extensaoBack%" neq "" (call :Build-back Mobile.Abastecimentos "BBC - Mobile")
		) else (
			if "%extensaoBack%" neq "" (call :Build-back Mobile "BBC - Mobile")
		)
	) else if "!caractere!"=="7" (
		if "%extensaoBack%" neq "" (call :Build-back ApiCloud "BBC - API Service")
	) else if "!caractere!"=="8" (
		if "%extensaoBack%" neq "" (call :Build-back ApiCiot "BBC - API Ciot")
	) else if "!caractere!"=="9" (
		if "%extensaoBack%" neq "" (call :Build-back Mobile.Pagamentos "BBC - Mobile Pagamentos")
	) else if "!caractere!"=="A" (
     	if "%extensaoBack%" neq "" (call :Build-back ApiCiotPublico "BBC - Ciot - Publico - back")
     	if "%extensaoFront%" neq "" (call :Build-Front-Ng "Web CIOT BBC" "BBC - Ciot - Publico")
    ) else (
		call :warn opcao nao existente! !caractere!
		goto :input
	)
)
call :voltarNode
call :fim

:Build-back
	if %clean%==0 (
		call :log Clean
		dotnet clean  -c Release "%projeto%\src\%solution%.sln"
		set clean=1
	)
	if %errorlevel%==1 (goto :erro)
	call :info Build %~2
	dotnet build "%projeto%\src\%~1\%~1.csproj" -c Release
	if %errorlevel%==1 (goto :erro)
	call :Copy %~1 "%~2"
	exit /b 0

:Copy
	robocopy "%projeto%\src\%~1\bin\Release\netcoreapp2.1" "%projeto%\src\artefatos\%~2" %extensaoBack% /XD publish win-x64 /s /z /r:2 /mt:10 /lev:3
	exit /b 0

:Build-Front
	call :node
	call :log %projeto%\src\%~1
	cd "%projeto%\src\%~1"
	if %errorlevel%==1 (goto :erro)
	cmd /c node -v
	cmd /c npm -v
	echo verifica bower_components e node_modules
	if not exist "bower_components" rd /S /Q "node_modules"
	if not exist "node_modules" (
	rd /S /Q "bower_components"
	call :log npm install
	cmd /c npm install
	if %errorlevel%==1 (goto :erro)
	call :log npm install -g gulp
	cmd /c npm install -g gulp
	if %errorlevel%==1 (goto :erro)
	)
	echo ok
	if exist "dist" cmd /c gulp clean
	if exist "node_modules" if exist "bower_components" (cmd /c gulp build) else (goto :erro)
	robocopy "%projeto%\src\%~1\dist" "%projeto%\src\artefatos\%~2" %extensaoFront% /s /z /r:2 /mt:10 /lev:3 /s
	::if %errorlevel%==1 (goto :erro)
	exit /b 0

:Build-Front-Ng
    call :node
    cd "%projeto%\src\%~1"
    cmd /c node -v
    cmd /c npm -v
    echo verifica e node_modules
    if not exist "node_modules" (
    echo npm install
    cmd /c npm install
    cmd /c npm install -g @angular/cli@6.0.8
    cmd /c npm i node-sass@4.14.1
    )
    echo ok
    if exist "node_modules" (cmd /c npm run-script build) else (goto :erro)
    robocopy "%projeto%\src\%~1\dist" "%projeto%\src\artefatos\%~2" %extensaoFront% /s /z /r:2 /mt:10 /lev:3 /s
    exit /b 0

:node
set "TARGET_VERSION=v10"
call :verificaVersaoNode
if "!current_major!" neq "%TARGET_VERSION%" (
	call :log "!current_major!" "%TARGET_VERSION%"
    echo Versão atual é !CLEAN_VERSION!, trocando para %TARGET_VERSION%...
    nvm use %TARGET_VERSION%
)
if %errorlevel%==1 (call :erro)
exit /b 0

:voltarNode
if "!current_major!" neq "%TARGET_VERSION%" (
	call :log "!current_major!" "%TARGET_VERSION%"
    echo Voltando versão %TARGET_VERSION%, trocando para !current_major!...
    nvm use !current_major!
)
if %errorlevel%==1 (call :erro)
exit /b 0

:verificaVersaoNode
for /f "delims=" %%v in ('nvm current') do (
    set "CURRENT_VERSION=%%v"
)

for /f "tokens=1" %%a in ("!CURRENT_VERSION!") do (
    set "CLEAN_VERSION=%%a"
)

for /f "delims=." %%B in ("!CLEAN_VERSION!") do (
    set "current_major=%%B"
)
if %errorlevel%==1 (call :erro)
exit /b 0

:fim
	call %pastaOrigem%remove-config.bat
	if %errorlevel%==1 (call :erro) else (call :sucesso)
	exit %errorlevel%
	
:: Sets up the ESC string for use later in this script
:setESC
    for /F "tokens=1,2 delims=#" %%a in ('"prompt #$H#$E# & echo on & for %%b in (1) do rem"') do (
      set ESC=%%b
      exit /B 0
    )
    exit /B 0
	
:opt
	call :setESC
	echo !ESC![92m %* !!ESC![0m
	exit /B 0
	
:log
	:: %~n0 = nome arquivo | %~x0 = extensão arquivo
	echo.
	call :setESC
	echo !ESC![95m===================================================================================================!!ESC![0m
	echo !ESC![95m	%*
	echo !ESC![95m===================================================================================================!!ESC![0m
	echo.
	exit /B 0
	
:info
	:: %~n0 = nome arquivo | %~x0 = extensão arquivo
	echo.
	call :setESC
	echo !ESC![94m===================================================================================================!!ESC![0m
	echo !ESC![95m	%*
	echo !ESC![94m===================================================================================================!!ESC![0m
	echo.
	exit /B 0

:warn
	:: %~n0 = nome arquivo | %~x0 = extensão arquivo
	echo.
	call :setESC
	echo !ESC![93m===================================================================================================!!ESC![0m
	echo !ESC![93m	%*
	echo !ESC![93m===================================================================================================!!ESC![0m
	echo.
	exit /B 0
	
:erro
	echo.
	call :setESC
	echo !ESC![91m===================================================================================================!!ESC![0m
	echo !ESC![91m	%~n0%~x0^> %*
	echo !ESC![91m===================================================================================================!!ESC![0m
	echo.
	if %NoStop%==False (pause)
	exit 1

:sucesso
	echo.
	call :setESC
	echo !ESC![92m===================================================================================================!!ESC![0m
	echo !ESC![92m	Operacao Concluida!!ESC![0m
	echo !ESC![92m===================================================================================================!!ESC![0m
	echo.
	if %NoStop%==False (pause)
	exit 0
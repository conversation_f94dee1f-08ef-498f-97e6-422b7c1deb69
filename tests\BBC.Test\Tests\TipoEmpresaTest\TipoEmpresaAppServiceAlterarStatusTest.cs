﻿using BBC.Test.Tests.TipoEmpresa.Fixture;
using Moq;
using SistemaInfo.BBC.Application.Objects.Web.TipoEmpresa;
using SistemaInfo.BBC.Application.Services.TipoEmpresa;
using SistemaInfo.BBC.Domain.Models.TipoEmpresa.Repository;
using Xunit;

namespace BBC.Test.Tests.TipoEmpresa
{
    [Collection(nameof(TipoEmpresaCollection))]
    public class TipoEmpresaAppServiceAlterarStatusTest
    {
        private readonly TipoEmpresaFixture _fixture;
        private readonly TipoEmpresaAppService _appService;
        private readonly Mock<ITipoEmpresaReadRepository> _readRepository;
        public TipoEmpresaAppServiceAlterarStatusTest(TipoEmpresaFixture fixture)
        {
            _fixture = fixture;
            _appService = fixture.Mocker.CreateInstance<TipoEmpresaAppService>();
            _readRepository = fixture.Mocker.GetMock<ITipoEmpresaReadRepository>();
        }
        
        [Fact(DisplayName = "Save com sucesso")]
        [Trait(nameof(TipoEmpresaAppService), nameof(TipoEmpresaAppService.AlterarStatus))]
        public async void AlterarStatus_RetornaObjetoMapeado()
        {
            var lRequest = new TipoEmpresaStatusRequest {id = 1};

            // Arrange
            var lResponse = await _appService.AlterarStatus(lRequest);

            // Assert
            Assert.True(lResponse);
        }
    }
}
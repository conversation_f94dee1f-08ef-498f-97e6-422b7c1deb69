using System;
using System.Collections.Generic;
using Newtonsoft.Json;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Domain.Enum;

namespace SistemaInfo.BBC.Application.Objects.Api.Viagem
{
    /// <summary>
    /// Response para integração completa de viagem V2
    /// </summary>
    public class ViagemIntegrarV2Response : RespPadrao
    {
        public ViagemIntegrarV2Response()
        {
            data = new ViagemIntegrarV2Data();
        }

        public ViagemIntegrarV2Response(bool aSucesso, string aMensagem) : base(aSucesso, aMensagem)
        {
            data = new ViagemIntegrarV2Data();
        }

        public ViagemIntegrarV2Response(bool aSucesso, string aMensagem, ViagemIntegrarV2Data aData) : base(aSucesso, aMensagem, aData)
        {
        }

        public new ViagemIntegrarV2Data data { get; set; }
    }

    /// <summary>
    /// Dados da resposta de integração V2
    /// </summary>
    public class ViagemIntegrarV2Data
    {
        public ViagemIntegrarV2Data()
        {
            PagamentosEventos = new List<PagamentoV2Response>();
            Veiculos = new List<VeiculoV2Response>();
        }

        // Dados da Viagem
        public int Id { get; set; }
        public int? ViagemExternoId { get; set; }
        // Dados do CIOT
        public string Ciot { get; set; }
        public int Status { get; set; }
        public int StatusCiot { get; set; }
        public string MensagemCiot { get; set; }
        public DateTime? DataDeclaracaoCiot { get; set; }
        public decimal ValorFrete { get; set; }
        
        public List<PagamentoV2Response> PagamentosEventos { get; set; }
        public List<VeiculoV2Response> Veiculos { get; set; }
    }
    

    /// <summary>
    /// Response do pagamento para integração V2
    /// </summary>
    public class PagamentoV2Response
    {
        public bool Sucesso { get; set; }
        public int? Id { get; set; }
        public int? PagamentoExternoId { get; set; }
        public int Tipo { get; set; }
        public int FormaPagamento { get; set; }
        public decimal? ValorParcela { get; set; }
        public decimal? ValorMotorista { get; set; }
        public int? StatusPagamento { get; set; }
        public string CodigoTransacao { get; set; }
        public string Mensagem { get; set; }
        public bool PagamentoAgendado { get; set; }
        public DateTime? DataPrevisaoPagamento { get; set; }
        public List<PagamentoV2TransacaoResponse> Transacoes { get; set; }
    }
    
    ///<summary>
    ///Response do veículo para integração V2
    ///</summary>
    public class VeiculoV2Response
    {
        public int? VeiculoId { get; set; }
        public string Placa { get; set; }
        public string RNTRC { get; set; }
        public bool VeiculoCadastrado { get; set; }
        public bool VeiculoVinculado { get; set; }
        public string Mensagem { get; set; }
    }

    /// <summary>
    /// Response da transação para integração V2
    /// </summary>
    public class PagamentoV2TransacaoResponse
    {
        public int Id { get; set; }
        public int Status { get; set; }
        public string CodigoTransacaoCancelamento { get; set; }
        public string CodigoTransacao { get; set; }
        public string DocumentoDestino { get; set; }
        public string DocumentoOrigem { get; set; }
        public int? IdContaOrigem { get; set; }
        public int? IdContaDestino { get; set; }
        public decimal? Valor { get; set; }
        public string Agencia { get; set; }
        public string Conta { get; set; }
        public string CodigoBanco { get; set; }
        public StatusPagamento StatusEnum { get; set; }
    }

    /// <summary>
    /// Response interno para operações de pagamento V2 (não exposto na API)
    /// Substitui o uso de ViagemIntegrarResponse da V1
    /// </summary>
    public class ViagemIntegrarV2InternalResponse
    {
        public ViagemIntegrarV2InternalResponse()
        {
        }

        public ViagemIntegrarV2InternalResponse(bool sucesso, string mensagem)
        {
            Sucesso = sucesso;
            Mensagem = mensagem;
        }

        public bool Sucesso { get; set; }
        public int ViagemId { get; set; }
        public int? ViagemExternoId { get; set; }
        public int? StatusViagem { get; set; }
        public string Mensagem { get; set; }
        public PagamentoViagemV2InternalResponse Pagamento { get; set; }
    }

    /// <summary>
    /// Response interno para pagamento V2 (não exposto na API)
    /// Substitui o uso de PagamentoViagemResponse da V1
    /// </summary>
    public class PagamentoViagemV2InternalResponse
    {
        public int? PagamentoEventoId { get; set; }
        public int? PagamentoExternoId { get; set; }
        public decimal? ValorParcela { get; set; }
        public decimal? ValorMotorista { get; set; }
        public int? StatusPagamento { get; set; }
        public string CodigoTransacao { get; set; }
        public int? FormaPagamento { get; set; }
        public string Mensagem { get; set; }
        public List<PagamentoV2TransacaoResponse> Transacoes { get; set; }
    }
}

﻿using System;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.Objects.Mobile.Viagem.Request;

public class ConsultarPagamentosRequest
{
    public string CpfCnpj { get; set; }
    public int? ViagemId { get; set; }
    public int? ViagemExternoId { get; set; }
    public int? PagamentoId { get; set; }
    public int? PagamentoExternoId { get; set; }
    
    public int? IdConta { get; set; }
    public int Page { get; set; } = 0;
    public int Limit { get; set; } = 50;
    public DateTime? DataInicial { get; set; }
    public DateTime? DataFinal { get; set; }
    public string Ordem { get; set; } = "DESC"; // "ASC" ou "DESC"
    
    public string GrupoStatus { get; set; }

    public string validarFiltros()
    {
        if (Page < 0)
        {
            Page = 0;
        }

        if (Limit <= 0)
        {
            Limit = 50;
        }

        if (CpfCnpj.IsEmpty())
        {
            return "Informe o documento.";
        }
        
        CpfCnpj = CpfCnpj.Replace(".", "").Replace("-", "").Replace(",", "").Replace("/", "").Replace("-", "");
        if (CpfCnpj.Length == 11)
        {
            if (!CpfCnpj.ValidaCPF())
            {
                return "Informe um documento válido.";
            }
        }
        else
        {
            if (!CpfCnpj.ValidaCNPJ())
            {
                return "Informe um documento válido.";
            }
        }
        
        return null;
    }
}
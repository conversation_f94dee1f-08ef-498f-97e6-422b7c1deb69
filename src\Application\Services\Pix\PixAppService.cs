using System;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;
using NLog;
using SistemaInfo.BBC.Application.Helpers;
using SistemaInfo.BBC.Application.Interface.CentralPendencias;
using SistemaInfo.BBC.Application.Interface.Mensagem;
using SistemaInfo.BBC.Application.Interface.PagamentoEvento;
using SistemaInfo.BBC.Application.Interface.Pix;
using SistemaInfo.BBC.Application.Interface.Usuario;
using SistemaInfo.BBC.Application.Interface.Viagem;
using SistemaInfo.BBC.Application.Objects.Api.Pix.Transferencia;
using SistemaInfo.BBC.Application.Objects.Api.Viagem;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.Mensagem;
using SistemaInfo.BBC.Domain.CloudTranslationService;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Domain.External.Conductor.DTO;
using SistemaInfo.BBC.Domain.External.Conductor.DTO.Pix;
using SistemaInfo.BBC.Domain.External.Conductor.Interface;
using SistemaInfo.BBC.Domain.Models.PagamentoEvento.Repository;
using SistemaInfo.BBC.Domain.Models.Parametros.Repository;
using SistemaInfo.BBC.Domain.Models.Pix.Requests;
using SistemaInfo.BBC.Domain.Models.Pix.Requests.Transferencia;
using SistemaInfo.BBC.Domain.Models.Pix.Responses.Transferencia;
using SistemaInfo.BBC.Domain.Models.Portador.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.Services.Pix
{
    public class PixAppService : AppService, IPixAppService
    {
        private readonly IConductorPixRepository _conductorPixRepository;
        private readonly IPagamentoEventoReadRepository _pagamentoEventoReadRepository;
        private readonly IParametrosReadRepository _parametrosReadRepository;
        private readonly ICloudTranslationService _cloudTranslationService;
        private readonly IPagamentoEventoAppSerivce _pagamentoEventoAppSerivce;
        private readonly IMensagemAppService _mensagemAppService;

        public PixAppService(IAppEngine engine,
            IConductorPixRepository conductorPixRepository,
            IPagamentoEventoReadRepository pagamentoEventoReadRepository,
            IParametrosReadRepository parametrosReadRepository,
            ICloudTranslationService cloudTranslationService, IPagamentoEventoAppSerivce pagamentoEventoAppSerivce,
            IMensagemAppService mensagemAppService) : base(engine)
        {
            _conductorPixRepository = conductorPixRepository;
            _pagamentoEventoReadRepository = pagamentoEventoReadRepository;
            _parametrosReadRepository = parametrosReadRepository;
            _cloudTranslationService = cloudTranslationService;
            _pagamentoEventoAppSerivce = pagamentoEventoAppSerivce;
            _mensagemAppService = mensagemAppService;
        }

        public async Task<CriarTransferenciaPixResponse> CriarTransferenciaPix(CriarTransferenciaPixRequest request,
            int idContaOrigem)
        {
            try
            {
                #region Transforma os códigos de enums em enums

                var lTipoTransferenciaPix = (ETipoTransferenciaPix)request.TipoTransferenciaPix;

                if (!Enum.IsDefined(typeof(ETipoTransferenciaPix), lTipoTransferenciaPix))
                {
                    return new CriarTransferenciaPixResponse()
                    {
                        sucesso = false,
                        mensagem = "Tipo de transferência Pix inválido."
                    };
                }

                #endregion

                var lValidacaoTransferenciaDuplicada = await ValidaPixDuplicado(request, idContaOrigem);

                if (!lValidacaoTransferenciaDuplicada.sucesso)
                    return new CriarTransferenciaPixResponse(lValidacaoTransferenciaDuplicada.sucesso,
                        lValidacaoTransferenciaDuplicada.mensagem, lValidacaoTransferenciaDuplicada.data);


                #region Cria a transferência

                var lCriarTransferenciaRequest = new CriarTransferenciaPixBaaSRequest
                {
                    idAccount = request.IdAccount,
                    idEndToEnd = request.IdEndToEnd,
                    payee = Mapper.Map<InformacoesBeneficiarioPixBaaSRequest>(request.InformacoesBeneficiario),
                    finalAmount = request.ValorFinalPagamento,
                    idTx = request.IdTx,
                    payerAnswer = request.Descricao,
                    transferType = lTipoTransferenciaPix.GetHashCode()
                };

                var lSerializedRequest = JsonConvert.SerializeObject(lCriarTransferenciaRequest);

                var lDockResponse = await _conductorPixRepository.CriarTransferencia(lSerializedRequest);

                #endregion

                #region Trata o retorno da Dock

                var mensagemDockTraduzida =
                    await _cloudTranslationService.Traduzir(lDockResponse.Mensagem ?? lDockResponse.Retorno?.Message);

                var mensagemRetorno = await RetornarMensagemDockPix(mensagemDockTraduzida,
                    lDockResponse.Mensagem, lDockResponse.Retorno?.Message);

                lDockResponse.Mensagem = lDockResponse.StatusCode switch
                {
                    202 => "Pix criado com sucesso.",
                    _ => $"Erro Dock: {mensagemRetorno}"
                };

                return new CriarTransferenciaPixResponse()
                {
                    sucesso = lDockResponse.Sucesso,
                    mensagem = lDockResponse.Mensagem,
                    data = lDockResponse.Retorno,
                    StatusCodeDock = lDockResponse.StatusCode
                };

                #endregion
            }
            catch (Exception e)
            {
                return new CriarTransferenciaPixResponse()
                {
                    sucesso = false,
                    mensagem = e.Message
                };
            }
        }

        public async Task<RespPadrao> ValidaPixDuplicado(CriarTransferenciaPixRequest request, int idContaOrigem)
        {
            try
            {
                new LogHelper().LogOperationStart("ValidaPixDuplicado");

                var lTempoRetroativo =
                    ((await _parametrosReadRepository.GetByTipoDoParametroAsync(Domain.Models.Parametros.Parametros
                        .TipoDoParametro.TempoRetroativoPixDuplicado))?.Valor ?? "10").ToIntSafe();
                var requestRecibos = new ListarRecibosTransferenciaPixBaaSRequest
                {
                    from = DateTime.Now.AddHours(+3).AddMinutes(-lTempoRetroativo).ToString("s"),
                    idAccount = idContaOrigem
                };

                var respostaRecibos =
                    await _conductorPixRepository.ListarRecibosTransferencia(requestRecibos);

                if (respostaRecibos?.Retorno?.Items == null || !respostaRecibos.Retorno.Items.Any())
                {
                    return new RespPadrao()
                    {
                        sucesso = true,
                        mensagem = "Esta transferência não é duplicada.",
                        data = null
                    };
                }

                if (respostaRecibos.Retorno.Items.Any(c => request.ValorFinalPagamento.CompareTo(c.FinalAmount) == 0))
                {
                    return new RespPadrao()
                    {
                        sucesso = false,
                        mensagem = "Um Pix duplicado foi detectado e não foi executado.",
                        data = null
                    };
                }

                return new RespPadrao()
                {
                    sucesso = true,
                    mensagem = "Esta transferência não é duplicada.",
                    data = null
                };
            }
            catch (Exception ex)
            {
                new LogHelper().Error(ex, "Erro ao executar ValidaPixDuplicado");
                throw;
            }
            finally
            {
                new LogHelper().LogOperationEnd("ValidaPixDuplicado");
            }
        }

        public async Task<ConsultarTransferenciaResponse> ConsultarTransferencia(string transationCode)
        {
            try
            {
                #region Consulta o reembolso especificado

                var lDockResponse = await _conductorPixRepository.ConsultarTransferencia(transationCode);

                #endregion

                #region Trata o retorno da Dock

                lDockResponse.Retorno.IdAccount = null;

                var mensagemDockTraduzida =
                    await _cloudTranslationService.Traduzir(lDockResponse.Mensagem ?? lDockResponse.Retorno?.Message);

                var mensagemRetorno = await RetornarMensagemDockPix(mensagemDockTraduzida,
                    lDockResponse.Mensagem, lDockResponse.Retorno?.Message);

                lDockResponse.Mensagem = lDockResponse.StatusCode switch
                {
                    200 => "Transferência consultada com sucesso!",
                    _ => $"Erro Dock: {mensagemRetorno}"
                };

                if (lDockResponse.Retorno != null)
                {
                    if (lDockResponse.Retorno.ErrorType != null)
                    {
                        lDockResponse.Retorno.ErroDescricao = lDockResponse.Retorno.ErrorType switch
                        {
                            1 => "Liquidação abortada devido ao tempo limite.",
                            2 => "Transação interrompida devido a erro no Agente Beneficiário.",
                            3 => "Número da conta do beneficiário inválido ou ausente.",
                            4 => "Conta especificada bloqueada, proibindo transações a envolvendo.",
                            5 => "Número da conta do beneficiário encerrada.",
                            6 => "Tipo de conta do beneficiário ausente ou inválido.",
                            7 => "Tipo de transação não suportado/autorizado nesta conta.",
                            8 => "Participante do ISPB não identificado.",
                            9 => "A quantidade de mensagem especificada é igual a zero.",
                            10 => "Sem limite disponível para a transação.",
                            11 => "O valor recebido não é o valor acordado ou esperado.",
                            12 =>
                                "O número de transações é inválido ou ausente. Uso genérico se não puder especificar entre os níveis de informação de grupo e de pagamento.",
                            13 => "A identificação do cliente final não é consistente com o número da conta associada.",
                            14 => "O valor no identificador do beneficiário está incorreto.",
                            15 => "O conteúdo está incorreto.",
                            16 => "A ordem foi rejeitada pelo lado do banco (por razões de conteúdo).",
                            17 => "O signatário não tem permissão para assinar este tipo de operação.",
                            18 => "Data e hora de criação inválidas.",
                            19 =>
                                "Mensagem associada, bloqueio de informações de pagamento ou transação foi recebida após a data limite de processamento acordada, por exemplo, data no passado.",
                            20 => "A liquidação da transação falhou ou tem um EndToEnd duplicado.",
                            21 => "EndToEndId ausente ou inválido.",
                            22 => "O participante pagador é inválido ou ausente.",
                            23 => "O participante beneficiário é inválido ou ausente.",
                            27 => "Valor não permitido.",
                            32 => "Usuário ainda não ativado.",
                            36 => "Transação interrompida por timeout no Agente Devedor.",
                            37 => "Código de identificação do credor ou credor final ausente ou inválido.",
                            40 =>
                                "Não é possível enviar Pix para este participante. Este ISPB não permite enviar ou receber Pix.",
                            _ => "ErrorType dock code: " + lDockResponse.Retorno.ErrorType
                        };
                    }
                }

                return new ConsultarTransferenciaResponse()
                {
                    sucesso = lDockResponse.Sucesso,
                    mensagem = lDockResponse.Mensagem,
                    data = lDockResponse.Retorno,
                    StatusCodeDock = lDockResponse.StatusCode
                };

                #endregion
            }
            catch (Exception e)
            {
                return new ConsultarTransferenciaResponse()
                {
                    sucesso = false,
                    mensagem = e.Message
                };
            }
        }

        public async Task<RespPadrao> ConsultarPixManual(int idPagamentoEvento)
        {
            var lLog = LogManager.GetCurrentClassLogger();
            try
            {
                var lPagamentoEvento = await _pagamentoEventoReadRepository
                    .GetByIdIncludeTransacoesEViagemAsync(idPagamentoEvento);

                if (lPagamentoEvento.CodigoTransacao == null)
                {
                    return new RespPadrao()
                    {
                        sucesso = false,
                        mensagem = "Código de transação não encontrado."
                    };
                }

                if (lPagamentoEvento.Status != StatusPagamento.Pendente)
                {
                    return new RespPadrao()
                    {
                        sucesso = false,
                        mensagem = "Verificação manual habilitada apenas para Pix pendentes."
                    };
                }

                var lConsultaPix = await ConsultarTransferencia(lPagamentoEvento.CodigoTransacao);

                if (!lConsultaPix.sucesso)
                {
                    return new RespPadrao()
                    {
                        sucesso = false,
                        mensagem = "Não foi possível reconsultar o status deste Pix."
                    };
                }

                var lPixInfo = (ConsultarTransferenciaPixBaaSResponse)lConsultaPix.data;

                if (lPixInfo == null)
                {
                    return new RespPadrao()
                    {
                        sucesso = false,
                        mensagem = "Não foi possível reconsultar o status deste Pix."
                    };
                }

                var statusInicial = lPagamentoEvento.Status;
                var dataDock = lPixInfo.TransactionDate;
                switch (lPixInfo.TransactionStatus)
                {
                    case "NOT_EXECUTED":
                    {
                        lPagamentoEvento.Status = StatusPagamento.NaoExecutado;
                        lPagamentoEvento.MotivoPendencia = lPixInfo.ErroDescricao;
                        var lTransacao =
                            lPagamentoEvento.Transacao.FirstOrDefault(x =>
                                x.FormaPagamento == FormaPagamentoEvento.Pix);
                        if (lTransacao == null) break;
                        lTransacao.Status = StatusPagamento.NaoExecutado;
                        lTransacao.Descricao = "Transação atualizada para não executada pelo serviço de reconsulta. " +
                                               lPixInfo.ErroDescricao;
                        lTransacao.JsonRespostaDock = JsonConvert.SerializeObject(lConsultaPix);
                        lTransacao.ResponseCodeDock = lConsultaPix.StatusCodeDock ?? 0;
                        lTransacao.DataRetornoDock = DateTime.Now;
                        break;
                    }
                    case "EXECUTED":
                    {
                        lPagamentoEvento.Status = StatusPagamento.Fechado;
                        lPagamentoEvento.MotivoPendencia = null;
                        lPagamentoEvento.DataBaixa = lPixInfo.TransactionDate != null
                            ? lPixInfo.TransactionDate.ToDateTimeSafe().AddHours(-3)
                            : DateTime.Now;
                        var lTransacao =
                            lPagamentoEvento.Transacao.FirstOrDefault(x =>
                                x.FormaPagamento == FormaPagamentoEvento.Pix);
                        if (lTransacao == null) break;
                        lTransacao.Status = StatusPagamento.Fechado;
                        lTransacao.Descricao = "Transação Pix executada com sucesso!";
                        lTransacao.DataBaixa = lPixInfo.TransactionDate != null
                            ? lPixInfo.TransactionDate.ToDateTimeSafe().AddHours(-3)
                            : DateTime.Now;
                        lTransacao.JsonRespostaDock = JsonConvert.SerializeObject(lConsultaPix);
                        lTransacao.ResponseCodeDock = lConsultaPix.StatusCodeDock ?? 0;
                        lTransacao.DataRetornoDock = DateTime.Now;
                        if (lPagamentoEvento.Tipo != null && (Tipo)lPagamentoEvento.Tipo == Tipo.Saldo)
                            lPagamentoEvento.Viagem.Status = StatusViagem.Baixado;
                        break;
                    }
                    case "PENDING":
                    {
                        lPagamentoEvento.Status = StatusPagamento.Pendente;
                        lPagamentoEvento.MotivoPendencia = "Transação Pix criada com status pendente.";
                        var lTransacao =
                            lPagamentoEvento.Transacao.FirstOrDefault(x =>
                                x.FormaPagamento == FormaPagamentoEvento.Pix);
                        if (lTransacao == null) break;
                        lTransacao.Status = StatusPagamento.Pendente;
                        lTransacao.Descricao = "Transação Pix criada com status pendente.";
                        lTransacao.JsonRespostaDock = JsonConvert.SerializeObject(lConsultaPix);
                        lTransacao.ResponseCodeDock = lConsultaPix.StatusCodeDock ?? 0;
                        lTransacao.DataRetornoDock = DateTime.Now;
                        break;
                    }
                }

                await _pagamentoEventoReadRepository.SaveChangesAsync();
                if (lPagamentoEvento.Status != statusInicial)
                    await _pagamentoEventoAppSerivce.EnviaWebHookAtualizacaoStatus(lPagamentoEvento);

                return new RespPadrao()
                {
                    sucesso = true,
                    mensagem = "Transação Pix atualizada com sucesso."
                };
            }
            catch (Exception e)
            {
                lLog.Error(e);
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = "Erro interno ao reconsultar o status deste Pix: " + e.Message
                };
            }
        }

        public async Task<RespPadrao> ConsultarChave(string documento, string chave)
        {
            try
            {
                if (!documento.ValidaCNPJ() && !documento.ValidaCPF())
                    return new RespPadrao(false, "CPF ou CNPJ informado inválido.");
                if (chave.IsNullOrWhiteSpace()) return new RespPadrao(false, "Chave pix inválida.");

                var lDadosContaPorChave = await _conductorPixRepository.ConsultarDadosContaPorChave(chave);
                if (lDadosContaPorChave?.Retorno?.Items == null) return new RespPadrao(false, "Chave pix inválida.");

                return lDadosContaPorChave.Retorno.Items[0].KeyAccount.NationalRegistration == documento &&
                       lDadosContaPorChave.Retorno.Items[0].KeyStatus == "OPEN"
                    ? new RespPadrao(true, "Chave pix válida.")
                    : new RespPadrao(false, "Chave pix inválida.");
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return new RespPadrao(false, "Chave pix inválida.");
            }
        }

        public async Task<RespPadrao> ValidarChave(int idContaResponsavelPelaValidacao, string chave)
        {
            try
            {
                #region Valida a chave pix

                var lDockResponse = await _conductorPixRepository
                    .ValidarChave(chave, idContaResponsavelPelaValidacao.ToString());

                #endregion

                #region Trata o retorno da Dock

                var mensagemDockTraduzida =
                    await _cloudTranslationService.Traduzir(lDockResponse.Mensagem ?? lDockResponse.Retorno?.Message);

                var mensagemRetorno = await RetornarMensagemDockPix(mensagemDockTraduzida,
                    lDockResponse.Mensagem, lDockResponse.Retorno?.Message);

                lDockResponse.Mensagem = lDockResponse.StatusCode switch
                {
                    200 => "Chave validada com sucesso!",
                    _ => $"Erro Dock: {mensagemRetorno}"
                };

                return new RespPadrao
                {
                    sucesso = lDockResponse.Sucesso,
                    mensagem = lDockResponse.Mensagem,
                    data = lDockResponse.Retorno
                };

                #endregion
            }
            catch (Exception e)
            {
                return new RespPadrao
                {
                    sucesso = false,
                    mensagem = "Erro ao validar chave Pix. " + e.Message
                };
            }
        }

        public async Task ServiceConsultarStatusPix()
        {
            var lLog = LogManager.GetCurrentClassLogger();
            try
            {
                var lPixPendentes = await _pagamentoEventoReadRepository.GetPixPendentes();

                var lParametroQuantidadeTentativa = (await _parametrosReadRepository.GetParametrosAsync(-1,
                    Domain.Models.Parametros.Parametros.TipoDoParametro.QuantidadeVezesConsultaPix,
                    Domain.Models.Parametros.Parametros.TipoDoValor.Number)).Valor.ToIntSafe();

                if (lParametroQuantidadeTentativa == 0) lParametroQuantidadeTentativa = 3;

                foreach (var lPix in lPixPendentes)
                {
                    try
                    {
                        var statusInicial = lPix.Status;

                        if (lPix.CodigoTransacao == null) continue;
                        if (lPix.ContadorVerificacaoStatusPix >= lParametroQuantidadeTentativa) continue;
                        lPix.ContadorVerificacaoStatusPix ??= 0;
                        var lRetorno = await ConsultarTransferencia(lPix.CodigoTransacao);
                        if (!lRetorno.sucesso) continue;
                        var lInfoPix = (ConsultarTransferenciaPixBaaSResponse)lRetorno.data;
                        var dataDock = lInfoPix.TransactionDate;
                        switch (lInfoPix.TransactionStatus)
                        {
                            case "NOT_EXECUTED":
                            {
                                lPix.Status = StatusPagamento.NaoExecutado;
                                lPix.ContadorVerificacaoStatusPix++;
                                lPix.MotivoPendencia = lInfoPix.ErroDescricao;
                                var lPixTransacao =
                                    lPix.Transacao.FirstOrDefault(x => x.FormaPagamento == FormaPagamentoEvento.Pix);
                                if (lPixTransacao == null) continue;
                                lPixTransacao.Status = StatusPagamento.NaoExecutado;
                                lPixTransacao.Descricao =
                                    "Transação atualizada para não executada pelo serviço de reconsulta. " +
                                    lInfoPix.ErroDescricao;
                                lPixTransacao.JsonRespostaDock = JsonConvert.SerializeObject(lRetorno);
                                lPixTransacao.ResponseCodeDock = lRetorno.StatusCodeDock ?? 0;
                                lPixTransacao.DataRetornoDock = DateTime.Now;
                                break;
                            }
                            case "EXECUTED":
                            {
                                lPix.Status = StatusPagamento.Fechado;
                                lPix.ContadorVerificacaoStatusPix++;
                                lPix.DataBaixa = lInfoPix.TransactionDate != null
                                    ? lInfoPix.TransactionDate.ToDateTimeSafe().AddHours(-3)
                                    : DateTime.Now;
                                var lPixTransacao =
                                    lPix.Transacao.FirstOrDefault(x => x.FormaPagamento == FormaPagamentoEvento.Pix);
                                if (lPixTransacao == null) continue;
                                lPixTransacao.Status = StatusPagamento.Fechado;
                                lPixTransacao.Descricao = "Transação Pix executada com sucesso!";
                                lPixTransacao.DataBaixa =
                                    dataDock != null ? dataDock.ToDateTimeSafe().AddHours(-3) : DateTime.Now;
                                lPixTransacao.JsonRespostaDock = JsonConvert.SerializeObject(lRetorno);
                                lPixTransacao.ResponseCodeDock = lRetorno.StatusCodeDock ?? 0;
                                lPixTransacao.DataRetornoDock = DateTime.Now;
                                if (lPix.Tipo != null && (Tipo)lPix.Tipo == Tipo.Saldo)
                                    lPix.Viagem.Status = StatusViagem.Baixado;
                                break;
                            }
                            case "PENDING":
                            {
                                lPix.Status = StatusPagamento.Pendente;
                                lPix.ContadorVerificacaoStatusPix++;
                                var lPixTransacao =
                                    lPix.Transacao.FirstOrDefault(x => x.FormaPagamento == FormaPagamentoEvento.Pix);
                                if (lPixTransacao == null) continue;
                                lPixTransacao.Status = StatusPagamento.Pendente;
                                lPixTransacao.JsonRespostaDock = JsonConvert.SerializeObject(lRetorno);
                                lPixTransacao.ResponseCodeDock = lRetorno.StatusCodeDock ?? 0;
                                lPixTransacao.DataRetornoDock = DateTime.Now;
                                if (lPix.ContadorVerificacaoStatusPix >= lParametroQuantidadeTentativa)
                                {
                                    lPix.DataTerceiraVerificacaoStatusPix = DateTime.Now;
                                    lPix.MotivoPendencia = "Transação Pix ficou pendente por mais de 1h30min.";
                                    lPixTransacao.Descricao = "Transação Pix ficou pendente por mais de 1h30min.";
                                }

                                break;
                            }
                        }

                        if (statusInicial != lPix.Status)
                            await _pagamentoEventoAppSerivce.EnviaWebHookAtualizacaoStatus(lPix);
                    }
                    catch (Exception e)
                    {
                        lLog.Error(e, $"BAT_PIX_01 ERRO PagamentoEvento {lPix.Id}");
                    }
                }

                await _pagamentoEventoReadRepository.SaveChangesAsync();
            }
            catch (Exception e)
            {
                lLog.Error(e, "BAT_PIX_01 ERRO");
            }
        }

        public async Task<string> RetornarMensagemDockPix(string mensagemTraduzida,
            string dockResponseMessage, string dockResponseReturnMessage)
        {
            var mensagemRetornada = await _mensagemAppService.RegistraMensagem(new MensagemRequest
            {
                CodigoAplicacao = ECodigoAplicacao.Dock,
                TextoMensagemOriginal = dockResponseMessage ?? dockResponseReturnMessage,
                TextoMensagemPadrao = mensagemTraduzida,
                Ativo = 1,
                CodigoMensagem = null,
                DataInicioMensagem = null,
                DataFimMensagem = null,
                DescricaoMensagem = null,
                ImagemMensagem = null
            });

            var mensagemRetornoDockPagamento = "";

            if (mensagemRetornada.novaMensagem)
                mensagemRetornoDockPagamento = mensagemRetornada.data.TextoMensagemPadrao ?? mensagemTraduzida;

            if (!mensagemRetornada.novaMensagem)
            {
                if (mensagemRetornada.data.Ativo == 1 && mensagemRetornada.data.MensagemTratada == 1)
                {
                    mensagemRetornoDockPagamento = mensagemRetornada.data.TextoMensagem ?? mensagemTraduzida;
                }
                else if (mensagemRetornada.data.Ativo == 1 && mensagemRetornada.data.MensagemTratada == 0)
                {
                    mensagemRetornoDockPagamento = mensagemRetornada.data.TextoMensagemPadrao ?? mensagemTraduzida;
                }
                else
                {
                    mensagemRetornoDockPagamento = mensagemTraduzida;
                }
            }

            return mensagemRetornoDockPagamento;
        }
    }
}
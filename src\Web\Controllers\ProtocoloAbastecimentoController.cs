using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SistemaInfo.BBC.Application.Interface.ProtocoloAbastecimento;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.PagamentoAbastecimento;
using SistemaInfo.BBC.Application.Objects.Web.ProtocoloAbastecimento;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Infra.Reports.Interfaces;
using SistemaInfo.BBC.Infra.Reports.Objects;
using SistemaInfo.BBC.Web.Attributes;
using SistemaInfo.BBC.Web.Controllers.Base;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;

namespace SistemaInfo.BBC.Web.Controllers
{
    /// <summary>
    /// Definição da rota e instanciamento da classe, com suas devidas implementações
    /// </summary>
    [Route("ProtocoloAbastecimento")]
    public class ProtocoloAbastecimentoController : WebControllerBase<IProtocoloAbastecimentoAppService>
    {
        private readonly IReportExporterFinanceiro _reportExporterFinanceiro;

        /// <summary>
        /// Construtor da classe com heranças
        /// </summary>
        /// <param name="engine"></param>
        /// <param name="appService"></param>
        /// <param name="reportExporterFinanceiro"></param>
        public ProtocoloAbastecimentoController(IAppEngine engine, IProtocoloAbastecimentoAppService appService,
            IReportExporterFinanceiro reportExporterFinanceiro) : base(engine, appService)
        {
            _reportExporterFinanceiro = reportExporterFinanceiro;
        }
        
        /// <summary>
        /// Método de consulta referente a grid de protocolo de abastecimento. Utiliza os filtros de
        /// período e empresa(através do id)
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("ConsultarGridProtocoloAbastecimento")]
        [Menu(new[] { EMenus.PainelPedidosPendentes,EMenus.PainelProtocoloAbastecimento, EMenus.ProtocoloAbastecimento })]
        public JsonResult ConsultarGridProtocoloAbastecimento([FromBody]ConsultarGridProtocoloAbastecimentoRequest request)
        {
            var consultarGridProtocoloAbastecimento = AppService.ConsultarGridProtocoloAbastecimento(request);
            return ResponseBase.BigJson(consultarGridProtocoloAbastecimento);
            return ResponseBase.BigJson(consultarGridProtocoloAbastecimento);
        }
        
        
        /// <summary>
        /// Exporta a grid como relatório 
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("ExportarRelatorio")]
        [Menu(new[] { EMenus.ProtocoloAbastecimento, EMenus.PainelProtocoloAbastecimento })]
        public JsonResult ExportarRelatorio([FromBody]DtoExportarRelatorioProtocoloAbastecimento request)
        {
            try
            {
                var lExportList = AppService.ExportarRelatorio(request);
        
                var headerNames = new HeaderNames()
                {
                    AgrupadorName = null,
                    ItensName = "Abastecimentos"
                };
                var lBase64 = _reportExporterFinanceiro.Export(lExportList, request.TipoExport, request.Fields, headerNames);
                return ResponseBase.ResponderSucesso(lBase64);
            }
            catch(Exception e)
            {
                return ResponseBase.ResponderErro("Ocorreu um erro ao exportar relatório " + e.Message);
            }
        }
        
        /// <summary>
        /// Exporta a grid como relatório 
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("ExportarRelatorioControle")]
        [Menu(new[] { EMenus.PainelProtocoloAbastecimento, EMenus.ProtocoloAbastecimento })]
        public JsonResult ExportarRelatorioControle([FromBody]DtoExportarRelatorioProtocoloAbastecimentoControle request)
        {
            try
            {
                var lExportList = AppService.ExportarRelatorioControle(request);
        
                var headerNames = new HeaderNames()
                {
                    AgrupadorName = null,
                    ItensName = "Abastecimentos"
                };
                
                var lBase64 = _reportExporterFinanceiro.Export(lExportList, request.TipoExport, request.Fields, headerNames);
                
                return ResponseBase.ResponderSucesso(lBase64);
            }
            catch(Exception e)
            {
                return ResponseBase.ResponderErro("Ocorreu um erro ao exportar o relatório. " + e.Message);
            }
        }
        
        /// <summary>
        /// Método responsável por trazer em tela alguns valores do xml
        /// </summary>
        /// <param name="lXmlAbastecimentoRequest"></param>
        /// <returns></returns>
        [HttpPost("CarregarDadosXml")]
        [Menu(new[] { EMenus.ProtocoloAbastecimento })]
        public JsonResult CarregarDadosXml([FromBody]XmlAbastecimentoRequest lXmlAbastecimentoRequest)
        {
            try
            {
                var lCarregarDadosXml = AppService.CarregarDadosXml(lXmlAbastecimentoRequest);
                
                return ResponseBase.BigJson(lCarregarDadosXml);
            }
            catch (Exception)
            {
                return ResponseBase.ResponderErro("Não foi possível realizar a operação.");
            }
        }
        
        /// <summary>
        /// Método responsável por fazer as várias requisições do xml em relação aos abastecimentos selecionados
        /// </summary>
        /// <param name="lValidacaoXmlReq"></param>
        /// <returns></returns>
        [HttpPost("ValidarXml")]
        [Menu(new[] { EMenus.ProtocoloAbastecimento })]
        public JsonResult ValidarXml([FromBody]ValidacaoXmlRequest lValidacaoXmlReq)
        {
            try
            {
                var lValidarXml = AppService.ValidarXml(lValidacaoXmlReq);
                
                return ResponseBase.BigJson(lValidarXml);
            }
            catch (Exception)
            {
                return ResponseBase.ResponderErro("Não foi possível realizar a operação.");
            }
        }
        
        /// <summary>
        /// Metodo responsavel por validar a relação de produto nota com abatecimentos
        /// </summary>
        /// <param name="itensValidacao"></param>
        /// <returns></returns>
        [HttpPost("ValidaXmlNota")]
        [Menu(new[] { EMenus.ProtocoloAbastecimento })]
        public async Task<JsonResult> ValidaXmlNota([FromBody]ItensAbastecimentoValidarXml itensValidacao)
        {
            try
            {
                var lValidarXml = await AppService.ValidarXmlNota(itensValidacao, Engine.User.AdministradoraId);
                return ResponseBase.BigJson(lValidarXml);
            }
            catch (Exception)
            {
                return ResponseBase.ResponderErro("Não foi possível realizar a operação.");
            }
        }
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="lValidacaoXmlReq"></param>
        /// <returns></returns>
        [HttpPost("SaveProtocoloAbastecimentoSap")]
        [Menu(new[] { EMenus.ProtocoloAbastecimento })]
        public JsonResult SaveProtocoloAbastecimentoSap([FromBody]ValidacaoXmlRequest lValidacaoXmlReq)
        {
            try
            {
                var lValidarXml = AppService.ValidarXml(lValidacaoXmlReq);
                
                return ResponseBase.BigJson(lValidarXml);
            }
            catch (Exception)
            {
                return ResponseBase.ResponderErro("Não foi possível realizar a operação.");
            }
        }
        
        /// <summary>
        /// Consulta grid do painel de protocolo abastecimento em abinete CIOT
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("ConsultarGridPainelProtocolo")]
        [Menu(new[] { EMenus.PainelProtocoloAbastecimento })]
        public async Task<JsonResult> ConsultarGridPainelProtocoloAbastecimento([FromBody]ConsultaGridProtocoloAbastecimentoRequest request)
        {
            var consultarGridPagamentoAbastecimentoCombo = await AppService.ConsultarGridPainelProtocoloAbastecimento(request.EmpresaId, request.Status, request.DtInicial, request.DtFinal, request.Take, request.Page, request.Order, request.Filters);
            return ResponseBase.ResponderSucesso(consultarGridPagamentoAbastecimentoCombo);
        }
        
        /// <summary>
        /// Metodo que gera os relatórios
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("ConsultarGridPainelProtocoloAbastecimentoRelatorio")]
        [Menu(new[] { EMenus.PainelProtocoloAbastecimento })]
        public JsonResult ConsultarGridPainelProtocoloAbastecimentoRelatorio([FromBody]ConsultaGridProtocoloAbastecimentoRequest request)
        {
            var consultarGridPagamentoAbastecimentoCombo = AppService.ConsultarGridPainelProtocoloAbastecimentoRelatorio(request.EmpresaId, request.Status, request.DtInicial, request.DtFinal, request.Take, request.Page, request.Order, request.Filters);
            return ResponseBase.ResponderSucesso(consultarGridPagamentoAbastecimentoCombo);
        }
        
        /// <summary>
        /// Metodo de reprovação de protocolos
        /// </summary>
        /// <param name="protocoloId"></param>
        /// <returns></returns>
        [HttpPost("ReprovarProtocolo")]
        [Menu(new[] { EMenus.PainelProtocoloAbastecimento })]
        public JsonResult ReprovarProtocoloPendente([FromBody]List<ProtocoloPendenteRequest> protocoloId)
        {
            var reprovarProtocoloPendente = AppService.ReprovarProtocoloPendente(protocoloId).Result;
            return ResponseBase.ResponderSucesso(reprovarProtocoloPendente);
        }
        
        /// <summary>
        /// Metodo de aprovação de protocolos
        /// </summary>
        /// <param name="protocoloId"></param>
        /// <returns></returns>
        [HttpPost("AprovarProtocolo")]
        [Menu(new[] { EMenus.PainelProtocoloAbastecimento })]
        public JsonResult AprovarProtocoloPendente([FromBody]List<ProtocoloPendenteRequest> protocoloId)
        {
            var aprovarProtocoloPendente = AppService.AprovarProtocoloPendente(protocoloId).Result;
            return aprovarProtocoloPendente.sucesso 
                ? ResponseBase.Responder(true, aprovarProtocoloPendente.mensagem, aprovarProtocoloPendente.data)
                : ResponseBase.Responder(false, aprovarProtocoloPendente.mensagem, aprovarProtocoloPendente.data);
        }
        
        /// <summary>
        /// Busca os dados da empresa para realizar a edição
        /// </summary>
        /// <param name="lvalidarAbastecimentoRequest"></param>
        /// <returns></returns>
        [HttpPost("ValidarValorAbastecimento")]
        [Menu(new[] { EMenus.ProtocoloAbastecimento })]
        public JsonResult ValidarValorAbastecimento([FromBody]ValidarAbastecimentoRequest lvalidarAbastecimentoRequest)
        {
            try
            {
                var retorno = AppService.ValidarValorAbastecimento(lvalidarAbastecimentoRequest);
                
                return retorno.sucesso == true ? 
                    ResponseBase.ResponderSucesso(retorno) : 
                    ResponseBase.Responder(false, retorno.mensagem, retorno.data);
            }
            catch (Exception e)
            {
                return ResponseBase.ResponderErro("Ocorreu um erro: " + e.Message);
            }
        }
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="protocoloId"></param>
        /// <returns></returns>
        [HttpPost("ReenviarPedidoPendente")]
        [Menu(new[] { EMenus.PainelPedidosPendentes, EMenus.ProtocoloAbastecimento })]
        public JsonResult IntegracaoSap([FromBody]int protocoloId)
        {
            var retornoReenvioPendente = AppService.IntegracaoSap(protocoloId).Result;
            
            return retornoReenvioPendente.Sucesso 
                ? ResponseBase.Responder(true, retornoReenvioPendente.Mensagem, null) 
                : ResponseBase.ResponderErro(retornoReenvioPendente.Mensagem);
        }
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="dataFinal"></param>
        /// <param name="dataInicial"></param>
        /// <param name="empresaId"></param>
        /// <returns></returns>
        [HttpGet("ConsultarGridProtocoloReenvioSap")]
        [Menu(new[] { EMenus.ProtocoloAbastecimento })]
        public JsonResult ConsultarGridProtocoloReenvioSap(string dataInicial, string dataFinal, int empresaId)
        {
            var ConsultarGridProtocoloReenvioSap = AppService.ConsultarGridProtocoloReenvioSap(dataInicial, dataFinal, empresaId);
            return ResponseBase.BigJson(ConsultarGridProtocoloReenvioSap);
        }
        
        /// <summary>
        /// Método de consulta referente a grid do painel de pedidos pendentes do ciot
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("ConsultarGridProtocoloPedidosPendentes")]
        [Menu(new[] { EMenus.PainelPedidosPendentes })]
        public JsonResult ConsultarGridProtocoloPedidosPendentes([FromBody] DtoConsultaGridPedidosPendentes request)
        {
            var consultarGridProtocoloAbastecimento = AppService.ConsultarGridProtocoloPedidosPendentes(request);
            return ResponseBase.ResponderSucesso(consultarGridProtocoloAbastecimento);
        }
    }
}
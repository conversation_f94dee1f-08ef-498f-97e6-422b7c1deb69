using BBC.Test.Tests.EnumExtensions.Fixture;
using SistemaInfo.BBC.Domain.Enum;
using Xunit;

namespace BBC.Test.Tests.EnumExtensions
{
    [Collection(nameof(EnumExtensionsCollection))]
    public class EnumExtensionsTest
    {
        private readonly EnumExtensionsFixture _fixture;

        public EnumExtensionsTest(EnumExtensionsFixture fixture)
        {
            _fixture = fixture;
        }

        [Theory(DisplayName = "GetEnumDescription - deve retornar descrição correta para ETipoPessoa")]
        [Trait("EnumExtensions", "GetEnumDescription")]
        [InlineData("Fisica", "Física")]
        [InlineData("Juridica", "Jurídica")]
        public void GetEnumDescription_DeveRetornarDescricaoCorreta_ParaETipoPessoa(string enumNome, string descricaoEsperada)
        {
            // Arrange
            var enumValue = System.Enum.Parse<SistemaInfo.BBC.Domain.Enum.ETipoPessoa>(enumNome);

            // Act
            var resultado = enumValue.GetEnumDescription();

            // Assert
            Assert.Equal(descricaoEsperada, resultado);
        }

        [Theory(DisplayName = "GetEnumDescription - deve retornar descrição correta para ESexo")]
        [Trait("EnumExtensions", "GetEnumDescription")]
        [InlineData("Masculino", "Masculino")]
        [InlineData("Feminino", "Feminino")]
        [InlineData("Outros", "Outros")]
        [InlineData("Indefinido", "Indefinido")]
        public void GetEnumDescription_DeveRetornarDescricaoCorreta_ParaESexo(string enumNome, string descricaoEsperada)
        {
            // Arrange
            var enumValue = System.Enum.Parse<ESexo>(enumNome);

            // Act
            var resultado = enumValue.GetEnumDescription();

            // Assert
            Assert.Equal(descricaoEsperada, resultado);
        }

        [Theory(DisplayName = "GetEnumDescription - deve retornar descrição correta para StatusCadastro")]
        [Trait("EnumExtensions", "GetEnumDescription")]
        [InlineData("Bloqueado", "Bloqueado")]
        [InlineData("Ativo", "Ativo")]
        [InlineData("PendentedeValidação", "Pendente de validação")]
        public void GetEnumDescription_DeveRetornarDescricaoCorreta_ParaStatusCadastro(string enumNome, string descricaoEsperada)
        {
            // Arrange
            var enumValue = System.Enum.Parse<StatusCadastro>(enumNome);

            // Act
            var resultado = enumValue.GetEnumDescription();

            // Assert
            Assert.Equal(descricaoEsperada, resultado);
        }

        [Theory(DisplayName = "GetEnumDescription - deve retornar descrição correta para TipoEmpresa")]
        [Trait("EnumExtensions", "GetEnumDescription")]
        [InlineData("JSL", "JSL")]
        [InlineData("Movida", "Movida")]
        [InlineData("BBC", "BBC")]
        public void GetEnumDescription_DeveRetornarDescricaoCorreta_ParaTipoEmpresa(string enumNome, string descricaoEsperada)
        {
            // Arrange
            var enumValue = System.Enum.Parse<SistemaInfo.BBC.Domain.Enum.TipoEmpresa>(enumNome);

            // Act
            var resultado = enumValue.GetEnumDescription();

            // Assert
            Assert.Equal(descricaoEsperada, resultado);
        }

        [Theory(DisplayName = "GetEnumDescription - deve retornar descrição correta para StatusVeiculo")]
        [Trait("EnumExtensions", "GetEnumDescription")]
        [InlineData("Bloqueado", "Bloqueado")]
        [InlineData("Ativo", "Ativo")]
        public void GetEnumDescription_DeveRetornarDescricaoCorreta_ParaStatusVeiculo(string enumNome, string descricaoEsperada)
        {
            // Arrange
            var enumValue = System.Enum.Parse<StatusVeiculo>(enumNome);

            // Act
            var resultado = enumValue.GetEnumDescription();

            // Assert
            Assert.Equal(descricaoEsperada, resultado);
        }

        [Fact(DisplayName = "GetEnumDescription - deve usar fixture para obter valores")]
        [Trait("EnumExtensions", "Fixture")]
        public void GetEnumDescription_DeveUsarFixtureParaObterValores()
        {
            // Arrange & Act
            var tipoPessoaFisica = _fixture.ObterTipoPessoaFisica();
            var tipoPessoaJuridica = _fixture.ObterTipoPessoaJuridica();
            var sexoMasculino = _fixture.ObterSexoMasculino();
            var sexoFeminino = _fixture.ObterSexoFeminino();

            // Assert
            Assert.Equal(SistemaInfo.BBC.Domain.Enum.ETipoPessoa.Fisica, tipoPessoaFisica);
            Assert.Equal(SistemaInfo.BBC.Domain.Enum.ETipoPessoa.Juridica, tipoPessoaJuridica);
        }
    }
}

using Xunit;

namespace BBC.Test.Tests.TipoEmpresaEnum.Fixture
{
    [CollectionDefinition(nameof(TipoEmpresaEnumCollection))]
    public class TipoEmpresaEnumCollection : ICollectionFixture<TipoEmpresaEnumFixture>
    {
        
    }

    public class TipoEmpresaEnumFixture : MockEngine
    {
        public SistemaInfo.BBC.Domain.Enum.TipoEmpresa ObterTipoEmpresaJSL()
        {
            return SistemaInfo.BBC.Domain.Enum.TipoEmpresa.JSL;
        }

        public SistemaInfo.BBC.Domain.Enum.TipoEmpresa ObterTipoEmpresaMovida()
        {
            return SistemaInfo.BBC.Domain.Enum.TipoEmpresa.Movida;
        }

        public SistemaInfo.BBC.Domain.Enum.TipoEmpresa ObterTipoEmpresaBBC()
        {
            return SistemaInfo.BBC.Domain.Enum.TipoEmpresa.BBC;
        }

        public SistemaInfo.BBC.Domain.Enum.TipoEmpresa[] ObterTodosTiposEmpresa()
        {
            return new[]
            {
                SistemaInfo.BBC.Domain.Enum.TipoEmpresa.JSL,
                SistemaInfo.BBC.Domain.Enum.TipoEmpresa.Movida,
                SistemaInfo.BBC.Domain.Enum.TipoEmpresa.BBC
            };
        }

        public string[] ObterDescricoesTipoEmpresa()
        {
            return
            [
                "JSL",
                "Movida",
                "BBC"
            ];
        }

        public int[] ObterValoresTipoEmpresa()
        {
            return new[] { 0, 1, 2 };
        }
    }
}

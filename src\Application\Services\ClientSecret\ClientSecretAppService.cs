using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using AutoMapper.QueryableExtensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Extensions.Internal;
using Microsoft.Extensions.Internal;
using NLog;
using SistemaInfo.BBC.Application.Interface.ClientSecret;
using SistemaInfo.BBC.Application.Interface.Usuario;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.ClientSecret;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.BBC.Domain.Models.ClientSecret.Commands;
using SistemaInfo.BBC.Domain.Models.ClientSecret.Repository;
using SistemaInfo.BBC.Domain.Models.Empresa.Repository;
using SistemaInfo.BBC.Domain.Models.Usuario.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.Services.ClientSecret
{
    public class ClientSecretAppService : AppService<Domain.Models.ClientSecret.ClientSecret,
            IClientSecretReadRepository, IClientSecretWriteRepository>,
        IClientSecretAppService
    {
        private readonly IUsuarioReadRepository _usuarioReadRepository;
        private readonly IEmpresaReadRepository _empresaReadRepository;

        public ClientSecretAppService(IAppEngine engine,
            IClientSecretReadRepository readRepository,
            IClientSecretWriteRepository writeRepository,
            IUsuarioReadRepository usuarioReadRepository,
            IEmpresaReadRepository empresaReadRepository)
            : base(engine, readRepository, writeRepository)
        {
            _usuarioReadRepository = usuarioReadRepository;
            _empresaReadRepository = empresaReadRepository;
        }

        public async Task<RespPadrao> ConsultarPorId(int idClientSecret)
        {
            var lLog = LogManager.GetCurrentClassLogger();
            try
            {
                if (idClientSecret <= 0)
                    throw new Exception("Id inválido.");

                var usuario = await _usuarioReadRepository.GetByIdAsync(User.Id);

                if (usuario == null)
                    throw new Exception("Usuário não encontrado.");

                var clientsecret = await Repository.Query.GetByIdIncludeEmpresaGrupoAsync(idClientSecret);

                if (clientsecret == null)
                    throw new Exception("Client secret não encontrada.");

                if (!usuario.GrupoEmpresaId.HasValue && usuario.EmpresaId.HasValue && clientsecret.GrupoEmpresaId.HasValue)
                    throw new Exception("Usuário sem autorização para consultar essa client secret.");

                var empresasId = await _usuarioReadRepository.GetEmpresasAcessoUsuario(usuario.Id);   
                if (!usuario.GrupoEmpresaId.HasValue && usuario.EmpresaId.HasValue && clientsecret.IdEmpresa.HasValue &&
                    !empresasId.Contains(clientsecret.IdEmpresa.Value))
                    throw new Exception("Usuário não pertence à empresa da client secret.");

                //if (usuario.GrupoEmpresaId.HasValue && clientsecret.GrupoEmpresaId.HasValue &&
                //    usuario.GrupoEmpresaId.Value != clientsecret.GrupoEmpresaId.Value)
                //    throw new Exception("Usuário não pertence ao grupo de empresa da client secret.");

                if (usuario.GrupoEmpresaId.HasValue && clientsecret.IdEmpresa.HasValue)
                {
                    var empresas = await _empresaReadRepository.GetEmpresasIdByGrupoEmpresaId(usuario.GrupoEmpresaId.Value);
                    if (!empresas.Contains(clientsecret.IdEmpresa.Value))
                        throw new Exception("Empresa da client secret não pertence ao grupo de empresa do usuário.");
                }

                return new RespPadrao(true, "Sucesso!", Mapper.Map<ConsultarClientSecretResponse>(clientsecret));
            }
            catch (Exception e)
            {
                lLog.Error(e);
                return new RespPadrao(false, e.Message);
            }
        }

        public async Task<RespPadrao> ConsultarGridClientSecret(int take, int page,
            OrderFilters orderFilters, List<QueryFilters> filters)
        {
            var lLog = LogManager.GetCurrentClassLogger();
            try
            {
                
                #region Consulta personalizada filtros da grid

                foreach (var item in filters)
                {
                    item.Valor = item.Campo switch
                    {
                        "cnpjSearch" => item.Valor.Replace(".", "").Replace("/", "").Replace("-", ""),
                        _ => item.Valor
                    };
                }

                #endregion
                
                var lClientSecret = Repository.Query
                    .Include(c => c.Empresa)
                    .Include(c => c.GrupoEmpresa)
                    .Include(c => c.Usuario)
                    .AsQueryable();
                
                var empresas = await _usuarioReadRepository.GetEmpresasAcessoUsuario(Engine.User.Id);
                if (!empresas.IsEmpty())
                {
                    lClientSecret = lClientSecret.Where(x =>
                        x.IdEmpresa.HasValue && empresas.Contains(x.IdEmpresa.ToInt()));
                }
                
                //var lUser = await _usuarioReadRepository.GetByIdAsync(Engine.User.Id);
                
                /*if (lUser.EmpresaId != null)
                    lClientSecret = lClientSecret.Where(x => x.IdEmpresa == lUser.EmpresaId);
                */
                
                /*if (lUser.EmpresaId == null && lUser.GrupoEmpresaId != null)
                {
                    var lEmpresasGrupo = await _empresaReadRepository
                        .GetEmpresasIdByGrupoEmpresaId(lUser.GrupoEmpresaId.ToInt());
                    lClientSecret = lClientSecret.Where(x =>
                        x.GrupoEmpresaId == lUser.GrupoEmpresaId ||
                        x.IdEmpresa.HasValue && lEmpresasGrupo.Contains(x.IdEmpresa.ToInt()));
                }*/

                var lClientSecretQuery =
                    lClientSecret.ProjectTo<ConsultarClientSecretGrid>(Engine.Mapper.ConfigurationProvider);
            
                lClientSecretQuery = lClientSecretQuery.AplicarFiltrosDinamicos(filters);

                lClientSecretQuery = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                    ? lClientSecretQuery.OrderByDescending(c => c.Id)
                    : lClientSecretQuery.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

                var totalItems = await lClientSecretQuery.CountAsync();

                lClientSecretQuery = lClientSecretQuery.Skip((page - 1) * take).Take(take);

                var lClientSecretList = await lClientSecretQuery.ToListAsync();


                return new RespPadrao()
                {
                    sucesso = true,
                    mensagem = "sucesso",
                    data = new {
                        items = lClientSecretList,
                        totalItems
                    }
                };
            }
            catch (Exception e)
            {
                lLog.Error(e);
                return new RespPadrao(false, e.Message);
            }
        }

        public async Task<RespPadrao> SaveClientSecret(ClientSecretRequest lModel)
        {
            var lLog = LogManager.GetCurrentClassLogger();
            try
            {
                if (lModel is null)
                    throw new InvalidOperationException("Requisição enviada com dados inválidos.");
                
                await DesativarClientSecretExpirada();

                if (!lModel.IdEmpresa.HasValue && !lModel.GrupoEmpresaId.HasValue)
                    throw new InvalidOperationException("Client secret precisa de empresa ou grupo de empresa.");

                if (lModel.IdEmpresa.HasValue && lModel.GrupoEmpresaId.HasValue)
                    lModel.GrupoEmpresaId = null;

                var usuario = await _usuarioReadRepository.GetByIdAsync(Engine.User.Id);

                if (usuario.GrupoEmpresaId != null)
                {
                    if (lModel.GrupoEmpresaId.HasValue && lModel.GrupoEmpresaId.Value != usuario.GrupoEmpresaId)
                        throw new InvalidOperationException(
                            "Usuário sem autorização para salvar client secrets para o grupo de empresas selecionado.");

                    if (lModel.IdEmpresa.HasValue)
                    {
                        var empresas = await _empresaReadRepository
                            .GetEmpresasIdByGrupoEmpresaId(usuario.GrupoEmpresaId.Value);

                        if (!empresas.Contains(lModel.IdEmpresa.Value))
                            throw new InvalidOperationException(
                                "Empresa não pertence ao grupo de empresas do usuário.");
                    }
                }

                var empresasAcesso = await _usuarioReadRepository.GetEmpresasAcessoUsuario(usuario.Id);
                if (!empresasAcesso.IsEmpty())
                {
                    if (usuario.GrupoEmpresaId == null && lModel.GrupoEmpresaId.HasValue)
                        throw new InvalidOperationException(
                            "Usuário sem autorização para salvar client secrets para grupos de empresa.");

                    if (!lModel.IdEmpresa.HasValue)
                        throw new InvalidOperationException("Client secret precisa de empresa.");
                    
                    if (lModel.IdEmpresa.HasValue && !empresasAcesso.Contains(lModel.IdEmpresa.Value))
                        throw new InvalidOperationException(
                            "Usuário sem autorização para salvar client secrets para a empresa selecionada.");
                }

                if (lModel.DataExpiracao.HasValue)
                    lModel.DataExpiracao = lModel.DataExpiracao.ToDateTime().Date.EndOfDay();

                if (!string.IsNullOrWhiteSpace(lModel.Senha))
                    lModel.Senha = lModel.Senha.GetHashSha1();

                if (lModel.Id != 0)
                {
                    var lClientSecretUpdate = Mapper.Map<ClientSecretEditarCommand>(lModel);
                    await Engine.CommandBus.SendCommandAsync(lClientSecretUpdate);
                    return new RespPadrao()
                    {
                        sucesso = true,
                        mensagem = "Client secret editada com sucesso.",
                        data = lModel.SecretKey
                    };
                }

                var secretKey = GenerateClientSecret(42);

                lModel.SecretKey = secretKey;

                if (lModel.GrupoEmpresaId.HasValue)
                {
                    if (await Repository.Query.AnyAtivaPorGrupoEmpresaIdAsync(lModel.GrupoEmpresaId.Value))
                        throw new InvalidOperationException(
                            "Client secret já cadastrada para o grupo de empresa.");
                }

                if (lModel.IdEmpresa.HasValue)
                {
                    if (await Repository.Query.AnyAtivaPorEmpresaIdAsync(lModel.IdEmpresa.Value))
                        throw new InvalidOperationException(
                            "Client secret já cadastrada para a empresa.");
                }

                var command = Mapper.Map<ClientSecretAdicionarCommand>(lModel);

                await Engine.CommandBus.SendCommandAsync(command);

                return new RespPadrao
                {
                    sucesso = true,
                    mensagem = "Client secret salva com sucesso.",
                    data = lModel.SecretKey
                };
            }
            catch (Exception e)
            {
                lLog.Error(e);
                return new RespPadrao(false, "Não foi possível salvar a client secret. " + e.Message);
            }
        }

        public async Task DesativarClientSecretExpirada()
        {
            try
            {
                var clientSecrets = await Repository.Query.GetAtivasPorPeriodoAsync();

                foreach (var clientSecret in clientSecrets)
                {
                    clientSecret.Ativo = 0;
                    clientSecret.DataDesativacao = DateTime.Now;
                    clientSecret.UsuarioAlteracaoId = User.Id == 0 ? clientSecret.UsuarioAlteracaoId : User.Id;
                }
                await Repository.Command.SaveChangesAsync();
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                throw;
            }
        }

        public async Task<RespPadrao> AlterarStatus(ClientSecretAlterarStatusRequest lModel)
        {
            try
            {
                var command = Mapper.Map<ClientSecretAlterarStatusCommand>(lModel);

                await Engine.CommandBus.SendCommandAsync<Domain.Models.ClientSecret.ClientSecret>(command);

                return new RespPadrao(true, "Processo concluído.");
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = "Ocorreu um erro ao tentar alterar o status da client secret.",
                    data = e.Message
                };
            }
        }

        private string GenerateClientSecret(int length)
        {
            try
            {
                const string allowedChars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
                string md5Hash;
                byte[] inputBytes = Encoding.UTF8.GetBytes(DateTime.Now.ToString("yyyyMMddHHmmss"));

                using (MD5 md5 = MD5.Create())
                {
                    byte[] hashBytes = md5.ComputeHash(inputBytes);
                    StringBuilder sbData = new StringBuilder();
                    for (int i = 0; i < hashBytes.Length; i++)
                    {
                        sbData.Append(hashBytes[i].ToString("x2"));
                    }

                    md5Hash = sbData.ToString();
                }
                
                using (RandomNumberGenerator rng = new RNGCryptoServiceProvider())
                {
                    StringBuilder sb = new StringBuilder(length);

                    byte[] randomBytes = new byte[length - md5Hash.Length];

                    rng.GetBytes(randomBytes);

                    foreach (byte b in randomBytes)
                    {
                        sb.Append(allowedChars[b % allowedChars.Length]);
                    }

                    return md5Hash + sb;
                }
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                throw;
            }
        }
    }
}

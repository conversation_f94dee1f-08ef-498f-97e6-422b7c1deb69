using SistemaInfo.BBC.Domain.Enum;
using Xunit;

namespace BBC.Test.Tests.EnumHelper.Fixture
{
    [CollectionDefinition(nameof(EnumHelperCollection))]
    public class EnumHelperCollection : ICollectionFixture<EnumHelperFixture>
    {

    }

    public class EnumHelperFixture : MockEngine
    {
        public ESexo ObterESexoMasculino()
        {
            return ESexo.Masculino;
        }

        public ESexo ObterESexoFeminino()
        {
            return ESexo.Feminino;
        }

        public StatusVeiculo ObterStatusVeiculoAtivo()
        {
            return StatusVeiculo.Ativo;
        }

        public StatusVeiculo ObterStatusVeiculoBloqueado()
        {
            return StatusVeiculo.Bloqueado;
        }

        public Tipo ObterTipoAdiantamento()
        {
            return Tipo.Adiantamento;
        }

        public Tipo ObterTipoSaldo()
        {
            return Tipo.Saldo;
        }

        public Tipo ObterTipoComplemento()
        {
            return Tipo.Complemento;
        }
    }
}

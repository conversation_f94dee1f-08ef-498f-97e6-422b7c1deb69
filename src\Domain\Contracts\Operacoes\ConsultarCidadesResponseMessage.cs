using System.Collections.Generic;
using SistemaInfo.BBC.Domain.External.CIOT.DTO;

namespace SistemaInfo.BBC.Domain.Contracts.Operacoes
{
    
    public class ConsultarCidadesReqMessage
    {
        public string Nome { get; set; }
    }
    
    public class ConsultarCidadesRespMessage
    {
        public ConsultarCidadesRespMessage(bool sucesso, string excecao)
        {
            Sucesso = sucesso;
            Erro = new Excecao()
            {
                Mensagem = excecao
            };
        }
        public ConsultarCidadesRespMessage() { }
        public bool Sucesso { get; set; }
        public Excecao Erro { get; set; }
        public List<Cidade> Cidades { get; set; }
    }
}
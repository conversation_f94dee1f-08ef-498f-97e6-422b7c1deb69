using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SistemaInfo.BBC.Application.Interface.Empresa;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.Documentos;
using SistemaInfo.BBC.Application.Objects.Web.Empresa;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Web.Attributes;
using SistemaInfo.BBC.Web.Controllers.Base;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;


namespace SistemaInfo.BBC.Web.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [Route("Empresa")]
    public class EmpresaController : WebControllerBase<IEmpresaAppService>
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="engine"></param>
        /// <param name="appService"></param>
        public EmpresaController(IAppEngine engine, IEmpresaAppService appService) : base(engine, appService)
        {
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="empresa"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost("Cadastre")]
        public async Task<JsonResult> CadastrarSe([FromBody] EmpresaRequest empresa) =>
            ResponseBase.Responder(await AppService.CadastreSe(empresa));

        /// <summary>
        /// Chamado ao atualizar o cadastro de uma empresa já existente
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("Cadastrar")]
        [Menu(new[] { EMenus.Empresa })]
        public async Task<JsonResult> Cadastrar([FromBody] EmpresaRequest request) =>
            ResponseBase.Responder(await AppService.Cadastrar(request));
        
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("ConsultarGridDocumentosEmpresa")]
        public async Task<JsonResult> ConsultarGridDocumentosEmpresa([FromBody] ConsultarGridDocumentosEmpresaRequest request) =>
            ResponseBase.ResponderSucesso(await AppService.ConsultarGridDocumentosEmpresa(request));
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("ExcluirDocumentosEmpresa")]
        public async Task<JsonResult> ExcluirDocumentosEmpresa(int id) =>
            ResponseBase.ResponderSucesso(await AppService.ExcluirDocumentosEmpresa(id));
        
        

        /// <summary>
        /// Chamado ao cadastrar uma empresa nova
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("CadastrarComRetorno")]
        [Menu(new[] { EMenus.Empresa })]
        public async Task<JsonResult> CadastrarComRetorno([FromBody] EmpresaRequest request) =>
            ResponseBase.Responder(await AppService.CadastrarComRetornoAsync(request));

        /// <summary>
        /// 
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("AtivarInativarEmpresa")]
        [Menu(new[] { EMenus.Empresa })]
        public async Task<JsonResult> AtivarInativar([FromBody] EmpresaRequest request)
        {
            var resp = await AppService.AtivarInativar(request);
            return new JsonResult(new
            {
                message = resp.mensagem,
                success = resp.sucesso,
                data = resp.data
            });
        }
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("ConsultarDadosGridAvaliacaoEmpresa")]
        [Menu(new[] { EMenus.Empresa })]
        public JsonResult GridAvaliacaoEmpresa([FromBody] BaseGridRequest request)
        {
            try
            {
                var response = AppService.DadosGridAvaliacaoEmpresa(request.Take, request.Page, request.Order, request.Filters);
                return ResponseBase.JsonGrid(response.Registros, response.TotalRegistros);
            }
            catch (Exception e)
            {
                return ResponseBase.ResponderErro(e.Message);
            }
        }
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("ConsultarDadosGridEmpresa")]
        [Menu(new[] { EMenus.Empresa })]
        public JsonResult DadosGridAEmpresa([FromBody] BaseGridRequest request)
        {
            try
            {
                var response = AppService.DadosGridEmpresa(request.Take, request.Page, request.Order, request.Filters);
                return ResponseBase.JsonGrid(response.Registros, response.TotalRegistros);
            }
            catch (Exception e)
            {
                return ResponseBase.ResponderErro(e.Message);
            }
        }
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("ConsultarGridPorGrupoEmpresa")]
        [Menu(new[] { EMenus.Empresa })]
        public JsonResult GridPorGrupoEmpresa([FromBody] ConsultarGridPorGrupoEmpresaRequest request)
        {
            try
            {
                if (request.GrupoEmpresaId == null || request.GrupoEmpresaId == 0)
                    return ResponseBase.JsonGrid(null, 0);
                
                var response = AppService.DadosGridEmpresa(request.Take, request.Page, request.Order, request.Filters, request.GrupoEmpresaId);
                
                return ResponseBase.JsonGrid(response.Registros, response.TotalRegistros);
            }
            catch (Exception e)
            {
                return ResponseBase.ResponderErro(e.Message);
            }
        }
        
        /// <summary>
        /// Retorna os dados da empresa a qual será aviada 
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("DadosParaValidar")]
        [Menu(new[] { EMenus.Empresa })]
        public JsonResult DadosEmpresaParaValidar(int id)
        {
            try
            {
                return ResponseBase.ResponderSucesso(AppService.DadosEmpresaParaValidar(id));
            }
            catch (Exception e)
            {
                return ResponseBase.ResponderErro(e.Message);
            }
        }
        
        /// <summary>
        /// Busca os dados da empresa para realizar a edição
        /// </summary>
        /// <param name="idEmpresa"></param>
        /// <returns></returns>
        [HttpGet("DadosEmpresaParaEditar")]
        [Menu(new[] { EMenus.Empresa })]
        public JsonResult DadosEmpresaParaEditar(int idEmpresa)
        {
            try
            {
                return ResponseBase.ResponderSucesso(AppService.BuscarPorIdParaEdicao(idEmpresa).Result);
            }
            catch (Exception e)
            {
                return ResponseBase.ResponderErro("Empresa não encontrada. Mensagem: " + e.Message);
            }
        }
        
        // <summary>
        /// Busca os dados da empresa para realizar a edição
        /// 
        /// <param name="idEmpresa"></param>
        /// <returns></returns>
        [HttpGet("ConsultarPorId")]
        [Menu(new[] { EMenus.Empresa, EMenus.GrupoEmpresa, EMenus.TipoEmpresa, EMenus.ClientSecret})]
        public JsonResult ConsultarPorId(int idEmpresa)
        {
            try
            {
                return ResponseBase.ResponderSucesso(AppService.BuscarPorId(idEmpresa));
            }
            catch (Exception e)
            {
                return ResponseBase.ResponderErro("Empresa não encontrada. Mensagem: " + e.Message);
            }
        }
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="destinatario"></param>
        /// <param name="statusCadastro"></param>
        /// <param name="parecerExterno"></param>
        /// <param name="usuario"></param>
        /// <param name="senha"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet("EnviarEmailValidacaoEmpresa")]
        public JsonResult EnviarEmailValidacaoEmpresa(string destinatario, StatusCadastro statusCadastro, string parecerExterno, string usuario, string senha)
        {
            try
            {
                AppService.EnviarEmailValidacaoCadastro(destinatario, statusCadastro, parecerExterno, usuario, senha);
                return ResponseBase.ResponderSucesso(null);
            }
            catch (Exception e)
            {
                return ResponseBase.ResponderErro(e.Message);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("ConsultarGridEmpresaCombo")] 
        [Menu(new[] { EMenus.Banco, EMenus.BloqueioSpd, EMenus.CentralNotificacoes, EMenus.CentralPendencias, 
            EMenus.CentroCusto, EMenus.ClientSecret, EMenus.Cliente, EMenus.Fabricante, EMenus.GrupoUsuario, EMenus.ModeloVeiculo,
            EMenus.PainelCiot, EMenus.PainelPagamento,EMenus.PainelPagamentoAbastecimento, 
            EMenus.PainelPagamentoValePedagio, EMenus.PainelPedidosPendentes, EMenus.ProtocoloAbastecimento, EMenus.Portador, 
            EMenus.TipoEmpresa,EMenus.Usuario, EMenus.Veiculo, EMenus.Viagens, EMenus.ManutencaoAbastecimento, EMenus.UsuarioFrota })]
        public async Task<JsonResult> ConsultarGridEmpresaCombo([FromBody] BaseGridRequest request)
        {
            var consultaGridEmpresa = await AppService.ConsultarGridEmpresaCombo(request.Take, request.Page, request.Order, request.Filters);
            return ResponseBase.ResponderSucesso(consultaGridEmpresa);
        }
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("ConsultarGridEmpresaComboAbastecimentos")] 
        [Menu(new[] { EMenus.Banco, EMenus.BloqueioSpd, EMenus.CentralNotificacoes, EMenus.CentralPendencias, 
            EMenus.CentroCusto, EMenus.ClientSecret, EMenus.Cliente, EMenus.Fabricante, EMenus.GrupoUsuario, EMenus.ModeloVeiculo,
            EMenus.PainelCiot, EMenus.PainelPagamento,EMenus.PainelPagamentoAbastecimento, 
            EMenus.PainelPagamentoValePedagio, EMenus.PainelPedidosPendentes, EMenus.ProtocoloAbastecimento, EMenus.Portador, 
            EMenus.TipoEmpresa,EMenus.Usuario, EMenus.Veiculo, EMenus.Viagens, EMenus.ManutencaoAbastecimento })]
        public async Task<JsonResult> ConsultarGridEmpresaComboAbastecimentos([FromBody] BaseGridRequest request)
        {
            var consultaGridEmpresa = await AppService.ConsultarGridEmpresaComboAbastecimentos(request.Take, request.Page, request.Order, request.Filters);
            return ResponseBase.ResponderSucesso(consultaGridEmpresa);
        }
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("ConsultarGridEmpresaComboClientSecret")]
        [Menu(new[] { EMenus.ClientSecret })]
        public async Task<JsonResult> ConsultarGridEmpresaComboClientSecret([FromBody] BaseGridRequest request)
        {
            var consultaGridEmpresa = await AppService.ConsultarGridEmpresaComboClientSecret(request.Take, request.Page, request.Order, request.Filters);
            return ResponseBase.ResponderSucesso(consultaGridEmpresa);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="empresaCnpj"></param>
        /// <returns></returns>
        [HttpGet("ConsultarClienteIps")]
        [Menu(new[] { EMenus.Empresa })]
        public async Task<JsonResult> ConsultarClienteIps(string empresaCnpj) =>
            ResponseBase.ResponderSucesso(await AppService.ConsultarClienteIps(empresaCnpj));
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("ConsultarGridEmpresaComboProtocoloAbastecimento")]
        [Menu(new[] { EMenus.ProtocoloAbastecimento })]
        public JsonResult ConsultarGridEmpresaComboProtocoloAbastecimento([FromBody] BaseGridRequest request)
        {
            var consultaGridEmpresa = AppService
                .ConsultarGridEmpresaComboProtocoloAbastecimento(request.Take, request.Page, 
                    request.Order, request.Filters);
            return ResponseBase.ResponderSucesso(consultaGridEmpresa);
        }
        
        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpPost("ConsultarEmpresaCombo")]
        public JsonResult ConsultarEmpresaCombo()
        {
            var consultarEmpresaCombo = AppService.ConsultarEmpresaCombo();
            return ResponseBase.ResponderSucesso(consultarEmpresaCombo);
        }
        
        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpGet("ConsultaParametroEmpresaPermiteEncerramentoCiot")]
        [Menu(new[] { EMenus.PainelCiot })]
        public async Task<JsonResult> ConsultaParametroPermiteEncerramentoCiot()
        {
            var retorno = await AppService.ConsultaParametroEmpresaPermiteEncerramentoCiot();
            return ResponseBase.Responder(retorno.sucesso, retorno.mensagem, retorno.data);
        }
    }
}
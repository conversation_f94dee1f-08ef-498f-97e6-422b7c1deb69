using System.Collections.Generic;
using Newtonsoft.Json;
using SistemaInfo.BBC.Domain.Enum;

namespace SistemaInfo.BBC.Application.Objects.Api.Viagem
{
    public class CancelamentoEventoViagemResponse
    {
        public CancelamentoEventoViagemResponse(bool sucesso, string mensagem)
        {
            Sucesso = sucesso;
            Mensagem = mensagem;
        }

        public CancelamentoEventoViagemResponse()
        {
        }

        public bool Sucesso { get; set; }
        public string Mensagem { get; set; }
        public CancelamentoEventoViagemPagamentoResponse PagamentoEvento { get; set; }
        public List<CancelamentoEventoViagemTransacaoResponse> Transacoes { get; set; }

        public string ToJson()
        {
            return JsonConvert.SerializeObject(this);
        }
    }
    
    public class CancelamentoEventoViagemPagamentoResponse
    {
        public int Id { get; set; }
        public string Status { get; set; }
        public string Tipo { get; set; }
    }
    
    public class CancelamentoEventoViagemTransacaoResponse
    {
        public bool Sucesso { get; set; }
        public string Mensagem { get; set; }
        public decimal Valor { get; set; }
        public int ContaOrigem { get; set; }
        public int? ContaDestino { get; set; }
        public string Agencia { get; set; }
        public string Conta { get; set; }
        public string CodigoBanco { get; set; }
        public string Status { get; set; }
        public StatusPagamento StatusEnum { get; set; }
        public int Id { get; set; }
    }
}
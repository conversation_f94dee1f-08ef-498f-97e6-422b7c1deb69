using BBC.Test.Tests.TipoEmpresaEnum.Fixture;
using System.ComponentModel;
using System.Reflection;
using Xunit;

namespace BBC.Test.Tests.TipoEmpresaEnum
{
    [Collection(nameof(TipoEmpresaEnumCollection))]
    public class TipoEmpresaEnumTest
    {
        private readonly TipoEmpresaEnumFixture _fixture;

        public TipoEmpresaEnumTest(TipoEmpresaEnumFixture fixture)
        {
            _fixture = fixture;
        }

        [Theory(DisplayName = "TipoEmpresa - deve conter valores corretos")]
        [Trait("TipoEmpresaEnum", "ValoresEnum")]
        [InlineData(0, "JSL")]
        [InlineData(1, "Movida")]
        [InlineData(2, "BBC")]
        public void TipoEmpresa_DeveConterValoresCorretos(int valorEsperado, string nomeEnum)
        {
            // Arrange & Act
            var enumValue = System.Enum.Parse<SistemaInfo.BBC.Domain.Enum.TipoEmpresa>(nomeEnum);

            // Assert
            Assert.Equal(valorEsperado, (int)enumValue);
        }

        [Theory(DisplayName = "TipoEmpresa - deve conter descrições corretas")]
        [Trait("TipoEmpresaEnum", "DescricoesEnum")]
        [InlineData("JSL", "JSL")]
        [InlineData("Movida", "Movida")]
        [InlineData("BBC", "BBC")]
        public void TipoEmpresa_DeveConterDescricoesCorretas(string nomeEnum, string descricaoEsperada)
        {
            // Arrange
            var enumValue = System.Enum.Parse<SistemaInfo.BBC.Domain.Enum.TipoEmpresa>(nomeEnum);
            var field = typeof(SistemaInfo.BBC.Domain.Enum.TipoEmpresa).GetField(enumValue.ToString());
            var attribute = field.GetCustomAttribute<DescriptionAttribute>();

            // Act & Assert
            Assert.NotNull(attribute);
            Assert.Equal(descricaoEsperada, attribute.Description);
        }

        [Fact(DisplayName = "TipoEmpresa - deve conter três valores")]
        [Trait("TipoEmpresaEnum", "QuantidadeValores")]
        public void TipoEmpresa_DeveConterTresValores()
        {
            // Arrange & Act
            var valores = System.Enum.GetValues(typeof(SistemaInfo.BBC.Domain.Enum.TipoEmpresa));

            // Assert
            Assert.Equal(3, valores.Length);
        }

        [Theory(DisplayName = "TipoEmpresa - deve permitir conversão para string")]
        [Trait("TipoEmpresaEnum", "ConversaoString")]
        [InlineData("JSL")]
        [InlineData("Movida")]
        [InlineData("BBC")]
        public void TipoEmpresa_DevePermitirConversaoParaString(string nomeEsperado)
        {
            // Arrange & Act
            var enumValue = System.Enum.Parse<SistemaInfo.BBC.Domain.Enum.TipoEmpresa>(nomeEsperado);
            var resultado = enumValue.ToString();

            // Assert
            Assert.Equal(nomeEsperado, resultado);
        }

        [Theory(DisplayName = "TipoEmpresa - deve permitir conversão de string")]
        [Trait("TipoEmpresaEnum", "ConversaoDeString")]
        [InlineData("JSL", SistemaInfo.BBC.Domain.Enum.TipoEmpresa.JSL)]
        [InlineData("Movida", SistemaInfo.BBC.Domain.Enum.TipoEmpresa.Movida)]
        [InlineData("BBC", SistemaInfo.BBC.Domain.Enum.TipoEmpresa.BBC)]
        public void TipoEmpresa_DevePermitirConversaoDeString(string nomeEnum, SistemaInfo.BBC.Domain.Enum.TipoEmpresa enumEsperado)
        {
            // Arrange & Act
            var resultado = System.Enum.Parse<SistemaInfo.BBC.Domain.Enum.TipoEmpresa>(nomeEnum);

            // Assert
            Assert.Equal(enumEsperado, resultado);
        }

        [Theory(DisplayName = "TipoEmpresa - deve verificar se valor é definido")]
        [Trait("TipoEmpresaEnum", "ValorDefinido")]
        [InlineData(0, true)]  // JSL
        [InlineData(1, true)]  // Movida
        [InlineData(2, true)]  // BBC
        [InlineData(999, false)] // Valor inválido
        public void TipoEmpresa_DeveVerificarSeValorEDefinido(int valor, bool esperado)
        {
            // Arrange & Act
            var resultado = System.Enum.IsDefined(typeof(SistemaInfo.BBC.Domain.Enum.TipoEmpresa), valor);

            // Assert
            Assert.Equal(esperado, resultado);
        }

        [Fact(DisplayName = "TipoEmpresa - deve usar fixture para obter valores")]
        [Trait("TipoEmpresaEnum", "Fixture")]
        public void TipoEmpresa_DeveUsarFixtureParaObterValores()
        {
            // Arrange & Act
            var jsl = _fixture.ObterTipoEmpresaJSL();
            var movida = _fixture.ObterTipoEmpresaMovida();
            var bbc = _fixture.ObterTipoEmpresaBBC();

            // Assert
            Assert.Equal(SistemaInfo.BBC.Domain.Enum.TipoEmpresa.JSL, jsl);
            Assert.Equal(SistemaInfo.BBC.Domain.Enum.TipoEmpresa.Movida, movida);
            Assert.Equal(SistemaInfo.BBC.Domain.Enum.TipoEmpresa.BBC, bbc);
        }
    }
}

echo off
setlocal enabledelayedexpansion
cd %~dp0
set pasta_raiz=%~dp0
call utils\variaveis.bat

::===================================================================================
::Config site
set dllPadrao=BBC
set origemBat=Web
::===================================================================================
:input
	call :info Selecione o projeto de deploy
	call :opt 0 - Controle
	call :opt 1 - Posto
	call :opt 2 - Ciot Publico
	echo.
	set /p tipo_projeto=""
	set tipo_projeto=%tipo_projeto%
	call :info Projeto selecionado: %tipo_projeto%
	
if %tipo_projeto% == 0 (
set src_project=Web
set src_front=Front
set site=BBC-Controle
set src_pack=BBC - BBC Controle
set src_pack_back=BBC - BBC Controle\bin
goto :input0
)
if %tipo_projeto% == 1 (
set src_project=Web
set src_front=Front.Posto
set site=BBC-Posto
set src_pack=BBC - Rede BBC Controle
set src_pack_back=BBC - Rede BBC Controle\bin
goto :input0
)
if %tipo_projeto% == 2 (
set src_project=ApiCiotPublico
set src_front=Web CIOT BBC
set site=BBC-Ciot-Publico
set src_pack=BBC - Ciot - Publico
set src_pack_back=BBC - Ciot - Publico\bin
goto :input0
)
else (
goto :input
)
:input0
	call :info Selecione o ambiente de deploy
	call :opt 0 - DEV
	call :opt 1 - HML
	call :opt 2 - PROD-SIMULADO
	echo.
	set /p tipo_ambiente=""
	set tipo_ambiente=%tipo_ambiente%
	call :info Ambiente selecionado: %tipo_ambiente%

if %tipo_ambiente% == 0 (
set ambiente=DEV
goto :input1
)
if %tipo_ambiente% == 1 (
set ambiente=HML
goto :input1
)
if %tipo_ambiente% == 2 (
set ambiente=PROD
goto :input1
)
else (
goto :input0
)

:input1
	call :info Digite o tipo de build existente
	call :opt 0 - Debug
	call :opt 1 - Release
	echo.
	set /p tipo_build=""
	set tipo_build=%tipo_build%
	call :info tipo_build selecionado: %tipo_build%

if %tipo_build% == 0 (
set tipo=Debug
goto :input2
)
if %tipo_build% == 1 (
set tipo=Release
goto :input2
)
else (
goto :input1
)

:input2
	call :info Digite o tipo de pacote a ser gerado
	call :opt 0 - dll padrao
	call :opt 1 - full dll
	call :opt 2 - pacote completo
	call :opt 3 - dll padrao + front
	call :opt 4 - front
	echo.
	set /p tipo_pacote=""
	set tipo_pacote=%tipo_pacote%
	call :info tipo_pacote selecionado: %tipo_pacote%

if %tipo_pacote%==0 (
	set extensao=*%dllPadrao%*.dll bin/*%dllPadrao%*.dll
	)
if %tipo_pacote%==1 (
	set extensao=*.dll bin/*.dll
	)
if %tipo_pacote%==2 (
	set extensao=*
	)
if %tipo_pacote%==3 (
	set extensao=bin/*%dllPadrao%*.dll Scripts *.html styles/*.css scripts/*.js assets/lib/*.js
	)
if %tipo_pacote%==4 (
	set extensao=*
	)
if %tipo_pacote% gtr 4 (
	goto :input2
	)
	
:deploy
call %projeto%\scripts\deploy\utils\deploy.bat
goto :fim
exit 0
	
:: Sets up the ESC string for use later in this script
:setESC
    for /F "tokens=1,2 delims=#" %%a in ('"prompt #$H#$E# & echo on & for %%b in (1) do rem"') do (
      set ESC=%%b
      exit /B 0
    )
    exit /B 0
	
:opt
	call :setESC
	echo !ESC![92m %* !!ESC![0m
	exit /B 0
	
:log
	:: %~n0 = nome arquivo | %~x0 = extensão arquivo
	echo.
	call :setESC
	echo !ESC![95m===================================================================================================!!ESC![0m
	echo !ESC![95m	%*
	echo !ESC![95m===================================================================================================!!ESC![0m
	echo.
	exit /B 0
	
:info
	:: %~n0 = nome arquivo | %~x0 = extensão arquivo
	echo.
	call :setESC
	echo !ESC![94m===================================================================================================!!ESC![0m
	echo !ESC![95m	%*
	echo !ESC![94m===================================================================================================!!ESC![0m
	echo.
	exit /B 0

:warn
	:: %~n0 = nome arquivo | %~x0 = extensão arquivo
	echo.
	call :setESC
	echo !ESC![93m===================================================================================================!!ESC![0m
	echo !ESC![93m	%*
	echo !ESC![93m===================================================================================================!!ESC![0m
	echo.
	exit /B 0
	
:erro
	echo.
	call :setESC
	echo !ESC![91m===================================================================================================!!ESC![0m
	echo !ESC![91m	%~n0%~x0^> %*
	echo !ESC![91m===================================================================================================!!ESC![0m
	echo.
	if %NoStop%==False (pause)
	exit 1
	
:fim
	pause
	exit 0
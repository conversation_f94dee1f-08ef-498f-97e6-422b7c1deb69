<div class="container main-container">
    <br>
    <form novalidate (ngSubmit)="cancelar()" [formGroup]="cancelarForm">
        <div class="form-horizontal">
            <div class="alert alert-danger" *ngIf="errors.length > 0">
                <h3 id="msgRetorno">Opa! Alguma coisa não deu certo:</h3>
                <ul>
                    <li *ngFor="let error of errors">{{ error }}</li>
                </ul>
            </div>
            <div class="row">
                <div class="col-sm-12 col-md-6 col-lg-3">
                    <div class="form-group required" [ngClass]="{'has-error': displayMessage.ciotCancelar }">
                        <label class="control-label" for="ciotCancelar">CIOT</label>
                        <input class="form-control" id="ciotCancelar" type="text" formControlName="ciotCancelar" placeholder="Informe o CIOT" />
                        <span class="text-danger" *ngIf="displayMessage.ciotCancelar">
              <p [innerHTML]="displayMessage.ciotCancelar"></p>
            </span>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12 col-md-6 col-lg-3">
                    <div class="form-group required" [ngClass]="{'has-error': displayMessage.senhaCancelar }">
                        <label class="control-label" for="senhaCancelar">Senha</label>
                        <input class="form-control" id="senhaCancelar" type="password" OnlyNumber formControlName="senhaCancelar" placeholder="Informe a senha" />
                        <span class="text-danger" *ngIf="displayMessage.senhaCancelar">
              <p [innerHTML]="displayMessage.senhaCancelar"></p>
            </span>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12 col-md-6 col-lg-3">
                    <div class="form-group" [ngClass]="{'has-error': displayMessage.motivo }">
                        <label class="control-label" for="motivo">Motivo</label>
                        <textarea class="form-control" id="motivo" formControlName="motivo" placeholder="Informe o motivo"></textarea>
                        <span class="text-danger" *ngIf="displayMessage.motivo">
              <p [innerHTML]="displayMessage.motivo"></p>
            </span>
                    </div>
                </div>
            </div>
            <!-- <div class="g-recaptcha" data-sitekey="6LdTj0cUAAAAAJMT7t5dVnEU4k6chdIc0m-y-PZF"></div> -->
            <br/>
<!--            <re-captcha (resolved)="resolved($event)" siteKey="6LcqvgEVAAAAAEtksgMX9T9-7zHU9rje34hsJ3_5"></re-captcha>-->
            <br/>
            <button class="btn btn-danger" id="cancelar" type="submit" [disabled]='!cancelarForm.valid' style="background-color: #00622c; border-color: #00622c;">Cancelar</button>
        </div>
    </form>
</div>
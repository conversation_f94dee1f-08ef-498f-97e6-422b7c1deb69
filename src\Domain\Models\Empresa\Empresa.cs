﻿using System;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Domain.Models.Empresa.Exeptions;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Models;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Models.Validator;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Domain.Models.Empresa
{
    public class Empresa : Entity<Empresa, int, NotImplementedEntityValidator<Empresa>>
    {
        private static readonly Regex _regex = new Regex(@"^(?!\.)(""([^""\r\\]|\\[""\r\\])*""|"
                                                         + @"([-a-z0-9!#$%&'*+/=?^_`{|}~]|(?<!\.)\.)*)(?<!\.)"
                                                         + @"@[a-z0-9][\w\.-]*[a-z0-9]\.[a-z][a-z\.]*[a-z]$", RegexOptions.IgnoreCase);
        
        public string NomeFantasia { get; set; }
        public string Cnpj { get; set; }
        public string Email { get; set; }
        public string Endereco { get; set; }
        public int? CidadeId { get; set; }
        public string Cep { get; set; }
        public int? EnderecoNumero { get; set; }
        public string Telefone { get; set; }
        public int? UsuarioCadastroId { get; set; }
        public DateTime DataCadastro { get; set; }
        public int Ativo { get; set; }
        public string Celular { get; set; }
        public string Bairro { get; set; }
        public string Complemento { get; set; }
        public string RazaoSocial { get; set; }
        public int? UsuarioBloqueioId { get; set; }
        public DateTime? DataBloqueio { get; set; }
        public int? UsuarioDesbloqueioId { get; set; }
        public DateTime? DataDesbloqueio { get; set; }
        public StatusCadastro StatusCadastro { get; set; }
        public int? UsuarioValidacaoId { get; set; }
        public DateTime? DataValidacao { get; set; }
        public string ParecerInterno { get; set; }
        public string ParecerExterno { get; set; }
        public string RNTRC { get; set; }
        public string InscricaoEstadual { get; set; }
        public DateTime? DataAberturaEmpresa { get; set; }
        public string FormaConstituicao { get; set; }
        public string SenhaApi { get; set; }
        public int? TempoAbastecimento { get; set; }
        public decimal? ValorTolerancia { get; set; }
        public int? ControlaOdometro { get; set; }
        public int? ControlaAutonomia { get; set; }
        public decimal? TaxaAbastecimento { get; set; }
        public decimal? Cashback { get; set; }
        public int LiberaBloqueioSPD { get; set; } = 1;
        public int CobrancaTarifa { get; set; } = 1;
        public int? RegistraCiot { get; set; } = 0;
        public int? TipoEmpresaId { get; set; }
        public decimal? ImpostoIRRF { get; set; } = 0;
        public decimal? ImpostoCSLL { get; set; } = 0;
        public decimal? ImpostoCOFINS { get; set; } = 0;
        public decimal? ImpostoPIS { get; set; } = 0;
        public string Link { get; set; }
        public string SenhaLink { get; set; }
        public string LinkSAP { get; set; }
        public string UsuarioSAP { get; set; }
        public string SenhaSAP { get; set; }
        public decimal? PercentualAutonomiaInferior { get; set; }
        public decimal? PercentualAutonomiaSuperior { get; set; }
        public int ControlaContingencia { get; set; } = 1;
        public int? ContaAbastecimento { get; set; }
        public DateTime? DataAlteracaoContaAbastecimento { get; set; }
        public int? UsuarioAlteracaoContaAbastecimentoId { get; set; }

        public int? ContaValePedagio { get; set; }
        public DateTime? DataAlteracaoContaValePedagio { get; set; }
        public int? UsuarioAlteracaoContaValePedagioId { get; set; }

        public int? ContaFrete { get; set; }
        public DateTime? DataAlteracaoContaFrete { get; set; }
        public int? UsuarioAlteracaoContaFreteId { get; set; }

        public int? Prazo { get; set; }
        public int? GrupoEmpresaId { get; set; }
        public int UtilizaTarifaEmpresa { get; set; } = 1;
        public DateTime? DataAlteracaoModelo { get; set; }        
        public int? DebitoProtocolo { get; set; } = 1;
        public int? DebitoPrazo { get; set; } = 0;
        public int? QtdMensalSemTaxaPix { get; set; } = 0;
        public decimal? ValorTarifaPix { get; set; } = 0;
        public decimal? ValorTarifaBbc { get; set; } = 0;
        public bool? RecebedorAutorizado { get; set; }
        public decimal? PorcentagemTarifaServiceValePedagio { get; set; }
        public int? PermitirPagamentoValePedagio { get; set; } = 1;
        public int? CobrarTarifaBbcValePedagio { get; set; } = 1;
        public int? UtilizaTarifaEmpresaPagamentoPedagio { get; set; } = 1;
        public string NotificacaoContingenciaCiot { get; set; }
        public int? StatusReprocessamentoPagamentoFrete { get; set; } = 1;
        public int? HabilitaReprocessamentoValePedagio { get; set; } = 1;
        public int HabilitaPainelSaldo { get; set; }
        public byte[] ImagemCartao { get; set; }
        public decimal ValorAdiantamentoBbc { get; set; }

        public int? PermitirEncerramentoPainelCiot { get; set; } = 0;
        public int? UtilizaCiot { get; set; } = 0;

        #region Viagem 2
        public EVersaoIntegracao? VersaoIntegracaoViagem { get; set; }
        
        #endregion

        #region Propriedades de Navegacao

        public virtual ICollection<EmpresaCfop.EmpresaCfop> EmpresaCfop { get; set; }
        public virtual Cidade.Cidade Cidade { get; set; }
        public virtual ICollection<EmpresaUsuario.EmpresaUsuario> EmpresasUsuario { get; set; }
        public virtual Usuario.Usuario Usuario { get; set; }
        public virtual Usuario.Usuario UsuarioBloqueio { get; set; }
        public virtual Usuario.Usuario UsuarioDesbloqueio { get; set; }
        public virtual Usuario.Usuario UsuarioAlteracaoContaAbastecimento { get; set; }
        public virtual TipoEmpresa.TipoEmpresa TipoEmpresa { get; set; }
        public virtual GrupoEmpresa.GrupoEmpresa GrupoEmpresa { get; set; }
        
        public virtual ICollection<CentroCusto.CentroCusto> CentroCustos { get; set; }
        public virtual ICollection<Documento.Documento> Documentos { get; set; }
        public virtual ICollection<ClientSecret.ClientSecret> ClientSecret { get; set; }
        public virtual ICollection<Filial.Filial> Filial { get; set; }

        #endregion

        public void ValidarCriacao()
        {
            if (string.IsNullOrEmpty(NomeFantasia))
                throw new EmpresaInvalidException("Nome Fantasia é obrigatório.");
            
            if (NomeFantasia.Length > 200)
                throw new EmpresaInvalidException("Nome Fantasia permite apenas 200 caracteres.");
            
            if (string.IsNullOrEmpty(RazaoSocial))
                throw new EmpresaInvalidException("Razão Social é obrigatório.");
            
            if (RazaoSocial.Length > 200)
                throw new EmpresaInvalidException("Razão Social permite apenas 200 caracteres.");
            
            if (string.IsNullOrEmpty(Email))
                throw new EmpresaInvalidException("E-mail é obrigatório.");
            
            if (Email.Length > 200)
                throw new EmpresaInvalidException("E-mail permite apenas 200 caracteres.");
            
            if (!_regex.IsMatch(Email))
                throw new EmpresaInvalidException("E-mail inválido.");
            
            if (string.IsNullOrEmpty(Cnpj))
                throw new EmpresaInvalidException("CNPJ é obrigatório.");
            
            if (!Cnpj.OnlyNumbers().ValidaCNPJ())
                throw new EmpresaInvalidException("CNPJ inválido.");
            
            if (string.IsNullOrEmpty(Telefone))
                throw new EmpresaInvalidException("Telefone é obrigatório.");
            
            if (string.IsNullOrEmpty(Celular))
                throw new EmpresaInvalidException("Celular é obrigatório.");
            
            if (string.IsNullOrEmpty(Endereco))
                throw new EmpresaInvalidException("Endereço é obrigatório.");
                
            if (Endereco.Length > 200)
                throw new EmpresaInvalidException("Endereço permite apenas 200 caracteres.");
            
            if (string.IsNullOrEmpty(Bairro))
                throw new EmpresaInvalidException("Bairro é obrigatório.");
            
            if (Bairro.Length > 200)
                throw new EmpresaInvalidException("Bairro permite apenas 200 caracteres.");
            
            if (!string.IsNullOrEmpty(Complemento))
                if (Complemento.Length > 200)
                    throw new EmpresaInvalidException("Complemento permite apenas 200 caracteres.");
                
            if (!CidadeId.HasValue || CidadeId == 0)
                throw new EmpresaInvalidException("Cidade é obrigatório.");
        }

        public bool UsaCiot()
        {
            return UtilizaCiot == 1;
        }
    }
}
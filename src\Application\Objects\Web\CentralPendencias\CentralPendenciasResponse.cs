using System;
using System.Collections.Generic;

namespace SistemaInfo.BBC.Application.Objects.Web.CentralPendencias
{
    public class CentralPendenciasResponse
    {
        public int Id { get; set; }
        public string Valor { get; set; }
        public int? Status { get; set; }
        public string MotivoPendencia { get; set; }
        public int? Tipo { get; set; }
        public int? ContadorReenvio { get; set; }
        public int ViagemId { get; set; }
        public DateTime? DataBaixa { get; set; }
        public int? PagamentoExternoId { get; set; }

        public int UsuarioCadastroId { get; set; }
        public DateTime DataCadastro { get; set; }
    }

    public class CentralPendenciasDetalhesResponse
    {
        public int Id { get; set; }
        public string Valor { get; set; }
        public int? Status { get; set; }
        public string StatusDescricao { get; set; }
        public string MotivoPendencia { get; set; }
        public int? Tipo { get; set; }
        public string TipoDescricao { get; set; }
        public int? ContadorReenvio { get; set; }
        public int ViagemId { get; set; }
        public string ViagemExternoId { get; set; }
        public string? DataBaixa { get; set; }
        public int? PagamentoExternoId { get; set; }
        public string DataCadastro { get; set; }
        public int UsuarioCadastroId { get; set; }

        // Informações da Viagem/Pagamento
        public string DataPrevisaoPagamento { get; set; }
        public string FormaPagamentoDescricao { get; set; }

        // Informações do Proprietário
        public string NomeProprietario { get; set; }
        public string CpfCnpjProprietario { get; set; }

        // Informações da Empresa
        public string RazaoSocialEmpresa { get; set; }
        public string CnpjEmpresa { get; set; }

        // Informações de Antecipação
        public string StatusAntecipacaoDescricao { get; set; }
        public string AntecipacaoMotivo { get; set; }
        public DateTime? DataCadastroAntecipacao { get; set; }
        public DateTime? DataAlteracaoAntecipacao { get; set; }

        // Transações relacionadas
        public List<CentralPendenciasTransacaoResponse> Transacoes { get; set; } = new List<CentralPendenciasTransacaoResponse>();
    }

    public class CentralPendenciasTransacaoResponse
    {
        public int Id { get; set; }
        public string Valor { get; set; }
        public string Status { get; set; }
        public string StatusDescricao { get; set; }
        public DateTime? DataCriacao { get; set; }
        public DateTime? DataProcessamento { get; set; }
        public string CodigoTransacao { get; set; }
        public string JsonEnvio { get; set; }
        public string JsonResposta { get; set; }
        public string MensagemErro { get; set; }
    }
}
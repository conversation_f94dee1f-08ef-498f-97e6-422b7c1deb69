﻿using System.Collections.Generic;
using System.Threading.Tasks;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.Ciot;
using SistemaInfo.BBC.Application.Objects.Web.Documentos;
using SistemaInfo.BBC.Application.Objects.Web.Empresa;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Models.Empresa.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;

namespace SistemaInfo.BBC.Application.Interface.Empresa
{
    public interface IEmpresaAppService : IAppService<IEmpresaReadRepository, IEmpresaWriteRepository>
    {
        Task<RespPadrao> CadastreSe(EmpresaRequest empresaRequest);

        GridAvaliacaoEmpresaResponse DadosGridAvaliacaoEmpresa(int take, int page, OrderFilters orderFilters,List<QueryFilters> filters);

        GridEmpresaResponse DadosGridEmpresa(int take, int page, OrderFilters orderFilters, List<QueryFilters> filters, int? grupoEmpresaId = null);

        EmpresaParaAvaliacaoResponse DadosEmpresaParaValidar(int id);

        Task<RespPadrao> Cadastrar(EmpresaRequest request);

        Task<RespPadrao> AtivarInativar(EmpresaRequest empresaRequest);

        Task<RespPadrao> CadastrarComRetornoAsync(EmpresaRequest empresaRequest);

        Task<ConsultarPorIdEmpresaResponse> BuscarPorIdParaEdicao(int id);
        
        ConsultarPorIdEmpresaResponse BuscarPorId(int id);
        
        void EnviarEmailValidacaoCadastro(string destinatario, StatusCadastro statusCadastro, string parecerExterno, string usuario, string senha);
        Task<ConsultarEmpresaComboResponse> ConsultarGridEmpresaCombo(int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
        Task<ConsultarEmpresaComboResponse> ConsultarGridEmpresaComboClientSecret(int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
        Task<ConsultarEmpresaComboResponse> ConsultarGridEmpresaComboAbastecimentos(int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
        ConsultarEmpresaComboResponse ConsultarGridEmpresaComboProtocoloAbastecimento(int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
        
        List<ConsultarEmpresaCombo> ConsultarEmpresaCombo();
        Task<RespPadrao> ConsultaParametroEmpresaPermiteEncerramentoCiot();
        Task<List<CiotClienteIpResponseAdaptado>> ConsultarClienteIps(string empresaCnpj);
        Task<ConsultarGridDocumentosEmpresaResponse> ConsultarGridDocumentosEmpresa(ConsultarGridDocumentosEmpresaRequest request);
        Task<RespPadrao> ExcluirDocumentosEmpresa(int id);
    }
}